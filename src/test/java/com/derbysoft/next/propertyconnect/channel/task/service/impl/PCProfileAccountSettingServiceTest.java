package com.derbysoft.next.propertyconnect.channel.task.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.derbysoft.next.propertyconnect.channel.task.controller.vo.ChannelAccountVO;
import com.derbysoft.next.propertyconnect.channel.task.service.RemoteService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * Comprehensive unit tests for PCProfileAccountSettingService
 * Tests account settings retrieval from profile service
 */
@ExtendWith(MockitoExtension.class)
class PCProfileAccountSettingServiceTest {

    @InjectMocks
    private PCProfileAccountSettingService accountSettingService;

    @Mock
    private RemoteService remoteService;

    private JSONObject mockDistributorDetails;
    private JSONObject mockHotelConnections;
    private Map<String, Object> mockChannelSettings;

    @BeforeEach
    void setUp() {
        mockChannelSettings = new HashMap<>();
        mockDistributorDetails = new JSONObject();
        mockHotelConnections = new JSONObject();
    }

    @Test
    void testService_IsSpringService() {
        // Given
        Class<?> serviceClass = PCProfileAccountSettingService.class;

        // When
        boolean hasServiceAnnotation = serviceClass.isAnnotationPresent(org.springframework.stereotype.Service.class);

        // Then
        assertTrue(hasServiceAnnotation, "Service should be annotated with @Service");
    }

    @Test
    void testConstants_AreDefinedCorrectly() {
        // When & Then
        assertEquals("apiKey", PCProfileAccountSettingService.KEY_API_KEY);
        assertEquals("userName", PCProfileAccountSettingService.KEY_USER_NAME);
        assertEquals("password", PCProfileAccountSettingService.KEY_PASSWORD);
    }

    @Test
    void testGetAccountSettings_WithGroupHotelCredentials_ReturnsHotelLevelSettings() {
        // Given
        String channelId = "BOOKING";
        String channelHotelId = "HOTEL_123";
        
        Map<String, Object> hotelSettings = new HashMap<>();
        hotelSettings.put("hotelType", "Group");
        hotelSettings.put(PCProfileAccountSettingService.KEY_API_KEY, "hotel-api-key");
        hotelSettings.put(PCProfileAccountSettingService.KEY_USER_NAME, "hotel-user");
        hotelSettings.put(PCProfileAccountSettingService.KEY_PASSWORD, "hotel-pass");
        
        mockHotelConnections.put("channelSettings", hotelSettings);
        
        when(remoteService.uniqueHotelConnections(channelId, channelHotelId))
            .thenReturn(Optional.of(mockHotelConnections));

        // When
        Map<String, Object> result = accountSettingService.getAccountSettings(channelId, channelHotelId);

        // Then
        assertNotNull(result);
        assertTrue(result instanceof ChannelAccountVO.AccountSetting);
        ChannelAccountVO.AccountSetting setting = (ChannelAccountVO.AccountSetting) result;
        assertEquals("hotel-api-key", setting.getApiKey());
        assertEquals("hotel-user", setting.getUsername());
        assertEquals("hotel-pass", setting.getPassword());
        
        verify(remoteService).uniqueHotelConnections(channelId, channelHotelId);
        verify(remoteService, never()).getDistributorDetails(anyString());
    }

    @Test
    void testGetAccountSettings_WithNoGroupHotelCredentials_FallsBackToChannelLevel() {
        // Given
        String channelId = "BOOKING";
        String channelHotelId = "HOTEL_123";
        
        Map<String, Object> channelSettings = new HashMap<>();
        channelSettings.put(PCProfileAccountSettingService.KEY_API_KEY, "channel-api-key");
        channelSettings.put(PCProfileAccountSettingService.KEY_USER_NAME, "channel-user");
        
        mockDistributorDetails.put("settings", channelSettings);
        
        when(remoteService.uniqueHotelConnections(channelId, channelHotelId))
            .thenReturn(Optional.empty());
        when(remoteService.getDistributorDetails(channelId))
            .thenReturn(Optional.of(mockDistributorDetails));

        // When
        Map<String, Object> result = accountSettingService.getAccountSettings(channelId, channelHotelId);

        // Then
        assertNotNull(result);
        assertTrue(result instanceof ChannelAccountVO.AccountSetting);
        ChannelAccountVO.AccountSetting setting = (ChannelAccountVO.AccountSetting) result;
        assertEquals("channel-api-key", setting.getApiKey());
        assertEquals("channel-user", setting.getUsername());
        assertNull(setting.getPassword());
        
        verify(remoteService).uniqueHotelConnections(channelId, channelHotelId);
        verify(remoteService).getDistributorDetails(channelId);
    }

    @Test
    void testGetAccountSettings_WithNonGroupHotelType_FallsBackToChannelLevel() {
        // Given
        String channelId = "BOOKING";
        String channelHotelId = "HOTEL_123";
        
        Map<String, Object> hotelSettings = new HashMap<>();
        hotelSettings.put("hotelType", "Individual"); // Not "Group"
        hotelSettings.put(PCProfileAccountSettingService.KEY_API_KEY, "hotel-api-key");
        
        Map<String, Object> channelSettings = new HashMap<>();
        channelSettings.put(PCProfileAccountSettingService.KEY_API_KEY, "channel-api-key");
        
        mockHotelConnections.put("channelSettings", hotelSettings);
        mockDistributorDetails.put("settings", channelSettings);
        
        when(remoteService.uniqueHotelConnections(channelId, channelHotelId))
            .thenReturn(Optional.of(mockHotelConnections));
        when(remoteService.getDistributorDetails(channelId))
            .thenReturn(Optional.of(mockDistributorDetails));

        // When
        Map<String, Object> result = accountSettingService.getAccountSettings(channelId, channelHotelId);

        // Then
        assertNotNull(result);
        assertTrue(result instanceof ChannelAccountVO.AccountSetting);
        ChannelAccountVO.AccountSetting setting = (ChannelAccountVO.AccountSetting) result;
        assertEquals("channel-api-key", setting.getApiKey());
        
        verify(remoteService).uniqueHotelConnections(channelId, channelHotelId);
        verify(remoteService).getDistributorDetails(channelId);
    }

    @Test
    void testGetAccountSettings_WithUsernamePasswordCredentials_ReturnsCorrectSettings() {
        // Given
        String channelId = "AGODA";
        String channelHotelId = "HOTEL_456";
        
        Map<String, Object> hotelSettings = new HashMap<>();
        hotelSettings.put("hotelType", "Group");
        hotelSettings.put(PCProfileAccountSettingService.KEY_USER_NAME, "test-user");
        hotelSettings.put(PCProfileAccountSettingService.KEY_PASSWORD, "test-password");
        // No API key
        
        mockHotelConnections.put("channelSettings", hotelSettings);
        
        when(remoteService.uniqueHotelConnections(channelId, channelHotelId))
            .thenReturn(Optional.of(mockHotelConnections));

        // When
        Map<String, Object> result = accountSettingService.getAccountSettings(channelId, channelHotelId);

        // Then
        assertNotNull(result);
        assertTrue(result instanceof ChannelAccountVO.AccountSetting);
        ChannelAccountVO.AccountSetting setting = (ChannelAccountVO.AccountSetting) result;
        assertNull(setting.getApiKey());
        assertEquals("test-user", setting.getUsername());
        assertEquals("test-password", setting.getPassword());
    }

    @Test
    void testGetAccountSettings_WithInsufficientCredentials_FallsBackToChannelLevel() {
        // Given
        String channelId = "EXPEDIA";
        String channelHotelId = "HOTEL_789";
        
        Map<String, Object> hotelSettings = new HashMap<>();
        hotelSettings.put("hotelType", "Group");
        hotelSettings.put(PCProfileAccountSettingService.KEY_USER_NAME, "test-user");
        // Missing password, no API key
        
        Map<String, Object> channelSettings = new HashMap<>();
        channelSettings.put(PCProfileAccountSettingService.KEY_API_KEY, "fallback-api-key");
        
        mockHotelConnections.put("channelSettings", hotelSettings);
        mockDistributorDetails.put("settings", channelSettings);
        
        when(remoteService.uniqueHotelConnections(channelId, channelHotelId))
            .thenReturn(Optional.of(mockHotelConnections));
        when(remoteService.getDistributorDetails(channelId))
            .thenReturn(Optional.of(mockDistributorDetails));

        // When
        Map<String, Object> result = accountSettingService.getAccountSettings(channelId, channelHotelId);

        // Then
        assertNotNull(result);
        assertTrue(result instanceof ChannelAccountVO.AccountSetting);
        ChannelAccountVO.AccountSetting setting = (ChannelAccountVO.AccountSetting) result;
        assertEquals("fallback-api-key", setting.getApiKey());
        
        verify(remoteService).uniqueHotelConnections(channelId, channelHotelId);
        verify(remoteService).getDistributorDetails(channelId);
    }

    @Test
    void testGetAccountSettings_WithEmptyChannelId_ReturnsNull() {
        // Given
        String channelId = "";
        String channelHotelId = "HOTEL_123";

        // When
        Map<String, Object> result = accountSettingService.getAccountSettings(channelId, channelHotelId);

        // Then
        assertNull(result);
        verify(remoteService, never()).uniqueHotelConnections(anyString(), anyString());
        verify(remoteService, never()).getDistributorDetails(anyString());
    }

    @Test
    void testGetAccountSettings_WithNullChannelId_ReturnsNull() {
        // Given
        String channelId = null;
        String channelHotelId = "HOTEL_123";

        // When
        Map<String, Object> result = accountSettingService.getAccountSettings(channelId, channelHotelId);

        // Then
        assertNull(result);
        verify(remoteService, never()).uniqueHotelConnections(anyString(), anyString());
        verify(remoteService, never()).getDistributorDetails(anyString());
    }

    @Test
    void testGetAccountSettings_WithEmptyChannelHotelId_FallsBackToChannelLevel() {
        // Given
        String channelId = "BOOKING";
        String channelHotelId = "";
        
        Map<String, Object> channelSettings = new HashMap<>();
        channelSettings.put(PCProfileAccountSettingService.KEY_API_KEY, "channel-api-key");
        
        mockDistributorDetails.put("settings", channelSettings);
        
        when(remoteService.getDistributorDetails(channelId))
            .thenReturn(Optional.of(mockDistributorDetails));

        // When
        Map<String, Object> result = accountSettingService.getAccountSettings(channelId, channelHotelId);

        // Then
        assertNotNull(result);
        verify(remoteService, never()).uniqueHotelConnections(anyString(), anyString());
        verify(remoteService).getDistributorDetails(channelId);
    }

    @Test
    void testGetAccountSettings_WithNoCredentialsFound_ReturnsNull() {
        // Given
        String channelId = "BOOKING";
        String channelHotelId = "HOTEL_123";
        
        when(remoteService.uniqueHotelConnections(channelId, channelHotelId))
            .thenReturn(Optional.empty());
        when(remoteService.getDistributorDetails(channelId))
            .thenReturn(Optional.empty());

        // When
        Map<String, Object> result = accountSettingService.getAccountSettings(channelId, channelHotelId);

        // Then
        assertNull(result);
        verify(remoteService).uniqueHotelConnections(channelId, channelHotelId);
        verify(remoteService).getDistributorDetails(channelId);
    }

    @Test
    void testSaveOrUpdateAccountSettings_ThrowsUnsupportedOperationException() {
        // Given
        String channelId = "BOOKING";
        String channelHotelId = "HOTEL_123";
        Map<String, Object> accountSettings = new HashMap<>();

        // When & Then
        UnsupportedOperationException exception = assertThrows(
            UnsupportedOperationException.class,
            () -> accountSettingService.saveOrUpdateAccountSettings(channelId, channelHotelId, accountSettings)
        );
        
        assertEquals("Account in Profile Service can not be updated.", exception.getMessage());
    }

    @Test
    void testDeleteAccountSettings_ThrowsUnsupportedOperationException() {
        // Given
        String channelId = "BOOKING";
        String channelHotelId = "HOTEL_123";

        // When & Then
        UnsupportedOperationException exception = assertThrows(
            UnsupportedOperationException.class,
            () -> accountSettingService.deleteAccountSettings(channelId, channelHotelId)
        );
        
        assertEquals("Account in Profile Service can not be updated.", exception.getMessage());
    }

    @Test
    void testGetAccountSettings_WithNullObjectValues_HandlesGracefully() {
        // Given
        String channelId = "BOOKING";
        String channelHotelId = "HOTEL_123";
        
        Map<String, Object> hotelSettings = new HashMap<>();
        hotelSettings.put("hotelType", "Group");
        hotelSettings.put(PCProfileAccountSettingService.KEY_API_KEY, null);
        hotelSettings.put(PCProfileAccountSettingService.KEY_USER_NAME, null);
        hotelSettings.put(PCProfileAccountSettingService.KEY_PASSWORD, "test-password");
        
        mockHotelConnections.put("channelSettings", hotelSettings);
        
        when(remoteService.uniqueHotelConnections(channelId, channelHotelId))
            .thenReturn(Optional.of(mockHotelConnections));

        // When
        Map<String, Object> result = accountSettingService.getAccountSettings(channelId, channelHotelId);

        // Then
        assertNotNull(result);
        assertTrue(result instanceof ChannelAccountVO.AccountSetting);
        ChannelAccountVO.AccountSetting setting = (ChannelAccountVO.AccountSetting) result;
        assertNull(setting.getApiKey());
        assertNull(setting.getUsername());
        assertEquals("test-password", setting.getPassword());
    }
}
