package com.derbysoft.next.propertyconnect.channel.task.controller;

import com.derbysoft.next.propertyconenct.channel.common.exception.response.UnifyResult;
import com.derbysoft.next.propertyconnect.channel.task.controller.vo.hotel.ChannelHotelVO;
import com.derbysoft.next.propertyconnect.channel.task.service.ChannelHotelAPIService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Comprehensive unit tests for ChannelHotelAPIController
 * Tests REST API endpoints for channel hotel operations
 */
@ExtendWith(MockitoExtension.class)
class ChannelHotelAPIControllerTest {

    @InjectMocks
    private ChannelHotelAPIController channelHotelAPIController;

    @Mock
    private ChannelHotelAPIService channelHotelAPIService;

    private ChannelHotelVO testChannelHotelVO;

    @BeforeEach
    void setUp() {
        testChannelHotelVO = new ChannelHotelVO();
        testChannelHotelVO.setChannelId("BOOKING");
        testChannelHotelVO.setChannelHotelId("HOTEL_123");
        testChannelHotelVO.setSupplierId("PROPERTYCONNECT");
        testChannelHotelVO.setOperationToken("test-token-123");
    }

    @Test
    void testController_IsRestController() {
        // Given
        Class<?> controllerClass = ChannelHotelAPIController.class;

        // When
        boolean hasRestControllerAnnotation = controllerClass.isAnnotationPresent(org.springframework.web.bind.annotation.RestController.class);

        // Then
        assertTrue(hasRestControllerAnnotation, "Controller should be annotated with @RestController");
    }

    @Test
    void testGetChannelHotel_WithValidParameters_ReturnsChannelHotelVO() {
        // Given
        String channelId = "BOOKING";
        String channelHotelId = "HOTEL_123";
        
        when(channelHotelAPIService.getChannelHotel(channelId, channelHotelId))
            .thenReturn(testChannelHotelVO);

        // When
        UnifyResult<ChannelHotelVO> result = channelHotelAPIController.getChannelHotel(channelId, channelHotelId);

        // Then
        assertNotNull(result);
        assertEquals(testChannelHotelVO, result.getObject());
        verify(channelHotelAPIService).getChannelHotel(channelId, channelHotelId);
    }

    @Test
    void testGetChannelHotel_WithDifferentChannels_CallsServiceCorrectly() {
        // Given
        String[] channelIds = {"BOOKING", "AGODA", "EXPEDIA", "CTRIP"};
        String channelHotelId = "COMMON_HOTEL";

        for (String channelId : channelIds) {
            ChannelHotelVO expectedVO = new ChannelHotelVO();
            expectedVO.setChannelId(channelId);
            expectedVO.setChannelHotelId(channelHotelId);
            
            when(channelHotelAPIService.getChannelHotel(channelId, channelHotelId))
                .thenReturn(expectedVO);

            // When
            UnifyResult<ChannelHotelVO> result = channelHotelAPIController.getChannelHotel(channelId, channelHotelId);

            // Then
            assertNotNull(result);
            assertEquals(expectedVO, result.getObject());
            verify(channelHotelAPIService).getChannelHotel(channelId, channelHotelId);
        }
    }

    @Test
    void testSaveChannelHotel_WithValidParameters_ReturnsChannelHotelVO() {
        // Given
        String channelId = "BOOKING";
        String channelHotelId = "HOTEL_123";
        ChannelHotelVO channelHotelRQ = new ChannelHotelVO();
        channelHotelRQ.setSupplierId("PROPERTYCONNECT");
        String notifyUrl = "https://example.com/notify";
        Boolean syncMode = false;
        Boolean retry = false;
        Boolean fullUpdate = false;
        Boolean saveOnly = false;
        
        when(channelHotelAPIService.saveChannelHotel(
            channelId, channelHotelId, channelHotelRQ, notifyUrl, syncMode, retry, fullUpdate, saveOnly))
            .thenReturn(testChannelHotelVO);

        // When
        UnifyResult<ChannelHotelVO> result = channelHotelAPIController.saveChannelHotel(
            channelId, channelHotelId, channelHotelRQ, notifyUrl, syncMode, retry, fullUpdate, saveOnly);

        // Then
        assertNotNull(result);
        assertEquals(testChannelHotelVO, result.getObject());
        verify(channelHotelAPIService).saveChannelHotel(
            channelId, channelHotelId, channelHotelRQ, notifyUrl, syncMode, retry, fullUpdate, saveOnly);
    }

    @Test
    void testSaveChannelHotel_WithDefaultParameters_UsesDefaultValues() {
        // Given
        String channelId = "AGODA";
        String channelHotelId = "HOTEL_456";
        ChannelHotelVO channelHotelRQ = new ChannelHotelVO();
        channelHotelRQ.setSupplierId("PROPERTYCONNECT");
        
        when(channelHotelAPIService.saveChannelHotel(
            eq(channelId), eq(channelHotelId), eq(channelHotelRQ), 
            isNull(), eq(false), eq(false), eq(false), eq(false)))
            .thenReturn(testChannelHotelVO);

        // When
        UnifyResult<ChannelHotelVO> result = channelHotelAPIController.saveChannelHotel(
            channelId, channelHotelId, channelHotelRQ, null, null, null, null, null);

        // Then
        assertNotNull(result);
        assertEquals(testChannelHotelVO, result.getObject());
        verify(channelHotelAPIService).saveChannelHotel(
            channelId, channelHotelId, channelHotelRQ, null, false, false, false, false);
    }

    @Test
    void testSaveChannelHotel_WithSyncModeTrue_CallsServiceWithSyncMode() {
        // Given
        String channelId = "EXPEDIA";
        String channelHotelId = "HOTEL_789";
        ChannelHotelVO channelHotelRQ = new ChannelHotelVO();
        channelHotelRQ.setSupplierId("PROPERTYCONNECT");
        Boolean syncMode = true;
        
        when(channelHotelAPIService.saveChannelHotel(
            channelId, channelHotelId, channelHotelRQ, null, syncMode, false, false, false))
            .thenReturn(testChannelHotelVO);

        // When
        UnifyResult<ChannelHotelVO> result = channelHotelAPIController.saveChannelHotel(
            channelId, channelHotelId, channelHotelRQ, null, syncMode, false, false, false);

        // Then
        assertNotNull(result);
        assertEquals(testChannelHotelVO, result.getObject());
        verify(channelHotelAPIService).saveChannelHotel(
            channelId, channelHotelId, channelHotelRQ, null, true, false, false, false);
    }

    @Test
    void testSaveChannelHotel_WithAllParametersTrue_CallsServiceWithAllFlags() {
        // Given
        String channelId = "CTRIP";
        String channelHotelId = "HOTEL_999";
        ChannelHotelVO channelHotelRQ = new ChannelHotelVO();
        channelHotelRQ.setSupplierId("PROPERTYCONNECT");
        String notifyUrl = "https://webhook.example.com";
        
        when(channelHotelAPIService.saveChannelHotel(
            channelId, channelHotelId, channelHotelRQ, notifyUrl, true, true, true, true))
            .thenReturn(testChannelHotelVO);

        // When
        UnifyResult<ChannelHotelVO> result = channelHotelAPIController.saveChannelHotel(
            channelId, channelHotelId, channelHotelRQ, notifyUrl, true, true, true, true);

        // Then
        assertNotNull(result);
        assertEquals(testChannelHotelVO, result.getObject());
        verify(channelHotelAPIService).saveChannelHotel(
            channelId, channelHotelId, channelHotelRQ, notifyUrl, true, true, true, true);
    }

    @Test
    void testSyncChannelHotel_WithValidParameters_ReturnsChannelHotelVO() {
        // Given
        String channelId = "BOOKING";
        String channelHotelId = "HOTEL_123";
        String procedure = "SavePropertyCombo,TriggerARIRefresh";
        String notifyUrl = "https://example.com/notify";
        Boolean ignorePropertyStatusCheck = false;
        String supplierId = "PROPERTYCONNECT";
        ChannelHotelVO channelHotelRQ = new ChannelHotelVO();
        
        when(channelHotelAPIService.syncChannelHotel(
            channelId, channelHotelId, procedure, notifyUrl, ignorePropertyStatusCheck, supplierId, channelHotelRQ))
            .thenReturn(testChannelHotelVO);

        // When
        UnifyResult<ChannelHotelVO> result = channelHotelAPIController.syncChannelHotel(
            channelId, channelHotelId, procedure, notifyUrl, ignorePropertyStatusCheck, supplierId, channelHotelRQ);

        // Then
        assertNotNull(result);
        assertEquals(testChannelHotelVO, result.getObject());
        verify(channelHotelAPIService).syncChannelHotel(
            channelId, channelHotelId, procedure, notifyUrl, ignorePropertyStatusCheck, supplierId, channelHotelRQ);
    }

    @Test
    void testSyncChannelHotel_WithDefaultParameters_UsesDefaultValues() {
        // Given
        String channelId = "AGODA";
        String channelHotelId = "HOTEL_456";
        
        when(channelHotelAPIService.syncChannelHotel(
            eq(channelId), eq(channelHotelId), isNull(), isNull(), eq(false), eq("PROPERTYCONNECT"), isNull()))
            .thenReturn(testChannelHotelVO);

        // When
        UnifyResult<ChannelHotelVO> result = channelHotelAPIController.syncChannelHotel(
            channelId, channelHotelId, null, null, null, null, null);

        // Then
        assertNotNull(result);
        assertEquals(testChannelHotelVO, result.getObject());
        verify(channelHotelAPIService).syncChannelHotel(
            channelId, channelHotelId, null, null, false, "PROPERTYCONNECT", null);
    }

    @Test
    void testSyncChannelHotel_WithIgnoreStatusCheckTrue_CallsServiceWithFlag() {
        // Given
        String channelId = "EXPEDIA";
        String channelHotelId = "HOTEL_789";
        Boolean ignorePropertyStatusCheck = true;
        String supplierId = "CUSTOM_SUPPLIER";
        
        when(channelHotelAPIService.syncChannelHotel(
            channelId, channelHotelId, null, null, ignorePropertyStatusCheck, supplierId, null))
            .thenReturn(testChannelHotelVO);

        // When
        UnifyResult<ChannelHotelVO> result = channelHotelAPIController.syncChannelHotel(
            channelId, channelHotelId, null, null, ignorePropertyStatusCheck, supplierId, null);

        // Then
        assertNotNull(result);
        assertEquals(testChannelHotelVO, result.getObject());
        verify(channelHotelAPIService).syncChannelHotel(
            channelId, channelHotelId, null, null, true, supplierId, null);
    }

    @Test
    void testDeleteChannelHotel_WithValidParameters_ReturnsVoidResult() {
        // Given
        String channelId = "BOOKING";
        String channelHotelId = "HOTEL_123";
        
        doNothing().when(channelHotelAPIService).deleteChannelHotel(channelId, channelHotelId);

        // When
        UnifyResult<Void> result = channelHotelAPIController.deleteChannelHotel(channelId, channelHotelId);

        // Then
        assertNotNull(result);
        assertNull(result.getObject());
        verify(channelHotelAPIService).deleteChannelHotel(channelId, channelHotelId);
    }

    @Test
    void testDeleteChannelHotel_WithDifferentChannels_CallsServiceCorrectly() {
        // Given
        String[] channelIds = {"BOOKING", "AGODA", "EXPEDIA", "CTRIP"};
        String channelHotelId = "COMMON_HOTEL";

        for (String channelId : channelIds) {
            doNothing().when(channelHotelAPIService).deleteChannelHotel(channelId, channelHotelId);

            // When
            UnifyResult<Void> result = channelHotelAPIController.deleteChannelHotel(channelId, channelHotelId);

            // Then
            assertNotNull(result);
            assertNull(result.getObject());
            verify(channelHotelAPIService).deleteChannelHotel(channelId, channelHotelId);
        }
    }

    @Test
    void testController_ImplementsChannelHotelAPI() {
        // Given
        Class<?> controllerClass = ChannelHotelAPIController.class;

        // When
        boolean implementsAPI = com.derbysoft.next.propertyconnect.channel.task.controller.api.ChannelHotelAPI.class
            .isAssignableFrom(controllerClass);

        // Then
        assertTrue(implementsAPI, "Controller should implement ChannelHotelAPI interface");
    }

    @Test
    void testController_HasRequestMappingAnnotation() {
        // Given
        Class<?> controllerClass = ChannelHotelAPIController.class;

        // When
        boolean hasRequestMapping = controllerClass.isAnnotationPresent(org.springframework.web.bind.annotation.RequestMapping.class);

        // Then
        assertTrue(hasRequestMapping, "Controller should have @RequestMapping annotation");
    }

    @Test
    void testSaveChannelHotel_WithNullChannelHotelRQ_CallsService() {
        // Given
        String channelId = "BOOKING";
        String channelHotelId = "HOTEL_123";
        ChannelHotelVO channelHotelRQ = null;
        
        when(channelHotelAPIService.saveChannelHotel(
            channelId, channelHotelId, channelHotelRQ, null, false, false, false, false))
            .thenReturn(testChannelHotelVO);

        // When
        UnifyResult<ChannelHotelVO> result = channelHotelAPIController.saveChannelHotel(
            channelId, channelHotelId, channelHotelRQ, null, null, null, null, null);

        // Then
        assertNotNull(result);
        assertEquals(testChannelHotelVO, result.getObject());
        verify(channelHotelAPIService).saveChannelHotel(
            channelId, channelHotelId, null, null, false, false, false, false);
    }

    @Test
    void testSyncChannelHotel_WithMultipleProcedures_CallsServiceCorrectly() {
        // Given
        String channelId = "CTRIP";
        String channelHotelId = "HOTEL_999";
        String procedure = "SavePropertyCombo,TriggerARIRefresh,SaveRoomTypes";
        String supplierId = "CUSTOM_SUPPLIER";
        
        when(channelHotelAPIService.syncChannelHotel(
            channelId, channelHotelId, procedure, null, false, supplierId, null))
            .thenReturn(testChannelHotelVO);

        // When
        UnifyResult<ChannelHotelVO> result = channelHotelAPIController.syncChannelHotel(
            channelId, channelHotelId, procedure, null, null, supplierId, null);

        // Then
        assertNotNull(result);
        assertEquals(testChannelHotelVO, result.getObject());
        verify(channelHotelAPIService).syncChannelHotel(
            channelId, channelHotelId, procedure, null, false, supplierId, null);
    }
}
