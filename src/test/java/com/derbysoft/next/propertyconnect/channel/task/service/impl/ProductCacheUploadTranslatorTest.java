package com.derbysoft.next.propertyconnect.channel.task.service.impl;

import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelProductsDTO;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Comprehensive unit tests for ProductCacheUploadTranslator
 * Tests Excel file processing functionality for channel products
 */
@ExtendWith(MockitoExtension.class)
class ProductCacheUploadTranslatorTest {

    @InjectMocks
    private ProductCacheUploadTranslator translator;

    private MultipartFile createTestExcelFile() throws IOException {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("Products");

        // Create header row
        Row headerRow = sheet.createRow(0);
        headerRow.createCell(0).setCellValue("Channel ID");
        headerRow.createCell(1).setCellValue("Channel Hotel ID");
        headerRow.createCell(2).setCellValue("Hotel Name");
        headerRow.createCell(3).setCellValue("Channel Room ID");
        headerRow.createCell(4).setCellValue("Channel Room Name");
        headerRow.createCell(5).setCellValue("Channel Rate ID");
        headerRow.createCell(6).setCellValue("Channel Rate Name");
        headerRow.createCell(7).setCellValue("Avail Status");

        // Create data row
        Row dataRow = sheet.createRow(1);
        dataRow.createCell(0).setCellValue("BOOKING");
        dataRow.createCell(1).setCellValue("HOTEL_123");
        dataRow.createCell(2).setCellValue("Test Hotel");
        dataRow.createCell(3).setCellValue("ROOM_001");
        dataRow.createCell(4).setCellValue("Standard Room");
        dataRow.createCell(5).setCellValue("RATE_001");
        dataRow.createCell(6).setCellValue("Standard Rate");
        dataRow.createCell(7).setCellValue("true");

        // Create second data row
        Row dataRow2 = sheet.createRow(2);
        dataRow2.createCell(0).setCellValue("BOOKING");
        dataRow2.createCell(1).setCellValue("HOTEL_123");
        dataRow2.createCell(2).setCellValue("Test Hotel");
        dataRow2.createCell(3).setCellValue("ROOM_002");
        dataRow2.createCell(4).setCellValue("Deluxe Room");
        dataRow2.createCell(5).setCellValue("RATE_002");
        dataRow2.createCell(6).setCellValue("Deluxe Rate");
        dataRow2.createCell(7).setCellValue("false");

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        workbook.write(outputStream);
        workbook.close();

        return new MockMultipartFile(
            "file",
            "products.xlsx",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            outputStream.toByteArray()
        );
    }

    private MultipartFile createExcelFileWithEmptyRows() throws IOException {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("Products");

        // Create header row
        Row headerRow = sheet.createRow(0);
        headerRow.createCell(0).setCellValue("Channel ID");
        headerRow.createCell(1).setCellValue("Channel Hotel ID");
        headerRow.createCell(2).setCellValue("Hotel Name");
        headerRow.createCell(3).setCellValue("Channel Room ID");
        headerRow.createCell(4).setCellValue("Channel Room Name");
        headerRow.createCell(5).setCellValue("Channel Rate ID");
        headerRow.createCell(6).setCellValue("Channel Rate Name");
        headerRow.createCell(7).setCellValue("Avail Status");

        // Create data row with valid data
        Row dataRow1 = sheet.createRow(1);
        dataRow1.createCell(0).setCellValue("BOOKING");
        dataRow1.createCell(1).setCellValue("HOTEL_123");
        dataRow1.createCell(2).setCellValue("Test Hotel");
        dataRow1.createCell(3).setCellValue("ROOM_001");
        dataRow1.createCell(4).setCellValue("Standard Room");
        dataRow1.createCell(5).setCellValue("RATE_001");
        dataRow1.createCell(6).setCellValue("Standard Rate");
        dataRow1.createCell(7).setCellValue("true");

        // Create row with empty room ID (should be skipped)
        Row dataRow2 = sheet.createRow(2);
        dataRow2.createCell(0).setCellValue("BOOKING");
        dataRow2.createCell(1).setCellValue("HOTEL_123");
        dataRow2.createCell(2).setCellValue("Test Hotel");
        dataRow2.createCell(3).setCellValue(""); // Empty room ID
        dataRow2.createCell(4).setCellValue("Empty Room");
        dataRow2.createCell(5).setCellValue("RATE_002");
        dataRow2.createCell(6).setCellValue("Empty Rate");
        dataRow2.createCell(7).setCellValue("false");

        // Create row with empty rate ID (should be skipped)
        Row dataRow3 = sheet.createRow(3);
        dataRow3.createCell(0).setCellValue("BOOKING");
        dataRow3.createCell(1).setCellValue("HOTEL_123");
        dataRow3.createCell(2).setCellValue("Test Hotel");
        dataRow3.createCell(3).setCellValue("ROOM_003");
        dataRow3.createCell(4).setCellValue("Another Room");
        dataRow3.createCell(5).setCellValue(""); // Empty rate ID
        dataRow3.createCell(6).setCellValue("Another Rate");
        dataRow3.createCell(7).setCellValue("true");

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        workbook.write(outputStream);
        workbook.close();

        return new MockMultipartFile(
            "file",
            "products.xlsx",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            outputStream.toByteArray()
        );
    }

    @BeforeEach
    void setUp() {
        translator = new ProductCacheUploadTranslator();
    }

    @Test
    void testTranslator_IsSpringComponent() {
        // Given
        Class<?> translatorClass = ProductCacheUploadTranslator.class;

        // When
        boolean hasComponentAnnotation = translatorClass.isAnnotationPresent(org.springframework.stereotype.Component.class);

        // Then
        assertTrue(hasComponentAnnotation, "Translator should be annotated with @Component");
    }

    @Test
    void testProcessExcelFile_WithValidFile_ReturnsChannelProductsDTO() throws IOException {
        // Given
        MultipartFile file = createTestExcelFile();

        // When
        ChannelProductsDTO result = translator.processExcelFile(file);

        // Then
        assertNotNull(result, "Result should not be null");
        assertEquals("BOOKING", result.getChannelId());
        assertEquals("HOTEL_123", result.getChannelHotelId());
        assertEquals("Test Hotel", result.getHotelName());
        assertNotNull(result.getChannelProducts());
        assertEquals(2, result.getChannelProducts().size());
        assertNotNull(result.getRetrieveDate());
    }

    @Test
    void testProcessExcelFile_WithValidFile_ParsesProductsCorrectly() throws IOException {
        // Given
        MultipartFile file = createTestExcelFile();

        // When
        ChannelProductsDTO result = translator.processExcelFile(file);

        // Then
        ChannelProductsDTO.Product product1 = result.getChannelProducts().get(0);
        assertEquals("ROOM_001", product1.getChannelRoomId());
        assertEquals("Standard Room", product1.getChannelRoomName());
        assertEquals("RATE_001", product1.getChannelRateId());
        assertEquals("Standard Rate", product1.getChannelRateName());
        assertTrue(product1.getAvailStatus());

        ChannelProductsDTO.Product product2 = result.getChannelProducts().get(1);
        assertEquals("ROOM_002", product2.getChannelRoomId());
        assertEquals("Deluxe Room", product2.getChannelRoomName());
        assertEquals("RATE_002", product2.getChannelRateId());
        assertEquals("Deluxe Rate", product2.getChannelRateName());
        assertFalse(product2.getAvailStatus());
    }

    @Test
    void testProcessExcelFile_WithEmptyRows_SkipsInvalidRows() throws IOException {
        // Given
        MultipartFile file = createExcelFileWithEmptyRows();

        // When
        ChannelProductsDTO result = translator.processExcelFile(file);

        // Then
        assertNotNull(result);
        assertEquals(1, result.getChannelProducts().size(), "Should only include valid rows");
        
        ChannelProductsDTO.Product product = result.getChannelProducts().get(0);
        assertEquals("ROOM_001", product.getChannelRoomId());
        assertEquals("RATE_001", product.getChannelRateId());
    }

    @Test
    void testProcessExcelFile_WithNullAvailStatus_HandlesNullCorrectly() throws IOException {
        // Given
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("Products");

        // Create header row
        Row headerRow = sheet.createRow(0);
        headerRow.createCell(0).setCellValue("Channel ID");
        headerRow.createCell(1).setCellValue("Channel Hotel ID");
        headerRow.createCell(2).setCellValue("Hotel Name");
        headerRow.createCell(3).setCellValue("Channel Room ID");
        headerRow.createCell(4).setCellValue("Channel Room Name");
        headerRow.createCell(5).setCellValue("Channel Rate ID");
        headerRow.createCell(6).setCellValue("Channel Rate Name");
        headerRow.createCell(7).setCellValue("Avail Status");

        // Create data row with null avail status
        Row dataRow = sheet.createRow(1);
        dataRow.createCell(0).setCellValue("BOOKING");
        dataRow.createCell(1).setCellValue("HOTEL_123");
        dataRow.createCell(2).setCellValue("Test Hotel");
        dataRow.createCell(3).setCellValue("ROOM_001");
        dataRow.createCell(4).setCellValue("Standard Room");
        dataRow.createCell(5).setCellValue("RATE_001");
        dataRow.createCell(6).setCellValue("Standard Rate");
        // Cell 7 is left empty (null)

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        workbook.write(outputStream);
        workbook.close();

        MultipartFile file = new MockMultipartFile(
            "file",
            "products.xlsx",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            outputStream.toByteArray()
        );

        // When
        ChannelProductsDTO result = translator.processExcelFile(file);

        // Then
        assertNotNull(result);
        assertEquals(1, result.getChannelProducts().size());
        
        ChannelProductsDTO.Product product = result.getChannelProducts().get(0);
        assertNull(product.getAvailStatus(), "Avail status should be null when cell is empty");
    }

    @Test
    void testProcessExcelFile_SetsRetrieveDateCorrectly() throws IOException {
        // Given
        MultipartFile file = createTestExcelFile();
        LocalDateTime beforeCall = LocalDateTime.now(ZoneOffset.UTC);

        // When
        ChannelProductsDTO result = translator.processExcelFile(file);

        // Then
        LocalDateTime afterCall = LocalDateTime.now(ZoneOffset.UTC);
        assertNotNull(result.getRetrieveDate());
        
        // Parse the retrieve date and verify it's within the expected range
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS");
        LocalDateTime retrieveDateTime = LocalDateTime.parse(result.getRetrieveDate(), formatter);
        
        assertTrue(retrieveDateTime.isAfter(beforeCall.minusSeconds(1)) && 
                  retrieveDateTime.isBefore(afterCall.plusSeconds(1)),
                  "Retrieve date should be set to current UTC time");
    }

    @Test
    void testProcessExcelFile_WithIOException_HandlesGracefully() {
        // Given
        MultipartFile mockFile = new MockMultipartFile(
            "file",
            "invalid.xlsx",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            "invalid content".getBytes()
        );

        // When
        ChannelProductsDTO result = translator.processExcelFile(mockFile);

        // Then
        assertNotNull(result, "Should return non-null result even with IO exception");
        assertNotNull(result.getChannelProducts(), "Should have empty product list");
        assertTrue(result.getChannelProducts().isEmpty(), "Product list should be empty");
        assertNotNull(result.getRetrieveDate(), "Retrieve date should still be set");
    }

    @Test
    void testProcessExcelFile_WithEmptyFile_ReturnsEmptyResult() throws IOException {
        // Given
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("Products");
        
        // Only create header row, no data rows
        Row headerRow = sheet.createRow(0);
        headerRow.createCell(0).setCellValue("Channel ID");

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        workbook.write(outputStream);
        workbook.close();

        MultipartFile file = new MockMultipartFile(
            "file",
            "empty.xlsx",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            outputStream.toByteArray()
        );

        // When
        ChannelProductsDTO result = translator.processExcelFile(file);

        // Then
        assertNotNull(result);
        assertNotNull(result.getChannelProducts());
        assertTrue(result.getChannelProducts().isEmpty());
        assertNotNull(result.getRetrieveDate());
    }
}
