#
# A fatal error has been detected by the Java Runtime Environment:
#
#  EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ffa8bfec5c2, pid=33452, tid=68688
#
# JRE version: OpenJDK Runtime Environment Corretto-*********.1 (17.0.12+7) (build 17.0.12+7-LTS)
# Java VM: OpenJDK 64-Bit Server VM Corretto-*********.1 (17.0.12+7-LTS, mixed mode, emulated-client, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# Problematic frame:
# V  [jvm.dll+0x2ec5c2]
#
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#
# If you would like to submit a bug report, please visit:
#   https://github.com/corretto/corretto-17/issues/
#

---------------  S U M M A R Y ------------

Command Line: -agentlib:jdwp=transport=dt_socket,address=127.0.0.1:38269,suspend=y,server=n -agentpath:C:\Users\<USER>\AppData\Local\Temp\idea_libasyncProfiler_dll_temp_folder78745\libasyncProfiler.dll=version,jfr,event=wall,interval=10ms,cstack=no,file=C:\Users\<USER>\IdeaSnapshots\ChannelTaskApplication_2025_03_19_133037.jfr,dbghelppath=C:\Users\<USER>\AppData\Local\Temp\idea_dbghelp_dll_temp_folder8\dbghelp.dll,log=C:\Users\<USER>\AppData\Local\Temp\ChannelTaskApplication_2025_03_19_133037.jfr.log.txt,logLevel=DEBUG -Dcool.request.port=39783 -XX:TieredStopAtLevel=1 -Dspring.output.ansi.enabled=always -Dcom.sun.management.jmxremote -Dspring.jmx.enabled=true -Dspring.liveBeansView.mbeanDomain -Dspring.application.admin.enabled=true -Dmanagement.endpoints.jmx.exposure.include=* -javaagent:C:\Users\<USER>\AppData\Local\JetBrains\IntelliJIdea2024.3\groovyHotSwap\gragent.jar -javaagent:C:\Users\<USER>\AppData\Local\JetBrains\IntelliJIdea2024.3\captureAgent\debugger-agent.jar=file:/C:/Users/<USER>/AppData/Local/Temp/capture10.props -Dkotlinx.coroutines.debug.enable.creation.stack.trace=false -Ddebugger.agent.enable.coroutines=true -Dkotlinx.coroutines.debug.enable.flows.stack.trace=true -Dkotlinx.coroutines.debug.enable.mutable.state.flows.stack.trace=true -Dfile.encoding=UTF-8 com.derbysoft.next.propertyconnect.channel.task.ChannelTaskApplication

Host: Intel(R) Core(TM) i7-10700 CPU @ 2.90GHz, 16 cores, 31G,  Windows 11 , 64 bit Build 26100 (10.0.26100.3323)
Time: Wed Mar 19 13:30:48 2025 China Standard Time elapsed time: 11.018652 seconds (0d 0h 0m 11s)

---------------  T H R E A D  ---------------

Current thread (0x00000273d9f46740):  GCTaskThread "GC Thread#6" [stack: 0x000000d6aa400000,0x000000d6aa500000] [id=68688]

Stack: [0x000000d6aa400000,0x000000d6aa500000],  sp=0x000000d6aa4ff928,  free space=1022k
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x2ec5c2]
V  [jvm.dll+0x30a2db]
V  [jvm.dll+0x2dde89]
V  [jvm.dll+0x2de53a]
V  [jvm.dll+0x2de637]
V  [jvm.dll+0x2e3bdb]
V  [jvm.dll+0x86479b]
V  [jvm.dll+0x8647e6]
V  [jvm.dll+0x7e62ac]
V  [jvm.dll+0x67e3c7]
C  [ucrtbase.dll+0x37b0]
C  [KERNEL32.DLL+0x2e8d7]
C  [ntdll.dll+0xbbf6c]


siginfo: EXCEPTION_ACCESS_VIOLATION (0xc0000005), writing address 0x0000000000000000


Registers:
RAX=0x000000000000001a, RBX=0x000000000000000a, RCX=0x000000000000000d, RDX=0x00000000ffffffff
RSP=0x000000d6aa4ff928, RBP=0x0000000000000004, RSI=0x0000000000000000, RDI=0x00000273f9745810
R8 =0x0000000000000000, R9 =0x0000000000000006, R10=0x0000000000000000, R11=0x00000273f9766c30
R12=0x0000000000000000, R13=0x00000273df735380, R14=0x0000000000000030, R15=0x000000d6aa4ff980
RIP=0x00007ffa8bfec5c2, EFLAGS=0x0000000000010293


Register to memory mapping:

RIP=0x00007ffa8bfec5c2 jvm.dll
RAX=0x000000000000001a is an unknown value
RBX=0x000000000000000a is an unknown value
RCX=0x000000000000000d is an unknown value
RDX=0x00000000ffffffff is an unknown value
RSP=0x000000d6aa4ff928 points into unknown readable memory: 0x0000000000000013 | 13 00 00 00 00 00 00 00
RBP=0x0000000000000004 is an unknown value
RSI=0x0 is NULL
RDI=0x00000273f9745810 points into unknown readable memory: 0x00007ffa8c656130 | 30 61 65 8c fa 7f 00 00
R8 =0x0 is NULL
R9 =0x0000000000000006 is an unknown value
R10=0x0 is NULL
R11=0x00000273f9766c30 points into unknown readable memory: 0x0000000000000000 | 00 00 00 00 00 00 00 00
R12=0x0 is NULL
R13=0x00000273df735380 points into unknown readable memory: 0x00007ffa8c65c8d8 | d8 c8 65 8c fa 7f 00 00
R14=0x0000000000000030 is an unknown value
R15=0x000000d6aa4ff980 points into unknown readable memory: 0x0000000609e3b629 | 29 b6 e3 09 06 00 00 00


Top of Stack: (sp=0x000000d6aa4ff928)
0x000000d6aa4ff928:   0000000000000013 00000273df735380
0x000000d6aa4ff938:   0000000000000004 000000d6aa4ffa50
0x000000d6aa4ff948:   00007ffa8c00a2db 0000094e0000346d
0x000000d6aa4ff958:   00000273df735380 00000273f9745810
0x000000d6aa4ff968:   0000000609dcd6d4 000000d6aa4ff9e0
0x000000d6aa4ff978:   00007ffa8bfdde89 0000000609e3b629
0x000000d6aa4ff988:   0000000000000000 000000d6aa4ffa50
0x000000d6aa4ff998:   0000000000000000 000000d6a8ffed00
0x000000d6aa4ff9a8:   00007ffa8c37899f 0000000000000000
0x000000d6aa4ff9b8:   0000000000000000 0000000000000000
0x000000d6aa4ff9c8:   0000000000000000 0000000000000000
0x000000d6aa4ff9d8:   00007ffa8c4f4b27 000002739381aa60
0x000000d6aa4ff9e8:   00000273d95fe920 00000000000003d8
0x000000d6aa4ff9f8:   000002739381ae48 000000000000000a
0x000000d6aa4ffa08:   00007ffa8bfde53a 00000273df735380
0x000000d6aa4ffa18:   0000000000000013 000000d6a8ffed00
0x000000d6aa4ffa28:   0000000000000000 00007ffa8c65d9a0
0x000000d6aa4ffa38:   0000000a00000001 00000000068eccf2
0x000000d6aa4ffa48:   0000000767d91637 00007ffa8c656108
0x000000d6aa4ffa58:   4026015c05ba5424 3edd5c3159600000
0x000000d6aa4ffa68:   0000000000000005 00000273f9756a00
0x000000d6aa4ffa78:   00000273df735380 00000273f9745810
0x000000d6aa4ffa88:   000000d6a8ffed30 0000000000000013
0x000000d6aa4ffa98:   0000000000000000 0000000000000000
0x000000d6aa4ffaa8:   0000000000000000 000002739381aa70
0x000000d6aa4ffab8:   00007ffa8bfde637 00000273df735380
0x000000d6aa4ffac8:   000000000000000a 00000273d90c3000
0x000000d6aa4ffad8:   000000d6a8ffed00 0000000000000015
0x000000d6aa4ffae8:   0000000000000013 000000d6a8ffed00
0x000000d6aa4ffaf8:   00007ffa8bfe3bdb 00000273df735380
0x000000d6aa4ffb08:   00000273d90c3000 0000000000000000
0x000000d6aa4ffb18:   0000000000000000 0000000000000000 

Instructions: (pc=0x00007ffa8bfec5c2)
0x00007ffa8bfec4c2:   a8 00 00 00 73 10 48 8b 87 c0 00 00 00 48 39 87
0x00007ffa8bfec4d2:   b8 00 00 00 72 13 48 8b cf e8 f0 f1 ff ff 84 c0
0x00007ffa8bfec4e2:   75 07 c6 87 d0 00 00 00 01 48 8b 5c 24 30 48 83
0x00007ffa8bfec4f2:   c4 20 5f c3 cc cc cc cc cc cc cc cc cc cc 40 53
0x00007ffa8bfec502:   48 83 ec 20 48 8b d9 e8 c2 f1 ff ff 84 c0 74 20
0x00007ffa8bfec512:   48 8b 43 20 48 8b 88 f0 01 00 00 48 85 c9 75 10
0x00007ffa8bfec522:   38 8b d0 00 00 00 75 08 32 c0 48 83 c4 20 5b c3
0x00007ffa8bfec532:   b0 01 48 83 c4 20 5b c3 cc cc cc cc cc cc 48 8b
0x00007ffa8bfec542:   41 08 0f b6 80 12 05 00 00 c3 cc cc cc cc 40 53
0x00007ffa8bfec552:   48 83 ec 20 0f b6 05 13 eb 8b 00 48 8b d9 84 c0
0x00007ffa8bfec562:   74 16 e8 57 4c 4b 00 48 8b 43 08 0f b6 80 12 05
0x00007ffa8bfec572:   00 00 48 83 c4 20 5b c3 48 8b 41 08 0f b6 80 12
0x00007ffa8bfec582:   05 00 00 48 83 c4 20 5b c3 cc cc cc cc cc 48 89
0x00007ffa8bfec592:   5c 24 10 48 89 6c 24 18 48 89 74 24 20 57 41 54
0x00007ffa8bfec5a2:   41 56 41 57 45 33 e4 8b da 48 8b f9 4d 8b f8 8b
0x00007ffa8bfec5b2:   49 08 41 8b ec 8d 04 09 85 c0 0f 84 59 02 00 00
0x00007ffa8bfec5c2:   83 f9 02 0f 86 c7 01 00 00 48 8b 47 10 4c 8b 1c
0x00007ffa8bfec5d2:   d8 45 8b 8b 00 02 00 00 41 83 f9 ff 75 49 45 8b
0x00007ffa8bfec5e2:   83 04 02 00 00 b8 5f 0b 4e 83 41 f7 e8 41 03 d0
0x00007ffa8bfec5f2:   c1 fa 10 8b c2 c1 e8 1f 03 d0 41 69 c0 a7 41 00
0x00007ffa8bfec602:   00 69 ca ff ff ff 7f 2b c1 85 c0 8d 88 ff ff ff
0x00007ffa8bfec612:   7f 0f 4e c1 33 d2 41 89 83 04 02 00 00 f7 77 08
0x00007ffa8bfec622:   44 8b ca 3b d3 74 b7 8b d3 0f 1f 44 00 00 3b d3
0x00007ffa8bfec632:   74 05 41 3b d1 75 44 45 8b 83 04 02 00 00 b8 5f
0x00007ffa8bfec642:   0b 4e 83 41 f7 e8 41 03 d0 c1 fa 10 8b c2 c1 e8
0x00007ffa8bfec652:   1f 03 d0 41 69 c0 a7 41 00 00 69 ca ff ff ff 7f
0x00007ffa8bfec662:   2b c1 85 c0 8d 88 ff ff ff 7f 0f 4e c1 33 d2 41
0x00007ffa8bfec672:   89 83 04 02 00 00 f7 77 08 eb b3 41 8b c1 4c 8d
0x00007ffa8bfec682:   34 c5 00 00 00 00 48 8b 47 10 49 8b 0c 06 8b 81
0x00007ffa8bfec692:   00 01 00 00 44 8b 91 80 00 00 00 44 2b d0 8b c2
0x00007ffa8bfec6a2:   41 81 e2 ff ff 01 00 41 81 fa ff ff 01 00 45 0f
0x00007ffa8bfec6b2:   44 d4 48 8d 34 c5 00 00 00 00 48 8b 47 10 48 8b 


Stack slot to memory mapping:
stack at sp + 0 slots: 0x0000000000000013 is an unknown value
stack at sp + 1 slots: 0x00000273df735380 points into unknown readable memory: 0x00007ffa8c65c8d8 | d8 c8 65 8c fa 7f 00 00
stack at sp + 2 slots: 0x0000000000000004 is an unknown value
stack at sp + 3 slots: 0x000000d6aa4ffa50 points into unknown readable memory: 0x00007ffa8c656108 | 08 61 65 8c fa 7f 00 00
stack at sp + 4 slots: 0x00007ffa8c00a2db jvm.dll
stack at sp + 5 slots: 0x0000094e0000346d is an unknown value
stack at sp + 6 slots: 0x00000273df735380 points into unknown readable memory: 0x00007ffa8c65c8d8 | d8 c8 65 8c fa 7f 00 00
stack at sp + 7 slots: 0x00000273f9745810 points into unknown readable memory: 0x00007ffa8c656130 | 30 61 65 8c fa 7f 00 00


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00000273e5dae1c0, length=43, elements={
0x00000273f11ee830, 0x00000273935d8510, 0x00000273935d97f0, 0x000002739360a2c0,
0x00000273935fecf0, 0x00000273935fff60, 0x00000273936035d0, 0x0000027393616fa0,
0x00000273936231d0, 0x00000273936a2880, 0x00000273937f7550, 0x00000273937f85e0,
0x00000273938142a0, 0x00000273d9036d40, 0x00000273d90564a0, 0x00000273d903b7d0,
0x00000273d9f77bf0, 0x00000273d9f78520, 0x00000273da022da0, 0x00000273da0476c0,
0x00000273da047fe0, 0x00000273da053550, 0x00000273d9f6f300, 0x00000273da3c75a0,
0x00000273da8db470, 0x00000273dab88d30, 0x00000273dab88820, 0x00000273dc355530,
0x00000273dc357db0, 0x00000273dcfdc5d0, 0x00000273dcfdf360, 0x00000273dd7c0d40,
0x00000273dd7c4f10, 0x00000273df0bac30, 0x00000273df0ba210, 0x00000273df0bbb60,
0x00000273e00358e0, 0x00000273e0033570, 0x00000273e0856c60, 0x00000273e0856240,
0x00000273e08539c0, 0x00000273e0855310, 0x00000273e61e89e0
}

Java Threads: ( => current thread )
  0x00000273f11ee830 JavaThread "main" [_thread_blocked, id=46712, stack(0x000000d6a8900000,0x000000d6a8a00000)]
  0x00000273935d8510 JavaThread "Reference Handler" daemon [_thread_blocked, id=26924, stack(0x000000d6a9000000,0x000000d6a9100000)]
  0x00000273935d97f0 JavaThread "Finalizer" daemon [_thread_blocked, id=31228, stack(0x000000d6a9100000,0x000000d6a9200000)]
  0x000002739360a2c0 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=8136, stack(0x000000d6a9200000,0x000000d6a9300000)]
  0x00000273935fecf0 JavaThread "Attach Listener" daemon [_thread_blocked, id=48236, stack(0x000000d6a9300000,0x000000d6a9400000)]
  0x00000273935fff60 JavaThread "Service Thread" daemon [_thread_blocked, id=78012, stack(0x000000d6a9400000,0x000000d6a9500000)]
  0x00000273936035d0 JavaThread "Monitor Deflation Thread" daemon [_thread_blocked, id=68268, stack(0x000000d6a9500000,0x000000d6a9600000)]
  0x0000027393616fa0 JavaThread "C1 CompilerThread0" daemon [_thread_blocked, id=68112, stack(0x000000d6a9600000,0x000000d6a9700000)]
  0x00000273936231d0 JavaThread "Sweeper thread" daemon [_thread_blocked, id=58828, stack(0x000000d6a9700000,0x000000d6a9800000)]
  0x00000273936a2880 JavaThread "C1 CompilerThread1" daemon [_thread_blocked, id=81952, stack(0x000000d6a9800000,0x000000d6a9900000)]
  0x00000273937f7550 JavaThread "C1 CompilerThread2" daemon [_thread_blocked, id=61632, stack(0x000000d6a9900000,0x000000d6a9a00000)]
  0x00000273937f85e0 JavaThread "C1 CompilerThread3" daemon [_thread_blocked, id=56140, stack(0x000000d6a9a00000,0x000000d6a9b00000)]
  0x00000273938142a0 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=64452, stack(0x000000d6a9b00000,0x000000d6a9c00000)]
  0x00000273d9036d40 JavaThread "JDWP Transport Listener: dt_socket" daemon [_thread_blocked, id=31904, stack(0x000000d6a9c00000,0x000000d6a9d00000)]
  0x00000273d90564a0 JavaThread "JDWP Event Helper Thread" daemon [_thread_blocked, id=81184, stack(0x000000d6a9d00000,0x000000d6a9e00000)]
  0x00000273d903b7d0 JavaThread "JDWP Command Reader" daemon [_thread_in_native, id=66132, stack(0x000000d6a9e00000,0x000000d6a9f00000)]
  0x00000273d9f77bf0 JavaThread "C1 CompilerThread4" daemon [_thread_blocked, id=64976, stack(0x000000d6aaa00000,0x000000d6aab00000)]
  0x00000273d9f78520 JavaThread "C1 CompilerThread5" daemon [_thread_blocked, id=82048, stack(0x000000d6aab00000,0x000000d6aac00000)]
  0x00000273da022da0 JavaThread "C1 CompilerThread6" daemon [_thread_blocked, id=21600, stack(0x000000d6aac00000,0x000000d6aad00000)]
  0x00000273da0476c0 JavaThread "C1 CompilerThread7" daemon [_thread_blocked, id=79348, stack(0x000000d6aad00000,0x000000d6aae00000)]
  0x00000273da047fe0 JavaThread "C1 CompilerThread8" daemon [_thread_blocked, id=56636, stack(0x000000d6aae00000,0x000000d6aaf00000)]
  0x00000273da053550 JavaThread "C1 CompilerThread9" daemon [_thread_blocked, id=53180, stack(0x000000d6aaf00000,0x000000d6ab000000)]
  0x00000273d9f6f300 JavaThread "C1 CompilerThread10" daemon [_thread_blocked, id=44724, stack(0x000000d6ab000000,0x000000d6ab100000)]
  0x00000273da3c75a0 JavaThread "Notification Thread" daemon [_thread_blocked, id=12960, stack(0x000000d6ab100000,0x000000d6ab200000)]
  0x00000273da8db470 JavaThread "RMI TCP Accept-0" daemon [_thread_in_native, id=27704, stack(0x000000d6ab200000,0x000000d6ab300000)]
  0x00000273dab88d30 JavaThread "RMI TCP Connection(1)-**************" daemon [_thread_blocked, id=6796, stack(0x000000d6ab500000,0x000000d6ab600000)]
  0x00000273dab88820 JavaThread "RMI Scheduler(0)" daemon [_thread_blocked, id=69508, stack(0x000000d6ab600000,0x000000d6ab700000)]
  0x00000273dc355530 JavaThread "service-Configuration-scheduler" [_thread_blocked, id=78208, stack(0x000000d6ab700000,0x000000d6ab800000)]
  0x00000273dc357db0 JavaThread "service-Configuration-scheduler" [_thread_blocked, id=15576, stack(0x000000d6aba00000,0x000000d6abb00000)]
  0x00000273dcfdc5d0 JavaThread "grpc-nio-worker-ELG-1-1" daemon [_thread_in_native, id=20372, stack(0x000000d6abb00000,0x000000d6abc00000)]
  0x00000273dcfdf360 JavaThread "grpc-nio-worker-ELG-1-2" daemon [_thread_in_native, id=37300, stack(0x000000d6abc00000,0x000000d6abd00000)]
  0x00000273dd7c0d40 JavaThread "grpc-nio-worker-ELG-1-3" daemon [_thread_in_native, id=60316, stack(0x000000d6abf00000,0x000000d6ac000000)]
  0x00000273dd7c4f10 JavaThread "Configuration_Watch_pc-channel:cn-northwest-1::::" daemon [_thread_blocked, id=73648, stack(0x000000d6abe00000,0x000000d6abf00000)]
  0x00000273df0bac30 JavaThread "Catalina-utility-1" [_thread_blocked, id=55972, stack(0x000000d6ac000000,0x000000d6ac100000)]
  0x00000273df0ba210 JavaThread "Catalina-utility-2" [_thread_blocked, id=32064, stack(0x000000d6ac100000,0x000000d6ac200000)]
  0x00000273df0bbb60 JavaThread "container-0" [_thread_blocked, id=82624, stack(0x000000d6ac200000,0x000000d6ac300000)]
  0x00000273e00358e0 JavaThread "HikariPool-1 housekeeper" daemon [_thread_blocked, id=45236, stack(0x000000d6ac300000,0x000000d6ac400000)]
  0x00000273e0033570 JavaThread "HikariPool-1 connection adder" daemon [_thread_blocked, id=32968, stack(0x000000d6ac600000,0x000000d6ac700000)]
  0x00000273e0856c60 JavaThread "BufferPoolPruner-1-thread-1" daemon [_thread_blocked, id=28244, stack(0x000000d6ac700000,0x000000d6ac800000)]
  0x00000273e0856240 JavaThread "cluster-ClusterId{value='67da5686bbc76f32514941e1', description='null'}-**************:27017" daemon [_thread_in_native, id=67100, stack(0x000000d6ac800000,0x000000d6ac900000)]
  0x00000273e08539c0 JavaThread "cluster-rtt-ClusterId{value='67da5686bbc76f32514941e1', description='null'}-**************:27017" daemon [_thread_blocked, id=74664, stack(0x000000d6ac900000,0x000000d6aca00000)]
  0x00000273e0855310 JavaThread "MaintenanceTimer-2-thread-1" daemon [_thread_blocked, id=17884, stack(0x000000d6aca00000,0x000000d6acb00000)]
  0x00000273e61e89e0 JavaThread "idle-connection-reaper" daemon [_thread_blocked, id=20800, stack(0x000000d6acb00000,0x000000d6acc00000)]

Other Threads:
  0x00000273935d2600 VMThread "VM Thread" [stack: 0x000000d6a8f00000,0x000000d6a9000000] [id=27824]
  0x00000273da4fbf30 WatcherThread [stack: 0x000000d6ab300000,0x000000d6ab400000] [id=10980]
  0x00000273f97fa720 GCTaskThread "GC Thread#0" [stack: 0x000000d6a8a00000,0x000000d6a8b00000] [id=53260]
  0x00000273d97fecc0 GCTaskThread "GC Thread#1" [stack: 0x000000d6a9f00000,0x000000d6aa000000] [id=44284]
  0x00000273d95ff0d0 GCTaskThread "GC Thread#2" [stack: 0x000000d6aa000000,0x000000d6aa100000] [id=61360]
  0x00000273d95ff390 GCTaskThread "GC Thread#3" [stack: 0x000000d6aa100000,0x000000d6aa200000] [id=48560]
  0x00000273d9f45180 GCTaskThread "GC Thread#4" [stack: 0x000000d6aa200000,0x000000d6aa300000] [id=81308]
  0x00000273d9f45c60 GCTaskThread "GC Thread#5" [stack: 0x000000d6aa300000,0x000000d6aa400000] [id=66472]
=>0x00000273d9f46740 GCTaskThread "GC Thread#6" [stack: 0x000000d6aa400000,0x000000d6aa500000] [id=68688]
  0x00000273d9f46a00 GCTaskThread "GC Thread#7" [stack: 0x000000d6aa500000,0x000000d6aa600000] [id=62288]
  0x00000273d9f56cd0 GCTaskThread "GC Thread#8" [stack: 0x000000d6aa600000,0x000000d6aa700000] [id=65784]
  0x00000273d9f67fd0 GCTaskThread "GC Thread#9" [stack: 0x000000d6aa700000,0x000000d6aa800000] [id=55400]
  0x00000273d9f6b8f0 GCTaskThread "GC Thread#10" [stack: 0x000000d6aa800000,0x000000d6aa900000] [id=59228]
  0x00000273d9f6b370 GCTaskThread "GC Thread#11" [stack: 0x000000d6aa900000,0x000000d6aaa00000] [id=23916]
  0x00000273d9f6b0b0 GCTaskThread "GC Thread#12" [stack: 0x000000d6ab400000,0x000000d6ab500000] [id=22256]
  0x00000273f980b350 ConcurrentGCThread "G1 Main Marker" [stack: 0x000000d6a8b00000,0x000000d6a8c00000] [id=37056]
  0x00000273f980d6e0 ConcurrentGCThread "G1 Conc#0" [stack: 0x000000d6a8c00000,0x000000d6a8d00000] [id=84344]
  0x00000273dabb6da0 ConcurrentGCThread "G1 Conc#1" [stack: 0x000000d6ab800000,0x000000d6ab900000] [id=48912]
  0x00000273dabb75e0 ConcurrentGCThread "G1 Conc#2" [stack: 0x000000d6ab900000,0x000000d6aba00000] [id=73612]
  0x00000273f989bf00 ConcurrentGCThread "G1 Refine#0" [stack: 0x000000d6a8d00000,0x000000d6a8e00000] [id=47176]
  0x00000273dfbd7820 ConcurrentGCThread "G1 Refine#1" [stack: 0x000000d6ac400000,0x000000d6ac500000] [id=73824]
  0x00000273dfbd7530 ConcurrentGCThread "G1 Refine#2" [stack: 0x000000d6ac500000,0x000000d6ac600000] [id=62428]
  0x00000273f989c6e0 ConcurrentGCThread "G1 Service" [stack: 0x000000d6a8e00000,0x000000d6a8f00000] [id=78192]

Threads with active compile tasks:
C1 CompilerThread2    11212 8212       1       java.util.AbstractList$ListItr::nextIndex (5 bytes)
C1 CompilerThread3    11212 8211       1       java.io.ObjectInputStream::readObject (7 bytes)

VM state: at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00000273f973e6e0] Threads_lock - owner thread: 0x00000273935d2600
[0x00000273f973eb60] Heap_lock - owner thread: 0x00000273dab88d30

Heap address: 0x0000000604400000, size: 8124 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000027394000000-0x0000027394bb0000-0x0000027394bb0000), size 12255232, SharedBaseAddress: 0x0000027394000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x0000027395000000-0x00000273d5000000, reserved size: 1073741824
Narrow klass base: 0x0000027394000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CPUs: 16 total, 16 available
 Memory: 32483M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 4M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 508M
 Heap Max Capacity: 8124M
 Pre-touch: Disabled
 Parallel Workers: 13
 Concurrent Workers: 3
 Concurrent Refinement Workers: 13
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 204800K, used 167638K [0x0000000604400000, 0x0000000800000000)
  region size 4096K, 26 young (106496K), 3 survivors (12288K)
 Metaspace       used 103874K, committed 104640K, reserved 1179648K
  class space    used 13805K, committed 14208K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, OA=open archive, CA=closed archive, TAMS=top-at-mark-start (previous, next)
|   0|0x0000000604400000, 0x0000000604800000, 0x0000000604800000|100%| O|  |TAMS 0x0000000604800000, 0x0000000604400000| Untracked 
|   1|0x0000000604800000, 0x0000000604c00000, 0x0000000604c00000|100%| O|  |TAMS 0x0000000604c00000, 0x0000000604800000| Untracked 
|   2|0x0000000604c00000, 0x0000000605000000, 0x0000000605000000|100%| O|  |TAMS 0x0000000605000000, 0x0000000604c00000| Untracked 
|   3|0x0000000605000000, 0x0000000605400000, 0x0000000605400000|100%| O|  |TAMS 0x0000000605400000, 0x0000000605000000| Untracked 
|   4|0x0000000605400000, 0x0000000605800000, 0x0000000605800000|100%| O|  |TAMS 0x0000000605800000, 0x0000000605400000| Untracked 
|   5|0x0000000605800000, 0x0000000605b8ca00, 0x0000000605c00000| 88%| O|  |TAMS 0x0000000605b8ca00, 0x0000000605800000| Untracked 
|   6|0x0000000605c00000, 0x0000000606000000, 0x0000000606000000|100%| O|  |TAMS 0x0000000606000000, 0x0000000605c00000| Untracked 
|   7|0x0000000606000000, 0x0000000606400000, 0x0000000606400000|100%| O|  |TAMS 0x0000000606400000, 0x0000000606000000| Untracked 
|   8|0x0000000606400000, 0x0000000606800000, 0x0000000606800000|100%| O|  |TAMS 0x0000000606800000, 0x0000000606400000| Untracked 
|   9|0x0000000606800000, 0x0000000606a20200, 0x0000000606c00000| 53%| O|  |TAMS 0x0000000606a20200, 0x0000000606800000| Untracked 
|  10|0x0000000606c00000, 0x0000000607000000, 0x0000000607000000|100%| O|  |TAMS 0x0000000607000000, 0x0000000606c00000| Untracked 
|  11|0x0000000607000000, 0x0000000607400000, 0x0000000607400000|100%| O|  |TAMS 0x0000000607400000, 0x0000000607000000| Untracked 
|  12|0x0000000607400000, 0x0000000607800000, 0x0000000607800000|100%| O|  |TAMS 0x0000000607800000, 0x0000000607400000| Untracked 
|  13|0x0000000607800000, 0x0000000607c00000, 0x0000000607c00000|100%| O|  |TAMS 0x0000000607c00000, 0x0000000607800000| Untracked 
|  14|0x0000000607c00000, 0x0000000608000000, 0x0000000608000000|100%| O|  |TAMS 0x0000000608000000, 0x0000000607c00000| Untracked 
|  15|0x0000000608000000, 0x0000000608400000, 0x0000000608400000|100%| O|  |TAMS 0x000000060801da00, 0x0000000608000000| Untracked 
|  16|0x0000000608400000, 0x000000060879aa00, 0x0000000608800000| 90%| O|  |TAMS 0x0000000608400000, 0x0000000608400000| Untracked 
|  17|0x0000000608800000, 0x0000000608800000, 0x0000000608c00000|  0%| F|  |TAMS 0x0000000608800000, 0x0000000608800000| Untracked 
|  18|0x0000000608c00000, 0x0000000608c00000, 0x0000000609000000|  0%| F|  |TAMS 0x0000000608c00000, 0x0000000608c00000| Untracked 
|  19|0x0000000609000000, 0x0000000609000000, 0x0000000609400000|  0%| F|  |TAMS 0x0000000609000000, 0x0000000609000000| Untracked 
|  20|0x0000000609400000, 0x0000000609400000, 0x0000000609800000|  0%| F|  |TAMS 0x0000000609400000, 0x0000000609400000| Untracked 
|  21|0x0000000609800000, 0x0000000609800000, 0x0000000609c00000|  0%| F|  |TAMS 0x0000000609800000, 0x0000000609800000| Untracked 
|  22|0x0000000609c00000, 0x0000000609ec6df0, 0x000000060a000000| 69%| S|  |TAMS 0x0000000609c00000, 0x0000000609c00000| Complete 
|  23|0x000000060a000000, 0x000000060a400000, 0x000000060a400000|100%| S|  |TAMS 0x000000060a000000, 0x000000060a000000| Complete 
|  24|0x000000060a400000, 0x000000060a800000, 0x000000060a800000|100%| S|  |TAMS 0x000000060a400000, 0x000000060a400000| Complete 
|  25|0x000000060a800000, 0x000000060ac00000, 0x000000060ac00000|100%| E|CS|TAMS 0x000000060a800000, 0x000000060a800000| Complete 
|  26|0x000000060ac00000, 0x000000060b000000, 0x000000060b000000|100%| E|CS|TAMS 0x000000060ac00000, 0x000000060ac00000| Complete 
|  27|0x000000060b000000, 0x000000060b400000, 0x000000060b400000|100%| E|CS|TAMS 0x000000060b000000, 0x000000060b000000| Complete 
|  28|0x000000060b400000, 0x000000060b800000, 0x000000060b800000|100%| E|CS|TAMS 0x000000060b400000, 0x000000060b400000| Complete 
|  29|0x000000060b800000, 0x000000060bc00000, 0x000000060bc00000|100%| E|CS|TAMS 0x000000060b800000, 0x000000060b800000| Complete 
|  30|0x000000060bc00000, 0x000000060c000000, 0x000000060c000000|100%| E|CS|TAMS 0x000000060bc00000, 0x000000060bc00000| Complete 
|  31|0x000000060c000000, 0x000000060c400000, 0x000000060c400000|100%| E|CS|TAMS 0x000000060c000000, 0x000000060c000000| Complete 
|  32|0x000000060c400000, 0x000000060c800000, 0x000000060c800000|100%| E|CS|TAMS 0x000000060c400000, 0x000000060c400000| Complete 
|  33|0x000000060c800000, 0x000000060cc00000, 0x000000060cc00000|100%| E|CS|TAMS 0x000000060c800000, 0x000000060c800000| Complete 
|  34|0x000000060cc00000, 0x000000060d000000, 0x000000060d000000|100%| E|CS|TAMS 0x000000060cc00000, 0x000000060cc00000| Complete 
|  35|0x000000060d000000, 0x000000060d400000, 0x000000060d400000|100%| E|CS|TAMS 0x000000060d000000, 0x000000060d000000| Complete 
|  36|0x000000060d400000, 0x000000060d800000, 0x000000060d800000|100%| E|CS|TAMS 0x000000060d400000, 0x000000060d400000| Complete 
|  37|0x000000060d800000, 0x000000060dc00000, 0x000000060dc00000|100%| E|CS|TAMS 0x000000060d800000, 0x000000060d800000| Complete 
|  38|0x000000060dc00000, 0x000000060e000000, 0x000000060e000000|100%| E|CS|TAMS 0x000000060dc00000, 0x000000060dc00000| Complete 
|  39|0x000000060e000000, 0x000000060e400000, 0x000000060e400000|100%| E|CS|TAMS 0x000000060e000000, 0x000000060e000000| Complete 
|  40|0x000000060e400000, 0x000000060e800000, 0x000000060e800000|100%| E|CS|TAMS 0x000000060e400000, 0x000000060e400000| Complete 
|  41|0x000000060e800000, 0x000000060ec00000, 0x000000060ec00000|100%| E|CS|TAMS 0x000000060e800000, 0x000000060e800000| Complete 
|  42|0x000000060ec00000, 0x000000060ee6e4c8, 0x000000060f000000| 60%| E|CS|TAMS 0x000000060ec00000, 0x000000060ec00000| Complete 
|  43|0x000000060f000000, 0x000000060f400000, 0x000000060f400000|100%| E|CS|TAMS 0x000000060f000000, 0x000000060f000000| Complete 
|  44|0x000000060f400000, 0x000000060f800000, 0x000000060f800000|100%| E|CS|TAMS 0x000000060f400000, 0x000000060f400000| Complete 
|  47|0x0000000610000000, 0x0000000610400000, 0x0000000610400000|100%| E|CS|TAMS 0x0000000610000000, 0x0000000610000000| Complete 
|  48|0x0000000610400000, 0x0000000610800000, 0x0000000610800000|100%| E|CS|TAMS 0x0000000610400000, 0x0000000610400000| Complete 
|  49|0x0000000610800000, 0x0000000610c00000, 0x0000000610c00000|100%| E|CS|TAMS 0x0000000610800000, 0x0000000610800000| Complete 
|  50|0x0000000610c00000, 0x0000000611000000, 0x0000000611000000|100%| E|CS|TAMS 0x0000000610c00000, 0x0000000610c00000| Complete 
| 126|0x0000000623c00000, 0x0000000624000000, 0x0000000624000000|100%| E|CS|TAMS 0x0000000623c00000, 0x0000000623c00000| Complete 

Card table byte_map: [0x00000273fe700000,0x00000273ff6e0000] _byte_map_base: 0x00000273fb6de000

Marking Bits (Prev, Next): (CMBitMap*) 0x00000273f97fac50, (CMBitMap*) 0x00000273f97fac90
 Prev Bits: [0x0000027380fe0000, 0x0000027388ed0000)
 Next Bits: [0x0000027388ed0000, 0x0000027390dc0000)

Polling page: 0x00000273f10b0000

Metaspace:

Usage:
  Non-class:     87.96 MB used.
      Class:     13.48 MB used.
       Both:    101.44 MB used.

Virtual space:
  Non-class space:      128.00 MB reserved,      88.31 MB ( 69%) committed,  2 nodes.
      Class space:        1.00 GB reserved,      13.88 MB (  1%) committed,  1 nodes.
             Both:        1.12 GB reserved,     102.19 MB (  9%) committed. 

Chunk freelists:
   Non-Class:  7.38 MB
       Class:  1.98 MB
        Both:  9.36 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 165.88 MB
CDS: on
MetaspaceReclaimPolicy: balanced
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - new_chunks_are_fully_committed: 0.
 - uncommit_free_chunks: 1.
 - use_allocation_guard: 0.
 - handle_deallocations: 1.


Internal statistics:

num_allocs_failed_limit: 12.
num_arena_births: 1124.
num_arena_deaths: 0.
num_vsnodes_births: 3.
num_vsnodes_deaths: 0.
num_space_committed: 1635.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 12.
num_chunks_taken_from_freelist: 4801.
num_chunk_merges: 12.
num_chunk_splits: 3559.
num_chunks_enlarged: 2845.
num_inconsistent_stats: 0.

CodeCache: size=49152Kb used=24093Kb max_used=24093Kb free=25058Kb
 bounds [0x00000273f98e0000, 0x00000273fb070000, 0x00000273fc8e0000]
 total_blobs=8871 nmethods=8052 adapters=736
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 10.974 Thread 0x00000273936a2880 nmethod 8201 0x00000273fb060990 code [0x00000273fb060b80, 0x00000273fb060eb8]
Event: 10.974 Thread 0x00000273da022da0 8203   !   1       jdk.proxy2.$Proxy26::annotationType (29 bytes)
Event: 10.974 Thread 0x00000273da022da0 nmethod 8203 0x00000273fb061410 code [0x00000273fb0615e0, 0x00000273fb061838]
Event: 10.975 Thread 0x0000027393616fa0 nmethod 8202 0x00000273fb061a90 code [0x00000273fb061ec0, 0x00000273fb0636b8]
Event: 10.977 Thread 0x00000273d9f6f300 8204       1       org.springframework.beans.BeanWrapperImpl::<init> (6 bytes)
Event: 10.977 Thread 0x00000273da022da0 8205       1       org.springframework.beans.BeanWrapperImpl::<init> (6 bytes)
Event: 10.977 Thread 0x00000273937f7550 8206       1       org.springframework.beans.AbstractNestablePropertyAccessor::<init> (37 bytes)
Event: 10.977 Thread 0x00000273da022da0 nmethod 8205 0x00000273fb065290 code [0x00000273fb065420, 0x00000273fb065508]
Event: 10.977 Thread 0x00000273d9f6f300 nmethod 8204 0x00000273fb065610 code [0x00000273fb0657a0, 0x00000273fb065888]
Event: 10.977 Thread 0x00000273937f7550 nmethod 8206 0x00000273fb065990 code [0x00000273fb065b20, 0x00000273fb065db8]
Event: 10.978 Thread 0x00000273da0476c0 8207       1       java.lang.Class::getSimpleName (26 bytes)
Event: 10.978 Thread 0x00000273da0476c0 nmethod 8207 0x00000273fb066010 code [0x00000273fb0661c0, 0x00000273fb066358]
Event: 10.979 Thread 0x00000273d9f77bf0 8208       1       java.util.concurrent.ConcurrentHashMap::replace (36 bytes)
Event: 10.979 Thread 0x00000273d9f77bf0 nmethod 8208 0x00000273fb066510 code [0x00000273fb0666c0, 0x00000273fb066898]
Event: 10.985 Thread 0x00000273da047fe0 8209       1       java.beans.FeatureDescriptor::getTable (23 bytes)
Event: 10.985 Thread 0x00000273da047fe0 nmethod 8209 0x00000273fb066a10 code [0x00000273fb066be0, 0x00000273fb066da8]
Event: 10.986 Thread 0x00000273d9f78520 8210       1       org.springframework.beans.BeanWrapperImpl::setBeanInstance (35 bytes)
Event: 10.986 Thread 0x00000273d9f78520 nmethod 8210 0x00000273fb066f10 code [0x00000273fb0670e0, 0x00000273fb0674d8]
Event: 10.999 Thread 0x00000273937f85e0 8211       1       java.io.ObjectInputStream::readObject (7 bytes)
Event: 10.999 Thread 0x00000273937f7550 8212       1       java.util.AbstractList$ListItr::nextIndex (5 bytes)

GC Heap History (20 events):
Event: 2.753 GC heap after
{Heap after GC invocations=7 (full 0):
 garbage-first heap   total 122880K, used 33094K [0x0000000604400000, 0x0000000800000000)
  region size 4096K, 3 young (12288K), 3 survivors (12288K)
 Metaspace       used 29814K, committed 30272K, reserved 1114112K
  class space    used 3787K, committed 4032K, reserved 1048576K
}
Event: 3.144 GC heap before
{Heap before GC invocations=7 (full 0):
 garbage-first heap   total 122880K, used 69958K [0x0000000604400000, 0x0000000800000000)
  region size 4096K, 13 young (53248K), 3 survivors (12288K)
 Metaspace       used 35727K, committed 36160K, reserved 1114112K
  class space    used 4515K, committed 4736K, reserved 1048576K
}
Event: 3.150 GC heap after
{Heap after GC invocations=8 (full 0):
 garbage-first heap   total 122880K, used 35962K [0x0000000604400000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 2 survivors (8192K)
 Metaspace       used 35727K, committed 36160K, reserved 1114112K
  class space    used 4515K, committed 4736K, reserved 1048576K
}
Event: 4.039 GC heap before
{Heap before GC invocations=9 (full 0):
 garbage-first heap   total 122880K, used 85114K [0x0000000604400000, 0x0000000800000000)
  region size 4096K, 14 young (57344K), 2 survivors (8192K)
 Metaspace       used 41694K, committed 42176K, reserved 1114112K
  class space    used 5219K, committed 5440K, reserved 1048576K
}
Event: 4.043 GC heap after
{Heap after GC invocations=10 (full 0):
 garbage-first heap   total 323584K, used 39115K [0x0000000604400000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 2 survivors (8192K)
 Metaspace       used 41694K, committed 42176K, reserved 1114112K
  class space    used 5219K, committed 5440K, reserved 1048576K
}
Event: 5.726 GC heap before
{Heap before GC invocations=10 (full 0):
 garbage-first heap   total 323584K, used 223435K [0x0000000604400000, 0x0000000800000000)
  region size 4096K, 47 young (192512K), 2 survivors (8192K)
 Metaspace       used 53255K, committed 53696K, reserved 1114112K
  class space    used 6650K, committed 6848K, reserved 1048576K
}
Event: 5.733 GC heap after
{Heap after GC invocations=11 (full 0):
 garbage-first heap   total 323584K, used 49646K [0x0000000604400000, 0x0000000800000000)
  region size 4096K, 5 young (20480K), 5 survivors (20480K)
 Metaspace       used 53255K, committed 53696K, reserved 1114112K
  class space    used 6650K, committed 6848K, reserved 1048576K
}
Event: 6.733 GC heap before
{Heap before GC invocations=11 (full 0):
 garbage-first heap   total 323584K, used 160238K [0x0000000604400000, 0x0000000800000000)
  region size 4096K, 33 young (135168K), 5 survivors (20480K)
 Metaspace       used 60273K, committed 60800K, reserved 1114112K
  class space    used 7767K, committed 8064K, reserved 1048576K
}
Event: 6.740 GC heap after
{Heap after GC invocations=12 (full 0):
 garbage-first heap   total 323584K, used 54963K [0x0000000604400000, 0x0000000800000000)
  region size 4096K, 4 young (16384K), 4 survivors (16384K)
 Metaspace       used 60273K, committed 60800K, reserved 1114112K
  class space    used 7767K, committed 8064K, reserved 1048576K
}
Event: 7.716 GC heap before
{Heap before GC invocations=13 (full 0):
 garbage-first heap   total 204800K, used 161459K [0x0000000604400000, 0x0000000800000000)
  region size 4096K, 30 young (122880K), 4 survivors (16384K)
 Metaspace       used 69386K, committed 69888K, reserved 1114112K
  class space    used 8926K, committed 9152K, reserved 1048576K
}
Event: 7.723 GC heap after
{Heap after GC invocations=14 (full 0):
 garbage-first heap   total 204800K, used 60050K [0x0000000604400000, 0x0000000800000000)
  region size 4096K, 3 young (12288K), 3 survivors (12288K)
 Metaspace       used 69386K, committed 69888K, reserved 1114112K
  class space    used 8926K, committed 9152K, reserved 1048576K
}
Event: 8.619 GC heap before
{Heap before GC invocations=14 (full 0):
 garbage-first heap   total 204800K, used 158354K [0x0000000604400000, 0x0000000800000000)
  region size 4096K, 27 young (110592K), 3 survivors (12288K)
 Metaspace       used 82454K, committed 83008K, reserved 1179648K
  class space    used 10961K, committed 11200K, reserved 1048576K
}
Event: 8.623 GC heap after
{Heap after GC invocations=15 (full 0):
 garbage-first heap   total 204800K, used 62729K [0x0000000604400000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 2 survivors (8192K)
 Metaspace       used 82454K, committed 83008K, reserved 1179648K
  class space    used 10961K, committed 11200K, reserved 1048576K
}
Event: 9.456 GC heap before
{Heap before GC invocations=15 (full 0):
 garbage-first heap   total 204800K, used 165129K [0x0000000604400000, 0x0000000800000000)
  region size 4096K, 27 young (110592K), 2 survivors (8192K)
 Metaspace       used 90241K, committed 90880K, reserved 1179648K
  class space    used 12102K, committed 12416K, reserved 1048576K
}
Event: 9.461 GC heap after
{Heap after GC invocations=16 (full 0):
 garbage-first heap   total 204800K, used 67243K [0x0000000604400000, 0x0000000800000000)
  region size 4096K, 4 young (16384K), 4 survivors (16384K)
 Metaspace       used 90241K, committed 90880K, reserved 1179648K
  class space    used 12102K, committed 12416K, reserved 1048576K
}
Event: 10.160 GC heap before
{Heap before GC invocations=16 (full 0):
 garbage-first heap   total 204800K, used 161451K [0x0000000604400000, 0x0000000800000000)
  region size 4096K, 27 young (110592K), 4 survivors (16384K)
 Metaspace       used 98228K, committed 98816K, reserved 1179648K
  class space    used 13088K, committed 13376K, reserved 1048576K
}
Event: 10.166 GC heap after
{Heap after GC invocations=17 (full 0):
 garbage-first heap   total 204800K, used 72486K [0x0000000604400000, 0x0000000800000000)
  region size 4096K, 4 young (16384K), 4 survivors (16384K)
 Metaspace       used 98228K, committed 98816K, reserved 1179648K
  class space    used 13088K, committed 13376K, reserved 1048576K
}
Event: 10.312 GC heap before
{Heap before GC invocations=17 (full 0):
 garbage-first heap   total 204800K, used 92966K [0x0000000604400000, 0x0000000800000000)
  region size 4096K, 10 young (40960K), 4 survivors (16384K)
 Metaspace       used 100991K, committed 101568K, reserved 1179648K
  class space    used 13315K, committed 13568K, reserved 1048576K
}
Event: 10.318 GC heap after
{Heap after GC invocations=18 (full 0):
 garbage-first heap   total 204800K, used 73430K [0x0000000604400000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 2 survivors (8192K)
 Metaspace       used 100991K, committed 101568K, reserved 1179648K
  class space    used 13315K, committed 13568K, reserved 1048576K
}
Event: 10.999 GC heap before
{Heap before GC invocations=19 (full 0):
 garbage-first heap   total 204800K, used 167638K [0x0000000604400000, 0x0000000800000000)
  region size 4096K, 25 young (102400K), 2 survivors (8192K)
 Metaspace       used 103874K, committed 104640K, reserved 1179648K
  class space    used 13805K, committed 14208K, reserved 1048576K
}

Dll operation events (13 events):
Event: 0.043 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.12\bin\java.dll
Event: 0.048 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.12\bin\zip.dll
Event: 0.066 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.12\bin\jsvml.dll
Event: 0.622 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.12\bin\instrument.dll
Event: 0.630 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.12\bin\net.dll
Event: 0.633 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.12\bin\nio.dll
Event: 0.636 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.12\bin\zip.dll
Event: 0.875 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.12\bin\verify.dll
Event: 0.907 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.12\bin\jimage.dll
Event: 0.943 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.12\bin\management.dll
Event: 0.957 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.12\bin\management_ext.dll
Event: 1.238 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.12\bin\extnet.dll
Event: 2.797 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.12\bin\sunmscapi.dll

Deoptimization events (20 events):
Event: 10.997 Thread 0x00000273f11ee830 DEOPT PACKING pc=0x00000273fa20d97c sp=0x000000d6a89fbac0
Event: 10.997 Thread 0x00000273f11ee830 DEOPT UNPACKING pc=0x00000273f9937143 sp=0x000000d6a89faf78 mode 1
Event: 10.997 Thread 0x00000273f11ee830 DEOPT PACKING pc=0x00000273fa1ecf5c sp=0x000000d6a89fc4a0
Event: 10.997 Thread 0x00000273f11ee830 DEOPT UNPACKING pc=0x00000273f9937143 sp=0x000000d6a89fb990 mode 1
Event: 10.997 Thread 0x00000273f11ee830 DEOPT PACKING pc=0x00000273fa5cd224 sp=0x000000d6a89fc4e0
Event: 10.997 Thread 0x00000273f11ee830 DEOPT UNPACKING pc=0x00000273f9937143 sp=0x000000d6a89fb9b0 mode 1
Event: 10.998 Thread 0x00000273f11ee830 DEOPT PACKING pc=0x00000273fa20eb64 sp=0x000000d6a89fbb50
Event: 10.998 Thread 0x00000273f11ee830 DEOPT UNPACKING pc=0x00000273f9937143 sp=0x000000d6a89fafe8 mode 1
Event: 10.998 Thread 0x00000273f11ee830 DEOPT PACKING pc=0x00000273fa20d97c sp=0x000000d6a89fbc20
Event: 10.998 Thread 0x00000273f11ee830 DEOPT UNPACKING pc=0x00000273f9937143 sp=0x000000d6a89fb0d8 mode 1
Event: 10.998 Thread 0x00000273f11ee830 DEOPT PACKING pc=0x00000273fa1ecf5c sp=0x000000d6a89fc600
Event: 10.998 Thread 0x00000273f11ee830 DEOPT UNPACKING pc=0x00000273f9937143 sp=0x000000d6a89fbaf0 mode 1
Event: 10.998 Thread 0x00000273f11ee830 DEOPT PACKING pc=0x00000273fa20eb64 sp=0x000000d6a89fbae0
Event: 10.998 Thread 0x00000273f11ee830 DEOPT UNPACKING pc=0x00000273f9937143 sp=0x000000d6a89faf78 mode 1
Event: 10.998 Thread 0x00000273f11ee830 DEOPT PACKING pc=0x00000273fa20d97c sp=0x000000d6a89fbbb0
Event: 10.998 Thread 0x00000273f11ee830 DEOPT UNPACKING pc=0x00000273f9937143 sp=0x000000d6a89fb068 mode 1
Event: 10.998 Thread 0x00000273f11ee830 DEOPT PACKING pc=0x00000273fa1ecf5c sp=0x000000d6a89fc590
Event: 10.998 Thread 0x00000273f11ee830 DEOPT UNPACKING pc=0x00000273f9937143 sp=0x000000d6a89fba80 mode 1
Event: 10.998 Thread 0x00000273f11ee830 DEOPT PACKING pc=0x00000273fa5cd224 sp=0x000000d6a89fc5d0
Event: 10.998 Thread 0x00000273f11ee830 DEOPT UNPACKING pc=0x00000273f9937143 sp=0x000000d6a89fbaa0 mode 1

Classes loaded (20 events):
Event: 10.996 Loading class groovy/lang/GroovyInterceptable
Event: 10.996 Loading class groovy/lang/GroovyInterceptable done
Event: 10.996 Loading class groovy/runtime/metaclass/org/slf4j/LoggerFactoryMetaClass
Event: 10.996 Loading class groovy/runtime/metaclass/org/slf4j/LoggerFactoryMetaClass done
Event: 10.996 Loading class org/slf4j/event/SubstituteLoggingEvent
Event: 10.996 Loading class org/slf4j/event/SubstituteLoggingEvent done
Event: 10.997 Loading class org/slf4j/LoggerFactoryBeanInfo
Event: 10.997 Loading class org/slf4j/LoggerFactoryBeanInfo done
Event: 10.997 Loading class org/slf4j/LoggerFactoryBeanInfo
Event: 10.997 Loading class org/slf4j/LoggerFactoryBeanInfo done
Event: 10.997 Loading class org/slf4j/LoggerFactoryBeanInfo
Event: 10.997 Loading class org/slf4j/LoggerFactoryBeanInfo done
Event: 10.997 Loading class org/slf4j/LoggerFactoryCustomizer
Event: 10.997 Loading class org/slf4j/LoggerFactoryCustomizer done
Event: 10.998 Loading class org/slf4j/LoggerFactoryCustomizer
Event: 10.998 Loading class org/slf4j/LoggerFactoryCustomizer done
Event: 10.998 Loading class org/slf4j/LoggerFactoryCustomizer
Event: 10.998 Loading class org/slf4j/LoggerFactoryCustomizer done
Event: 10.998 Loading class org/codehaus/groovy/runtime/metaclass/MetaMethodIndex$CacheEntry
Event: 10.998 Loading class org/codehaus/groovy/runtime/metaclass/MetaMethodIndex$CacheEntry done

Classes unloaded (0 events):
No events

Classes redefined (1 events):
Event: 0.894 Thread 0x00000273935d2600 redefined class name=java.lang.Throwable, count=1

Internal exceptions (20 events):
Event: 10.907 Thread 0x00000273f11ee830 Exception <a 'java/lang/NoSuchMethodError'{0x000000060b1b0e28}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000060b1b0e28) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 10.908 Thread 0x00000273f11ee830 Exception <a 'java/lang/NoSuchMethodError'{0x000000060b1ba1d0}: 'int java.lang.invoke.Invokers$Holder.exactInvoker(java.lang.Object, java.lang.Object)'> (0x000000060b1ba1d0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 10.910 Thread 0x00000273f11ee830 Exception <a 'java/lang/NoSuchMethodError'{0x000000060b1d42e8}: 'java.lang.Object java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000060b1d42e8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 10.910 Thread 0x00000273f11ee830 Exception <a 'java/lang/NoSuchMethodError'{0x000000060b1f3b90}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x000000060b1f3b90) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 10.919 Thread 0x00000273f11ee830 Exception <a 'java/lang/ClassNotFoundException'{0x000000060b2f7480}: com/derbysoft/next/propertyconnect/channel/task/util/CloneUtilBeanInfo> (0x000000060b2f7480) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 10.920 Thread 0x00000273f11ee830 Exception <a 'java/lang/ClassNotFoundException'{0x000000060b303ca8}: com/derbysoft/next/propertyconnect/channel/task/util/CloneUtilCustomizer> (0x000000060b303ca8) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 10.921 Thread 0x00000273f11ee830 Exception <a 'java/lang/NoSuchMethodError'{0x000000060b31b998}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000060b31b998) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 10.921 Thread 0x00000273f11ee830 Exception <a 'java/lang/IncompatibleClassChangeError'{0x000000060b31f510}: Found class java.lang.Object, but interface was expected> (0x000000060b31f510) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 826]
Event: 10.921 Thread 0x00000273f11ee830 Exception <a 'java/lang/NoSuchMethodError'{0x000000060b3201a8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000060b3201a8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 10.921 Thread 0x00000273f11ee830 Exception <a 'java/lang/NoSuchMethodError'{0x000000060b324558}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000060b324558) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 10.921 Thread 0x00000273f11ee830 Exception <a 'java/lang/IncompatibleClassChangeError'{0x000000060b3282b8}: Found class java.lang.Object, but interface was expected> (0x000000060b3282b8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 826]
Event: 10.925 Thread 0x00000273f11ee830 Exception <a 'java/lang/NoSuchMethodError'{0x000000060b38e6d8}: 'java.lang.Object java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000060b38e6d8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 10.929 Thread 0x00000273f11ee830 Exception <a 'java/lang/ClassNotFoundException'{0x000000060b3e7520}: com/derbysoft/next/propertyconnect/channel/task/service/impl/ctrip/CtripServiceImplBeanInfo> (0x000000060b3e7520) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 10.930 Thread 0x00000273f11ee830 Exception <a 'java/lang/ClassNotFoundException'{0x000000060b3f4c48}: com/derbysoft/next/propertyconnect/channel/task/service/impl/ctrip/CtripServiceImplCustomizer> (0x000000060b3f4c48) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 10.935 Thread 0x00000273f11ee830 Exception <a 'java/lang/reflect/InvocationTargetException'{0x000000060ac9ac68}> (0x000000060ac9ac68) 
thrown [s\src\hotspot\share\runtime\reflection.cpp, line 1128]
Event: 10.963 Thread 0x00000273f11ee830 Exception <a 'java/lang/reflect/InvocationTargetException'{0x000000060a888598}> (0x000000060a888598) 
thrown [s\src\hotspot\share\runtime\reflection.cpp, line 1128]
Event: 10.984 Thread 0x00000273f11ee830 Exception <a 'java/lang/ClassNotFoundException'{0x000000060aa8a698}: com/derbysoft/next/propertyconnect/channel/task/service/impl/expedia/ExpediaProductsServiceImplBeanInfo> (0x000000060aa8a698) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 10.985 Thread 0x00000273f11ee830 Exception <a 'java/lang/ClassNotFoundException'{0x000000060aa98980}: com/derbysoft/next/propertyconnect/channel/task/service/impl/expedia/ExpediaProductsServiceImplCustomizer> (0x000000060aa98980) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 10.997 Thread 0x00000273f11ee830 Exception <a 'java/lang/ClassNotFoundException'{0x000000060ab838e0}: org/slf4j/LoggerFactoryBeanInfo> (0x000000060ab838e0) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 10.998 Thread 0x00000273f11ee830 Exception <a 'java/lang/ClassNotFoundException'{0x000000060ab8e048}: org/slf4j/LoggerFactoryCustomizer> (0x000000060ab8e048) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]

VM Operations (20 events):
Event: 9.468 Executing VM operation: HandshakeAllThreads done
Event: 9.664 Executing VM operation: HandshakeAllThreads
Event: 9.664 Executing VM operation: HandshakeAllThreads done
Event: 9.805 Executing VM operation: HandshakeAllThreads
Event: 9.805 Executing VM operation: HandshakeAllThreads done
Event: 10.130 Executing VM operation: HandshakeAllThreads
Event: 10.130 Executing VM operation: HandshakeAllThreads done
Event: 10.160 Executing VM operation: G1CollectForAllocation
Event: 10.166 Executing VM operation: G1CollectForAllocation done
Event: 10.312 Executing VM operation: CollectForMetadataAllocation
Event: 10.318 Executing VM operation: CollectForMetadataAllocation done
Event: 10.339 Executing VM operation: G1PauseRemark
Event: 10.345 Executing VM operation: G1PauseRemark done
Event: 10.355 Executing VM operation: G1PauseCleanup
Event: 10.355 Executing VM operation: G1PauseCleanup done
Event: 10.893 Executing VM operation: ICBufferFull
Event: 10.894 Executing VM operation: ICBufferFull done
Event: 10.908 Executing VM operation: HandshakeAllThreads
Event: 10.908 Executing VM operation: HandshakeAllThreads done
Event: 10.999 Executing VM operation: G1CollectForAllocation

Events (20 events):
Event: 7.574 Thread 0x00000273936231d0 flushing nmethod 0x00000273face6410
Event: 7.677 Thread 0x00000273e00358e0 Thread added: 0x00000273e00358e0
Event: 7.785 Thread 0x00000273e0033570 Thread added: 0x00000273e0033570
Event: 7.968 Thread 0x00000273e084fd00 Thread added: 0x00000273e084fd00
Event: 7.971 Thread 0x00000273e084fd00 Thread exited: 0x00000273e084fd00
Event: 8.473 Thread 0x00000273e0851140 Thread added: 0x00000273e0851140
Event: 8.476 Thread 0x00000273e0851140 Thread exited: 0x00000273e0851140
Event: 8.850 Thread 0x00000273e0856c60 Thread added: 0x00000273e0856c60
Event: 8.921 Thread 0x00000273e0856240 Thread added: 0x00000273e0856240
Event: 8.967 Thread 0x00000273e08539c0 Thread added: 0x00000273e08539c0
Event: 8.970 Thread 0x00000273e0855310 Thread added: 0x00000273e0855310
Event: 8.977 Thread 0x00000273e085b850 Thread added: 0x00000273e085b850
Event: 8.981 Thread 0x00000273e085b850 Thread exited: 0x00000273e085b850
Event: 9.483 Thread 0x00000273e61e6b80 Thread added: 0x00000273e61e6b80
Event: 9.487 Thread 0x00000273e61e6b80 Thread exited: 0x00000273e61e6b80
Event: 9.989 Thread 0x00000273e61e7ab0 Thread added: 0x00000273e61e7ab0
Event: 9.992 Thread 0x00000273e61e7ab0 Thread exited: 0x00000273e61e7ab0
Event: 10.104 Thread 0x00000273e61e89e0 Thread added: 0x00000273e61e89e0
Event: 10.493 Thread 0x00000273e61eb770 Thread added: 0x00000273e61eb770
Event: 10.496 Thread 0x00000273e61eb770 Thread exited: 0x00000273e61eb770


Dynamic libraries:
0x00007ff63e4d0000 - 0x00007ff63e4de000 	C:\Users\<USER>\.jdks\corretto-17.0.12\bin\java.exe
0x00007ffb0e100000 - 0x00007ffb0e363000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffb0d730000 - 0x00007ffb0d7f7000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffb0b570000 - 0x00007ffb0b939000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffb0b940000 - 0x00007ffb0ba8c000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffafefe0000 - 0x00007ffafeffb000 	C:\Users\<USER>\.jdks\corretto-17.0.12\bin\VCRUNTIME140.dll
0x00007ffafebe0000 - 0x00007ffafebf7000 	C:\Users\<USER>\.jdks\corretto-17.0.12\bin\jli.dll
0x00007ffb0d0b0000 - 0x00007ffb0d27a000 	C:\WINDOWS\System32\USER32.dll
0x00007ffb0b540000 - 0x00007ffb0b567000 	C:\WINDOWS\System32\win32u.dll
0x00007ffae8d20000 - 0x00007ffae8fb0000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.3323_none_3e088096e3344490\COMCTL32.dll
0x00007ffb0df30000 - 0x00007ffb0dfd9000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffb0cce0000 - 0x00007ffb0cd0a000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffb0bc70000 - 0x00007ffb0bda1000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffb0bbc0000 - 0x00007ffb0bc63000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffb0ceb0000 - 0x00007ffb0cedf000 	C:\WINDOWS\System32\IMM32.DLL
0x00000273f10d0000 - 0x00000273f10e6000 	C:\WINDOWS\System32\umppc19205.dll
0x00007ffafebc0000 - 0x00007ffafebcc000 	C:\Users\<USER>\.jdks\corretto-17.0.12\bin\vcruntime140_1.dll
0x00007ffae7b60000 - 0x00007ffae7bed000 	C:\Users\<USER>\.jdks\corretto-17.0.12\bin\msvcp140.dll
0x00007ffa8bd00000 - 0x00007ffa8c96a000 	C:\Users\<USER>\.jdks\corretto-17.0.12\bin\server\jvm.dll
0x00007ffb0d8a0000 - 0x00007ffb0d952000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffb0cc20000 - 0x00007ffb0ccc6000 	C:\WINDOWS\System32\sechost.dll
0x00007ffb0de10000 - 0x00007ffb0df26000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffb0c140000 - 0x00007ffb0c1b4000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffb0b0e0000 - 0x00007ffb0b12e000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ffafbe70000 - 0x00007ffafbea6000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffb054c0000 - 0x00007ffb054cb000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffb0b0c0000 - 0x00007ffb0b0d4000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ffb0a0e0000 - 0x00007ffb0a0fa000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffaf7d00000 - 0x00007ffaf7d0a000 	C:\Users\<USER>\.jdks\corretto-17.0.12\bin\jimage.dll
0x00007ffafe1a0000 - 0x00007ffafe3e1000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffb0d3a0000 - 0x00007ffb0d722000 	C:\WINDOWS\System32\combase.dll
0x00007ffb0cb40000 - 0x00007ffb0cc16000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffafdbf0000 - 0x00007ffafdc29000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffb0bb20000 - 0x00007ffb0bbb9000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffae9300000 - 0x00007ffae933b000 	C:\Users\<USER>\.jdks\corretto-17.0.12\bin\jdwp.dll
0x00007ffa916a0000 - 0x00007ffa918ae000 	C:\Users\<USER>\AppData\Local\Temp\idea_libasyncProfiler_dll_temp_folder78745\libasyncProfiler.dll
0x00007ffafb700000 - 0x00007ffafb70e000 	C:\Users\<USER>\.jdks\corretto-17.0.12\bin\instrument.dll
0x00007ffae8b40000 - 0x00007ffae8b65000 	C:\Users\<USER>\.jdks\corretto-17.0.12\bin\java.dll
0x00007ffae9280000 - 0x00007ffae9298000 	C:\Users\<USER>\.jdks\corretto-17.0.12\bin\zip.dll
0x00007ffae7a80000 - 0x00007ffae7b57000 	C:\Users\<USER>\.jdks\corretto-17.0.12\bin\jsvml.dll
0x00007ffb0c240000 - 0x00007ffb0c955000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffb0bdb0000 - 0x00007ffb0bf18000 	C:\WINDOWS\System32\wintypes.dll
0x00007ffb08fc0000 - 0x00007ffb09805000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ffb0bfd0000 - 0x00007ffb0c0bb000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ffb0d840000 - 0x00007ffb0d89d000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ffb0b210000 - 0x00007ffb0b23f000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ffafb6f0000 - 0x00007ffafb6fc000 	C:\Users\<USER>\.jdks\corretto-17.0.12\bin\dt_socket.dll
0x00007ffb09bb0000 - 0x00007ffb09be3000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ffb0a5e0000 - 0x00007ffb0a64a000 	C:\WINDOWS\system32\mswsock.dll
0x00007ffaeed20000 - 0x00007ffaeed39000 	C:\Users\<USER>\.jdks\corretto-17.0.12\bin\net.dll
0x00007ffb04190000 - 0x00007ffb042ae000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ffaedb40000 - 0x00007ffaedb56000 	C:\Users\<USER>\.jdks\corretto-17.0.12\bin\nio.dll
0x00007ffaf1840000 - 0x00007ffaf1850000 	C:\Users\<USER>\.jdks\corretto-17.0.12\bin\verify.dll
0x00007ffb09bf0000 - 0x00007ffb09d15000 	C:\WINDOWS\SYSTEM32\DNSAPI.dll
0x00007ffb0ccd0000 - 0x00007ffb0ccda000 	C:\WINDOWS\System32\NSI.dll
0x00000000732f0000 - 0x0000000073316000 	C:\Program Files\Bonjour\mdnsNSP.dll
0x00007ffb03a50000 - 0x00007ffb03a5b000 	C:\Windows\System32\rasadhlp.dll
0x00007ffb03690000 - 0x00007ffb03715000 	C:\WINDOWS\System32\fwpuclnt.dll
0x00007ffafbac0000 - 0x00007ffafbac9000 	C:\Users\<USER>\.jdks\corretto-17.0.12\bin\management.dll
0x00007ffafb710000 - 0x00007ffafb71b000 	C:\Users\<USER>\.jdks\corretto-17.0.12\bin\management_ext.dll
0x00007ffb0d800000 - 0x00007ffb0d808000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ffb0a990000 - 0x00007ffb0a9ac000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ffb0a040000 - 0x00007ffb0a07a000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ffb0a680000 - 0x00007ffb0a6ab000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ffb0ab20000 - 0x00007ffb0ab46000 	C:\WINDOWS\SYSTEM32\bcrypt.dll
0x00007ffb0a810000 - 0x00007ffb0a81c000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ffb03fa0000 - 0x00007ffb03fbf000 	C:\WINDOWS\SYSTEM32\dhcpcsvc6.DLL
0x00007ffb03f70000 - 0x00007ffb03f95000 	C:\WINDOWS\SYSTEM32\dhcpcsvc.DLL
0x00007ffae8550000 - 0x00007ffae8568000 	C:\WINDOWS\system32\napinsp.dll
0x00007ffae8570000 - 0x00007ffae8582000 	C:\WINDOWS\System32\winrnr.dll
0x00007ffae8590000 - 0x00007ffae85b2000 	C:\WINDOWS\system32\nlansp_c.dll
0x00007ffb03d00000 - 0x00007ffb03d16000 	C:\WINDOWS\system32\wshbth.dll
0x00007ffae9110000 - 0x00007ffae9119000 	C:\Users\<USER>\.jdks\corretto-17.0.12\bin\extnet.dll
0x00007ffae9220000 - 0x00007ffae922e000 	C:\Users\<USER>\.jdks\corretto-17.0.12\bin\sunmscapi.dll
0x00007ffb0b3c0000 - 0x00007ffb0b537000 	C:\WINDOWS\System32\CRYPT32.dll
0x00007ffb0aae0000 - 0x00007ffb0ab10000 	C:\WINDOWS\SYSTEM32\ncrypt.dll
0x00007ffb0aa90000 - 0x00007ffb0aacf000 	C:\WINDOWS\SYSTEM32\NTASN1.dll
0x00007ffb02c30000 - 0x00007ffb02c38000 	C:\WINDOWS\system32\wshunix.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Users\<USER>\.jdks\corretto-17.0.12\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.3323_none_3e088096e3344490;C:\Users\<USER>\.jdks\corretto-17.0.12\bin\server;C:\Users\<USER>\AppData\Local\Temp\idea_libasyncProfiler_dll_temp_folder78745;C:\Program Files\Bonjour

VM Arguments:
jvm_args: -agentlib:jdwp=transport=dt_socket,address=127.0.0.1:38269,suspend=y,server=n -agentpath:C:\Users\<USER>\AppData\Local\Temp\idea_libasyncProfiler_dll_temp_folder78745\libasyncProfiler.dll=version,jfr,event=wall,interval=10ms,cstack=no,file=C:\Users\<USER>\IdeaSnapshots\ChannelTaskApplication_2025_03_19_133037.jfr,dbghelppath=C:\Users\<USER>\AppData\Local\Temp\idea_dbghelp_dll_temp_folder8\dbghelp.dll,log=C:\Users\<USER>\AppData\Local\Temp\ChannelTaskApplication_2025_03_19_133037.jfr.log.txt,logLevel=DEBUG -Dcool.request.port=39783 -XX:TieredStopAtLevel=1 -Dspring.output.ansi.enabled=always -Dcom.sun.management.jmxremote -Dspring.jmx.enabled=true -Dspring.liveBeansView.mbeanDomain -Dspring.application.admin.enabled=true -Dmanagement.endpoints.jmx.exposure.include=* -javaagent:C:\Users\<USER>\AppData\Local\JetBrains\IntelliJIdea2024.3\groovyHotSwap\gragent.jar -javaagent:C:\Users\<USER>\AppData\Local\JetBrains\IntelliJIdea2024.3\captureAgent\debugger-agent.jar=file:/C:/Users/<USER>/AppData/Local/Temp/capture10.props -Dkotlinx.coroutines.debug.enable.creation.stack.trace=false -Ddebugger.agent.enable.coroutines=true -Dkotlinx.coroutines.debug.enable.flows.stack.trace=true -Dkotlinx.coroutines.debug.enable.mutable.state.flows.stack.trace=true -Dfile.encoding=UTF-8 
java_command: com.derbysoft.next.propertyconnect.channel.task.ChannelTaskApplication
java_class_path (initial): C:\Users\<USER>\Documents\IdeaProjects\next-propertyconnect-channel\next-propertyconnect-channel-task\target\classes;C:\Users\<USER>\Documents\IdeaProjects\next-propertyconnect-channel\next-propertyconnect-channel-common\target\classes;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-configuration-processor\3.4.1\spring-boot-configuration-processor-3.4.1.jar;C:\Users\<USER>\.m2\repository\org\apache\groovy\groovy\4.0.24\groovy-4.0.24.jar;C:\Users\<USER>\.m2\repository\org\apache\groovy\groovy-xml\4.0.24\groovy-xml-4.0.24.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-web\3.4.1\spring-boot-starter-web-3.4.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter\3.4.1\spring-boot-starter-3.4.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot\3.4.1\spring-boot-3.4.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-logging\3.4.1\spring-boot-starter-logging-3.4.1.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-to-slf4j\2.24.3\log4j-to-slf4j-2.24.3.jar;C:\Users\<USER>\.m2\repository\org\slf4j\jul-to-slf4j\2.0.16\jul-to-slf4j-2.0.16.jar;C:\Users\<USER>\.m2\repository\org\yaml\snakeyaml\2.3\snakeyaml-2.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-json\3.4.1\spring-boot-starter-json-3.4.1.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-databind\2.18.2\jackson-databind-2.18.2.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.18.2\jackson-datatype-jdk8-2.18.2.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.18.2\jackson-datatype-jsr310-2.18.2.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.18.2\jackson-module-parameter-names-2.18.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-web\6.2.1\spring-web-6.2.1.jar;C:\Users\<USER>\Users\86727\.jdks\graalvm-ce-17
CLASSPATH=C:\Users\<USER>\.jdks\graalvm-ce-17\bin;C:\Users\<USER>\.jdks\corretto-11.0.15\bin;C:\ProgramData\chocolatey\lib\Tomcat\tools\apache-tomcat-9.0.62\lib
PATH=C:\Program Files (x86)\Embarcadero\Studio\22.0\bin;C:\Users\<USER>\Documents\Embarcadero\Studio\22.0\Bpl;C:\Program Files (x86)\Embarcadero\Studio\22.0\bin64;C:\Users\<USER>\Documents\Embarcadero\Studio\22.0\Bpl\Win64;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\ProgramData\chocolatey\bin;C:\tools\groovy-3.0.14\bin;C:\Program Files\PuTTY\;C:\Program Files\mvndaemon\mvnd-0.7.1-windows-amd64\bin;C:\ProgramData\chocolatey\lib\maven\apache-maven-3.8.4\bin;C:\Program Files\Amazon\AWSCLIV2\;C:\Program Files\dotnet\;C:\Program Files\Common Files\Autodesk Shared\;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Auxiliary\Build;C:\Program Files\MariaDB\MariaDB Connector C 64-bit\lib\;C:\Program Files\MariaDB\MariaDB Connector C 64-bit\lib\plugin\;C:\Program Files\nodejs\;C:\Program Files\Kubernetes\Minikube;C:\Program Files\Git\cmd;C:\Program Files\Docker\Docker\resources\bin;C:\Users\<USER>\.pythons\Python313;C:\Users\<USER>\.pythons\Python313\Scripts;;%SystemRoot%\system32;%SystemRoot%;%SystemRoot%\System32\Wbem;%SYSTEMROOT%\System32\WindowsPowerShell\v1.0\;%SYSTEMROOT%\System32\OpenSSH\;C:\Users\<USER>\AppData\Local\pnpm;C:\Program Files\platform-tools;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\IntelliJ IDEA 2021.2\bin;C:\Program Files\Bandizip\;C:\Program Files\nodejs;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Program Files\JetBrains\DataGrip 2021.1\bin;C:\tools\dart-sdk\bin;C:\Users\<USER>\AppData\Roaming\Pub\Cache\bin;C:\Program Files\JetBrains\JetBrains Rider 2020.3.4\bin;C:\Users\<USER>\AppData\Local\Programs\Fiddler;C:\Program Files (x86)\Nmap;C:\Users\<USER>\AppData\Local\JetBrains\Toolbox\scripts;C:\Program Files\OpenSSL-Win64\bin;C:\WINDOWS\system32\config\systemprofile\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\.konan\kotlin-native-prebuilt-windows-x86_64-1.8.0\bin;C:\Users\<USER>\.jdks\graalvm-ce-17\bi
USERNAME=86727
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 165 Stepping 5, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp



Periodic native trim disabled


---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 26100 (10.0.26100.3323)
OS uptime: 7 days 0:37 hours
Hyper-V role detected

CPU: total 16 (initial active 16) (8 cores per cpu, 2 threads per core) family 6 model 165 stepping 5 microcode 0xfe, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt, hv
Processor Information for all 16 processors :
  Max Mhz: 2901, Current Mhz: 2901, Mhz Limit: 2901

Memory: 4k page, system-wide physical 32483M (4995M free)
TotalPageFile size 53005M (AvailPageFile size 2621M)
current process WorkingSet (physical memory assigned to process): 519M, peak: 520M
current process commit charge ("private bytes"): 591M, peak: 702M

vm_info: OpenJDK 64-Bit Server VM (17.0.12+7-LTS) for windows-amd64 JRE (17.0.12+7-LTS), built on Jul 10 2024 20:46:33 by "Administrator" with MS VC++ 16.10 / 16.11 (VS2019)

END.
