image: image-center.dbaws.net/build/build-amazon-linux:20230717

pipelines:
  branches:
    develop:
      - parallel:
          - step:
              name: pcchannel-uat-build
              runs-on:
                - self.hosted
                - linux
              script:
                - pipe: docker://image-center.dbaws.net/base/derbysoft-ade:latest
                  variables:
                    ACTION: "build"
                    BUILD_PLAN_ID: "654afb34c8718d6ef253a309"
                    ARTIFACT_NAME: "build_msg_uat.json"
              artifacts:
                - build_msg_uat.json
              services:
                - docker
          - step:
              name: pcchannel-qa-build
              runs-on:
                - self.hosted
                - linux
              script:
                - pipe: docker://image-center.dbaws.net/base/derbysoft-ade:latest
                  variables:
                    ACTION: "build"
                    BUILD_PLAN_ID: "6595273bc8718d2d050ea682"
                    ARTIFACT_NAME: "build_msg_qa.json"
              artifacts:
                - build_msg_qa.json
              services:
                - docker

      - parallel:
          - step:
              name: pcchannel-uat-deployment-update-apply
              runs-on:
                - self.hosted
                - linux
              script:
                - echo "ENV=uat" > env-uat.txt
                - pipe: docker://image-center.dbaws.net/base/derbysoft-ade:latest
                  variables:
                    ACTION: "update_and_apply"
                    ADE_DEPLOYMENT_NAME: "pcchannel-uat-bgcusikqt"
                    ARTIFACTS_LIST: "build_msg_uat.json"
                    POD_NUMBER: "1"
              artifacts:
                - env-uat.txt
              services:
                - docker
          - step:
              name: pcchannel-rt-deployment-update-apply
              runs-on:
                - self.hosted
                - linux
              script:
                - echo "ENV=rt" > env-rt.txt
                - pipe: docker://image-center.dbaws.net/base/derbysoft-ade:latest
                  variables:
                    ACTION: "update_and_apply"
                    ADE_DEPLOYMENT_NAME: "pcchannel-rt-qgdd4tcwr"
                    ARTIFACTS_LIST: "build_msg_uat.json"
                    POD_NUMBER: "1"
              artifacts:
                - env-rt.txt
              services:
                - docker
          - step:
              name: pcchannel-qa-deployment-update-apply
              runs-on:
                - self.hosted
                - linux
              script:
                - echo "ENV=qa" > env-qa.txt
                - pipe: docker://image-center.dbaws.net/base/derbysoft-ade:latest
                  variables:
                    ACTION: "update_and_apply"
                    ADE_DEPLOYMENT_NAME: "pcchannel-qa-7rm406cwg"
                    ARTIFACTS_LIST: "build_msg_qa.json"
                    POD_NUMBER: "1"
              artifacts:
                - env-qa.txt
              services:
                - docker

      - step:
          name: Notify WeChat Work Bot
          runs-on:
            - self.hosted
            - linux
          script:
            - COMMIT_MSG="$(git log -1 --pretty=%B)"
            - COMMITTER="$(git log -1 --pretty=%an)"
            - |
              curl 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=93569137-e924-4435-abae-5e68bbc01df0' \
                -H 'Content-Type: application/json' \
                -d "{
                  \"msgtype\": \"markdown\",
                  \"markdown\": {
                    \"content\": \"**UAT&RT&QA PCChannel发布**\n> ${COMMIT_MSG}\n> ${COMMITTER}\"
                  }
                }"
