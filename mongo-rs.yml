version: "3.8"
services:
  mongo1:
    image: mongo:4.0.28
    container_name: mongo1
    ports:
      - 27017:27017
    volumes:
      - ./data1:/data/db
    command: mongod --replSet rs0 --bind_ip_all
    networks:
      - mongo-cluster

  mongo2:
    image: mongo:4.0.28
    container_name: mongo2
    ports:
      - 27018:27017
    volumes:
      - ./data2:/data/db
    command: mongod --replSet rs0 --bind_ip_all
    networks:
      - mongo-cluster

  mongo3:
    image: mongo:4.0.28
    container_name: mongo3
    ports:
      - 27019:27017
    volumes:
      - ./data3:/data/db
    command: mongod --replSet rs0 --bind_ip_all
    networks:
      - mongo-cluster

networks:
  mongo-cluster:
    driver: bridge