package com.derbysoft.next.propertyconenct.channel.common.exception.response;

import org.junit.jupiter.api.Test;

import java.util.*;
import java.util.function.Supplier;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Comprehensive unit tests for UnifyResult
 */
class UnifyResultTest {

    @Test
    void testConstructorWithHeaderAndPayload() {
        // Test constructor with header and payload
        Header header = new Header();
        String payload = "test payload";
        
        UnifyResult<String> result = new UnifyResult<>(header, payload);
        
        assertEquals(header, result.getHeader());
        assertEquals(payload, result.object);
        assertNull(result.list);
    }

    @Test
    void testConstructorWithHeaderAndCollectionPayload() {
        // Test constructor with header and collection payload
        Header header = new Header();
        List<String> payload = Arrays.asList("item1", "item2", "item3");
        
        UnifyResult<List<String>> result = new UnifyResult<>(header, payload);
        
        assertEquals(header, result.getHeader());
        assertNull(result.object);
        assertEquals(payload, result.list);
        assertEquals(payload, result.getList());
    }

    @Test
    void testConstructorWithHeaderAndNullPayload() {
        // Test constructor with header and null payload
        Header header = new Header();
        
        UnifyResult<String> result = new UnifyResult<>(header, null);
        
        assertEquals(header, result.getHeader());
        assertNull(result.object);
        assertNull(result.list);
    }

    @Test
    void testGetMapWithMapObject() {
        // Test getMap when object is a Map
        Header header = new Header();
        Map<String, Object> mapPayload = new HashMap<>();
        mapPayload.put("key1", "value1");
        mapPayload.put("key2", "value2");
        
        UnifyResult<Map<String, Object>> result = new UnifyResult<>(header, mapPayload);
        
        Map<?, ?> retrievedMap = result.getMap();
        assertNotNull(retrievedMap);
        assertEquals(mapPayload, retrievedMap);
        assertEquals("value1", retrievedMap.get("key1"));
        assertEquals("value2", retrievedMap.get("key2"));
    }

    @Test
    void testGetMapWithNonMapObject() {
        // Test getMap when object is not a Map
        Header header = new Header();
        String stringPayload = "not a map";
        
        UnifyResult<String> result = new UnifyResult<>(header, stringPayload);
        
        Map<?, ?> retrievedMap = result.getMap();
        assertNull(retrievedMap);
    }

    @Test
    void testGetMapWithNullObject() {
        // Test getMap when object is null
        Header header = new Header();
        
        UnifyResult<Object> result = new UnifyResult<>(header, null);
        
        Map<?, ?> retrievedMap = result.getMap();
        assertNull(retrievedMap);
    }

    @Test
    void testFromWithHeaderAndSupplier() {
        // Test from method with header and supplier
        Header header = new Header();
        Supplier<String> supplier = () -> "supplied value";
        
        UnifyResult<String> result = UnifyResult.from(header, supplier);
        
        assertEquals(header, result.getHeader());
        assertEquals("supplied value", result.object);
    }

    @Test
    void testFromWithSupplierOnly() {
        // Test from method with supplier only (uses default header)
        Supplier<Integer> supplier = () -> 42;
        
        UnifyResult<Integer> result = UnifyResult.from(supplier);
        
        assertNotNull(result.getHeader());
        assertEquals(42, result.object);
    }

    @Test
    void testFromWithRunnable() {
        // Test from method with runnable
        final List<String> sideEffect = new ArrayList<>();
        Runnable runnable = () -> sideEffect.add("executed");
        
        UnifyResult<Void> result = UnifyResult.from(runnable);
        
        assertNotNull(result.getHeader());
        assertNull(result.object);
        assertEquals(1, sideEffect.size());
        assertEquals("executed", sideEffect.get(0));
    }

    @Test
    void testToUnifyResult() {
        // Test toUnifyResult static method
        String data = "test data";
        
        UnifyResult<String> result = UnifyResult.toUnifyResult(data);
        
        assertNotNull(result.getHeader());
        assertEquals(data, result.object);
    }

    @Test
    void testToUnifyResultWithNull() {
        // Test toUnifyResult with null data
        UnifyResult<Object> result = UnifyResult.toUnifyResult(null);
        
        assertNotNull(result.getHeader());
        assertNull(result.object);
    }

    @Test
    void testToUnifyResultWithCollection() {
        // Test toUnifyResult with collection data
        List<String> data = Arrays.asList("a", "b", "c");
        
        UnifyResult<List<String>> result = UnifyResult.toUnifyResult(data);
        
        assertNotNull(result.getHeader());
        assertNull(result.object);
        assertEquals(data, result.list);
    }

    @Test
    void testSetHeader() {
        // Test setHeader method
        Header originalHeader = new Header();
        Header newHeader = new Header();
        
        UnifyResult<String> result = new UnifyResult<>(originalHeader, "test");
        result.setHeader(newHeader);
        
        assertEquals(newHeader, result.getHeader());
    }

    @Test
    void testMapTypeFilter() {
        // Test MapTypeFilter inner class
        UnifyResult.MapTypeFilter filter = new UnifyResult.MapTypeFilter();
        
        // Test with Map
        Map<String, Object> map = new HashMap<>();
        assertTrue(filter.equals(map));
        
        // Test with non-Map
        String notMap = "not a map";
        assertFalse(filter.equals(notMap));
        
        // Test with null
        assertFalse(filter.equals(null));
        
        // Test with different Map implementation
        TreeMap<String, Object> treeMap = new TreeMap<>();
        assertTrue(filter.equals(treeMap));
    }

    @Test
    void testComplexMapScenario() {
        // Test complex scenario with nested maps
        Map<String, Object> innerMap = new HashMap<>();
        innerMap.put("nested", "value");
        
        Map<String, Object> outerMap = new HashMap<>();
        outerMap.put("inner", innerMap);
        outerMap.put("simple", "test");
        
        Header header = new Header();
        UnifyResult<Map<String, Object>> result = new UnifyResult<>(header, outerMap);
        
        Map<?, ?> retrievedMap = result.getMap();
        assertNotNull(retrievedMap);
        assertEquals(outerMap, retrievedMap);
        
        @SuppressWarnings("unchecked")
        Map<String, Object> retrievedInner = (Map<String, Object>) retrievedMap.get("inner");
        assertNotNull(retrievedInner);
        assertEquals("value", retrievedInner.get("nested"));
    }

    @Test
    void testWithCustomObjects() {
        // Test with custom objects
        TestData testData = new TestData("test", 123);
        Header header = new Header();
        
        UnifyResult<TestData> result = new UnifyResult<>(header, testData);
        
        assertEquals(header, result.getHeader());
        assertEquals(testData, result.object);
        assertNull(result.getMap()); // Should be null since TestData is not a Map
    }

    @Test
    void testWithEmptyCollection() {
        // Test with empty collection
        List<String> emptyList = new ArrayList<>();
        Header header = new Header();
        
        UnifyResult<List<String>> result = new UnifyResult<>(header, emptyList);
        
        assertEquals(header, result.getHeader());
        assertNull(result.object);
        assertEquals(emptyList, result.list);
        assertTrue(result.getList().isEmpty());
    }

    @Test
    void testWithSet() {
        // Test with Set collection
        Set<String> set = new HashSet<>(Arrays.asList("a", "b", "c"));
        Header header = new Header();
        
        UnifyResult<Set<String>> result = new UnifyResult<>(header, set);
        
        assertEquals(header, result.getHeader());
        assertNull(result.object);
        assertEquals(set, result.list);
        assertEquals(3, result.getList().size());
    }

    @Test
    void testSupplierExceptionHandling() {
        // Test supplier that throws exception
        Supplier<String> faultySupplier = () -> {
            throw new RuntimeException("Supplier failed");
        };
        
        assertThrows(RuntimeException.class, () -> {
            UnifyResult.from(faultySupplier);
        });
    }

    @Test
    void testRunnableExceptionHandling() {
        // Test runnable that throws exception
        Runnable faultyRunnable = () -> {
            throw new RuntimeException("Runnable failed");
        };
        
        assertThrows(RuntimeException.class, () -> {
            UnifyResult.from(faultyRunnable);
        });
    }

    @Test
    void testChainedOperations() {
        // Test chained operations
        String originalData = "original";
        
        UnifyResult<String> result1 = UnifyResult.toUnifyResult(originalData);
        UnifyResult<String> result2 = UnifyResult.from(() -> result1.object + " modified");
        
        assertEquals("original", result1.object);
        assertEquals("original modified", result2.object);
    }

    @Test
    void testWithComplexGenericTypes() {
        // Test with complex generic types
        Map<String, List<Integer>> complexData = new HashMap<>();
        complexData.put("numbers", Arrays.asList(1, 2, 3, 4, 5));
        complexData.put("moreNumbers", Arrays.asList(6, 7, 8, 9, 10));
        
        UnifyResult<Map<String, List<Integer>>> result = UnifyResult.toUnifyResult(complexData);
        
        assertEquals(complexData, result.object);
        Map<?, ?> retrievedMap = result.getMap();
        assertEquals(complexData, retrievedMap);
    }

    // Helper class for testing
    private static class TestData {
        private final String name;
        private final int value;
        
        public TestData(String name, int value) {
            this.name = name;
            this.value = value;
        }
        
        @Override
        public boolean equals(Object obj) {
            if (this == obj) return true;
            if (obj == null || getClass() != obj.getClass()) return false;
            TestData testData = (TestData) obj;
            return value == testData.value && Objects.equals(name, testData.name);
        }
        
        @Override
        public int hashCode() {
            return Objects.hash(name, value);
        }
    }
}
