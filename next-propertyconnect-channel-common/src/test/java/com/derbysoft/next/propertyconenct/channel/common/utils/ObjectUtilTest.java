package com.derbysoft.next.propertyconenct.channel.common.utils;


import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;


class ObjectUtilTest {

    public static class Stu{
        public Stu() {
        }

        private String name;

        public String otherMethod(){
            return "test";
        }
        public String getOtherNullMethod(){
            return null;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }

    @Test
    void testGetReference() {
        var stu = new Stu();

        var reference = ObjectUtil.getReference(stu, Stu::getName, () -> "123");

        assertEquals("123", reference);
    }

    @Test
    void testGetReference2() {
        var stu = new Stu();

        stu.setName("456");

        var reference = ObjectUtil.getReference(stu, Stu::getName, () -> "123");

        assertEquals("456", reference);
    }

    @Test
    void testGetReference3() {
        var stu = new Stu();
        stu.setName("456");
        var reference = ObjectUtil.getReference(stu, Stu::otherMethod, () -> "123");
        assertEquals("test", reference);
    }
}
