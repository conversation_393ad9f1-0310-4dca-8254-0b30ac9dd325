package com.derbysoft.next.propertyconenct.channel.common.utils;

import org.junit.jupiter.api.Test;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Comprehensive unit tests for JSONUtil
 */
class JSONUtilTest {

    @Test
    void testGetCollectionWithDefaultClass() {
        // Test with Map containing array
        Map<String, Object> testMap = new HashMap<>();
        testMap.put("items", Arrays.asList("item1", "item2", "item3"));

        Collection<Object> result = JSONUtil.getCollection(testMap, "$.items");

        assertNotNull(result);
        assertEquals(3, result.size());
        assertTrue(result.contains("item1"));
        assertTrue(result.contains("item2"));
        assertTrue(result.contains("item3"));
    }

    @Test
    void testGetCollectionWithSpecificClass() {
        // Test with Map containing array of strings
        Map<String, Object> testMap = new HashMap<>();
        testMap.put("names", Arrays.asList("<PERSON>", "<PERSON>", "<PERSON>"));

        Collection<String> result = JSONUtil.getCollection(testMap, "$.names", String.class);

        assertNotNull(result);
        assertEquals(3, result.size());
        assertTrue(result.contains("Alice"));
        assertTrue(result.contains("Bob"));
        assertTrue(result.contains("Charlie"));
    }

    @Test
    void testGetCollectionWithNumbers() {
        // Test with Map containing array of numbers
        Map<String, Object> testMap = new HashMap<>();
        testMap.put("numbers", Arrays.asList(1, 2, 3, 4, 5));

        Collection<Integer> result = JSONUtil.getCollection(testMap, "$.numbers", Integer.class);

        assertNotNull(result);
        assertEquals(5, result.size());
        assertTrue(result.contains(1));
        assertTrue(result.contains(5));
    }

    @Test
    void testGetCollectionWithNullValue() {
        // Test with null value
        Map<String, Object> testMap = new HashMap<>();
        testMap.put("items", null);

        Collection<Object> result = JSONUtil.getCollection(testMap, "$.items");

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testGetCollectionWithEmptyArray() {
        // Test with empty array
        Map<String, Object> testMap = new HashMap<>();
        testMap.put("items", Arrays.asList());

        Collection<Object> result = JSONUtil.getCollection(testMap, "$.items");

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testGetCollectionWithNonExistentPath() {
        // Test with non-existent path
        Map<String, Object> testMap = new HashMap<>();
        testMap.put("items", Arrays.asList("item1", "item2"));

        Collection<Object> result = JSONUtil.getCollection(testMap, "$.nonexistent");

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testGetCollectionWithNonArrayValue() {
        // Test with non-array value
        Map<String, Object> testMap = new HashMap<>();
        testMap.put("singleValue", "not an array");

        Collection<Object> result = JSONUtil.getCollection(testMap, "$.singleValue");

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testGetCollectionWithNestedPath() {
        // Test with nested JSON path
        Map<String, Object> innerMap = new HashMap<>();
        innerMap.put("values", Arrays.asList("nested1", "nested2"));
        
        Map<String, Object> testMap = new HashMap<>();
        testMap.put("data", innerMap);

        Collection<String> result = JSONUtil.getCollection(testMap, "$.data.values", String.class);

        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.contains("nested1"));
        assertTrue(result.contains("nested2"));
    }

    @Test
    void testGetObjectWithSpecificClass() {
        // Test getObject with specific class
        Map<String, Object> testMap = new HashMap<>();
        testMap.put("name", "John Doe");

        String result = JSONUtil.getObject(testMap, "$.name", String.class);

        assertEquals("John Doe", result);
    }

    @Test
    void testGetObjectWithNumber() {
        // Test getObject with number
        Map<String, Object> testMap = new HashMap<>();
        testMap.put("age", 30);

        Integer result = JSONUtil.getObject(testMap, "$.age", Integer.class);

        assertEquals(30, result);
    }

    @Test
    void testGetObjectWithNullValue() {
        // Test getObject with null value
        Map<String, Object> testMap = new HashMap<>();
        testMap.put("value", null);

        String result = JSONUtil.getObject(testMap, "$.value", String.class);

        assertNull(result);
    }

    @Test
    void testGetObjectWithNonExistentPath() {
        // Test getObject with non-existent path
        Map<String, Object> testMap = new HashMap<>();
        testMap.put("existing", "value");

        String result = JSONUtil.getObject(testMap, "$.nonexistent", String.class);

        assertNull(result);
    }

    @Test
    void testGetObjectWithDefaultClass() {
        // Test getObject with default Object class
        Map<String, Object> testMap = new HashMap<>();
        testMap.put("data", "some value");

        Object result = JSONUtil.getObject(testMap, "$.data");

        assertEquals("some value", result);
    }

    @Test
    void testGetObjectWithComplexObject() {
        // Test getObject with complex nested object
        Map<String, Object> address = new HashMap<>();
        address.put("street", "123 Main St");
        address.put("city", "Anytown");
        
        Map<String, Object> person = new HashMap<>();
        person.put("name", "Jane Doe");
        person.put("address", address);
        
        Map<String, Object> testMap = new HashMap<>();
        testMap.put("person", person);

        @SuppressWarnings("unchecked")
        Map<String, Object> result = JSONUtil.getObject(testMap, "$.person.address", Map.class);

        assertNotNull(result);
        assertEquals("123 Main St", result.get("street"));
        assertEquals("Anytown", result.get("city"));
    }

    @Test
    void testGetMapWithKeyAndValueClasses() {
        // Test getMap with specific key and value classes
        Map<String, Object> innerMap = new HashMap<>();
        innerMap.put("key1", "value1");
        innerMap.put("key2", "value2");
        
        Map<String, Object> testMap = new HashMap<>();
        testMap.put("config", innerMap);

        Map<String, Object> result = JSONUtil.getMap(testMap, "$.config", String.class, Object.class);

        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("value1", result.get("key1"));
        assertEquals("value2", result.get("key2"));
    }

    @Test
    void testGetMapWithKeyClassOnly() {
        // Test getMap with only key class specified
        Map<String, Object> innerMap = new HashMap<>();
        innerMap.put("setting1", 100);
        innerMap.put("setting2", "text");
        
        Map<String, Object> testMap = new HashMap<>();
        testMap.put("settings", innerMap);

        Map<String, Object> result = JSONUtil.getMap(testMap, "$.settings", String.class);

        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(100, result.get("setting1"));
        assertEquals("text", result.get("setting2"));
    }

    @Test
    void testGetMapWithNullValue() {
        // Test getMap with null value
        Map<String, Object> testMap = new HashMap<>();
        testMap.put("config", null);

        Map<String, Object> result = JSONUtil.getMap(testMap, "$.config", String.class);

        assertNull(result);
    }

    @Test
    void testGetMapWithNonExistentPath() {
        // Test getMap with non-existent path
        Map<String, Object> testMap = new HashMap<>();
        testMap.put("existing", new HashMap<>());

        Map<String, Object> result = JSONUtil.getMap(testMap, "$.nonexistent", String.class);

        assertNull(result);
    }

    @Test
    void testGetStringMethod() {
        // Test getString method
        Map<String, Object> testMap = new HashMap<>();
        testMap.put("message", "Hello World");

        String result = JSONUtil.getString(testMap, "$.message");

        assertEquals("Hello World", result);
    }

    @Test
    void testGetStringWithNullValue() {
        // Test getString with null value
        Map<String, Object> testMap = new HashMap<>();
        testMap.put("message", null);

        String result = JSONUtil.getString(testMap, "$.message");

        assertNull(result);
    }

    @Test
    void testGetStringWithNonStringValue() {
        // Test getString with non-string value (should convert)
        Map<String, Object> testMap = new HashMap<>();
        testMap.put("number", 42);

        String result = JSONUtil.getString(testMap, "$.number");

        assertEquals("42", result);
    }

    @Test
    void testGetIntegerMethod() {
        // Test getInteger method
        Map<String, Object> testMap = new HashMap<>();
        testMap.put("count", 25);

        Integer result = JSONUtil.getInteger(testMap, "$.count");

        assertEquals(25, result);
    }

    @Test
    void testGetIntegerWithStringValue() {
        // Test getInteger with string value (should convert)
        Map<String, Object> testMap = new HashMap<>();
        testMap.put("count", "30");

        Integer result = JSONUtil.getInteger(testMap, "$.count");

        assertEquals(30, result);
    }

    @Test
    void testGetIntegerWithNullValue() {
        // Test getInteger with null value
        Map<String, Object> testMap = new HashMap<>();
        testMap.put("count", null);

        Integer result = JSONUtil.getInteger(testMap, "$.count");

        assertNull(result);
    }

    @Test
    void testGetBooleanMethod() {
        // Test getBoolean method
        Map<String, Object> testMap = new HashMap<>();
        testMap.put("active", true);

        Boolean result = JSONUtil.getBoolean(testMap, "$.active");

        assertTrue(result);
    }

    @Test
    void testGetBooleanWithStringValue() {
        // Test getBoolean with string value
        Map<String, Object> testMap = new HashMap<>();
        testMap.put("active", "false");

        Boolean result = JSONUtil.getBoolean(testMap, "$.active");

        assertFalse(result);
    }

    @Test
    void testGetBooleanWithNullValue() {
        // Test getBoolean with null value
        Map<String, Object> testMap = new HashMap<>();
        testMap.put("active", null);

        Boolean result = JSONUtil.getBoolean(testMap, "$.active");

        assertNull(result);
    }

    @Test
    void testComplexNestedStructure() {
        // Test with complex nested structure
        Map<String, Object> product = new HashMap<>();
        product.put("id", 1);
        product.put("name", "Laptop");
        product.put("price", 999.99);
        product.put("available", true);
        product.put("tags", Arrays.asList("electronics", "computer", "portable"));
        
        Map<String, Object> category = new HashMap<>();
        category.put("name", "Electronics");
        category.put("products", Arrays.asList(product));
        
        Map<String, Object> store = new HashMap<>();
        store.put("name", "Tech Store");
        store.put("categories", Arrays.asList(category));

        // Test getting nested string
        String storeName = JSONUtil.getString(store, "$.name");
        assertEquals("Tech Store", storeName);

        // Test getting nested collection
        Collection<Object> categories = JSONUtil.getCollection(store, "$.categories");
        assertEquals(1, categories.size());

        // Test getting deeply nested value
        String productName = JSONUtil.getString(store, "$.categories[0].products[0].name");
        assertEquals("Laptop", productName);

        // Test getting nested array
        Collection<String> tags = JSONUtil.getCollection(store, "$.categories[0].products[0].tags", String.class);
        assertEquals(3, tags.size());
        assertTrue(tags.contains("electronics"));
        assertTrue(tags.contains("computer"));
        assertTrue(tags.contains("portable"));
    }

    @Test
    void testEdgeCasesWithEmptyObjects() {
        // Test with empty map
        Map<String, Object> emptyMap = new HashMap<>();
        
        Collection<Object> result1 = JSONUtil.getCollection(emptyMap, "$.anything");
        assertTrue(result1.isEmpty());
        
        String result2 = JSONUtil.getString(emptyMap, "$.anything");
        assertNull(result2);
        
        Object result3 = JSONUtil.getObject(emptyMap, "$.anything");
        assertNull(result3);
    }

    @Test
    void testWithNullInput() {
        // Test with null input object - JSONUtil throws NPE with null input
        assertThrows(NullPointerException.class, () -> {
            JSONUtil.getCollection(null, "$.anything");
        });

        assertThrows(NullPointerException.class, () -> {
            JSONUtil.getString(null, "$.anything");
        });

        assertThrows(NullPointerException.class, () -> {
            JSONUtil.getObject(null, "$.anything");
        });
    }
}
