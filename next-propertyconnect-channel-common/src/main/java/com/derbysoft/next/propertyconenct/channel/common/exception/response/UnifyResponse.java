package com.derbysoft.next.propertyconenct.channel.common.exception.response;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.MultiValueMap;

import java.util.function.Supplier;

/**
 * @Created by <AUTHOR> on 2023/3/20
 */

public class UnifyResponse<T> extends ResponseEntity<UnifyResult<T>> {

    private UnifyResponse(UnifyResult<T> body, MultiValueMap<String, String> headers, HttpStatus status) {
        super(body, headers, status);
    }

    public static <R> UnifyResponse<R> from(Header header, HttpStatus httpStatus, Supplier<R> func) {
        return new UnifyResponse<>(UnifyResult.from(header, func), header.getHttpHeaders(), httpStatus);
    }

    public static <R> UnifyResponse<R> from(Header header, Supplier<R> func) {
        return from(header, HttpStatus.OK, func);
    }

    public static <R> UnifyResponse<R> from(Supplier<R> func) {
        return from(new Header(), func);
    }

    public static UnifyResponse<?> from(Runnable func) {
        return from(new Header(), () -> {
            func.run();
            return null;
        });
    }

    public static <R> UnifyResponse<R> toUnifyResponse(R data) {
        return from(() -> data);
    }
}
