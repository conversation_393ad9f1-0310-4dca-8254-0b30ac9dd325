package com.derbysoft.next.propertyconenct.channel.common.mapstruct;

import com.derbysoft.next.propertyconenct.channel.common.utils.GeneratedMapper;
import org.mapstruct.AnnotateWith;
import org.mapstruct.Mapper;

import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Created by <AUTHOR> on 2023/6/7
 */

@Mapper
@AnnotateWith(GeneratedMapper.class)
public interface CommMapper {
    default Map<String, String> objectMapToStringMap(Map<String, Object> map) {
        if (null == map) {
            return null;
        }
        return map.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, e -> String.valueOf(e.getValue())));
    }
}
