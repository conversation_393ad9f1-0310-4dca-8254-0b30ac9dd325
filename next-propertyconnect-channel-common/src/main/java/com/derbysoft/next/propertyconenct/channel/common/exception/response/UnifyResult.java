package com.derbysoft.next.propertyconenct.channel.common.exception.response;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonUnwrapped;

import java.util.Collection;
import java.util.Map;
import java.util.function.Supplier;

/**
 * @Created by <AUTHOR> on 2023/3/20
 */


public class UnifyResult<T> {
    @JsonInclude(JsonInclude.Include.NON_NULL)
    Header header;

    @JsonInclude(value = JsonInclude.Include.CUSTOM, valueFilter = MapTypeFilter.class)
    @JsonUnwrapped
    T object = null;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    T list = null;

    public static class MapTypeFilter {
        @Override
        public boolean equals(Object obj) {
            return obj instanceof Map;
        }
    }

    @JsonAnyGetter
    public Map<?, ?> getMap() {
        if (object instanceof Map) {
            return (Map<?, ?>) object;
        }
        return null;
    }

    protected UnifyResult(Header header, T payload) {
        this.header = header;
        if (payload instanceof Collection) {
            this.list = payload;
        } else {
            this.object = payload;
        }
    }

    public Header getHeader() {
        return this.header;
    }

    public void setHeader(Header header) {
        this.header = header;
    }


    public T getList() {
        return this.list;
    }

    public T getObject() {
        return this.object;
    }

    public static <B> UnifyResult<B> from(Header header, Supplier<B> func) {
        return new UnifyResult<>(header, func.get());
    }

    public static <B> UnifyResult<B> from(Supplier<B> func) {
        return from(new Header(), func);
    }

    public static UnifyResult<Void> from(Runnable func) {
        return from(new Header(), () -> {
            func.run();
            return null;
        });
    }

    public static <B> UnifyResult<B> toUnifyResult(B data) {
        return from(() -> data);
    }

}
