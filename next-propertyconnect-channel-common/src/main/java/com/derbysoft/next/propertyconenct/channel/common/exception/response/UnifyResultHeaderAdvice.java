package com.derbysoft.next.propertyconenct.channel.common.exception.response;

import org.springframework.core.MethodParameter;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

@ControllerAdvice
public class UnifyResultHeaderAdvice implements ResponseBodyAdvice<Object> {
    public static final String ENABLE_ERASE_HEADER = "enable-erase-header";

    @Override
    public boolean supports(MethodParameter returnType, Class<? extends HttpMessageConverter<?>> converterType) {
        return UnifyResult.class.isAssignableFrom(returnType.getParameterType()) || ResponseEntity.class.isAssignableFrom(returnType.getParameterType());
    }

    @Override
    public Object beforeBodyWrite(Object body, MethodParameter returnType, MediaType selectedContentType,
                                  Class<? extends HttpMessageConverter<?>> selectedConverterType,
                                  ServerHttpRequest request, ServerHttpResponse response) {
        if (body instanceof UnifyResult) {
            UnifyResult<?> unifyResponse = (UnifyResult<?>) body;
            if (null != unifyResponse.getHeader()){
                HttpHeaders headers = unifyResponse.getHeader().getHttpHeaders();
                headers.forEach(response.getHeaders()::addAll);
                if (request.getHeaders().containsKey(ENABLE_ERASE_HEADER) && request.getHeaders().get(ENABLE_ERASE_HEADER).contains("true")) {
                    unifyResponse.setHeader(null);
                }
            }
            return body;
        }
        return body;
    }
}
