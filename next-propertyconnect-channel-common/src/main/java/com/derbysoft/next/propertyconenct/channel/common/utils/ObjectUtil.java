package com.derbysoft.next.propertyconenct.channel.common.utils;

import lombok.NonNull;
import lombok.experimental.UtilityClass;
import org.apache.commons.lang3.reflect.MethodUtils;
import org.springframework.cglib.proxy.Enhancer;
import org.springframework.cglib.proxy.MethodInterceptor;
import org.springframework.cglib.proxy.MethodProxy;
import org.springframework.util.StringUtils;

import java.lang.reflect.Method;
import java.util.function.Function;
import java.util.function.Supplier;

/**
 * @Created by <AUTHOR> on 2023/10/30
 */

@UtilityClass
public class ObjectUtil {

    @SuppressWarnings("unchecked")
    public static <S, T> T getReference(@NonNull S superTarget, @NonNull Function<S, T> getterTarget, @NonNull Supplier<? extends T> orElse){
        S proxyTarget = (S) Enhancer.create(superTarget.getClass(),
                (MethodInterceptor) (Object proxy, Method method, Object[] args, MethodProxy methodProxy) -> {
                    if (!method.getName().startsWith("get")) {
                        return methodProxy.invokeSuper(proxy, args);
                    }
                    var methodResult = methodProxy.invoke(superTarget, args);
                    if (null != methodResult) {
                        return methodResult;
                    }
                    String setMethodName = StringUtils.uncapitalize(method.getName().replace("get", "set"));
                    MethodUtils.invokeMethod(superTarget, setMethodName, orElse.get());
                    return methodProxy.invoke(superTarget, args);
                });

        return getterTarget.apply(proxyTarget);
    }
}
