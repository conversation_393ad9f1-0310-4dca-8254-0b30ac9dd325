package com.derbysoft.next.propertyconenct.channel.common.exception.response;

import com.derbysoft.next.propertyconenct.channel.common.exception.BusinessAuthorizeException;
import com.derbysoft.next.propertyconenct.channel.common.exception.BusinessException;
import com.derbysoft.next.propertyconenct.channel.common.exception.ErrorCode;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.ConstraintViolationException;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.validator.internal.engine.path.PathImpl;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.util.StreamUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.BindException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.concurrent.RejectedExecutionException;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RestControllerAdvice
@Slf4j
public class ResponseExceptionHandler {

    public ResponseEntity<ErrorResult> handleArgumentException(String message) {
        return new ResponseEntity<>(new ErrorResult(ErrorCode.InvalidField.name(), message), HttpStatus.BAD_REQUEST);
    }

    public ResponseEntity<ErrorResult> handleArgumentException(List<ErrorDetail> errorDetails) {
        return new ResponseEntity<>(new ErrorResult(ErrorCode.InvalidField.name(), null, errorDetails), HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(value = {IllegalArgumentException.class, BindException.class})
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResponseEntity<ErrorResult> handleArgumentException(Throwable exception) {
        log.error(exception.getMessage(), exception);
        return this.handleArgumentException(exception.getMessage());
    }

    @ExceptionHandler(value = MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResponseEntity<ErrorResult> handleArgumentException(MethodArgumentNotValidException exception) {
        var errorMessages = exception.getBindingResult()
                .getFieldErrors()
                .stream()
                .map(fieldError -> new ErrorDetail(null, null, fieldError.getField() + ": " + fieldError.getDefaultMessage()))
                .collect(Collectors.toList());
        return this.handleArgumentException(errorMessages);
    }

    @ExceptionHandler(value = ConstraintViolationException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResponseEntity<ErrorResult> handleArgumentException(ConstraintViolationException exception) {
        return this.handleArgumentException(exception.getConstraintViolations().stream()
                .map(constraintViolation -> new ErrorDetail(((PathImpl) constraintViolation.getPropertyPath()).getLeafNode().getName(), null, constraintViolation.getMessage()))
                .collect(Collectors.toList())
        );
    }

    @ExceptionHandler(value = MethodArgumentTypeMismatchException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResponseEntity<ErrorResult> handleArgumentException(MethodArgumentTypeMismatchException exception) {
        return this.handleArgumentException(exception.getName() + ": " + exception.getValue());
    }

    @ExceptionHandler(value = {RejectedExecutionException.class})
    @ResponseStatus(HttpStatus.TOO_MANY_REQUESTS)
    public ResponseEntity<ErrorResult> handleServiceException(RejectedExecutionException exception) {
        log.error(exception.getMessage(), exception);
        ErrorResult errorResult = new ErrorResult(ErrorCode.System.name(), exception.getMessage());
        return new ResponseEntity<>(errorResult, HttpStatus.TOO_MANY_REQUESTS);
    }

    @ExceptionHandler(value = {UnsupportedOperationException.class})
    @ResponseStatus(HttpStatus.NOT_IMPLEMENTED)
    public ResponseEntity<ErrorResult> handleUnsupportedOperationException(UnsupportedOperationException exception) {
        log.error(exception.getMessage(), exception);
        ErrorResult errorResult = new ErrorResult(ErrorCode.NotImplemented.name(), ErrorCode.NotImplemented.getErrorMessage());
        return new ResponseEntity<>(errorResult, HttpStatus.NOT_IMPLEMENTED);
    }

    @ExceptionHandler(value = {RequestDataNotFoundException.class})
    @ResponseStatus(HttpStatus.NOT_FOUND)
    public ResponseEntity<ErrorResult> handleRequestDataNotFoundException(RequestDataNotFoundException exception) {
        log.error(exception.getMessage(), exception);
        ErrorResult errorResult = new ErrorResult(exception.getErrorCode(), exception.getMessage());
        return new ResponseEntity<>(errorResult, HttpStatus.NOT_FOUND);
    }

    @ExceptionHandler(value = {BusinessAuthorizeException.class})
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ResponseEntity<ErrorResult> handleBusinessAuthException(BusinessAuthorizeException exception) {
        log.error(exception.getMessage(), exception);
        ErrorResult errorResult = new ErrorResult("BusinessAuthorizeException", exception.getMessage());
        return new ResponseEntity<>(errorResult, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @ExceptionHandler(value = {BusinessException.class})
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ResponseEntity<ErrorResult> handleBusinessException(BusinessException exception) {
        log.error(exception.getMessage(), exception);
        ErrorResult errorResult = new ErrorResult(ErrorCode.BusinessFailed.name(), exception.getMessage());
        return new ResponseEntity<>(errorResult, HttpStatus.INTERNAL_SERVER_ERROR);
    }


    @ExceptionHandler(value = {RuntimeException.class})
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ResponseEntity<ErrorResult> globalUnifyExceptionHandler(RuntimeException exception) {
        log.error(exception.getMessage(), exception);
        ErrorResult errorResult = new ErrorResult(ErrorCode.System.name(), exception.getMessage());
        return new ResponseEntity<>(errorResult, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @ExceptionHandler(value = {HttpMessageNotReadableException.class})
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ResponseEntity<ErrorResult> HttpMessageNotReadableExceptionHandler(HttpMessageNotReadableException exception, HttpServletRequest request) {
        log.error(exception.getMessage(), exception);
        ErrorResult errorResult = null;
        var req = getRawRequestBody(request);
        log.error("Error parse request body from {}: {}", request.getRemoteHost(), req);
        errorResult = new ErrorResult(ErrorCode.System.name(), StringUtils.hasText(req) ? req : exception.getMessage());
        return new ResponseEntity<>(errorResult, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    private String getRawRequestBody(HttpServletRequest request) {
        try {
            return StreamUtils.copyToString(request.getInputStream(), StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new RuntimeException("Failed to retrieve raw request body", e);
        }
    }

}
