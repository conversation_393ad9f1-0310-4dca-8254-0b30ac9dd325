package com.derbysoft.next.propertyconenct.channel.common.exception.response;

import java.util.Objects;

public class ErrorDetail {
    private String target;
    private String errorCode;
    private String errorMessage;

    public ErrorDetail() {
    }

    public ErrorDetail(String target, String errorCode, String errorMessage) {
        this.target = target;
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
    }

    public String getTarget() {
        return this.target;
    }

    public void setTarget(String target) {
        this.target = target;
    }

    public String getErrorCode() {
        return this.errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorMessage() {
        return this.errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public boolean equals(Object o) {
        if (this == o) {
            return true;
        } else if (o != null && this.getClass() == o.getClass()) {
            ErrorDetail that = (ErrorDetail)o;
            return Objects.equals(this.target, that.target) && Objects.equals(this.errorCode, that.errorCode) && Objects.equals(this.errorMessage, that.errorMessage);
        } else {
            return false;
        }
    }

    public int hashCode() {
        return Objects.hash(new Object[]{this.target, this.errorCode, this.errorMessage});
    }

    public String toString() {
        return "ErrorDetail{target='" + this.target + "', errorCode='" + this.errorCode + "', errorMessage='" + this.errorMessage + "'}";
    }
}
