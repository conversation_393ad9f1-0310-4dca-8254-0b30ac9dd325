package com.derbysoft.next.propertyconenct.channel.common.exception;

public enum ErrorCode {
    System("Internal server error"),
    SystemRaw("%s"),
    Unknown("%s"),
    ServiceInvocationFailed("Invoke service failed by [%1$s]."),
    ServiceInvocationFailedDetail("Invoke service[%1$s.%2$s] failed by [%3$s]."),
    InvalidField("Invalid [%1$s] value [%2$s]."),
    MissingField("Missing [%1$s]."),
    DuplicateField("Duplicate [%1$s]."),
    AuthenticationFailed("Incorrect authentication credentials."),
    PermissionDenied("You don't have permission to perform action[%1$s]."),
    NotAuthenticated("Authentication credentials were not provided."),
    NotFound("Not found [%1$s]."),
    NotImplemented("Operation temporarily unavailable"),
    BusinessFailed("[%s]: %s"),
    PartnerFailed(null);

    private String errorMessage;

    private ErrorCode(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public String getErrorMessage(Object... args) {
        return String.format(this.errorMessage, args);
    }
}
