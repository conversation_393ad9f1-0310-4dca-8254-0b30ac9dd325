package com.derbysoft.next.propertyconenct.channel.common.exception.response;

import com.derbysoft.next.propertyconenct.channel.common.utils.Constants;
import com.derbysoft.next.propertyconenct.channel.common.utils.DateTimeUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import org.slf4j.MDC;
import org.springframework.http.HttpHeaders;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.UUID;

public class Header {
    private final HttpHeaders httpHeaders = new HttpHeaders();

    protected enum HeaderKey {
        EchoToken("Echo-Token", null),
        Timestamp("Timestamp", null),
        Version("Version", Constants.Common.HEADER_VERSION);

        private final String key;
        private final String defaultVal;
        HeaderKey(String key, String defaultVal) {
            this.key = key;
            this.defaultVal = defaultVal;
        }

        public String getKey() {
            return key;
        }
        public String getDefaultVal() {
            return defaultVal;
        }

    }

    public Header() {
        var logToken = MDC.get("LOG_TOKEN");
        this.httpHeaders.add(HeaderKey.EchoToken.getKey(), StringUtils.hasText(logToken) ? logToken : UUID.randomUUID().toString().replace("-", ""));
        this.httpHeaders.add(HeaderKey.Timestamp.getKey(), LocalDateTime.now().format(DateTimeUtil.ISO_LOCAL_DATE_TIME_WITH_MILLISECOND));
        this.httpHeaders.add(HeaderKey.Version.getKey(), HeaderKey.Version.getDefaultVal());
    }

    @JsonIgnore
    public HttpHeaders getHttpHeaders() {
        return this.httpHeaders;
    }
    @JsonProperty("Echo-Token")
    @Schema(example = "f5d2ce11e1954f76852b814d2a83b02a")
    public String getEchoToken() {
        return this.httpHeaders.getFirst(HeaderKey.EchoToken.getKey());
    }
    @JsonProperty("Timestamp")
    @Schema(example = "2000-01-01T01:01:01.001")
    public String getTimestamp() {
        return this.httpHeaders.getFirst(HeaderKey.Timestamp.getKey());
    }
    @JsonProperty("Version")
    @Schema(example = "1.0")
    public String getVersion() {
        return this.httpHeaders.getFirst(HeaderKey.Version.getKey());
    }

}
