package com.derbysoft.next.propertyconenct.channel.common.validation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import jakarta.validation.constraints.Pattern;

import java.lang.annotation.*;

/**
 * @Created by <AUTHOR> on 2023/8/1
 */


@Target({ElementType.FIELD, ElementType.ANNOTATION_TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Constraint(validatedBy = {})
@Pattern(regexp = "^-?((180(\\.0{1,15})?)|((1[0-7]?\\d|\\d{1,2})(\\.\\d{1,15})?))$", message = "Invalid position format")
public @interface PositionFormat {
    String message() default "";
    Class<?>[] groups() default {};
    Class<? extends Payload>[] payload() default {};
}
