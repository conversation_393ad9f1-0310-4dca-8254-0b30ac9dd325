package com.derbysoft.next.propertyconenct.channel.common.utils;

/**
 * <AUTHOR>
 */
public interface Constants {

    String STR_SUCCESSFUL = "Successful";
    String UNKNOWN = "Unknown";

    interface Perf {
        String ECHO_TOKEN_DERBY = "echo_token_derby";
        String CONSUME_TASK = "ConsumeTask";
        String CREATE_TASKS = "CreateTasks";
        String CHANGE_PROFILE = "ChangeHotelInfo";
        String TRIGGER_ARI_REFRESH = "TriggerARIRefresh";

        String IGNORE = "Ignore";

        String EXPORT_SUB_MAPPING = "ExportSubMapping";

        String UPDATE_EVENT = "UpdateEvent";

        String ACCURACY_REPORT = "AccuracyReport";
        String REPORT_SAVED = "report_saved";
        String CHANGE_MSG = "change_msg";

        String MISMATCH_RATE = "mismatch_rate";

        String TASK_NAME = "task_name";
        String TASK_TYPE = "task_type";

        String SAVE_DISTRIBUTOR_HOTEL = "SaveDistributorHotel";
        String DELETE = "Delete";
        String HOTEL_SYSTEM_CONNECTION = "endpoint_id";
        String PROCESS_TYPE = "process_type";

        String SUB_PROCESS = "sub_process";
        String HOTEL_CHANNEL = "hotel_channel";
        String CHANNEL = "channel";
        String HOTEL_SUPPLIER = "hotel_supplier";
        String EXT_PARAM = "ext_param";

        String PRODUCT_CNT = "product_cnt";
        String PRODUCT_CODE = "product_code";

        String ERR_CODE = "err_code";
        String ERR_MSG = "err_msg";

        String HOTEL = "hotel";
        String START_DATE = "start_date";
        String END_DATE = "end_date";

        String SUPPLIER = "supplier";

        String MESSAGE_TYPE = "message_type";

        String ROOMTYPE_CHANNEL = "room_type_channel";
        String RATEPLAN_CHANNEL = "rate_plan_channel";

        String MESSAGE_CNT = "message_cnt";
        String MESSAGE_FAIL_CNT = "message_fail_cnt";
    }

    interface Stream {
        String CHANNEL_REQUEST = "ChannelRequest";
    }

    interface PerfField {

    }

    interface Common {
        String HEADER_VERSION = "1.0";
        String SERVICE = "-service";
    }

    interface Client {
        String BOOKINGCOM = "BOOKINGCOM-client";
    }

    interface ContentType {
        String TEXT_XML = "text/xml; charset=utf-8";
    }

    interface PatternType {
        String HEADER_DATETIME_PATTERN = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'";
    }

    interface Channel {
        String BOOKINGCOM = "BOOKINGCOM";
    }

}
