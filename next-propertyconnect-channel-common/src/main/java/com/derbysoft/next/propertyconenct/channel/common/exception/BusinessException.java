package com.derbysoft.next.propertyconenct.channel.common.exception;

/**
 * @Created by <AUTHOR> on 1/25/2024
 */

public class BusinessException extends RuntimeException {
    private String errorCode;

    public BusinessException(String errorCode, String message) {
        super(String.format("[%s] %s", errorCode, message));
        this.errorCode = errorCode;
    }

    public BusinessException(String message) {
        super(message);
    }

    public String getErrorCode() {
        return errorCode;
    }
}
