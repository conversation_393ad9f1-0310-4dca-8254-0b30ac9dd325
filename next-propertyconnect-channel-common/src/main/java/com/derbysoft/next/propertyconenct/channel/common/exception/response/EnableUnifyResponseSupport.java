package com.derbysoft.next.propertyconenct.channel.common.exception.response;

import org.springframework.context.annotation.Import;
import org.springframework.web.servlet.resource.ResourceHttpRequestHandler;

import java.lang.annotation.*;

/**
 * @Created by <AUTHOR> on 2023/3/28
 */

@Inherited
@Documented
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Import({ResponseExceptionHandler.class, ResourceHttpRequestHandler.class, UnifyResultHeaderAdvice.class, UnifyResultSpringDocSupport.class})
public @interface EnableUnifyResponseSupport {
}
