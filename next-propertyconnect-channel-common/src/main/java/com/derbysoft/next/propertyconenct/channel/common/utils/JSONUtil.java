package com.derbysoft.next.propertyconenct.channel.common.utils;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONPath;
import lombok.NonNull;
import lombok.experimental.UtilityClass;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * @Created by <AUTHOR> on 2023/3/1
 */

@UtilityClass
public class JSONUtil {

    @NonNull
    public static <T> Collection<Object> getCollection(T obj, String jsonPath) {
        return getCollection(obj, jsonPath, Object.class);
    }

    @SuppressWarnings("unchecked")
    @NonNull
    public static <T, R> Collection<R> getCollection(T obj, String jsonPath, Class<R> clazz) {
        var val = JSONPath.eval(obj, jsonPath);
        if (null == val || !Collection.class.isAssignableFrom(val.getClass()) || ((Collection<?>) val).isEmpty()) {
            return List.of();
        }
        if (clazz.isAssignableFrom(((Collection<?>) val).iterator().next().getClass())) {
            return (Collection<R>) val;
        }
        return JSONArray.parseArray(JSONObject.toJSONString(val), clazz);
    }


    @SuppressWarnings("unchecked")
    public static <T, R> R getObject(T obj, String jsonPath, Class<R> clazz) {
        var val = JSONPath.eval(obj, jsonPath);
        if (null == val) {
            return null;
        }
        if (clazz.isAssignableFrom(val.getClass())) {
            return (R) val;
        }
        return JSONObject.parseObject(JSONObject.toJSONString(val), clazz);
    }

    @SuppressWarnings("unchecked")
    public static <K, V> Map<K, V> getMap(Object obj, String jsonPath, Class<K> keyClazz, Class<V> valueClazz) {
        return getObject(obj, jsonPath, Map.class);
    }

    public static <K> Map<K, Object> getMap(Object obj, String jsonPath, Class<K> keyClazz) {
        return getMap(obj, jsonPath, keyClazz, Object.class);
    }

    public static Object getObject(Object obj, String jsonPath) {
        return getObject(obj, jsonPath, Object.class);
    }

    public static String getString(Object obj, String jsonPath) {
        return getObject(obj, jsonPath, String.class);
    }

    public static Double getDouble(Object obj, String jsonPath) {
        return getObject(obj, jsonPath, Double.class);
    }

    public static Boolean getBoolean(Object obj, String jsonPath) {
        return getObject(obj, jsonPath, Boolean.class);
    }

    public static Integer getInteger(Object obj, String jsonPath) {
        return getObject(obj, jsonPath, Integer.class);
    }

    public static Long getLong(Object obj, String jsonPath) {return getObject(obj, jsonPath, Long.class);}

    @SuppressWarnings("unchecked")
    public static <T> T set(T obj, String jsonPath, Object value) {
        return (T) JSONPath.set(obj, jsonPath, value);
    }
}
