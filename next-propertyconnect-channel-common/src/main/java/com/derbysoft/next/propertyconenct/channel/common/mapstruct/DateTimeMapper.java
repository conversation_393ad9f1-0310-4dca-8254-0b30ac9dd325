package com.derbysoft.next.propertyconenct.channel.common.mapstruct;

import com.derbysoft.next.propertyconenct.channel.common.utils.DateTimeUtil;

import java.time.LocalDateTime;

/**
 * @Created by <AUTHOR> on 2023/3/21
 */

public interface DateTimeMapper {

    default String dateTimeToString(LocalDateTime value) {
        if (value == null) {
            return null;
        }
        return value.format(DateTimeUtil.ISO_LOCAL_DATE_TIME_WITH_MILLISECOND);
    }

    default LocalDateTime stringToDateTime(String value) {
        if (value == null) {
            return null;
        }
        return LocalDateTime.parse(value, DateTimeUtil.ISO_LOCAL_DATE_TIME_WITH_MILLISECOND);
    }
}
