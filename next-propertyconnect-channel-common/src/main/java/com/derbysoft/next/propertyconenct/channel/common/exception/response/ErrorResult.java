package com.derbysoft.next.propertyconenct.channel.common.exception.response;

import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.List;
import java.util.Objects;

public class ErrorResult extends UnifyResult<Void> {
    private String errorCode;
    private String errorMessage;
    private List<ErrorDetail> errorDetails;

    public ErrorResult(String errorCode, String errorMessage) {
        super(new Header(), null);
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
    }

    public ErrorResult(String errorCode, String errorMessage, List<ErrorDetail> errorDetails) {
        super(new Header(), null);
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
        this.errorDetails = errorDetails;
    }

    @JsonIgnore
    @Override
    public Void getList() {
        return super.getList();
    }

    public String getErrorCode() {
        return this.errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorMessage() {
        return this.errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public List<ErrorDetail> getErrorDetails() {
        return this.errorDetails;
    }

    public void setErrorDetails(List<ErrorDetail> errorDetails) {
        this.errorDetails = errorDetails;
    }

    public boolean equals(Object o) {
        if (this == o) {
            return true;
        } else if (o != null && this.getClass() == o.getClass()) {
            ErrorResult that = (ErrorResult)o;
            return Objects.equals(this.errorCode, that.errorCode) && Objects.equals(this.errorMessage, that.errorMessage) && Objects.equals(this.errorDetails, that.errorDetails);
        } else {
            return false;
        }
    }

    public int hashCode() {
        return Objects.hash(new Object[]{this.errorCode, this.errorMessage, this.errorDetails});
    }

    public String toString() {
        return "ErrorResult{errorCode='" + this.errorCode + "', errorMessage='" + this.errorMessage + "', errorDetails=" + this.errorDetails + "}";
    }
}
