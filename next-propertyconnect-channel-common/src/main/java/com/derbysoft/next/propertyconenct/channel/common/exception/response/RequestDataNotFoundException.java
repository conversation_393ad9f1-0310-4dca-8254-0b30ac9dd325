package com.derbysoft.next.propertyconenct.channel.common.exception.response;

import com.derbysoft.next.propertyconenct.channel.common.exception.ErrorCode;

/**
 * @Created by <AUTHOR> on 2023/4/13
 */

public class RequestDataNotFoundException extends RuntimeException {
    public RequestDataNotFoundException(String message) {
        super(ErrorCode.NotFound.getErrorMessage(message));
    }

    public RequestDataNotFoundException(Object... messages) {
        super(ErrorCode.NotFound.getErrorMessage(messages));
    }

    public String getErrorCode() {
        return ErrorCode.NotFound.name();
    }
}
