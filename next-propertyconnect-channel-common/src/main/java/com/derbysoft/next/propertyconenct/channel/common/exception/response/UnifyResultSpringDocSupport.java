package com.derbysoft.next.propertyconenct.channel.common.exception.response;

import io.swagger.v3.oas.models.media.ArraySchema;
import org.springdoc.core.customizers.OpenApiCustomizer;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.context.annotation.Bean;


@AutoConfiguration
public class UnifyResultSpringDocSupport {

    @Bean
    public OpenApiCustomizer customOpenApiCustomiser() {
        return openApi -> {
            //Remove payload property from UnifyResult
            openApi.getComponents().getSchemas().values().forEach(schema -> {
                var properties = schema.getProperties();
                if (properties != null && schema.getName().startsWith("UnifyResult")) {
                    var payload = properties.get("list");
                    if (!(payload instanceof ArraySchema)) {
                        properties.remove("list");
                    }
                }
            });

            //Set all response type to application/json if not set
            openApi.getPaths().values().forEach(pathItem -> pathItem.readOperations().forEach(operation -> operation.getResponses().values().forEach(apiResponse -> {
                var mediaType = apiResponse.getContent().get("*/*");
                if (null != mediaType) {
                    apiResponse.getContent().computeIfAbsent("application/json", k -> apiResponse.getContent().get("*/*"));
                    apiResponse.getContent().remove("*/*");
                }
            })));
        };
    }
}
