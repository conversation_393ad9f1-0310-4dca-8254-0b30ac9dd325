#!/usr/bin/env python3

import csv
import sys

def calculate_coverage():
    total_missed_instructions = 0
    total_covered_instructions = 0
    total_missed_lines = 0
    total_covered_lines = 0
    total_missed_branches = 0
    total_covered_branches = 0
    total_missed_methods = 0
    total_covered_methods = 0
    total_missed_classes = 0
    total_covered_classes = 0
    
    try:
        with open('target/site/jacoco/jacoco.csv', 'r', encoding='utf-8') as csvfile:
            reader = csv.reader(csvfile)
            header = next(reader)  # Skip header
            
            for row in reader:
                if len(row) >= 11 and row[0] == 'PCChannel-Task':
                    # CSV format: GROUP,PACKAGE,CLASS,INSTRUCTION_MISSED,INSTRUCTION_COVERED,BRANCH_MISSED,BRANCH_COVERED,LINE_MISSED,LINE_COVERED,COMPLEXITY_MISSED,COMPLEXITY_COVERED,METHOD_MISSED,METHOD_COVERED,CLASS_MISSED,<PERSON><PERSON><PERSON>_COVERED
                    try:
                        total_missed_instructions += int(row[3])
                        total_covered_instructions += int(row[4])
                        total_missed_branches += int(row[5])
                        total_covered_branches += int(row[6])
                        total_missed_lines += int(row[7])
                        total_covered_lines += int(row[8])
                        if len(row) >= 13:
                            total_missed_methods += int(row[11])
                            total_covered_methods += int(row[12])
                        if len(row) >= 15:
                            total_missed_classes += int(row[13])
                            total_covered_classes += int(row[14])
                    except (ValueError, IndexError):
                        continue
    
        # Calculate percentages
        total_instructions = total_missed_instructions + total_covered_instructions
        total_lines = total_missed_lines + total_covered_lines
        total_branches = total_missed_branches + total_covered_branches
        total_methods = total_missed_methods + total_covered_methods
        total_classes = total_missed_classes + total_covered_classes
        
        instruction_coverage = (total_covered_instructions / total_instructions * 100) if total_instructions > 0 else 0
        line_coverage = (total_covered_lines / total_lines * 100) if total_lines > 0 else 0
        branch_coverage = (total_covered_branches / total_branches * 100) if total_branches > 0 else 0
        method_coverage = (total_covered_methods / total_methods * 100) if total_methods > 0 else 0
        class_coverage = (total_covered_classes / total_classes * 100) if total_classes > 0 else 0
        
        print("=== JaCoCo Coverage Summary ===")
        print(f"Instructions: {total_covered_instructions}/{total_instructions} ({instruction_coverage:.1f}%)")
        print(f"Lines: {total_covered_lines}/{total_lines} ({line_coverage:.1f}%)")
        print(f"Branches: {total_covered_branches}/{total_branches} ({branch_coverage:.1f}%)")
        print(f"Methods: {total_covered_methods}/{total_methods} ({method_coverage:.1f}%)")
        print(f"Classes: {total_covered_classes}/{total_classes} ({class_coverage:.1f}%)")
        print()
        print(f"Overall Line Coverage: {line_coverage:.1f}%")

        # Find classes with low coverage
        print("\n=== Classes with Low Coverage (< 50%) ===")
        low_coverage_classes = []

        with open('target/site/jacoco/jacoco.csv', 'r', encoding='utf-8') as csvfile:
            reader = csv.reader(csvfile)
            header = next(reader)  # Skip header

            for row in reader:
                if len(row) >= 9 and row[0] == 'PCChannel-Task' and row[2]:  # Has class name
                    try:
                        missed_lines = int(row[7])
                        covered_lines = int(row[8])
                        total_class_lines = missed_lines + covered_lines

                        if total_class_lines > 10:  # Only consider classes with significant lines
                            class_coverage = (covered_lines / total_class_lines * 100) if total_class_lines > 0 else 0
                            if class_coverage < 50:
                                low_coverage_classes.append((row[1] + "." + row[2], class_coverage, total_class_lines, covered_lines))
                    except (ValueError, IndexError):
                        continue

        # Sort by coverage percentage
        low_coverage_classes.sort(key=lambda x: x[1])

        for class_name, coverage, total_lines, covered_lines in low_coverage_classes[:20]:  # Show top 20
            print(f"{class_name}: {coverage:.1f}% ({covered_lines}/{total_lines} lines)")

        print(f"\nFound {len(low_coverage_classes)} classes with < 50% coverage")
        
    except FileNotFoundError:
        print("Error: jacoco.csv file not found")
        sys.exit(1)
    except Exception as e:
        print(f"Error reading coverage data: {e}")
        sys.exit(1)

if __name__ == "__main__":
    calculate_coverage()
