{"$schema": "http://cyclonedx.org/schema/bom-1.5.schema.json", "bomFormat": "CycloneDX", "specVersion": "1.5", "serialNumber": "urn:uuid:9acabdc0-6d43-477f-b49c-91215efac5c7", "version": 1, "metadata": {"timestamp": "2025-05-27T04:10:43Z", "tools": {"components": [{"type": "application", "author": "Snyk", "name": "snyk-cli", "version": "1.1297.1"}], "services": [{"provider": {"name": "Snyk"}, "name": "SBOM Export API", "version": "v1.110.1"}]}, "component": {"bom-ref": "1-next-propertyconnect-channel", "type": "application", "name": "next-propertyconnect-channel"}}, "components": [{"bom-ref": "1-com.derbysoft.next:next-propertyconnect-channel@1.0.0", "type": "library", "group": "com.derbysoft.next", "name": "com.derbysoft.next:next-propertyconnect-channel", "version": "1.0.0", "purl": "pkg:maven/com.derbysoft.next/next-propertyconnect-channel@1.0.0"}, {"bom-ref": "2-com.alibaba.fastjson2:fastjson2@2.0.26", "type": "library", "group": "com.alibaba.fastjson2", "name": "com.alibaba.fastjson2:fastjson2", "version": "2.0.26", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/com.alibaba.fastjson2/fastjson2@2.0.26"}, {"bom-ref": "3-org.apache.commons:commons-collections4@4.4", "type": "library", "group": "org.apache.commons", "name": "org.apache.commons:commons-collections4", "version": "4.4", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.commons/commons-collections4@4.4"}, {"bom-ref": "4-jakarta.annotation:jakarta.annotation-api@2.1.1", "type": "library", "group": "jakarta.annotation", "name": "jakarta.annotation:jakarta.annotation-api", "version": "2.1.1", "licenses": [{"expression": "(EPL-2.0 OR GPL-2.0-with-classpath-exception)"}], "purl": "pkg:maven/jakarta.annotation/jakarta.annotation-api@2.1.1"}, {"bom-ref": "5-jakarta.xml.bind:jakarta.xml.bind-api@4.0.2", "type": "library", "group": "jakarta.xml.bind", "name": "jakarta.xml.bind:jakarta.xml.bind-api", "version": "4.0.2", "licenses": [{"expression": "EDL-1.0"}], "purl": "pkg:maven/jakarta.xml.bind/jakarta.xml.bind-api@4.0.2"}, {"bom-ref": "6-org.glassfish.jaxb:jaxb-runtime@4.0.5", "type": "library", "group": "org.glassfish.jaxb", "name": "org.glassfish.jaxb:jaxb-runtime", "version": "4.0.5", "licenses": [{"expression": "EDL-1.0"}], "purl": "pkg:maven/org.glassfish.jaxb/jaxb-runtime@4.0.5"}, {"bom-ref": "7-jakarta.activation:jakarta.activation-api@2.1.3", "type": "library", "group": "jakarta.activation", "name": "jakarta.activation:jakarta.activation-api", "version": "2.1.3", "licenses": [{"expression": "EDL-1.0"}], "purl": "pkg:maven/jakarta.activation/jakarta.activation-api@2.1.3"}, {"bom-ref": "8-org.glassfish.jaxb:jaxb-core@4.0.5", "type": "library", "group": "org.glassfish.jaxb", "name": "org.glassfish.jaxb:jaxb-core", "version": "4.0.5", "licenses": [{"expression": "EDL-1.0"}], "purl": "pkg:maven/org.glassfish.jaxb/jaxb-core@4.0.5"}, {"bom-ref": "9-org.eclipse.angus:angus-activation@2.0.2", "type": "library", "group": "org.eclipse.angus", "name": "org.eclipse.angus:angus-activation", "version": "2.0.2", "licenses": [{"expression": "EDL-1.0"}], "purl": "pkg:maven/org.eclipse.angus/angus-activation@2.0.2"}, {"bom-ref": "10-org.glassfish.jaxb:txw2@4.0.5", "type": "library", "group": "org.glassfish.jaxb", "name": "org.glassfish.jaxb:txw2", "version": "4.0.5", "licenses": [{"expression": "EDL-1.0"}], "purl": "pkg:maven/org.glassfish.jaxb/txw2@4.0.5"}, {"bom-ref": "11-com.sun.istack:istack-commons-runtime@4.1.2", "type": "library", "group": "com.sun.istack", "name": "com.sun.istack:istack-commons-runtime", "version": "4.1.2", "licenses": [{"expression": "EDL-1.0"}], "purl": "pkg:maven/com.sun.istack/istack-commons-runtime@4.1.2"}, {"bom-ref": "12-com.derbysoft.next:next-propertyconnect-channel-api@1.0.0", "type": "library", "group": "com.derbysoft.next", "name": "com.derbysoft.next:next-propertyconnect-channel-api", "version": "1.0.0", "purl": "pkg:maven/com.derbysoft.next/next-propertyconnect-channel-api@1.0.0"}, {"bom-ref": "13-com.alibaba.fastjson2:fastjson2@2.0.26", "type": "library", "group": "com.alibaba.fastjson2", "name": "com.alibaba.fastjson2:fastjson2", "version": "2.0.26", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/com.alibaba.fastjson2/fastjson2@2.0.26"}, {"bom-ref": "14-org.apache.commons:commons-collections4@4.4", "type": "library", "group": "org.apache.commons", "name": "org.apache.commons:commons-collections4", "version": "4.4", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.commons/commons-collections4@4.4"}, {"bom-ref": "15-jakarta.annotation:jakarta.annotation-api@2.1.1", "type": "library", "group": "jakarta.annotation", "name": "jakarta.annotation:jakarta.annotation-api", "version": "2.1.1", "licenses": [{"expression": "(EPL-2.0 OR GPL-2.0-with-classpath-exception)"}], "purl": "pkg:maven/jakarta.annotation/jakarta.annotation-api@2.1.1"}, {"bom-ref": "16-jakarta.xml.bind:jakarta.xml.bind-api@4.0.2", "type": "library", "group": "jakarta.xml.bind", "name": "jakarta.xml.bind:jakarta.xml.bind-api", "version": "4.0.2", "licenses": [{"expression": "EDL-1.0"}], "purl": "pkg:maven/jakarta.xml.bind/jakarta.xml.bind-api@4.0.2"}, {"bom-ref": "17-org.glassfish.jaxb:jaxb-runtime@4.0.5", "type": "library", "group": "org.glassfish.jaxb", "name": "org.glassfish.jaxb:jaxb-runtime", "version": "4.0.5", "licenses": [{"expression": "EDL-1.0"}], "purl": "pkg:maven/org.glassfish.jaxb/jaxb-runtime@4.0.5"}, {"bom-ref": "18-jakarta.activation:jakarta.activation-api@2.1.3", "type": "library", "group": "jakarta.activation", "name": "jakarta.activation:jakarta.activation-api", "version": "2.1.3", "licenses": [{"expression": "EDL-1.0"}], "purl": "pkg:maven/jakarta.activation/jakarta.activation-api@2.1.3"}, {"bom-ref": "19-org.glassfish.jaxb:jaxb-core@4.0.5", "type": "library", "group": "org.glassfish.jaxb", "name": "org.glassfish.jaxb:jaxb-core", "version": "4.0.5", "licenses": [{"expression": "EDL-1.0"}], "purl": "pkg:maven/org.glassfish.jaxb/jaxb-core@4.0.5"}, {"bom-ref": "20-org.eclipse.angus:angus-activation@2.0.2", "type": "library", "group": "org.eclipse.angus", "name": "org.eclipse.angus:angus-activation", "version": "2.0.2", "licenses": [{"expression": "EDL-1.0"}], "purl": "pkg:maven/org.eclipse.angus/angus-activation@2.0.2"}, {"bom-ref": "21-org.glassfish.jaxb:txw2@4.0.5", "type": "library", "group": "org.glassfish.jaxb", "name": "org.glassfish.jaxb:txw2", "version": "4.0.5", "licenses": [{"expression": "EDL-1.0"}], "purl": "pkg:maven/org.glassfish.jaxb/txw2@4.0.5"}, {"bom-ref": "22-com.sun.istack:istack-commons-runtime@4.1.2", "type": "library", "group": "com.sun.istack", "name": "com.sun.istack:istack-commons-runtime", "version": "4.1.2", "licenses": [{"expression": "EDL-1.0"}], "purl": "pkg:maven/com.sun.istack/istack-commons-runtime@4.1.2"}, {"bom-ref": "23-com.derbysoft.next:next-propertyconnect-channel-common@1.0.0", "type": "library", "group": "com.derbysoft.next", "name": "com.derbysoft.next:next-propertyconnect-channel-common", "version": "1.0.0", "purl": "pkg:maven/com.derbysoft.next/next-propertyconnect-channel-common@1.0.0"}, {"bom-ref": "24-org.springframework.boot:spring-boot-starter-web@3.4.1", "type": "library", "group": "org.springframework.boot", "name": "org.springframework.boot:spring-boot-starter-web", "version": "3.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.springframework.boot/spring-boot-starter-web@3.4.1"}, {"bom-ref": "25-org.springframework.boot:spring-boot-starter-validation@3.4.1", "type": "library", "group": "org.springframework.boot", "name": "org.springframework.boot:spring-boot-starter-validation", "version": "3.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.springframework.boot/spring-boot-starter-validation@3.4.1"}, {"bom-ref": "26-org.mapstruct:<EMAIL>", "type": "library", "group": "org.mapstruct", "name": "org.mapstruct:mapstruct", "version": "1.5.3.Final", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.mapstruct/<EMAIL>"}, {"bom-ref": "27-org.springdoc:springdoc-openapi-ui@1.7.0", "type": "library", "group": "org.springdoc", "name": "org.springdoc:springdoc-openapi-ui", "version": "1.7.0", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.springdoc/springdoc-openapi-ui@1.7.0"}, {"bom-ref": "28-com.alibaba.fastjson2:fastjson2@2.0.26", "type": "library", "group": "com.alibaba.fastjson2", "name": "com.alibaba.fastjson2:fastjson2", "version": "2.0.26", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/com.alibaba.fastjson2/fastjson2@2.0.26"}, {"bom-ref": "29-org.apache.commons:commons-collections4@4.4", "type": "library", "group": "org.apache.commons", "name": "org.apache.commons:commons-collections4", "version": "4.4", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.commons/commons-collections4@4.4"}, {"bom-ref": "30-jakarta.annotation:jakarta.annotation-api@2.1.1", "type": "library", "group": "jakarta.annotation", "name": "jakarta.annotation:jakarta.annotation-api", "version": "2.1.1", "licenses": [{"expression": "(EPL-2.0 OR GPL-2.0-with-classpath-exception)"}], "purl": "pkg:maven/jakarta.annotation/jakarta.annotation-api@2.1.1"}, {"bom-ref": "31-jakarta.xml.bind:jakarta.xml.bind-api@4.0.2", "type": "library", "group": "jakarta.xml.bind", "name": "jakarta.xml.bind:jakarta.xml.bind-api", "version": "4.0.2", "licenses": [{"expression": "EDL-1.0"}], "purl": "pkg:maven/jakarta.xml.bind/jakarta.xml.bind-api@4.0.2"}, {"bom-ref": "32-org.glassfish.jaxb:jaxb-runtime@4.0.5", "type": "library", "group": "org.glassfish.jaxb", "name": "org.glassfish.jaxb:jaxb-runtime", "version": "4.0.5", "licenses": [{"expression": "EDL-1.0"}], "purl": "pkg:maven/org.glassfish.jaxb/jaxb-runtime@4.0.5"}, {"bom-ref": "33-org.springframework.boot:spring-boot-starter@3.4.1", "type": "library", "group": "org.springframework.boot", "name": "org.springframework.boot:spring-boot-starter", "version": "3.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.springframework.boot/spring-boot-starter@3.4.1"}, {"bom-ref": "34-org.springframework.boot:spring-boot-starter-json@3.4.1", "type": "library", "group": "org.springframework.boot", "name": "org.springframework.boot:spring-boot-starter-json", "version": "3.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.springframework.boot/spring-boot-starter-json@3.4.1"}, {"bom-ref": "35-org.springframework.boot:spring-boot-starter-tomcat@3.4.1", "type": "library", "group": "org.springframework.boot", "name": "org.springframework.boot:spring-boot-starter-tomcat", "version": "3.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.springframework.boot/spring-boot-starter-tomcat@3.4.1"}, {"bom-ref": "36-org.springframework:spring-web@6.2.1", "type": "library", "group": "org.springframework", "name": "org.springframework:spring-web", "version": "6.2.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.springframework/spring-web@6.2.1"}, {"bom-ref": "37-org.springframework:spring-webmvc@6.2.1", "type": "library", "group": "org.springframework", "name": "org.springframework:spring-webmvc", "version": "6.2.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.springframework/spring-webmvc@6.2.1"}, {"bom-ref": "38-org.apache.tomcat.embed:tomcat-embed-el@10.1.34", "type": "library", "group": "org.apache.tomcat.embed", "name": "org.apache.tomcat.embed:tomcat-embed-el", "version": "10.1.34", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.tomcat.embed/tomcat-embed-el@10.1.34"}, {"bom-ref": "39-org.hibernate.validator:<EMAIL>", "type": "library", "group": "org.hibernate.validator", "name": "org.hibernate.validator:hibernate-validator", "version": "8.0.2.Final", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.hibernate.validator/<EMAIL>"}, {"bom-ref": "40-org.springdoc:springdoc-openapi-webmvc-core@1.7.0", "type": "library", "group": "org.springdoc", "name": "org.springdoc:springdoc-openapi-webmvc-core", "version": "1.7.0", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.springdoc/springdoc-openapi-webmvc-core@1.7.0"}, {"bom-ref": "41-org.webjars:swagger-ui@4.18.2", "type": "library", "group": "org.webjars", "name": "org.webjars:swagger-ui", "version": "4.18.2", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.webjars/swagger-ui@4.18.2"}, {"bom-ref": "42-jakarta.activation:jakarta.activation-api@2.1.3", "type": "library", "group": "jakarta.activation", "name": "jakarta.activation:jakarta.activation-api", "version": "2.1.3", "licenses": [{"expression": "EDL-1.0"}], "purl": "pkg:maven/jakarta.activation/jakarta.activation-api@2.1.3"}, {"bom-ref": "43-org.glassfish.jaxb:jaxb-core@4.0.5", "type": "library", "group": "org.glassfish.jaxb", "name": "org.glassfish.jaxb:jaxb-core", "version": "4.0.5", "licenses": [{"expression": "EDL-1.0"}], "purl": "pkg:maven/org.glassfish.jaxb/jaxb-core@4.0.5"}, {"bom-ref": "44-org.springframework.boot:spring-boot@3.4.1", "type": "library", "group": "org.springframework.boot", "name": "org.springframework.boot:spring-boot", "version": "3.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.springframework.boot/spring-boot@3.4.1"}, {"bom-ref": "45-org.springframework.boot:spring-boot-autoconfigure@3.4.1", "type": "library", "group": "org.springframework.boot", "name": "org.springframework.boot:spring-boot-autoconfigure", "version": "3.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.springframework.boot/spring-boot-autoconfigure@3.4.1"}, {"bom-ref": "46-org.springframework.boot:spring-boot-starter-logging@3.4.1", "type": "library", "group": "org.springframework.boot", "name": "org.springframework.boot:spring-boot-starter-logging", "version": "3.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.springframework.boot/spring-boot-starter-logging@3.4.1"}, {"bom-ref": "47-org.springframework:spring-core@6.2.1", "type": "library", "group": "org.springframework", "name": "org.springframework:spring-core", "version": "6.2.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.springframework/spring-core@6.2.1"}, {"bom-ref": "48-org.yaml:snakeyaml@2.3", "type": "library", "group": "org.yaml", "name": "org.yaml:snakeyaml", "version": "2.3", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.yaml/snakeyaml@2.3"}, {"bom-ref": "49-com.fasterxml.jackson.core:jackson-databind@2.18.2", "type": "library", "group": "com.fasterxml.jackson.core", "name": "com.fasterxml.jackson.core:jackson-databind", "version": "2.18.2", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/com.fasterxml.jackson.core/jackson-databind@2.18.2"}, {"bom-ref": "50-com.fasterxml.jackson.datatype:jackson-datatype-jdk8@2.18.2", "type": "library", "group": "com.fasterxml.jackson.datatype", "name": "com.fasterxml.jackson.datatype:jackson-datatype-jdk8", "version": "2.18.2", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/com.fasterxml.jackson.datatype/jackson-datatype-jdk8@2.18.2"}, {"bom-ref": "51-com.fasterxml.jackson.datatype:jackson-datatype-jsr310@2.18.2", "type": "library", "group": "com.fasterxml.jackson.datatype", "name": "com.fasterxml.jackson.datatype:jackson-datatype-jsr310", "version": "2.18.2", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/com.fasterxml.jackson.datatype/jackson-datatype-jsr310@2.18.2"}, {"bom-ref": "52-com.fasterxml.jackson.module:jackson-module-parameter-names@2.18.2", "type": "library", "group": "com.fasterxml.jackson.module", "name": "com.fasterxml.jackson.module:jackson-module-parameter-names", "version": "2.18.2", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/com.fasterxml.jackson.module/jackson-module-parameter-names@2.18.2"}, {"bom-ref": "53-org.apache.tomcat.embed:tomcat-embed-core@10.1.34", "type": "library", "group": "org.apache.tomcat.embed", "name": "org.apache.tomcat.embed:tomcat-embed-core", "version": "10.1.34", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.tomcat.embed/tomcat-embed-core@10.1.34"}, {"bom-ref": "54-org.apache.tomcat.embed:tomcat-embed-websocket@10.1.34", "type": "library", "group": "org.apache.tomcat.embed", "name": "org.apache.tomcat.embed:tomcat-embed-websocket", "version": "10.1.34", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.tomcat.embed/tomcat-embed-websocket@10.1.34"}, {"bom-ref": "55-org.springframework:spring-beans@6.2.1", "type": "library", "group": "org.springframework", "name": "org.springframework:spring-beans", "version": "6.2.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.springframework/spring-beans@6.2.1"}, {"bom-ref": "56-io.micrometer:micrometer-observation@1.14.2", "type": "library", "group": "io.micrometer", "name": "io.micrometer:micrometer-observation", "version": "1.14.2", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/io.micrometer/micrometer-observation@1.14.2"}, {"bom-ref": "57-org.springframework:spring-aop@6.2.1", "type": "library", "group": "org.springframework", "name": "org.springframework:spring-aop", "version": "6.2.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.springframework/spring-aop@6.2.1"}, {"bom-ref": "58-org.springframework:spring-context@6.2.1", "type": "library", "group": "org.springframework", "name": "org.springframework:spring-context", "version": "6.2.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.springframework/spring-context@6.2.1"}, {"bom-ref": "59-org.springframework:spring-expression@6.2.1", "type": "library", "group": "org.springframework", "name": "org.springframework:spring-expression", "version": "6.2.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.springframework/spring-expression@6.2.1"}, {"bom-ref": "60-jakarta.validation:jakarta.validation-api@3.0.2", "type": "library", "group": "jakarta.validation", "name": "jakarta.validation:jakarta.validation-api", "version": "3.0.2", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/jakarta.validation/jakarta.validation-api@3.0.2"}, {"bom-ref": "61-org.jboss.logging:<EMAIL>", "type": "library", "group": "org.jboss.logging", "name": "org.jboss.logging:jboss-logging", "version": "3.6.1.Final", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.jboss.logging/<EMAIL>"}, {"bom-ref": "62-com.fasterxml:classmate@1.7.0", "type": "library", "group": "com.fasterxml", "name": "com.fasterxml:classmate", "version": "1.7.0", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/com.fasterxml/classmate@1.7.0"}, {"bom-ref": "63-org.springdoc:springdoc-openapi-common@1.7.0", "type": "library", "group": "org.springdoc", "name": "org.springdoc:springdoc-openapi-common", "version": "1.7.0", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.springdoc/springdoc-openapi-common@1.7.0"}, {"bom-ref": "64-org.eclipse.angus:angus-activation@2.0.2", "type": "library", "group": "org.eclipse.angus", "name": "org.eclipse.angus:angus-activation", "version": "2.0.2", "licenses": [{"expression": "EDL-1.0"}], "purl": "pkg:maven/org.eclipse.angus/angus-activation@2.0.2"}, {"bom-ref": "65-org.glassfish.jaxb:txw2@4.0.5", "type": "library", "group": "org.glassfish.jaxb", "name": "org.glassfish.jaxb:txw2", "version": "4.0.5", "licenses": [{"expression": "EDL-1.0"}], "purl": "pkg:maven/org.glassfish.jaxb/txw2@4.0.5"}, {"bom-ref": "66-com.sun.istack:istack-commons-runtime@4.1.2", "type": "library", "group": "com.sun.istack", "name": "com.sun.istack:istack-commons-runtime", "version": "4.1.2", "licenses": [{"expression": "EDL-1.0"}], "purl": "pkg:maven/com.sun.istack/istack-commons-runtime@4.1.2"}, {"bom-ref": "67-ch.qos.logback:logback-classic@1.5.12", "type": "library", "group": "ch.qos.logback", "name": "ch.qos.logback:logback-classic", "version": "1.5.12", "licenses": [{"expression": "(EPL-1.0 OR LGPL-2.1)"}], "purl": "pkg:maven/ch.qos.logback/logback-classic@1.5.12"}, {"bom-ref": "68-org.apache.logging.log4j:log4j-to-slf4j@2.24.3", "type": "library", "group": "org.apache.logging.log4j", "name": "org.apache.logging.log4j:log4j-to-slf4j", "version": "2.24.3", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.logging.log4j/log4j-to-slf4j@2.24.3"}, {"bom-ref": "69-org.slf4j:jul-to-slf4j@2.0.16", "type": "library", "group": "org.slf4j", "name": "org.slf4j:jul-to-slf4j", "version": "2.0.16", "licenses": [{"expression": "MIT"}], "purl": "pkg:maven/org.slf4j/jul-to-slf4j@2.0.16"}, {"bom-ref": "70-com.fasterxml.jackson.core:jackson-annotations@2.18.2", "type": "library", "group": "com.fasterxml.jackson.core", "name": "com.fasterxml.jackson.core:jackson-annotations", "version": "2.18.2", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/com.fasterxml.jackson.core/jackson-annotations@2.18.2"}, {"bom-ref": "71-com.fasterxml.jackson.core:jackson-core@2.18.2", "type": "library", "group": "com.fasterxml.jackson.core", "name": "com.fasterxml.jackson.core:jackson-core", "version": "2.18.2", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/com.fasterxml.jackson.core/jackson-core@2.18.2"}, {"bom-ref": "72-io.micrometer:micrometer-commons@1.14.2", "type": "library", "group": "io.micrometer", "name": "io.micrometer:micrometer-commons", "version": "1.14.2", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/io.micrometer/micrometer-commons@1.14.2"}, {"bom-ref": "73-io.swagger.core.v3:swagger-core@2.2.9", "type": "library", "group": "io.swagger.core.v3", "name": "io.swagger.core.v3:swagger-core", "version": "2.2.9", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/io.swagger.core.v3/swagger-core@2.2.9"}, {"bom-ref": "74-ch.qos.logback:logback-core@1.5.12", "type": "library", "group": "ch.qos.logback", "name": "ch.qos.logback:logback-core", "version": "1.5.12", "licenses": [{"expression": "(EPL-1.0 OR LGPL-2.1)"}], "purl": "pkg:maven/ch.qos.logback/logback-core@1.5.12"}, {"bom-ref": "75-org.slf4j:slf4j-api@2.0.16", "type": "library", "group": "org.slf4j", "name": "org.slf4j:slf4j-api", "version": "2.0.16", "licenses": [{"expression": "MIT"}], "purl": "pkg:maven/org.slf4j/slf4j-api@2.0.16"}, {"bom-ref": "76-org.apache.logging.log4j:log4j-api@2.24.3", "type": "library", "group": "org.apache.logging.log4j", "name": "org.apache.logging.log4j:log4j-api", "version": "2.24.3", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.logging.log4j/log4j-api@2.24.3"}, {"bom-ref": "77-org.apache.commons:commons-lang3@3.17.0", "type": "library", "group": "org.apache.commons", "name": "org.apache.commons:commons-lang3", "version": "3.17.0", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.commons/commons-lang3@3.17.0"}, {"bom-ref": "78-com.fasterxml.jackson.dataformat:jackson-dataformat-yaml@2.18.2", "type": "library", "group": "com.fasterxml.jackson.dataformat", "name": "com.fasterxml.jackson.dataformat:jackson-dataformat-yaml", "version": "2.18.2", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/com.fasterxml.jackson.dataformat/jackson-dataformat-yaml@2.18.2"}, {"bom-ref": "79-io.swagger.core.v3:swagger-annotations@2.2.9", "type": "library", "group": "io.swagger.core.v3", "name": "io.swagger.core.v3:swagger-annotations", "version": "2.2.9", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/io.swagger.core.v3/swagger-annotations@2.2.9"}, {"bom-ref": "80-io.swagger.core.v3:swagger-models@2.2.9", "type": "library", "group": "io.swagger.core.v3", "name": "io.swagger.core.v3:swagger-models", "version": "2.2.9", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/io.swagger.core.v3/swagger-models@2.2.9"}, {"bom-ref": "81-com.derbysoft.next:next-propertyconnect-channel-task@1.0.0", "type": "library", "group": "com.derbysoft.next", "name": "com.derbysoft.next:next-propertyconnect-channel-task", "version": "1.0.0", "purl": "pkg:maven/com.derbysoft.next/next-propertyconnect-channel-task@1.0.0"}, {"bom-ref": "82-com.derbysoft.next:next-propertyconnect-channel-common@1.0.0", "type": "library", "group": "com.derbysoft.next", "name": "com.derbysoft.next:next-propertyconnect-channel-common", "version": "1.0.0", "purl": "pkg:maven/com.derbysoft.next/next-propertyconnect-channel-common@1.0.0"}, {"bom-ref": "83-org.springframework.boot:spring-boot-configuration-processor@3.4.1", "type": "library", "group": "org.springframework.boot", "name": "org.springframework.boot:spring-boot-configuration-processor", "version": "3.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.springframework.boot/spring-boot-configuration-processor@3.4.1"}, {"bom-ref": "84-org.apache.groovy:groovy@4.0.24", "type": "library", "group": "org.apache.groovy", "name": "org.apache.groovy:groovy", "version": "4.0.24", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.groovy/groovy@4.0.24"}, {"bom-ref": "85-org.apache.groovy:groovy-xml@4.0.24", "type": "library", "group": "org.apache.groovy", "name": "org.apache.groovy:groovy-xml", "version": "4.0.24", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.groovy/groovy-xml@4.0.24"}, {"bom-ref": "86-org.springframework.boot:spring-boot-starter-web@3.4.1", "type": "library", "group": "org.springframework.boot", "name": "org.springframework.boot:spring-boot-starter-web", "version": "3.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.springframework.boot/spring-boot-starter-web@3.4.1"}, {"bom-ref": "87-org.springframework.cloud:spring-cloud-starter-netflix-eureka-client@4.2.0", "type": "library", "group": "org.springframework.cloud", "name": "org.springframework.cloud:spring-cloud-starter-netflix-eureka-client", "version": "4.2.0", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.springframework.cloud/spring-cloud-starter-netflix-eureka-client@4.2.0"}, {"bom-ref": "88-com.derbysoft.next:next-commons-boot@3.0.1-SNAPSHOT", "type": "library", "group": "com.derbysoft.next", "name": "com.derbysoft.next:next-commons-boot", "version": "3.0.1-SNAPSHOT", "purl": "pkg:maven/com.derbysoft.next/next-commons-boot@3.0.1-SNAPSHOT"}, {"bom-ref": "89-org.springframework.boot:spring-boot-starter-data-mongodb@3.4.1", "type": "library", "group": "org.springframework.boot", "name": "org.springframework.boot:spring-boot-starter-data-mongodb", "version": "3.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.springframework.boot/spring-boot-starter-data-mongodb@3.4.1"}, {"bom-ref": "90-org.springframework.boot:spring-boot-starter-actuator@3.4.1", "type": "library", "group": "org.springframework.boot", "name": "org.springframework.boot:spring-boot-starter-actuator", "version": "3.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.springframework.boot/spring-boot-starter-actuator@3.4.1"}, {"bom-ref": "91-org.springframework.cloud:<EMAIL>", "type": "library", "group": "org.springframework.cloud", "name": "org.springframework.cloud:spring-cloud-starter-zipkin", "version": "2.2.8.RELEASE", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.springframework.cloud/<EMAIL>"}, {"bom-ref": "92-com.google.guava:guava@31.1-jre", "type": "library", "group": "com.google.guava", "name": "com.google.guava:guava", "version": "31.1-jre", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/com.google.guava/guava@31.1-jre"}, {"bom-ref": "93-org.springframework.cloud:spring-cloud-starter-bootstrap@4.2.0", "type": "library", "group": "org.springframework.cloud", "name": "org.springframework.cloud:spring-cloud-starter-bootstrap", "version": "4.2.0", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.springframework.cloud/spring-cloud-starter-bootstrap@4.2.0"}, {"bom-ref": "94-org.springframework.cloud:spring-cloud-starter-openfeign@4.2.0", "type": "library", "group": "org.springframework.cloud", "name": "org.springframework.cloud:spring-cloud-starter-openfeign", "version": "4.2.0", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.springframework.cloud/spring-cloud-starter-openfeign@4.2.0"}, {"bom-ref": "95-com.derbysoft.extension:aggregation-tool@1.0.1-SNAPSHOT", "type": "library", "group": "com.derbysoft.extension", "name": "com.derbysoft.extension:aggregation-tool", "version": "1.0.1-SNAPSHOT", "purl": "pkg:maven/com.derbysoft.extension/aggregation-tool@1.0.1-SNAPSHOT"}, {"bom-ref": "96-com.derbysoft.extension:schedulecenter-support@1.0.0-SNAPSHOT", "type": "library", "group": "com.derbysoft.extension", "name": "com.derbysoft.extension:schedulecenter-support", "version": "1.0.0-SNAPSHOT", "purl": "pkg:maven/com.derbysoft.extension/schedulecenter-support@1.0.0-SNAPSHOT"}, {"bom-ref": "97-com.derbysoft.schedulecenter:schedulecenter-taskframework@1.2.4", "type": "library", "group": "com.derbysoft.schedulecenter", "name": "com.derbysoft.schedulecenter:schedulecenter-taskframework", "version": "1.2.4", "purl": "pkg:maven/com.derbysoft.schedulecenter/schedulecenter-taskframework@1.2.4"}, {"bom-ref": "98-org.mapstruct:<EMAIL>", "type": "library", "group": "org.mapstruct", "name": "org.mapstruct:mapstruct", "version": "1.5.3.Final", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.mapstruct/<EMAIL>"}, {"bom-ref": "99-com.squareup.okhttp3:okhttp@4.12.0", "type": "library", "group": "com.squareup.okhttp3", "name": "com.squareup.okhttp3:okhttp", "version": "4.12.0", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/com.squareup.okhttp3/okhttp@4.12.0"}, {"bom-ref": "100-com.derbysoft.extension:springdoc-openapi@1.0.0-SNAPSHOT", "type": "library", "group": "com.derbysoft.extension", "name": "com.derbysoft.extension:springdoc-openapi", "version": "1.0.0-SNAPSHOT", "purl": "pkg:maven/com.derbysoft.extension/springdoc-openapi@1.0.0-SNAPSHOT"}, {"bom-ref": "101-org.springframework.boot:spring-boot-starter-validation@3.4.1", "type": "library", "group": "org.springframework.boot", "name": "org.springframework.boot:spring-boot-starter-validation", "version": "3.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.springframework.boot/spring-boot-starter-validation@3.4.1"}, {"bom-ref": "102-org.codehaus.gpars:gpars@1.2.1", "type": "library", "group": "org.codehaus.gpars", "name": "org.codehaus.gpars:gpars", "version": "1.2.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.codehaus.gpars/gpars@1.2.1"}, {"bom-ref": "103-org.apache.poi:poi@5.2.5", "type": "library", "group": "org.apache.poi", "name": "org.apache.poi:poi", "version": "5.2.5", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.poi/poi@5.2.5"}, {"bom-ref": "104-org.apache.poi:poi-ooxml@5.2.5", "type": "library", "group": "org.apache.poi", "name": "org.apache.poi:poi-ooxml", "version": "5.2.5", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.poi/poi-ooxml@5.2.5"}, {"bom-ref": "105-software.amazon.awssdk:s3@2.29.52", "type": "library", "group": "software.amazon.awssdk", "name": "software.amazon.awssdk:s3", "version": "2.29.52", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/software.amazon.awssdk/s3@2.29.52"}, {"bom-ref": "106-software.amazon.awssdk:sts@2.21.40", "type": "library", "group": "software.amazon.awssdk", "name": "software.amazon.awssdk:sts", "version": "2.21.40", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/software.amazon.awssdk/sts@2.21.40"}, {"bom-ref": "107-commons-fileupload:commons-fileupload@1.5", "type": "library", "group": "commons-fileupload", "name": "commons-fileupload:commons-fileupload", "version": "1.5", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/commons-fileupload/commons-fileupload@1.5"}, {"bom-ref": "108-commons-io:commons-io@2.14.0", "type": "library", "group": "commons-io", "name": "commons-io:commons-io", "version": "2.14.0", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/commons-io/commons-io@2.14.0"}, {"bom-ref": "109-com.alibaba.fastjson2:fastjson2@2.0.26", "type": "library", "group": "com.alibaba.fastjson2", "name": "com.alibaba.fastjson2:fastjson2", "version": "2.0.26", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/com.alibaba.fastjson2/fastjson2@2.0.26"}, {"bom-ref": "110-org.apache.commons:commons-collections4@4.4", "type": "library", "group": "org.apache.commons", "name": "org.apache.commons:commons-collections4", "version": "4.4", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.commons/commons-collections4@4.4"}, {"bom-ref": "111-jakarta.annotation:jakarta.annotation-api@2.1.1", "type": "library", "group": "jakarta.annotation", "name": "jakarta.annotation:jakarta.annotation-api", "version": "2.1.1", "licenses": [{"expression": "(EPL-2.0 OR GPL-2.0-with-classpath-exception)"}], "purl": "pkg:maven/jakarta.annotation/jakarta.annotation-api@2.1.1"}, {"bom-ref": "112-jakarta.xml.bind:jakarta.xml.bind-api@4.0.2", "type": "library", "group": "jakarta.xml.bind", "name": "jakarta.xml.bind:jakarta.xml.bind-api", "version": "4.0.2", "licenses": [{"expression": "EDL-1.0"}], "purl": "pkg:maven/jakarta.xml.bind/jakarta.xml.bind-api@4.0.2"}, {"bom-ref": "113-org.glassfish.jaxb:jaxb-runtime@4.0.5", "type": "library", "group": "org.glassfish.jaxb", "name": "org.glassfish.jaxb:jaxb-runtime", "version": "4.0.5", "licenses": [{"expression": "EDL-1.0"}], "purl": "pkg:maven/org.glassfish.jaxb/jaxb-runtime@4.0.5"}, {"bom-ref": "114-org.springframework.boot:spring-boot-starter@3.4.1", "type": "library", "group": "org.springframework.boot", "name": "org.springframework.boot:spring-boot-starter", "version": "3.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.springframework.boot/spring-boot-starter@3.4.1"}, {"bom-ref": "115-org.springframework.boot:spring-boot-starter-json@3.4.1", "type": "library", "group": "org.springframework.boot", "name": "org.springframework.boot:spring-boot-starter-json", "version": "3.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.springframework.boot/spring-boot-starter-json@3.4.1"}, {"bom-ref": "116-org.springframework.boot:spring-boot-starter-tomcat@3.4.1", "type": "library", "group": "org.springframework.boot", "name": "org.springframework.boot:spring-boot-starter-tomcat", "version": "3.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.springframework.boot/spring-boot-starter-tomcat@3.4.1"}, {"bom-ref": "117-org.springframework:spring-web@6.2.1", "type": "library", "group": "org.springframework", "name": "org.springframework:spring-web", "version": "6.2.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.springframework/spring-web@6.2.1"}, {"bom-ref": "118-org.springframework:spring-webmvc@6.2.1", "type": "library", "group": "org.springframework", "name": "org.springframework:spring-webmvc", "version": "6.2.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.springframework/spring-webmvc@6.2.1"}, {"bom-ref": "119-org.springframework.cloud:spring-cloud-starter@4.2.0", "type": "library", "group": "org.springframework.cloud", "name": "org.springframework.cloud:spring-cloud-starter", "version": "4.2.0", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.springframework.cloud/spring-cloud-starter@4.2.0"}, {"bom-ref": "120-org.springframework.cloud:spring-cloud-netflix-eureka-client@4.2.0", "type": "library", "group": "org.springframework.cloud", "name": "org.springframework.cloud:spring-cloud-netflix-eureka-client", "version": "4.2.0", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.springframework.cloud/spring-cloud-netflix-eureka-client@4.2.0"}, {"bom-ref": "121-com.netflix.eureka:eureka-client@2.0.4", "type": "library", "group": "com.netflix.eureka", "name": "com.netflix.eureka:eureka-client", "version": "2.0.4", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/com.netflix.eureka/eureka-client@2.0.4"}, {"bom-ref": "122-org.springframework.cloud:spring-cloud-starter-loadbalancer@4.2.0", "type": "library", "group": "org.springframework.cloud", "name": "org.springframework.cloud:spring-cloud-starter-loadbalancer", "version": "4.2.0", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.springframework.cloud/spring-cloud-starter-loadbalancer@4.2.0"}, {"bom-ref": "123-com.derbysoft.next:next-commons-core@3.0.1-SNAPSHOT", "type": "library", "group": "com.derbysoft.next", "name": "com.derbysoft.next:next-commons-core", "version": "3.0.1-SNAPSHOT", "purl": "pkg:maven/com.derbysoft.next/next-commons-core@3.0.1-SNAPSHOT"}, {"bom-ref": "124-com.derbysoft.next:next-commons-dto@3.0.1-SNAPSHOT", "type": "library", "group": "com.derbysoft.next", "name": "com.derbysoft.next:next-commons-dto", "version": "3.0.1-SNAPSHOT", "purl": "pkg:maven/com.derbysoft.next/next-commons-dto@3.0.1-SNAPSHOT"}, {"bom-ref": "125-com.derbysoft.next:next-commons-hibernate@3.0.1-SNAPSHOT", "type": "library", "group": "com.derbysoft.next", "name": "com.derbysoft.next:next-commons-hibernate", "version": "3.0.1-SNAPSHOT", "purl": "pkg:maven/com.derbysoft.next/next-commons-hibernate@3.0.1-SNAPSHOT"}, {"bom-ref": "126-org.springframework.boot:spring-boot-starter-data-jpa@3.4.1", "type": "library", "group": "org.springframework.boot", "name": "org.springframework.boot:spring-boot-starter-data-jpa", "version": "3.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.springframework.boot/spring-boot-starter-data-jpa@3.4.1"}, {"bom-ref": "127-org.apache.commons:commons-lang3@3.17.0", "type": "library", "group": "org.apache.commons", "name": "org.apache.commons:commons-lang3", "version": "3.17.0", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.commons/commons-lang3@3.17.0"}, {"bom-ref": "128-com.google.code.gson:gson@2.11.0", "type": "library", "group": "com.google.code.gson", "name": "com.google.code.gson:gson", "version": "2.11.0", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/com.google.code.gson/gson@2.11.0"}, {"bom-ref": "129-com.derby.nuke:nuke-logback-kafka-appender@1.7.0", "type": "library", "group": "com.derby.nuke", "name": "com.derby.nuke:nuke-logback-kafka-appender", "version": "1.7.0", "purl": "pkg:maven/com.derby.nuke/nuke-logback-kafka-appender@1.7.0"}, {"bom-ref": "130-org.springdoc:springdoc-openapi-starter-webmvc-ui@2.8.4", "type": "library", "group": "org.springdoc", "name": "org.springdoc:springdoc-openapi-starter-webmvc-ui", "version": "2.8.4", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.springdoc/springdoc-openapi-starter-webmvc-ui@2.8.4"}, {"bom-ref": "131-com.derbysoft.servreg:client@1.1.12", "type": "library", "group": "com.derbysoft.servreg", "name": "com.derbysoft.servreg:client", "version": "1.1.12", "purl": "pkg:maven/com.derbysoft.servreg/client@1.1.12"}, {"bom-ref": "132-org.mongodb:mongodb-driver-sync@5.2.1", "type": "library", "group": "org.mongodb", "name": "org.mongodb:mongodb-driver-sync", "version": "5.2.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.mongodb/mongodb-driver-sync@5.2.1"}, {"bom-ref": "133-org.springframework.data:spring-data-mongodb@4.4.1", "type": "library", "group": "org.springframework.data", "name": "org.springframework.data:spring-data-mongodb", "version": "4.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.springframework.data/spring-data-mongodb@4.4.1"}, {"bom-ref": "134-org.springframework.boot:spring-boot-actuator-autoconfigure@3.4.1", "type": "library", "group": "org.springframework.boot", "name": "org.springframework.boot:spring-boot-actuator-autoconfigure", "version": "3.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.springframework.boot/spring-boot-actuator-autoconfigure@3.4.1"}, {"bom-ref": "135-io.micrometer:micrometer-observation@1.14.2", "type": "library", "group": "io.micrometer", "name": "io.micrometer:micrometer-observation", "version": "1.14.2", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/io.micrometer/micrometer-observation@1.14.2"}, {"bom-ref": "136-io.micrometer:micrometer-jakarta9@1.14.2", "type": "library", "group": "io.micrometer", "name": "io.micrometer:micrometer-jakarta9", "version": "1.14.2", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/io.micrometer/micrometer-jakarta9@1.14.2"}, {"bom-ref": "137-org.springframework.cloud:<EMAIL>", "type": "library", "group": "org.springframework.cloud", "name": "org.springframework.cloud:spring-cloud-starter-sleuth", "version": "2.2.8.RELEASE", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.springframework.cloud/<EMAIL>"}, {"bom-ref": "138-org.springframework.cloud:<EMAIL>", "type": "library", "group": "org.springframework.cloud", "name": "org.springframework.cloud:spring-cloud-sleuth-zipkin", "version": "2.2.8.RELEASE", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.springframework.cloud/<EMAIL>"}, {"bom-ref": "139-com.google.guava:failureaccess@1.0.1", "type": "library", "group": "com.google.guava", "name": "com.google.guava:failureaccess", "version": "1.0.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/com.google.guava/failureaccess@1.0.1"}, {"bom-ref": "140-com.google.guava:listenablefuture@9999.0-empty-to-avoid-conflict-with-guava", "type": "library", "group": "com.google.guava", "name": "com.google.guava:listenablefuture", "version": "9999.0-empty-to-avoid-conflict-with-guava", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/com.google.guava/listenablefuture@9999.0-empty-to-avoid-conflict-with-guava"}, {"bom-ref": "141-com.google.code.findbugs:jsr305@3.0.2", "type": "library", "group": "com.google.code.findbugs", "name": "com.google.code.findbugs:jsr305", "version": "3.0.2", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/com.google.code.findbugs/jsr305@3.0.2"}, {"bom-ref": "142-org.checkerframework:checker-qual@3.12.0", "type": "library", "group": "org.checkerframework", "name": "org.checkerframework:checker-qual", "version": "3.12.0", "licenses": [{"expression": "MIT"}], "purl": "pkg:maven/org.checkerframework/checker-qual@3.12.0"}, {"bom-ref": "143-com.google.errorprone:error_prone_annotations@2.11.0", "type": "library", "group": "com.google.errorprone", "name": "com.google.errorprone:error_prone_annotations", "version": "2.11.0", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/com.google.errorprone/error_prone_annotations@2.11.0"}, {"bom-ref": "144-com.google.j2objc:j2objc-annotations@1.3", "type": "library", "group": "com.google.j2objc", "name": "com.google.j2objc:j2objc-annotations", "version": "1.3", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/com.google.j2objc/j2objc-annotations@1.3"}, {"bom-ref": "145-org.springframework.cloud:spring-cloud-openfeign-core@4.2.0", "type": "library", "group": "org.springframework.cloud", "name": "org.springframework.cloud:spring-cloud-openfeign-core", "version": "4.2.0", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.springframework.cloud/spring-cloud-openfeign-core@4.2.0"}, {"bom-ref": "146-org.springframework.cloud:spring-cloud-commons@4.2.0", "type": "library", "group": "org.springframework.cloud", "name": "org.springframework.cloud:spring-cloud-commons", "version": "4.2.0", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.springframework.cloud/spring-cloud-commons@4.2.0"}, {"bom-ref": "147-io.github.openfeign:feign-core@13.5", "type": "library", "group": "io.github.openfeign", "name": "io.github.openfeign:feign-core", "version": "13.5", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/io.github.openfeign/feign-core@13.5"}, {"bom-ref": "148-io.github.openfeign:feign-slf4j@13.5", "type": "library", "group": "io.github.openfeign", "name": "io.github.openfeign:feign-slf4j", "version": "13.5", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/io.github.openfeign/feign-slf4j@13.5"}, {"bom-ref": "149-com.squareup:javapoet@1.13.0", "type": "library", "group": "com.squareup", "name": "com.squareup:javapoet", "version": "1.13.0", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/com.squareup/javapoet@1.13.0"}, {"bom-ref": "150-com.google.auto.service:auto-service@1.1.1", "type": "library", "group": "com.google.auto.service", "name": "com.google.auto.service:auto-service", "version": "1.1.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/com.google.auto.service/auto-service@1.1.1"}, {"bom-ref": "151-org.springframework.boot:spring-boot-autoconfigure@3.4.1", "type": "library", "group": "org.springframework.boot", "name": "org.springframework.boot:spring-boot-autoconfigure", "version": "3.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.springframework.boot/spring-boot-autoconfigure@3.4.1"}, {"bom-ref": "152-com.derbysoft.schedulecenter:schedulecenter-protocol@1.2.4", "type": "library", "group": "com.derbysoft.schedulecenter", "name": "com.derbysoft.schedulecenter:schedulecenter-protocol", "version": "1.2.4", "purl": "pkg:maven/com.derbysoft.schedulecenter/schedulecenter-protocol@1.2.4"}, {"bom-ref": "153-org.slf4j:slf4j-api@2.0.16", "type": "library", "group": "org.slf4j", "name": "org.slf4j:slf4j-api", "version": "2.0.16", "licenses": [{"expression": "MIT"}], "purl": "pkg:maven/org.slf4j/slf4j-api@2.0.16"}, {"bom-ref": "154-com.squareup.okio:okio@2.8.0", "type": "library", "group": "com.squareup.okio", "name": "com.squareup.okio:okio", "version": "2.8.0", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/com.squareup.okio/okio@2.8.0"}, {"bom-ref": "155-org.jetbrains.kotlin:kotlin-stdlib-jdk8@1.9.25", "type": "library", "group": "org.jetbrains.kotlin", "name": "org.jetbrains.kotlin:kotlin-stdlib-jdk8", "version": "1.9.25", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.jetbrains.kotlin/kotlin-stdlib-jdk8@1.9.25"}, {"bom-ref": "156-org.springdoc:springdoc-openapi-ui@1.7.0", "type": "library", "group": "org.springdoc", "name": "org.springdoc:springdoc-openapi-ui", "version": "1.7.0", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.springdoc/springdoc-openapi-ui@1.7.0"}, {"bom-ref": "157-org.apache.tomcat.embed:tomcat-embed-el@10.1.34", "type": "library", "group": "org.apache.tomcat.embed", "name": "org.apache.tomcat.embed:tomcat-embed-el", "version": "10.1.34", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.tomcat.embed/tomcat-embed-el@10.1.34"}, {"bom-ref": "158-org.hibernate.validator:<EMAIL>", "type": "library", "group": "org.hibernate.validator", "name": "org.hibernate.validator:hibernate-validator", "version": "8.0.2.Final", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.hibernate.validator/<EMAIL>"}, {"bom-ref": "159-org.multiverse:multiverse-core@0.7.0", "type": "library", "group": "org.multiverse", "name": "org.multiverse:multiverse-core", "version": "0.7.0", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.multiverse/multiverse-core@0.7.0"}, {"bom-ref": "160-org.codehaus.jsr166-mirror:jsr166y@1.7.0", "type": "library", "group": "org.codehaus.jsr166-mirror", "name": "org.codehaus.jsr166-mirror:jsr166y", "version": "1.7.0", "licenses": [{"expression": "Unknown"}], "purl": "pkg:maven/org.codehaus.jsr166-mirror/jsr166y@1.7.0"}, {"bom-ref": "161-commons-codec:commons-codec@1.17.1", "type": "library", "group": "commons-codec", "name": "commons-codec:commons-codec", "version": "1.17.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/commons-codec/commons-codec@1.17.1"}, {"bom-ref": "162-org.apache.commons:commons-math3@3.6.1", "type": "library", "group": "org.apache.commons", "name": "org.apache.commons:commons-math3", "version": "3.6.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.commons/commons-math3@3.6.1"}, {"bom-ref": "163-com.zaxxer:SparseBitSet@1.3", "type": "library", "group": "com.zaxxer", "name": "com.zaxxer:SparseBitSet", "version": "1.3", "purl": "pkg:maven/com.zaxxer/SparseBitSet@1.3"}, {"bom-ref": "164-org.apache.logging.log4j:log4j-api@2.24.3", "type": "library", "group": "org.apache.logging.log4j", "name": "org.apache.logging.log4j:log4j-api", "version": "2.24.3", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.logging.log4j/log4j-api@2.24.3"}, {"bom-ref": "165-org.apache.poi:poi-ooxml-lite@5.2.5", "type": "library", "group": "org.apache.poi", "name": "org.apache.poi:poi-ooxml-lite", "version": "5.2.5", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.poi/poi-ooxml-lite@5.2.5"}, {"bom-ref": "166-org.apache.xmlbeans:xmlbeans@5.2.0", "type": "library", "group": "org.apache.xmlbeans", "name": "org.apache.xmlbeans:xmlbeans", "version": "5.2.0", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.xmlbeans/xmlbeans@5.2.0"}, {"bom-ref": "167-org.apache.commons:commons-compress@1.26.1", "type": "library", "group": "org.apache.commons", "name": "org.apache.commons:commons-compress", "version": "1.26.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.commons/commons-compress@1.26.1"}, {"bom-ref": "168-com.github.virtuald:curvesapi@1.08", "type": "library", "group": "com.github.virtuald", "name": "com.github.virtuald:curvesapi", "version": "1.08", "licenses": [{"expression": "BSD-2-<PERSON><PERSON>"}], "purl": "pkg:maven/com.github.virtuald/curvesapi@1.08"}, {"bom-ref": "169-software.amazon.awssdk:aws-xml-protocol@2.29.52", "type": "library", "group": "software.amazon.awssdk", "name": "software.amazon.awssdk:aws-xml-protocol", "version": "2.29.52", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/software.amazon.awssdk/aws-xml-protocol@2.29.52"}, {"bom-ref": "170-software.amazon.awssdk:protocol-core@2.29.52", "type": "library", "group": "software.amazon.awssdk", "name": "software.amazon.awssdk:protocol-core", "version": "2.29.52", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/software.amazon.awssdk/protocol-core@2.29.52"}, {"bom-ref": "171-software.amazon.awssdk:arns@2.29.52", "type": "library", "group": "software.amazon.awssdk", "name": "software.amazon.awssdk:arns", "version": "2.29.52", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/software.amazon.awssdk/arns@2.29.52"}, {"bom-ref": "172-software.amazon.awssdk:profiles@2.29.52", "type": "library", "group": "software.amazon.awssdk", "name": "software.amazon.awssdk:profiles", "version": "2.29.52", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/software.amazon.awssdk/profiles@2.29.52"}, {"bom-ref": "173-software.amazon.awssdk:crt-core@2.29.52", "type": "library", "group": "software.amazon.awssdk", "name": "software.amazon.awssdk:crt-core", "version": "2.29.52", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/software.amazon.awssdk/crt-core@2.29.52"}, {"bom-ref": "174-software.amazon.awssdk:http-auth@2.29.52", "type": "library", "group": "software.amazon.awssdk", "name": "software.amazon.awssdk:http-auth", "version": "2.29.52", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/software.amazon.awssdk/http-auth@2.29.52"}, {"bom-ref": "175-software.amazon.awssdk:identity-spi@2.29.52", "type": "library", "group": "software.amazon.awssdk", "name": "software.amazon.awssdk:identity-spi", "version": "2.29.52", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/software.amazon.awssdk/identity-spi@2.29.52"}, {"bom-ref": "176-software.amazon.awssdk:http-auth-spi@2.29.52", "type": "library", "group": "software.amazon.awssdk", "name": "software.amazon.awssdk:http-auth-spi", "version": "2.29.52", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/software.amazon.awssdk/http-auth-spi@2.29.52"}, {"bom-ref": "177-software.amazon.awssdk:http-auth-aws@2.29.52", "type": "library", "group": "software.amazon.awssdk", "name": "software.amazon.awssdk:http-auth-aws", "version": "2.29.52", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/software.amazon.awssdk/http-auth-aws@2.29.52"}, {"bom-ref": "178-software.amazon.awssdk:checksums@2.29.52", "type": "library", "group": "software.amazon.awssdk", "name": "software.amazon.awssdk:checksums", "version": "2.29.52", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/software.amazon.awssdk/checksums@2.29.52"}, {"bom-ref": "179-software.amazon.awssdk:checksums-spi@2.29.52", "type": "library", "group": "software.amazon.awssdk", "name": "software.amazon.awssdk:checksums-spi", "version": "2.29.52", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/software.amazon.awssdk/checksums-spi@2.29.52"}, {"bom-ref": "180-software.amazon.awssdk:retries-spi@2.29.52", "type": "library", "group": "software.amazon.awssdk", "name": "software.amazon.awssdk:retries-spi", "version": "2.29.52", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/software.amazon.awssdk/retries-spi@2.29.52"}, {"bom-ref": "181-software.amazon.awssdk:sdk-core@2.29.52", "type": "library", "group": "software.amazon.awssdk", "name": "software.amazon.awssdk:sdk-core", "version": "2.29.52", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/software.amazon.awssdk/sdk-core@2.29.52"}, {"bom-ref": "182-software.amazon.awssdk:auth@2.29.52", "type": "library", "group": "software.amazon.awssdk", "name": "software.amazon.awssdk:auth", "version": "2.29.52", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/software.amazon.awssdk/auth@2.29.52"}, {"bom-ref": "183-software.amazon.awssdk:http-client-spi@2.29.52", "type": "library", "group": "software.amazon.awssdk", "name": "software.amazon.awssdk:http-client-spi", "version": "2.29.52", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/software.amazon.awssdk/http-client-spi@2.29.52"}, {"bom-ref": "184-software.amazon.awssdk:regions@2.29.52", "type": "library", "group": "software.amazon.awssdk", "name": "software.amazon.awssdk:regions", "version": "2.29.52", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/software.amazon.awssdk/regions@2.29.52"}, {"bom-ref": "185-software.amazon.awssdk:annotations@2.29.52", "type": "library", "group": "software.amazon.awssdk", "name": "software.amazon.awssdk:annotations", "version": "2.29.52", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/software.amazon.awssdk/annotations@2.29.52"}, {"bom-ref": "186-software.amazon.awssdk:utils@2.29.52", "type": "library", "group": "software.amazon.awssdk", "name": "software.amazon.awssdk:utils", "version": "2.29.52", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/software.amazon.awssdk/utils@2.29.52"}, {"bom-ref": "187-software.amazon.awssdk:aws-core@2.29.52", "type": "library", "group": "software.amazon.awssdk", "name": "software.amazon.awssdk:aws-core", "version": "2.29.52", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/software.amazon.awssdk/aws-core@2.29.52"}, {"bom-ref": "188-software.amazon.awssdk:metrics-spi@2.29.52", "type": "library", "group": "software.amazon.awssdk", "name": "software.amazon.awssdk:metrics-spi", "version": "2.29.52", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/software.amazon.awssdk/metrics-spi@2.29.52"}, {"bom-ref": "189-software.amazon.awssdk:json-utils@2.29.52", "type": "library", "group": "software.amazon.awssdk", "name": "software.amazon.awssdk:json-utils", "version": "2.29.52", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/software.amazon.awssdk/json-utils@2.29.52"}, {"bom-ref": "190-software.amazon.awssdk:endpoints-spi@2.29.52", "type": "library", "group": "software.amazon.awssdk", "name": "software.amazon.awssdk:endpoints-spi", "version": "2.29.52", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/software.amazon.awssdk/endpoints-spi@2.29.52"}, {"bom-ref": "191-software.amazon.awssdk:apache-client@2.29.52", "type": "library", "group": "software.amazon.awssdk", "name": "software.amazon.awssdk:apache-client", "version": "2.29.52", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/software.amazon.awssdk/apache-client@2.29.52"}, {"bom-ref": "192-software.amazon.awssdk:netty-nio-client@2.29.52", "type": "library", "group": "software.amazon.awssdk", "name": "software.amazon.awssdk:netty-nio-client", "version": "2.29.52", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/software.amazon.awssdk/netty-nio-client@2.29.52"}, {"bom-ref": "193-software.amazon.awssdk:aws-query-protocol@2.21.40", "type": "library", "group": "software.amazon.awssdk", "name": "software.amazon.awssdk:aws-query-protocol", "version": "2.21.40", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/software.amazon.awssdk/aws-query-protocol@2.21.40"}, {"bom-ref": "194-jakarta.activation:jakarta.activation-api@2.1.3", "type": "library", "group": "jakarta.activation", "name": "jakarta.activation:jakarta.activation-api", "version": "2.1.3", "licenses": [{"expression": "EDL-1.0"}], "purl": "pkg:maven/jakarta.activation/jakarta.activation-api@2.1.3"}, {"bom-ref": "195-org.glassfish.jaxb:jaxb-core@4.0.5", "type": "library", "group": "org.glassfish.jaxb", "name": "org.glassfish.jaxb:jaxb-core", "version": "4.0.5", "licenses": [{"expression": "EDL-1.0"}], "purl": "pkg:maven/org.glassfish.jaxb/jaxb-core@4.0.5"}, {"bom-ref": "196-org.springframework.boot:spring-boot@3.4.1", "type": "library", "group": "org.springframework.boot", "name": "org.springframework.boot:spring-boot", "version": "3.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.springframework.boot/spring-boot@3.4.1"}, {"bom-ref": "197-org.springframework.boot:spring-boot-starter-logging@3.4.1", "type": "library", "group": "org.springframework.boot", "name": "org.springframework.boot:spring-boot-starter-logging", "version": "3.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.springframework.boot/spring-boot-starter-logging@3.4.1"}, {"bom-ref": "198-org.springframework:spring-core@6.2.1", "type": "library", "group": "org.springframework", "name": "org.springframework:spring-core", "version": "6.2.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.springframework/spring-core@6.2.1"}, {"bom-ref": "199-org.yaml:snakeyaml@2.3", "type": "library", "group": "org.yaml", "name": "org.yaml:snakeyaml", "version": "2.3", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.yaml/snakeyaml@2.3"}, {"bom-ref": "200-com.fasterxml.jackson.core:jackson-databind@2.18.2", "type": "library", "group": "com.fasterxml.jackson.core", "name": "com.fasterxml.jackson.core:jackson-databind", "version": "2.18.2", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/com.fasterxml.jackson.core/jackson-databind@2.18.2"}, {"bom-ref": "201-com.fasterxml.jackson.datatype:jackson-datatype-jdk8@2.18.2", "type": "library", "group": "com.fasterxml.jackson.datatype", "name": "com.fasterxml.jackson.datatype:jackson-datatype-jdk8", "version": "2.18.2", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/com.fasterxml.jackson.datatype/jackson-datatype-jdk8@2.18.2"}, {"bom-ref": "202-com.fasterxml.jackson.datatype:jackson-datatype-jsr310@2.18.2", "type": "library", "group": "com.fasterxml.jackson.datatype", "name": "com.fasterxml.jackson.datatype:jackson-datatype-jsr310", "version": "2.18.2", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/com.fasterxml.jackson.datatype/jackson-datatype-jsr310@2.18.2"}, {"bom-ref": "203-com.fasterxml.jackson.module:jackson-module-parameter-names@2.18.2", "type": "library", "group": "com.fasterxml.jackson.module", "name": "com.fasterxml.jackson.module:jackson-module-parameter-names", "version": "2.18.2", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/com.fasterxml.jackson.module/jackson-module-parameter-names@2.18.2"}, {"bom-ref": "204-org.springframework:spring-beans@6.2.1", "type": "library", "group": "org.springframework", "name": "org.springframework:spring-beans", "version": "6.2.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.springframework/spring-beans@6.2.1"}, {"bom-ref": "205-org.springframework:spring-aop@6.2.1", "type": "library", "group": "org.springframework", "name": "org.springframework:spring-aop", "version": "6.2.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.springframework/spring-aop@6.2.1"}, {"bom-ref": "206-org.springframework:spring-context@6.2.1", "type": "library", "group": "org.springframework", "name": "org.springframework:spring-context", "version": "6.2.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.springframework/spring-context@6.2.1"}, {"bom-ref": "207-org.springframework:spring-expression@6.2.1", "type": "library", "group": "org.springframework", "name": "org.springframework:spring-expression", "version": "6.2.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.springframework/spring-expression@6.2.1"}, {"bom-ref": "208-org.springframework.cloud:spring-cloud-context@4.2.0", "type": "library", "group": "org.springframework.cloud", "name": "org.springframework.cloud:spring-cloud-context", "version": "4.2.0", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.springframework.cloud/spring-cloud-context@4.2.0"}, {"bom-ref": "209-org.bouncycastle:bcprov-jdk18on@1.78", "type": "library", "group": "org.bouncycastle", "name": "org.bouncycastle:bcprov-jdk18on", "version": "1.78", "licenses": [{"expression": "MIT"}], "purl": "pkg:maven/org.bouncycastle/bcprov-jdk18on@1.78"}, {"bom-ref": "210-org.apache.httpcomponents.client5:httpclient5@5.4.1", "type": "library", "group": "org.apache.httpcomponents.client5", "name": "org.apache.httpcomponents.client5:httpclient5", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.httpcomponents.client5/httpclient5@5.4.1"}, {"bom-ref": "211-com.thoughtworks.xstream:xstream@1.4.20", "type": "library", "group": "com.thoughtworks.xstream", "name": "com.thoughtworks.xstream:xstream", "version": "1.4.20", "licenses": [{"expression": "BSD-3-<PERSON><PERSON>"}], "purl": "pkg:maven/com.thoughtworks.xstream/xstream@1.4.20"}, {"bom-ref": "212-jakarta.ws.rs:jakarta.ws.rs-api@3.1.0", "type": "library", "group": "jakarta.ws.rs", "name": "jakarta.ws.rs:jakarta.ws.rs-api", "version": "3.1.0", "licenses": [{"expression": "(EPL-2.0 OR GPL-2.0-with-classpath-exception)"}], "purl": "pkg:maven/jakarta.ws.rs/jakarta.ws.rs-api@3.1.0"}, {"bom-ref": "213-jakarta.inject:jakarta.inject-api@2.0.1", "type": "library", "group": "jakarta.inject", "name": "jakarta.inject:jakarta.inject-api", "version": "2.0.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/jakarta.inject/jakarta.inject-api@2.0.1"}, {"bom-ref": "214-com.netflix.spectator:spectator-api@1.7.3", "type": "library", "group": "com.netflix.spectator", "name": "com.netflix.spectator:spectator-api", "version": "1.7.3", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/com.netflix.spectator/spectator-api@1.7.3"}, {"bom-ref": "215-org.apache.httpcomponents:httpclient@4.5.3", "type": "library", "group": "org.apache.httpcomponents", "name": "org.apache.httpcomponents:httpclient", "version": "4.5.3", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.httpcomponents/httpclient@4.5.3"}, {"bom-ref": "216-commons-configuration:commons-configuration@1.10", "type": "library", "group": "commons-configuration", "name": "commons-configuration:commons-configuration", "version": "1.10", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/commons-configuration/commons-configuration@1.10"}, {"bom-ref": "217-com.fasterxml.jackson.core:jackson-annotations@2.18.2", "type": "library", "group": "com.fasterxml.jackson.core", "name": "com.fasterxml.jackson.core:jackson-annotations", "version": "2.18.2", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/com.fasterxml.jackson.core/jackson-annotations@2.18.2"}, {"bom-ref": "218-com.fasterxml.jackson.core:jackson-core@2.18.2", "type": "library", "group": "com.fasterxml.jackson.core", "name": "com.fasterxml.jackson.core:jackson-core", "version": "2.18.2", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/com.fasterxml.jackson.core/jackson-core@2.18.2"}, {"bom-ref": "219-com.netflix.netflix-commons:netflix-eventbus@0.3.0", "type": "library", "group": "com.netflix.netflix-commons", "name": "com.netflix.netflix-commons:netflix-eventbus", "version": "0.3.0", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/com.netflix.netflix-commons/netflix-eventbus@0.3.0"}, {"bom-ref": "220-javax.annotation:javax.annotation-api@1.2", "type": "library", "group": "javax.annotation", "name": "javax.annotation:javax.annotation-api", "version": "1.2", "licenses": [{"expression": "(CDDL-1.1 OR GPL-2.0-with-classpath-exception)"}], "purl": "pkg:maven/javax.annotation/javax.annotation-api@1.2"}, {"bom-ref": "221-org.codehaus.jettison:jettison@1.5.4", "type": "library", "group": "org.codehaus.jettison", "name": "org.codehaus.jettison:jettison", "version": "1.5.4", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.codehaus.jettison/jettison@1.5.4"}, {"bom-ref": "222-org.springframework.cloud:spring-cloud-loadbalancer@4.2.0", "type": "library", "group": "org.springframework.cloud", "name": "org.springframework.cloud:spring-cloud-loadbalancer", "version": "4.2.0", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.springframework.cloud/spring-cloud-loadbalancer@4.2.0"}, {"bom-ref": "223-org.springframework.boot:spring-boot-starter-cache@3.4.1", "type": "library", "group": "org.springframework.boot", "name": "org.springframework.boot:spring-boot-starter-cache", "version": "3.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.springframework.boot/spring-boot-starter-cache@3.4.1"}, {"bom-ref": "224-com.stoyanr:evictor@1.0.0", "type": "library", "group": "com.stoyanr", "name": "com.stoyanr:evictor", "version": "1.0.0", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/com.stoyanr/evictor@1.0.0"}, {"bom-ref": "225-com.derbysoft.log:log-sdk@1.5.0", "type": "library", "group": "com.derbysoft.log", "name": "com.derbysoft.log:log-sdk", "version": "1.5.0", "purl": "pkg:maven/com.derbysoft.log/log-sdk@1.5.0"}, {"bom-ref": "226-ch.qos.logback:logback-classic@1.5.12", "type": "library", "group": "ch.qos.logback", "name": "ch.qos.logback:logback-classic", "version": "1.5.12", "licenses": [{"expression": "(EPL-1.0 OR LGPL-2.1)"}], "purl": "pkg:maven/ch.qos.logback/logback-classic@1.5.12"}, {"bom-ref": "227-joda-time:joda-time@2.10.10", "type": "library", "group": "joda-time", "name": "joda-time:joda-time", "version": "2.10.10", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/joda-time/joda-time@2.10.10"}, {"bom-ref": "228-org.apache.commons:commons-text@1.10.0", "type": "library", "group": "org.apache.commons", "name": "org.apache.commons:commons-text", "version": "1.10.0", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.commons/commons-text@1.10.0"}, {"bom-ref": "229-com.fatboyindustrial.gson-javatime-serialisers:gson-javatime-serialisers@1.1.2", "type": "library", "group": "com.fatboyindustrial.gson-javatime-serialisers", "name": "com.fatboyindustrial.gson-javatime-serialisers:gson-javatime-serialisers", "version": "1.1.2", "licenses": [{"expression": "MIT"}], "purl": "pkg:maven/com.fatboyindustrial.gson-javatime-serialisers/gson-javatime-serialisers@1.1.2"}, {"bom-ref": "230-jakarta.validation:jakarta.validation-api@3.0.2", "type": "library", "group": "jakarta.validation", "name": "jakarta.validation:jakarta.validation-api", "version": "3.0.2", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/jakarta.validation/jakarta.validation-api@3.0.2"}, {"bom-ref": "231-commons-beanutils:commons-beanutils@1.9.4", "type": "library", "group": "commons-beanutils", "name": "commons-beanutils:commons-beanutils", "version": "1.9.4", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/commons-beanutils/commons-beanutils@1.9.4"}, {"bom-ref": "232-org.apache.shardingsphere:shardingsphere-jdbc-core@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-jdbc-core", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-jdbc-core@5.4.1"}, {"bom-ref": "233-org.springframework.boot:spring-boot-starter-jdbc@3.4.1", "type": "library", "group": "org.springframework.boot", "name": "org.springframework.boot:spring-boot-starter-jdbc", "version": "3.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.springframework.boot/spring-boot-starter-jdbc@3.4.1"}, {"bom-ref": "234-org.hibernate.orm:<EMAIL>", "type": "library", "group": "org.hibernate.orm", "name": "org.hibernate.orm:hibernate-core", "version": "6.6.4.Final", "licenses": [{"expression": "LGPL-2.1"}], "purl": "pkg:maven/org.hibernate.orm/<EMAIL>"}, {"bom-ref": "235-org.springframework.data:spring-data-jpa@3.4.1", "type": "library", "group": "org.springframework.data", "name": "org.springframework.data:spring-data-jpa", "version": "3.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.springframework.data/spring-data-jpa@3.4.1"}, {"bom-ref": "236-org.springframework:spring-aspects@6.2.1", "type": "library", "group": "org.springframework", "name": "org.springframework:spring-aspects", "version": "6.2.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.springframework/spring-aspects@6.2.1"}, {"bom-ref": "237-ch.qos.logback:logback-core@1.5.12", "type": "library", "group": "ch.qos.logback", "name": "ch.qos.logback:logback-core", "version": "1.5.12", "licenses": [{"expression": "(EPL-1.0 OR LGPL-2.1)"}], "purl": "pkg:maven/ch.qos.logback/logback-core@1.5.12"}, {"bom-ref": "238-javax.servlet:javax.servlet-api@3.1.0", "type": "library", "group": "javax.servlet", "name": "javax.servlet:javax.servlet-api", "version": "3.1.0", "licenses": [{"expression": "(CDDL-1.1 OR GPL-2.0-with-classpath-exception)"}], "purl": "pkg:maven/javax.servlet/javax.servlet-api@3.1.0"}, {"bom-ref": "239-org.springdoc:springdoc-openapi-starter-webmvc-api@2.8.4", "type": "library", "group": "org.springdoc", "name": "org.springdoc:springdoc-openapi-starter-webmvc-api", "version": "2.8.4", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.springdoc/springdoc-openapi-starter-webmvc-api@2.8.4"}, {"bom-ref": "240-org.webjars:swagger-ui@5.18.2", "type": "library", "group": "org.webjars", "name": "org.webjars:swagger-ui", "version": "5.18.2", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.webjars/swagger-ui@5.18.2"}, {"bom-ref": "241-org.webjars:webjars-locator-lite@1.0.1", "type": "library", "group": "org.webjars", "name": "org.webjars:webjars-locator-lite", "version": "1.0.1", "licenses": [{"expression": "MIT"}], "purl": "pkg:maven/org.webjars/webjars-locator-lite@1.0.1"}, {"bom-ref": "242-com.derbysoft.servreg:registry@1.1.12", "type": "library", "group": "com.derbysoft.servreg", "name": "com.derbysoft.servreg:registry", "version": "1.1.12", "purl": "pkg:maven/com.derbysoft.servreg/registry@1.1.12"}, {"bom-ref": "243-org.jetbrains:annotations@RELEASE", "type": "library", "group": "org.jetbrains", "name": "org.jetbrains:annotations", "version": "RELEASE", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.jetbrains/annotations@RELEASE"}, {"bom-ref": "244-com.derbysoft:sdc-client@1.0.0-SNAPSHOT", "type": "library", "group": "com.derbysoft", "name": "com.derbysoft:sdc-client", "version": "1.0.0-SNAPSHOT", "purl": "pkg:maven/com.derbysoft/sdc-client@1.0.0-SNAPSHOT"}, {"bom-ref": "245-com.google.inject:guice@4.1.0", "type": "library", "group": "com.google.inject", "name": "com.google.inject:guice", "version": "4.1.0", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/com.google.inject/guice@4.1.0"}, {"bom-ref": "246-com.fasterxml.jackson.dataformat:jackson-dataformat-cbor@2.18.2", "type": "library", "group": "com.fasterxml.jackson.dataformat", "name": "com.fasterxml.jackson.dataformat:jackson-dataformat-cbor", "version": "2.18.2", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/com.fasterxml.jackson.dataformat/jackson-dataformat-cbor@2.18.2"}, {"bom-ref": "247-io.grpc:grpc-netty-shaded@1.38.0", "type": "library", "group": "io.grpc", "name": "io.grpc:grpc-netty-shaded", "version": "1.38.0", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/io.grpc/grpc-netty-shaded@1.38.0"}, {"bom-ref": "248-io.grpc:grpc-protobuf@1.38.0", "type": "library", "group": "io.grpc", "name": "io.grpc:grpc-protobuf", "version": "1.38.0", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/io.grpc/grpc-protobuf@1.38.0"}, {"bom-ref": "249-com.google.protobuf:protobuf-java@3.19.6", "type": "library", "group": "com.google.protobuf", "name": "com.google.protobuf:protobuf-java", "version": "3.19.6", "licenses": [{"expression": "BSD-3-<PERSON><PERSON>"}], "purl": "pkg:maven/com.google.protobuf/protobuf-java@3.19.6"}, {"bom-ref": "250-io.grpc:grpc-stub@1.38.0", "type": "library", "group": "io.grpc", "name": "io.grpc:grpc-stub", "version": "1.38.0", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/io.grpc/grpc-stub@1.38.0"}, {"bom-ref": "251-com.googlecode.protobuf-java-format:protobuf-java-format@1.2", "type": "library", "group": "com.googlecode.protobuf-java-format", "name": "com.googlecode.protobuf-java-format:protobuf-java-format", "version": "1.2", "licenses": [{"expression": "BSD-3-<PERSON><PERSON>"}], "purl": "pkg:maven/com.googlecode.protobuf-java-format/protobuf-java-format@1.2"}, {"bom-ref": "252-org.mongodb:bson@5.2.1", "type": "library", "group": "org.mongodb", "name": "org.mongodb:bson", "version": "5.2.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.mongodb/bson@5.2.1"}, {"bom-ref": "253-org.mongodb:mongodb-driver-core@5.2.1", "type": "library", "group": "org.mongodb", "name": "org.mongodb:mongodb-driver-core", "version": "5.2.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.mongodb/mongodb-driver-core@5.2.1"}, {"bom-ref": "254-org.springframework:spring-tx@6.2.1", "type": "library", "group": "org.springframework", "name": "org.springframework:spring-tx", "version": "6.2.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.springframework/spring-tx@6.2.1"}, {"bom-ref": "255-org.springframework.data:spring-data-commons@3.4.1", "type": "library", "group": "org.springframework.data", "name": "org.springframework.data:spring-data-commons", "version": "3.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.springframework.data/spring-data-commons@3.4.1"}, {"bom-ref": "256-org.springframework.boot:spring-boot-actuator@3.4.1", "type": "library", "group": "org.springframework.boot", "name": "org.springframework.boot:spring-boot-actuator", "version": "3.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.springframework.boot/spring-boot-actuator@3.4.1"}, {"bom-ref": "257-io.micrometer:micrometer-commons@1.14.2", "type": "library", "group": "io.micrometer", "name": "io.micrometer:micrometer-commons", "version": "1.14.2", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/io.micrometer/micrometer-commons@1.14.2"}, {"bom-ref": "258-io.micrometer:micrometer-core@1.14.2", "type": "library", "group": "io.micrometer", "name": "io.micrometer:micrometer-core", "version": "1.14.2", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/io.micrometer/micrometer-core@1.14.2"}, {"bom-ref": "259-org.springframework.boot:spring-boot-starter-aop@3.4.1", "type": "library", "group": "org.springframework.boot", "name": "org.springframework.boot:spring-boot-starter-aop", "version": "3.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.springframework.boot/spring-boot-starter-aop@3.4.1"}, {"bom-ref": "260-org.springframework.cloud:<EMAIL>", "type": "library", "group": "org.springframework.cloud", "name": "org.springframework.cloud:spring-cloud-sleuth-core", "version": "2.2.8.RELEASE", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.springframework.cloud/<EMAIL>"}, {"bom-ref": "261-io.zipkin.zipkin2:zipkin@2.27.1", "type": "library", "group": "io.zipkin.zipkin2", "name": "io.zipkin.zipkin2:zipkin", "version": "2.27.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/io.zipkin.zipkin2/zipkin@2.27.1"}, {"bom-ref": "262-io.zipkin.reporter2:zipkin-reporter@3.4.3", "type": "library", "group": "io.zipkin.reporter2", "name": "io.zipkin.reporter2:zipkin-reporter", "version": "3.4.3", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/io.zipkin.reporter2/zipkin-reporter@3.4.3"}, {"bom-ref": "263-io.zipkin.reporter2:zipkin-reporter-brave@3.4.3", "type": "library", "group": "io.zipkin.reporter2", "name": "io.zipkin.reporter2:zipkin-reporter-brave", "version": "3.4.3", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/io.zipkin.reporter2/zipkin-reporter-brave@3.4.3"}, {"bom-ref": "264-io.zipkin.reporter2:zipkin-sender-kafka@3.4.3", "type": "library", "group": "io.zipkin.reporter2", "name": "io.zipkin.reporter2:zipkin-sender-kafka", "version": "3.4.3", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/io.zipkin.reporter2/zipkin-sender-kafka@3.4.3"}, {"bom-ref": "265-io.zipkin.reporter2:zipkin-sender-activemq-client@3.4.3", "type": "library", "group": "io.zipkin.reporter2", "name": "io.zipkin.reporter2:zipkin-sender-activemq-client", "version": "3.4.3", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/io.zipkin.reporter2/zipkin-sender-activemq-client@3.4.3"}, {"bom-ref": "266-io.zipkin.reporter2:zipkin-sender-amqp-client@3.4.3", "type": "library", "group": "io.zipkin.reporter2", "name": "io.zipkin.reporter2:zipkin-sender-amqp-client", "version": "3.4.3", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/io.zipkin.reporter2/zipkin-sender-amqp-client@3.4.3"}, {"bom-ref": "267-io.github.openfeign:feign-form-spring@13.5", "type": "library", "group": "io.github.openfeign", "name": "io.github.openfeign:feign-form-spring", "version": "13.5", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/io.github.openfeign/feign-form-spring@13.5"}, {"bom-ref": "268-org.springframework.security:spring-security-crypto@6.4.2", "type": "library", "group": "org.springframework.security", "name": "org.springframework.security:spring-security-crypto", "version": "6.4.2", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.springframework.security/spring-security-crypto@6.4.2"}, {"bom-ref": "269-com.google.auto.service:auto-service-annotations@1.1.1", "type": "library", "group": "com.google.auto.service", "name": "com.google.auto.service:auto-service-annotations", "version": "1.1.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/com.google.auto.service/auto-service-annotations@1.1.1"}, {"bom-ref": "270-com.google.auto:auto-common@1.2.1", "type": "library", "group": "com.google.auto", "name": "com.google.auto:auto-common", "version": "1.2.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/com.google.auto/auto-common@1.2.1"}, {"bom-ref": "271-com.alibaba:<PERSON><PERSON><PERSON>@1.2.76", "type": "library", "group": "com.alibaba", "name": "com.alibaba:fast<PERSON>son", "version": "1.2.76", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/com.alibaba/fastjson@1.2.76"}, {"bom-ref": "272-org.jetbrains.kotlin:kotlin-stdlib@1.9.25", "type": "library", "group": "org.jetbrains.kotlin", "name": "org.jetbrains.kotlin:kotlin-stdlib", "version": "1.9.25", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.jetbrains.kotlin/kotlin-stdlib@1.9.25"}, {"bom-ref": "273-org.jetbrains.kotlin:kotlin-stdlib-common@1.9.25", "type": "library", "group": "org.jetbrains.kotlin", "name": "org.jetbrains.kotlin:kotlin-stdlib-common", "version": "1.9.25", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.jetbrains.kotlin/kotlin-stdlib-common@1.9.25"}, {"bom-ref": "274-org.jetbrains.kotlin:kotlin-stdlib-jdk7@1.9.25", "type": "library", "group": "org.jetbrains.kotlin", "name": "org.jetbrains.kotlin:kotlin-stdlib-jdk7", "version": "1.9.25", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.jetbrains.kotlin/kotlin-stdlib-jdk7@1.9.25"}, {"bom-ref": "275-org.springdoc:springdoc-openapi-webmvc-core@1.7.0", "type": "library", "group": "org.springdoc", "name": "org.springdoc:springdoc-openapi-webmvc-core", "version": "1.7.0", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.springdoc/springdoc-openapi-webmvc-core@1.7.0"}, {"bom-ref": "276-org.jboss.logging:<EMAIL>", "type": "library", "group": "org.jboss.logging", "name": "org.jboss.logging:jboss-logging", "version": "3.6.1.Final", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.jboss.logging/<EMAIL>"}, {"bom-ref": "277-com.fasterxml:classmate@1.7.0", "type": "library", "group": "com.fasterxml", "name": "com.fasterxml:classmate", "version": "1.7.0", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/com.fasterxml/classmate@1.7.0"}, {"bom-ref": "278-org.reactivestreams:reactive-streams@1.0.4", "type": "library", "group": "org.reactivestreams", "name": "org.reactivestreams:reactive-streams", "version": "1.0.4", "licenses": [{"expression": "MIT-0"}], "purl": "pkg:maven/org.reactivestreams/reactive-streams@1.0.4"}, {"bom-ref": "279-software.amazon.awssdk:retries@2.29.52", "type": "library", "group": "software.amazon.awssdk", "name": "software.amazon.awssdk:retries", "version": "2.29.52", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/software.amazon.awssdk/retries@2.29.52"}, {"bom-ref": "280-software.amazon.awssdk:http-auth-aws-eventstream@2.29.52", "type": "library", "group": "software.amazon.awssdk", "name": "software.amazon.awssdk:http-auth-aws-eventstream", "version": "2.29.52", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/software.amazon.awssdk/http-auth-aws-eventstream@2.29.52"}, {"bom-ref": "281-software.amazon.eventstream:eventstream@1.0.1", "type": "library", "group": "software.amazon.eventstream", "name": "software.amazon.eventstream:eventstream", "version": "1.0.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/software.amazon.eventstream/eventstream@1.0.1"}, {"bom-ref": "282-software.amazon.awssdk:third-party-jackson-core@2.29.52", "type": "library", "group": "software.amazon.awssdk", "name": "software.amazon.awssdk:third-party-jackson-core", "version": "2.29.52", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/software.amazon.awssdk/third-party-jackson-core@2.29.52"}, {"bom-ref": "283-org.apache.httpcomponents:httpcore@4.4.16", "type": "library", "group": "org.apache.httpcomponents", "name": "org.apache.httpcomponents:httpcore", "version": "4.4.16", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.httpcomponents/httpcore@4.4.16"}, {"bom-ref": "284-io.netty:<EMAIL>", "type": "library", "group": "io.netty", "name": "io.netty:netty-codec-http", "version": "4.1.116.Final", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/io.netty/<EMAIL>"}, {"bom-ref": "285-io.netty:<EMAIL>", "type": "library", "group": "io.netty", "name": "io.netty:netty-codec-http2", "version": "4.1.116.Final", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/io.netty/<EMAIL>"}, {"bom-ref": "286-io.netty:<EMAIL>", "type": "library", "group": "io.netty", "name": "io.netty:netty-codec", "version": "4.1.116.Final", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/io.netty/<EMAIL>"}, {"bom-ref": "287-io.netty:<EMAIL>", "type": "library", "group": "io.netty", "name": "io.netty:netty-transport", "version": "4.1.116.Final", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/io.netty/<EMAIL>"}, {"bom-ref": "288-io.netty:<EMAIL>", "type": "library", "group": "io.netty", "name": "io.netty:netty-common", "version": "4.1.116.Final", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/io.netty/<EMAIL>"}, {"bom-ref": "289-io.netty:<EMAIL>", "type": "library", "group": "io.netty", "name": "io.netty:netty-buffer", "version": "4.1.116.Final", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/io.netty/<EMAIL>"}, {"bom-ref": "290-io.netty:<EMAIL>", "type": "library", "group": "io.netty", "name": "io.netty:netty-handler", "version": "4.1.116.Final", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/io.netty/<EMAIL>"}, {"bom-ref": "291-io.netty:<EMAIL>", "type": "library", "group": "io.netty", "name": "io.netty:netty-transport-classes-epoll", "version": "4.1.116.Final", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/io.netty/<EMAIL>"}, {"bom-ref": "292-io.netty:<EMAIL>", "type": "library", "group": "io.netty", "name": "io.netty:netty-resolver", "version": "4.1.116.Final", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/io.netty/<EMAIL>"}, {"bom-ref": "293-org.eclipse.angus:angus-activation@2.0.2", "type": "library", "group": "org.eclipse.angus", "name": "org.eclipse.angus:angus-activation", "version": "2.0.2", "licenses": [{"expression": "EDL-1.0"}], "purl": "pkg:maven/org.eclipse.angus/angus-activation@2.0.2"}, {"bom-ref": "294-org.glassfish.jaxb:txw2@4.0.5", "type": "library", "group": "org.glassfish.jaxb", "name": "org.glassfish.jaxb:txw2", "version": "4.0.5", "licenses": [{"expression": "EDL-1.0"}], "purl": "pkg:maven/org.glassfish.jaxb/txw2@4.0.5"}, {"bom-ref": "295-com.sun.istack:istack-commons-runtime@4.1.2", "type": "library", "group": "com.sun.istack", "name": "com.sun.istack:istack-commons-runtime", "version": "4.1.2", "licenses": [{"expression": "EDL-1.0"}], "purl": "pkg:maven/com.sun.istack/istack-commons-runtime@4.1.2"}, {"bom-ref": "296-org.apache.logging.log4j:log4j-to-slf4j@2.24.3", "type": "library", "group": "org.apache.logging.log4j", "name": "org.apache.logging.log4j:log4j-to-slf4j", "version": "2.24.3", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.logging.log4j/log4j-to-slf4j@2.24.3"}, {"bom-ref": "297-org.slf4j:jul-to-slf4j@2.0.16", "type": "library", "group": "org.slf4j", "name": "org.slf4j:jul-to-slf4j", "version": "2.0.16", "licenses": [{"expression": "MIT"}], "purl": "pkg:maven/org.slf4j/jul-to-slf4j@2.0.16"}, {"bom-ref": "298-org.apache.httpcomponents.core5:httpcore5@5.3.1", "type": "library", "group": "org.apache.httpcomponents.core5", "name": "org.apache.httpcomponents.core5:httpcore5", "version": "5.3.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.httpcomponents.core5/httpcore5@5.3.1"}, {"bom-ref": "299-org.apache.httpcomponents.core5:httpcore5-h2@5.3.1", "type": "library", "group": "org.apache.httpcomponents.core5", "name": "org.apache.httpcomponents.core5:httpcore5-h2", "version": "5.3.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.httpcomponents.core5/httpcore5-h2@5.3.1"}, {"bom-ref": "300-io.github.x-stream:mxparser@1.2.2", "type": "library", "group": "io.github.x-stream", "name": "io.github.x-stream:mxparser", "version": "1.2.2", "licenses": [{"expression": "Indiana-University-Extreme-Lab-1.2"}], "purl": "pkg:maven/io.github.x-stream/mxparser@1.2.2"}, {"bom-ref": "301-commons-lang:commons-lang@2.6", "type": "library", "group": "commons-lang", "name": "commons-lang:commons-lang", "version": "2.6", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/commons-lang/commons-lang@2.6"}, {"bom-ref": "302-com.netflix.netflix-commons:netflix-infix@0.3.0", "type": "library", "group": "com.netflix.netflix-commons", "name": "com.netflix.netflix-commons:netflix-infix", "version": "0.3.0", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/com.netflix.netflix-commons/netflix-infix@0.3.0"}, {"bom-ref": "303-com.netflix.servo:servo-core@0.5.3", "type": "library", "group": "com.netflix.servo", "name": "com.netflix.servo:servo-core", "version": "0.5.3", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/com.netflix.servo/servo-core@0.5.3"}, {"bom-ref": "304-org.apache.commons:commons-math@2.2", "type": "library", "group": "org.apache.commons", "name": "org.apache.commons:commons-math", "version": "2.2", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.commons/commons-math@2.2"}, {"bom-ref": "305-io.projectreactor:reactor-core@3.7.1", "type": "library", "group": "io.projectreactor", "name": "io.projectreactor:reactor-core", "version": "3.7.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/io.projectreactor/reactor-core@3.7.1"}, {"bom-ref": "306-io.projectreactor.addons:reactor-extra@3.5.2", "type": "library", "group": "io.projectreactor.addons", "name": "io.projectreactor.addons:reactor-extra", "version": "3.5.2", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/io.projectreactor.addons/reactor-extra@3.5.2"}, {"bom-ref": "307-org.springframework:spring-context-support@6.2.1", "type": "library", "group": "org.springframework", "name": "org.springframework:spring-context-support", "version": "6.2.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.springframework/spring-context-support@6.2.1"}, {"bom-ref": "308-commons-logging:commons-logging@1.2", "type": "library", "group": "commons-logging", "name": "commons-logging:commons-logging", "version": "1.2", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/commons-logging/commons-logging@1.2"}, {"bom-ref": "309-commons-collections:commons-collections@3.2.2", "type": "library", "group": "commons-collections", "name": "commons-collections:commons-collections", "version": "3.2.2", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/commons-collections/commons-collections@3.2.2"}, {"bom-ref": "310-org.apache.shardingsphere:shardingsphere-transaction-core@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-transaction-core", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-transaction-core@5.4.1"}, {"bom-ref": "311-org.apache.shardingsphere:shardingsphere-global-clock-core@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-global-clock-core", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-global-clock-core@5.4.1"}, {"bom-ref": "312-org.apache.shardingsphere:shardingsphere-global-clock-tso-core@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-global-clock-tso-core", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-global-clock-tso-core@5.4.1"}, {"bom-ref": "313-org.apache.shardingsphere:shardingsphere-parser-sql-sql92@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-parser-sql-sql92", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-parser-sql-sql92@5.4.1"}, {"bom-ref": "314-org.apache.shardingsphere:shardingsphere-parser-sql-mysql@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-parser-sql-mysql", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-parser-sql-mysql@5.4.1"}, {"bom-ref": "315-org.apache.shardingsphere:shardingsphere-parser-sql-postgresql@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-parser-sql-postgresql", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-parser-sql-postgresql@5.4.1"}, {"bom-ref": "316-org.apache.shardingsphere:shardingsphere-parser-sql-oracle@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-parser-sql-oracle", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-parser-sql-oracle@5.4.1"}, {"bom-ref": "317-org.apache.shardingsphere:shardingsphere-parser-sql-sqlserver@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-parser-sql-sqlserver", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-parser-sql-sqlserver@5.4.1"}, {"bom-ref": "318-org.apache.shardingsphere:shardingsphere-parser-sql-opengauss@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-parser-sql-opengauss", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-parser-sql-opengauss@5.4.1"}, {"bom-ref": "319-org.apache.shardingsphere:shardingsphere-mysql-dialect-exception@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-mysql-dialect-exception", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-mysql-dialect-exception@5.4.1"}, {"bom-ref": "320-org.apache.shardingsphere:shardingsphere-postgresql-dialect-exception@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-postgresql-dialect-exception", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-postgresql-dialect-exception@5.4.1"}, {"bom-ref": "321-org.apache.shardingsphere:shardingsphere-authority-core@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-authority-core", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-authority-core@5.4.1"}, {"bom-ref": "322-org.apache.shardingsphere:shardingsphere-single-core@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-single-core", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-single-core@5.4.1"}, {"bom-ref": "323-org.apache.shardingsphere:shardingsphere-traffic-core@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-traffic-core", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-traffic-core@5.4.1"}, {"bom-ref": "324-org.apache.shardingsphere:shardingsphere-infra-context@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-infra-context", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-infra-context@5.4.1"}, {"bom-ref": "325-org.apache.shardingsphere:shardingsphere-standalone-mode-core@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-standalone-mode-core", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-standalone-mode-core@5.4.1"}, {"bom-ref": "326-org.apache.shardingsphere:shardingsphere-standalone-mode-repository-jdbc@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-standalone-mode-repository-jdbc", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-standalone-mode-repository-jdbc@5.4.1"}, {"bom-ref": "327-org.apache.shardingsphere:shardingsphere-cluster-mode-core@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-cluster-mode-core", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-cluster-mode-core@5.4.1"}, {"bom-ref": "328-org.apache.shardingsphere:shardingsphere-sharding-core@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-sharding-core", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-sharding-core@5.4.1"}, {"bom-ref": "329-org.apache.shardingsphere:shardingsphere-broadcast-core@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-broadcast-core", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-broadcast-core@5.4.1"}, {"bom-ref": "330-org.apache.shardingsphere:shardingsphere-readwrite-splitting-core@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-readwrite-splitting-core", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-readwrite-splitting-core@5.4.1"}, {"bom-ref": "331-org.apache.shardingsphere:shardingsphere-encrypt-core@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-encrypt-core", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-encrypt-core@5.4.1"}, {"bom-ref": "332-org.apache.shardingsphere:shardingsphere-mask-core@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-mask-core", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-mask-core@5.4.1"}, {"bom-ref": "333-org.apache.shardingsphere:shardingsphere-shadow-core@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-shadow-core", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-shadow-core@5.4.1"}, {"bom-ref": "334-org.apache.shardingsphere:shardingsphere-sql-federation-core@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-sql-federation-core", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-sql-federation-core@5.4.1"}, {"bom-ref": "335-org.apache.shardingsphere:shardingsphere-sql-parser-core@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-sql-parser-core", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-sql-parser-core@5.4.1"}, {"bom-ref": "336-org.apache.shardingsphere:shardingsphere-logging-core@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-logging-core", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-logging-core@5.4.1"}, {"bom-ref": "337-com.h2database:h2@2.3.232", "type": "library", "group": "com.h2database", "name": "com.h2database:h2", "version": "2.3.232", "licenses": [{"expression": "(EPL-1.0 OR MPL-2.0)"}], "purl": "pkg:maven/com.h2database/h2@2.3.232"}, {"bom-ref": "338-org.slf4j:jcl-over-slf4j@2.0.16", "type": "library", "group": "org.slf4j", "name": "org.slf4j:jcl-over-slf4j", "version": "2.0.16", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.slf4j/jcl-over-slf4j@2.0.16"}, {"bom-ref": "339-com.zaxxer:HikariCP@5.1.0", "type": "library", "group": "com.zaxxer", "name": "com.zaxxer:<PERSON><PERSON><PERSON>", "version": "5.1.0", "purl": "pkg:maven/com.zaxxer/HikariCP@5.1.0"}, {"bom-ref": "340-org.springframework:spring-jdbc@6.2.1", "type": "library", "group": "org.springframework", "name": "org.springframework:spring-jdbc", "version": "6.2.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.springframework/spring-jdbc@6.2.1"}, {"bom-ref": "341-jakarta.persistence:jakarta.persistence-api@3.1.0", "type": "library", "group": "jakarta.persistence", "name": "jakarta.persistence:jakarta.persistence-api", "version": "3.1.0", "licenses": [{"expression": "(EDL-1.0 OR EPL-2.0)"}], "purl": "pkg:maven/jakarta.persistence/jakarta.persistence-api@3.1.0"}, {"bom-ref": "342-jakarta.transaction:jakarta.transaction-api@2.0.1", "type": "library", "group": "jakarta.transaction", "name": "jakarta.transaction:jakarta.transaction-api", "version": "2.0.1", "licenses": [{"expression": "(EPL-2.0 OR GPL-2.0-with-classpath-exception)"}], "purl": "pkg:maven/jakarta.transaction/jakarta.transaction-api@2.0.1"}, {"bom-ref": "343-org.hibernate.common:<EMAIL>", "type": "library", "group": "org.hibernate.common", "name": "org.hibernate.common:hibernate-commons-annotations", "version": "7.0.3.Final", "licenses": [{"expression": "LGPL-2.1"}], "purl": "pkg:maven/org.hibernate.common/<EMAIL>"}, {"bom-ref": "344-io.smallrye:jandex@3.2.0", "type": "library", "group": "io.smallrye", "name": "io.smallrye:jandex", "version": "3.2.0", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/io.smallrye/jandex@3.2.0"}, {"bom-ref": "345-net.bytebuddy:byte-buddy@1.15.11", "type": "library", "group": "net.bytebuddy", "name": "net.bytebuddy:byte-buddy", "version": "1.15.11", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/net.bytebuddy/byte-buddy@1.15.11"}, {"bom-ref": "346-org.antlr:antlr4-runtime@4.13.0", "type": "library", "group": "org.antlr", "name": "org.antlr:antlr4-runtime", "version": "4.13.0", "licenses": [{"expression": "BSD-3-<PERSON><PERSON>"}], "purl": "pkg:maven/org.antlr/antlr4-runtime@4.13.0"}, {"bom-ref": "347-org.springframework:spring-orm@6.2.1", "type": "library", "group": "org.springframework", "name": "org.springframework:spring-orm", "version": "6.2.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.springframework/spring-orm@6.2.1"}, {"bom-ref": "348-org.aspectj:aspectjweaver@********", "type": "library", "group": "org.aspectj", "name": "org.aspectj:aspectj<PERSON><PERSON>", "version": "********", "licenses": [{"expression": "EPL-1.0"}], "purl": "pkg:maven/org.aspectj/aspectjweaver@********"}, {"bom-ref": "349-org.springdoc:springdoc-openapi-starter-common@2.8.4", "type": "library", "group": "org.springdoc", "name": "org.springdoc:springdoc-openapi-starter-common", "version": "2.8.4", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.springdoc/springdoc-openapi-starter-common@2.8.4"}, {"bom-ref": "350-org.jspecify:jspecify@1.0.0", "type": "library", "group": "org.jspecify", "name": "org.jspecify:jspecify", "version": "1.0.0", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.jspecify/jspecify@1.0.0"}, {"bom-ref": "351-com.derbysoft.servreg:model@1.1.12", "type": "library", "group": "com.derbysoft.servreg", "name": "com.derbysoft.servreg:model", "version": "1.1.12", "purl": "pkg:maven/com.derbysoft.servreg/model@1.1.12"}, {"bom-ref": "352-javax.inject:javax.inject@1", "type": "library", "group": "javax.inject", "name": "javax.inject:javax.inject", "version": "1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/javax.inject/javax.inject@1"}, {"bom-ref": "353-aopalliance:aopalliance@1.0", "type": "library", "group": "aopalliance", "name": "aopalliance:aopalliance", "version": "1.0", "licenses": [{"expression": "Public-Domain"}], "purl": "pkg:maven/aopalliance/aopalliance@1.0"}, {"bom-ref": "354-io.grpc:grpc-core@1.38.0", "type": "library", "group": "io.grpc", "name": "io.grpc:grpc-core", "version": "1.38.0", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/io.grpc/grpc-core@1.38.0"}, {"bom-ref": "355-io.grpc:grpc-api@1.38.0", "type": "library", "group": "io.grpc", "name": "io.grpc:grpc-api", "version": "1.38.0", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/io.grpc/grpc-api@1.38.0"}, {"bom-ref": "356-com.google.api.grpc:proto-google-common-protos@2.0.1", "type": "library", "group": "com.google.api.grpc", "name": "com.google.api.grpc:proto-google-common-protos", "version": "2.0.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/com.google.api.grpc/proto-google-common-protos@2.0.1"}, {"bom-ref": "357-io.grpc:grpc-protobuf-lite@1.38.0", "type": "library", "group": "io.grpc", "name": "io.grpc:grpc-protobuf-lite", "version": "1.38.0", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/io.grpc/grpc-protobuf-lite@1.38.0"}, {"bom-ref": "358-org.codehaus.mojo:animal-sniffer-annotations@1.19", "type": "library", "group": "org.codehaus.mojo", "name": "org.codehaus.mojo:animal-sniffer-annotations", "version": "1.19", "licenses": [{"expression": "MIT"}], "purl": "pkg:maven/org.codehaus.mojo/animal-sniffer-annotations@1.19"}, {"bom-ref": "359-org.mongodb:bson-record-codec@5.2.1", "type": "library", "group": "org.mongodb", "name": "org.mongodb:bson-record-codec", "version": "5.2.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.mongodb/bson-record-codec@5.2.1"}, {"bom-ref": "360-org.hdrhistogram:HdrHistogram@2.2.2", "type": "library", "group": "org.hdrhistogram", "name": "org.hdrhistogram:HdrHistogram", "version": "2.2.2", "purl": "pkg:maven/org.hdrhistogram/HdrHistogram@2.2.2"}, {"bom-ref": "361-org.latencyutils:LatencyUtils@2.0.3", "type": "library", "group": "org.latencyutils", "name": "org.latencyutils:LatencyUtils", "version": "2.0.3", "purl": "pkg:maven/org.latencyutils/LatencyUtils@2.0.3"}, {"bom-ref": "362-org.aspectj:aspectjrt@********", "type": "library", "group": "org.aspectj", "name": "org.aspectj:aspectjrt", "version": "********", "licenses": [{"expression": "EPL-2.0"}], "purl": "pkg:maven/org.aspectj/aspectjrt@********"}, {"bom-ref": "363-io.zipkin.brave:brave@6.0.3", "type": "library", "group": "io.zipkin.brave", "name": "io.zipkin.brave:brave", "version": "6.0.3", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/io.zipkin.brave/brave@6.0.3"}, {"bom-ref": "364-io.zipkin.brave:brave-context-slf4j@6.0.3", "type": "library", "group": "io.zipkin.brave", "name": "io.zipkin.brave:brave-context-slf4j", "version": "6.0.3", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/io.zipkin.brave/brave-context-slf4j@6.0.3"}, {"bom-ref": "365-io.zipkin.brave:brave-instrumentation-messaging@6.0.3", "type": "library", "group": "io.zipkin.brave", "name": "io.zipkin.brave:brave-instrumentation-messaging", "version": "6.0.3", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/io.zipkin.brave/brave-instrumentation-messaging@6.0.3"}, {"bom-ref": "366-io.zipkin.brave:brave-instrumentation-rpc@6.0.3", "type": "library", "group": "io.zipkin.brave", "name": "io.zipkin.brave:brave-instrumentation-rpc", "version": "6.0.3", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/io.zipkin.brave/brave-instrumentation-rpc@6.0.3"}, {"bom-ref": "367-io.zipkin.brave:brave-instrumentation-spring-web@6.0.3", "type": "library", "group": "io.zipkin.brave", "name": "io.zipkin.brave:brave-instrumentation-spring-web", "version": "6.0.3", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/io.zipkin.brave/brave-instrumentation-spring-web@6.0.3"}, {"bom-ref": "368-io.zipkin.brave:brave-instrumentation-spring-rabbit@6.0.3", "type": "library", "group": "io.zipkin.brave", "name": "io.zipkin.brave:brave-instrumentation-spring-rabbit", "version": "6.0.3", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/io.zipkin.brave/brave-instrumentation-spring-rabbit@6.0.3"}, {"bom-ref": "369-io.zipkin.brave:brave-instrumentation-kafka-clients@6.0.3", "type": "library", "group": "io.zipkin.brave", "name": "io.zipkin.brave:brave-instrumentation-kafka-clients", "version": "6.0.3", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/io.zipkin.brave/brave-instrumentation-kafka-clients@6.0.3"}, {"bom-ref": "370-io.zipkin.brave:brave-instrumentation-kafka-streams@6.0.3", "type": "library", "group": "io.zipkin.brave", "name": "io.zipkin.brave:brave-instrumentation-kafka-streams", "version": "6.0.3", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/io.zipkin.brave/brave-instrumentation-kafka-streams@6.0.3"}, {"bom-ref": "371-io.zipkin.brave:brave-instrumentation-httpclient@6.0.3", "type": "library", "group": "io.zipkin.brave", "name": "io.zipkin.brave:brave-instrumentation-httpclient", "version": "6.0.3", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/io.zipkin.brave/brave-instrumentation-httpclient@6.0.3"}, {"bom-ref": "372-io.zipkin.brave:brave-instrumentation-httpasyncclient@6.0.3", "type": "library", "group": "io.zipkin.brave", "name": "io.zipkin.brave:brave-instrumentation-httpasyncclient", "version": "6.0.3", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/io.zipkin.brave/brave-instrumentation-httpasyncclient@6.0.3"}, {"bom-ref": "373-io.zipkin.brave:brave-instrumentation-spring-webmvc@6.0.3", "type": "library", "group": "io.zipkin.brave", "name": "io.zipkin.brave:brave-instrumentation-spring-webmvc", "version": "6.0.3", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/io.zipkin.brave/brave-instrumentation-spring-webmvc@6.0.3"}, {"bom-ref": "374-io.zipkin.brave:brave-instrumentation-jms@6.0.3", "type": "library", "group": "io.zipkin.brave", "name": "io.zipkin.brave:brave-instrumentation-jms", "version": "6.0.3", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/io.zipkin.brave/brave-instrumentation-jms@6.0.3"}, {"bom-ref": "375-io.zipkin.reporter2:zipkin-reporter-metrics-micrometer@3.4.3", "type": "library", "group": "io.zipkin.reporter2", "name": "io.zipkin.reporter2:zipkin-reporter-metrics-micrometer", "version": "3.4.3", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/io.zipkin.reporter2/zipkin-reporter-metrics-micrometer@3.4.3"}, {"bom-ref": "376-io.github.openfeign:feign-form@13.5", "type": "library", "group": "io.github.openfeign", "name": "io.github.openfeign:feign-form", "version": "13.5", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/io.github.openfeign/feign-form@13.5"}, {"bom-ref": "377-org.springdoc:springdoc-openapi-common@1.7.0", "type": "library", "group": "org.springdoc", "name": "org.springdoc:springdoc-openapi-common", "version": "1.7.0", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.springdoc/springdoc-openapi-common@1.7.0"}, {"bom-ref": "378-io.netty:<EMAIL>", "type": "library", "group": "io.netty", "name": "io.netty:netty-transport-native-unix-common", "version": "4.1.116.Final", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/io.netty/<EMAIL>"}, {"bom-ref": "379-xmlpull:xmlpull@*******", "type": "library", "group": "xmlpull", "name": "xmlpull:xmlpull", "version": "*******", "licenses": [{"expression": "Public-Domain"}], "purl": "pkg:maven/xmlpull/xmlpull@*******"}, {"bom-ref": "380-commons-jxpath:commons-jxpath@1.3", "type": "library", "group": "commons-jxpath", "name": "commons-jxpath:commons-jxpath", "version": "1.3", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/commons-jxpath/commons-jxpath@1.3"}, {"bom-ref": "381-org.antlr:antlr-runtime@3.4", "type": "library", "group": "org.antlr", "name": "org.antlr:antlr-runtime", "version": "3.4", "licenses": [{"expression": "BSD-2-<PERSON><PERSON>"}], "purl": "pkg:maven/org.antlr/antlr-runtime@3.4"}, {"bom-ref": "382-org.apache.shardingsphere:shardingsphere-transaction-api@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-transaction-api", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-transaction-api@5.4.1"}, {"bom-ref": "383-org.apache.shardingsphere:shardingsphere-infra-executor@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-infra-executor", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-infra-executor@5.4.1"}, {"bom-ref": "384-org.apache.shardingsphere:shardingsphere-global-clock-api@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-global-clock-api", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-global-clock-api@5.4.1"}, {"bom-ref": "385-org.apache.shardingsphere:shardingsphere-infra-common@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-infra-common", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-infra-common@5.4.1"}, {"bom-ref": "386-org.apache.shardingsphere:shardingsphere-mode-core@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-mode-core", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-mode-core@5.4.1"}, {"bom-ref": "387-org.apache.shardingsphere:shardingsphere-global-clock-tso-spi@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-global-clock-tso-spi", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-global-clock-tso-spi@5.4.1"}, {"bom-ref": "388-org.apache.shardingsphere:shardingsphere-infra-database-sql92@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-infra-database-sql92", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-infra-database-sql92@5.4.1"}, {"bom-ref": "389-org.apache.shardingsphere:shardingsphere-parser-sql-engine@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-parser-sql-engine", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-parser-sql-engine@5.4.1"}, {"bom-ref": "390-org.apache.shardingsphere:shardingsphere-infra-database-mysql@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-infra-database-mysql", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-infra-database-mysql@5.4.1"}, {"bom-ref": "391-org.apache.shardingsphere:shardingsphere-infra-database-postgresql@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-infra-database-postgresql", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-infra-database-postgresql@5.4.1"}, {"bom-ref": "392-org.apache.shardingsphere:shardingsphere-infra-database-oracle@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-infra-database-oracle", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-infra-database-oracle@5.4.1"}, {"bom-ref": "393-org.apache.shardingsphere:shardingsphere-infra-database-sqlserver@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-infra-database-sqlserver", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-infra-database-sqlserver@5.4.1"}, {"bom-ref": "394-org.apache.shardingsphere:shardingsphere-infra-database-opengauss@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-infra-database-opengauss", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-infra-database-opengauss@5.4.1"}, {"bom-ref": "395-org.apache.shardingsphere:shardingsphere-infra-exception-dialect-core@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-infra-exception-dialect-core", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-infra-exception-dialect-core@5.4.1"}, {"bom-ref": "396-org.apache.shardingsphere:shardingsphere-authority-api@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-authority-api", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-authority-api@5.4.1"}, {"bom-ref": "397-org.apache.shardingsphere:shardingsphere-mode-api@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-mode-api", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-mode-api@5.4.1"}, {"bom-ref": "398-org.apache.shardingsphere:shardingsphere-single-api@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-single-api", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-single-api@5.4.1"}, {"bom-ref": "399-org.apache.shardingsphere:shardingsphere-sql-federation-api@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-sql-federation-api", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-sql-federation-api@5.4.1"}, {"bom-ref": "400-org.apache.shardingsphere:shardingsphere-infra-route@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-infra-route", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-infra-route@5.4.1"}, {"bom-ref": "401-org.apache.shardingsphere:shardingsphere-infra-binder@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-infra-binder", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-infra-binder@5.4.1"}, {"bom-ref": "402-org.apache.shardingsphere:shardingsphere-traffic-api@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-traffic-api", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-traffic-api@5.4.1"}, {"bom-ref": "403-org.apache.shardingsphere:shardingsphere-infra-session@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-infra-session", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-infra-session@5.4.1"}, {"bom-ref": "404-org.apache.shardingsphere:shardingsphere-parser-distsql-engine@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-parser-distsql-engine", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-parser-distsql-engine@5.4.1"}, {"bom-ref": "405-org.apache.shardingsphere:shardingsphere-sql-translator-core@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-sql-translator-core", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-sql-translator-core@5.4.1"}, {"bom-ref": "406-org.apache.shardingsphere:shardingsphere-standalone-mode-repository-api@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-standalone-mode-repository-api", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-standalone-mode-repository-api@5.4.1"}, {"bom-ref": "407-javax.xml.bind:jaxb-api@2.3.0", "type": "library", "group": "javax.xml.bind", "name": "javax.xml.bind:jaxb-api", "version": "2.3.0", "licenses": [{"expression": "(CDDL-1.0 OR GPL-2.0-with-classpath-exception)"}], "purl": "pkg:maven/javax.xml.bind/jaxb-api@2.3.0"}, {"bom-ref": "408-com.sun.xml.bind:jaxb-core@4.0.5", "type": "library", "group": "com.sun.xml.bind", "name": "com.sun.xml.bind:jaxb-core", "version": "4.0.5", "licenses": [{"expression": "EDL-1.0"}], "purl": "pkg:maven/com.sun.xml.bind/jaxb-core@4.0.5"}, {"bom-ref": "409-com.sun.xml.bind:jaxb-impl@4.0.5", "type": "library", "group": "com.sun.xml.bind", "name": "com.sun.xml.bind:jaxb-impl", "version": "4.0.5", "licenses": [{"expression": "EDL-1.0"}], "purl": "pkg:maven/com.sun.xml.bind/jaxb-impl@4.0.5"}, {"bom-ref": "410-javax.activation:javax.activation-api@1.2.0", "type": "library", "group": "javax.activation", "name": "javax.activation:javax.activation-api", "version": "1.2.0", "licenses": [{"expression": "(CDDL-1.1 OR GPL-2.0-with-classpath-exception)"}], "purl": "pkg:maven/javax.activation/javax.activation-api@1.2.0"}, {"bom-ref": "411-org.apache.shardingsphere:shardingsphere-cluster-mode-repository-api@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-cluster-mode-repository-api", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-cluster-mode-repository-api@5.4.1"}, {"bom-ref": "412-org.apache.shardingsphere:shardingsphere-metadata-core@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-metadata-core", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-metadata-core@5.4.1"}, {"bom-ref": "413-org.apache.shardingsphere:shardingsphere-sharding-api@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-sharding-api", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-sharding-api@5.4.1"}, {"bom-ref": "414-org.apache.shardingsphere:shardingsphere-data-pipeline-api@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-data-pipeline-api", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-data-pipeline-api@5.4.1"}, {"bom-ref": "415-org.apache.shardingsphere:shardingsphere-infra-rewrite@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-infra-rewrite", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-infra-rewrite@5.4.1"}, {"bom-ref": "416-org.apache.shardingsphere:shardingsphere-infra-merge@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-infra-merge", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-infra-merge@5.4.1"}, {"bom-ref": "417-org.apache.shardingsphere:shardingsphere-time-service-core@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-time-service-core", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-time-service-core@5.4.1"}, {"bom-ref": "418-org.apache.shardingsphere:shardingsphere-system-time-service@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-system-time-service", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-system-time-service@5.4.1"}, {"bom-ref": "419-org.apache.shardingsphere:shardingsphere-infra-expr-core@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-infra-expr-core", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-infra-expr-core@5.4.1"}, {"bom-ref": "420-org.apache.shardingsphere:shardingsphere-broadcast-api@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-broadcast-api", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-broadcast-api@5.4.1"}, {"bom-ref": "421-org.apache.shardingsphere:shardingsphere-readwrite-splitting-api@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-readwrite-splitting-api", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-readwrite-splitting-api@5.4.1"}, {"bom-ref": "422-org.apache.shardingsphere:shardingsphere-encrypt-api@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-encrypt-api", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-encrypt-api@5.4.1"}, {"bom-ref": "423-org.apache.shardingsphere:shardingsphere-mask-api@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-mask-api", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-mask-api@5.4.1"}, {"bom-ref": "424-org.apache.shardingsphere:shardingsphere-shadow-api@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-shadow-api", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-shadow-api@5.4.1"}, {"bom-ref": "425-org.apache.calcite:calcite-core@1.35.0", "type": "library", "group": "org.apache.calcite", "name": "org.apache.calcite:calcite-core", "version": "1.35.0", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.calcite/calcite-core@1.35.0"}, {"bom-ref": "426-com.jayway.jsonpath:json-path@2.9.0", "type": "library", "group": "com.jayway.jsonpath", "name": "com.jayway.jsonpath:json-path", "version": "2.9.0", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/com.jayway.jsonpath/json-path@2.9.0"}, {"bom-ref": "427-org.apache.shardingsphere:shardingsphere-sql-parser-api@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-sql-parser-api", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-sql-parser-api@5.4.1"}, {"bom-ref": "428-org.apache.shardingsphere:shardingsphere-infra-parser@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-infra-parser", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-infra-parser@5.4.1"}, {"bom-ref": "429-org.apache.shardingsphere:shardingsphere-logging-api@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-logging-api", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-logging-api@5.4.1"}, {"bom-ref": "430-io.swagger.core.v3:swagger-core-jakarta@2.2.28", "type": "library", "group": "io.swagger.core.v3", "name": "io.swagger.core.v3:swagger-core-jakarta", "version": "2.2.28", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/io.swagger.core.v3/swagger-core-jakarta@2.2.28"}, {"bom-ref": "431-com.google.android:annotations@*******", "type": "library", "group": "com.google.android", "name": "com.google.android:annotations", "version": "*******", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/com.google.android/annotations@*******"}, {"bom-ref": "432-io.perfmark:perfmark-api@0.23.0", "type": "library", "group": "io.perfmark", "name": "io.perfmark:perfmark-api", "version": "0.23.0", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/io.perfmark/perfmark-api@0.23.0"}, {"bom-ref": "433-io.grpc:grpc-context@1.38.0", "type": "library", "group": "io.grpc", "name": "io.grpc:grpc-context", "version": "1.38.0", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/io.grpc/grpc-context@1.38.0"}, {"bom-ref": "434-io.zipkin.brave:brave-instrumentation-http@6.0.3", "type": "library", "group": "io.zipkin.brave", "name": "io.zipkin.brave:brave-instrumentation-http", "version": "6.0.3", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/io.zipkin.brave/brave-instrumentation-http@6.0.3"}, {"bom-ref": "435-io.zipkin.brave:brave-instrumentation-servlet@6.0.3", "type": "library", "group": "io.zipkin.brave", "name": "io.zipkin.brave:brave-instrumentation-servlet", "version": "6.0.3", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/io.zipkin.brave/brave-instrumentation-servlet@6.0.3"}, {"bom-ref": "436-io.swagger.core.v3:swagger-core@2.2.9", "type": "library", "group": "io.swagger.core.v3", "name": "io.swagger.core.v3:swagger-core", "version": "2.2.9", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/io.swagger.core.v3/swagger-core@2.2.9"}, {"bom-ref": "437-org.antlr:stringtemplate@3.2.1", "type": "library", "group": "org.antlr", "name": "org.antlr:stringtemplate", "version": "3.2.1", "licenses": [{"expression": "BSD-2-<PERSON><PERSON>"}], "purl": "pkg:maven/org.antlr/stringtemplate@3.2.1"}, {"bom-ref": "438-antlr:antlr@2.7.7", "type": "library", "group": "antlr", "name": "antlr:antlr", "version": "2.7.7", "licenses": [{"expression": "BSD-3-<PERSON><PERSON>"}], "purl": "pkg:maven/antlr/antlr@2.7.7"}, {"bom-ref": "439-com.alibaba:transmittable-thread-local@2.14.2", "type": "library", "group": "com.alibaba", "name": "com.alibaba:transmittable-thread-local", "version": "2.14.2", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/com.alibaba/transmittable-thread-local@2.14.2"}, {"bom-ref": "440-org.apache.shardingsphere:shardingsphere-infra-util@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-infra-util", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-infra-util@5.4.1"}, {"bom-ref": "441-org.apache.shardingsphere:shardingsphere-infra-database-mariadb@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-infra-database-mariadb", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-infra-database-mariadb@5.4.1"}, {"bom-ref": "442-org.apache.shardingsphere:shardingsphere-infra-database-h2@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-infra-database-h2", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-infra-database-h2@5.4.1"}, {"bom-ref": "443-org.apache.shardingsphere:shardingsphere-infra-data-source-pool-hikari@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-infra-data-source-pool-hikari", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-infra-data-source-pool-hikari@5.4.1"}, {"bom-ref": "444-org.apache.shardingsphere:shardingsphere-infra-database-core@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-infra-database-core", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-infra-database-core@5.4.1"}, {"bom-ref": "445-org.apache.shardingsphere:shardingsphere-parser-sql-spi@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-parser-sql-spi", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-parser-sql-spi@5.4.1"}, {"bom-ref": "446-org.apache.shardingsphere:shardingsphere-parser-sql-statement@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-parser-sql-statement", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-parser-sql-statement@5.4.1"}, {"bom-ref": "447-com.github.ben-manes.caffeine:caffeine@3.1.8", "type": "library", "group": "com.github.ben-manes.caffeine", "name": "com.github.ben-manes.caffeine:caffeine", "version": "3.1.8", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/com.github.ben-manes.caffeine/caffeine@3.1.8"}, {"bom-ref": "448-org.apache.shardingsphere:shardingsphere-parser-distsql-statement@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-parser-distsql-statement", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-parser-distsql-statement@5.4.1"}, {"bom-ref": "449-org.apache.shardingsphere:shardingsphere-sql-translator-api@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-sql-translator-api", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-sql-translator-api@5.4.1"}, {"bom-ref": "450-org.apache.shardingsphere:shardingsphere-sql-translator-native-provider@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-sql-translator-native-provider", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-sql-translator-native-provider@5.4.1"}, {"bom-ref": "451-org.apache.shardingsphere:shardingsphere-time-service-api@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-time-service-api", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-time-service-api@5.4.1"}, {"bom-ref": "452-org.apache.shardingsphere:shardingsphere-infra-expr-spi@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-infra-expr-spi", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-infra-expr-spi@5.4.1"}, {"bom-ref": "453-org.apache.shardingsphere:shardingsphere-infra-expr-groovy@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-infra-expr-groovy", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-infra-expr-groovy@5.4.1"}, {"bom-ref": "454-org.apache.shardingsphere:shardingsphere-infra-expr-literal@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-infra-expr-literal", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-infra-expr-literal@5.4.1"}, {"bom-ref": "455-org.apache.calcite:calcite-linq4j@1.35.0", "type": "library", "group": "org.apache.calcite", "name": "org.apache.calcite:calcite-linq4j", "version": "1.35.0", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.calcite/calcite-linq4j@1.35.0"}, {"bom-ref": "456-org.locationtech.jts:jts-core@1.19.0", "type": "library", "group": "org.locationtech.jts", "name": "org.locationtech.jts:jts-core", "version": "1.19.0", "licenses": [{"expression": "(EDL-1.0 OR EPL-1.0)"}], "purl": "pkg:maven/org.locationtech.jts/jts-core@1.19.0"}, {"bom-ref": "457-org.locationtech.jts.io:jts-io-common@1.19.0", "type": "library", "group": "org.locationtech.jts.io", "name": "org.locationtech.jts.io:jts-io-common", "version": "1.19.0", "licenses": [{"expression": "(EDL-1.0 OR EPL-1.0)"}], "purl": "pkg:maven/org.locationtech.jts.io/jts-io-common@1.19.0"}, {"bom-ref": "458-org.locationtech.proj4j:proj4j@1.2.2", "type": "library", "group": "org.locationtech.proj4j", "name": "org.locationtech.proj4j:proj4j", "version": "1.2.2", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.locationtech.proj4j/proj4j@1.2.2"}, {"bom-ref": "459-org.apache.calcite.avatica:avatica-core@1.23.0", "type": "library", "group": "org.apache.calcite.avatica", "name": "org.apache.calcite.avatica:avatica-core", "version": "1.23.0", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.calcite.avatica/avatica-core@1.23.0"}, {"bom-ref": "460-org.apiguardian:apiguardian-api@1.1.2", "type": "library", "group": "org.apiguardian", "name": "org.apiguardian:apiguardian-api", "version": "1.1.2", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apiguardian/apiguardian-api@1.1.2"}, {"bom-ref": "461-com.fasterxml.jackson.dataformat:jackson-dataformat-yaml@2.18.2", "type": "library", "group": "com.fasterxml.jackson.dataformat", "name": "com.fasterxml.jackson.dataformat:jackson-dataformat-yaml", "version": "2.18.2", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/com.fasterxml.jackson.dataformat/jackson-dataformat-yaml@2.18.2"}, {"bom-ref": "462-com.google.uzaygezen:uzaygezen-core@0.2", "type": "library", "group": "com.google.uzaygezen", "name": "com.google.uzaygezen:uzaygezen-core", "version": "0.2", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/com.google.uzaygezen/uzaygezen-core@0.2"}, {"bom-ref": "463-com.yahoo.datasketches:sketches-core@0.9.0", "type": "library", "group": "com.yahoo.datasketches", "name": "com.yahoo.datasketches:sketches-core", "version": "0.9.0", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/com.yahoo.datasketches/sketches-core@0.9.0"}, {"bom-ref": "464-net.hydromatic:aggdesigner-algorithm@6.0", "type": "library", "group": "net.hydromatic", "name": "net.hydromatic:aggdesigner-algorithm", "version": "6.0", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/net.hydromatic/aggdesigner-algorithm@6.0"}, {"bom-ref": "465-org.apache.commons:commons-dbcp2@2.12.0", "type": "library", "group": "org.apache.commons", "name": "org.apache.commons:commons-dbcp2", "version": "2.12.0", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.commons/commons-dbcp2@2.12.0"}, {"bom-ref": "466-org.codehaus.janino:commons-compiler@3.1.12", "type": "library", "group": "org.codehaus.janino", "name": "org.codehaus.janino:commons-compiler", "version": "3.1.12", "licenses": [{"expression": "BSD-3-<PERSON><PERSON>"}], "purl": "pkg:maven/org.codehaus.janino/commons-compiler@3.1.12"}, {"bom-ref": "467-org.codehaus.janino:janino@3.1.12", "type": "library", "group": "org.codehaus.janino", "name": "org.codehaus.janino:janino", "version": "3.1.12", "licenses": [{"expression": "BSD-3-<PERSON><PERSON>"}], "purl": "pkg:maven/org.codehaus.janino/janino@3.1.12"}, {"bom-ref": "468-io.swagger.core.v3:swagger-annotations-jakarta@2.2.28", "type": "library", "group": "io.swagger.core.v3", "name": "io.swagger.core.v3:swagger-annotations-jakarta", "version": "2.2.28", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/io.swagger.core.v3/swagger-annotations-jakarta@2.2.28"}, {"bom-ref": "469-io.swagger.core.v3:swagger-models-jakarta@2.2.28", "type": "library", "group": "io.swagger.core.v3", "name": "io.swagger.core.v3:swagger-models-jakarta", "version": "2.2.28", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/io.swagger.core.v3/swagger-models-jakarta@2.2.28"}, {"bom-ref": "470-io.swagger.core.v3:swagger-annotations@2.2.9", "type": "library", "group": "io.swagger.core.v3", "name": "io.swagger.core.v3:swagger-annotations", "version": "2.2.9", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/io.swagger.core.v3/swagger-annotations@2.2.9"}, {"bom-ref": "471-io.swagger.core.v3:swagger-models@2.2.9", "type": "library", "group": "io.swagger.core.v3", "name": "io.swagger.core.v3:swagger-models", "version": "2.2.9", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/io.swagger.core.v3/swagger-models@2.2.9"}, {"bom-ref": "472-org.apache.shardingsphere:shardingsphere-infra-spi@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-infra-spi", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-infra-spi@5.4.1"}, {"bom-ref": "473-org.apache.shardingsphere:shardingsphere-infra-data-source-pool-core@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-infra-data-source-pool-core", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-infra-data-source-pool-core@5.4.1"}, {"bom-ref": "474-org.apache.shardingsphere:shardingsphere-infra-exception-core@5.4.1", "type": "library", "group": "org.apache.shardingsphere", "name": "org.apache.shardingsphere:shardingsphere-infra-exception-core", "version": "5.4.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.shardingsphere/shardingsphere-infra-exception-core@5.4.1"}, {"bom-ref": "475-com.googlecode.json-simple:json-simple@1.1.1", "type": "library", "group": "com.googlecode.json-simple", "name": "com.googlecode.json-simple:json-simple", "version": "1.1.1", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/com.googlecode.json-simple/json-simple@1.1.1"}, {"bom-ref": "476-org.apache.calcite.avatica:avatica-metrics@1.23.0", "type": "library", "group": "org.apache.calcite.avatica", "name": "org.apache.calcite.avatica:avatica-metrics", "version": "1.23.0", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.calcite.avatica/avatica-metrics@1.23.0"}, {"bom-ref": "477-com.yahoo.datasketches:memory@0.9.0", "type": "library", "group": "com.yahoo.datasketches", "name": "com.yahoo.datasketches:memory", "version": "0.9.0", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/com.yahoo.datasketches/memory@0.9.0"}, {"bom-ref": "478-org.apache.commons:commons-pool2@2.12.0", "type": "library", "group": "org.apache.commons", "name": "org.apache.commons:commons-pool2", "version": "2.12.0", "licenses": [{"expression": "Apache-2.0"}], "purl": "pkg:maven/org.apache.commons/commons-pool2@2.12.0"}, {"bom-ref": "479-node-script@1.0.0", "type": "library", "name": "node-script", "version": "1.0.0", "licenses": [{"expression": "MIT"}], "purl": "pkg:npm/node-script@1.0.0"}], "dependencies": [{"ref": "1-next-propertyconnect-channel", "dependsOn": ["1-com.derbysoft.next:next-propertyconnect-channel@1.0.0", "12-com.derbysoft.next:next-propertyconnect-channel-api@1.0.0", "23-com.derbysoft.next:next-propertyconnect-channel-common@1.0.0", "81-com.derbysoft.next:next-propertyconnect-channel-task@1.0.0", "479-node-script@1.0.0"]}, {"ref": "1-com.derbysoft.next:next-propertyconnect-channel@1.0.0", "dependsOn": ["2-com.alibaba.fastjson2:fastjson2@2.0.26", "3-org.apache.commons:commons-collections4@4.4", "4-jakarta.annotation:jakarta.annotation-api@2.1.1", "5-jakarta.xml.bind:jakarta.xml.bind-api@4.0.2", "6-org.glassfish.jaxb:jaxb-runtime@4.0.5"]}, {"ref": "2-com.alibaba.fastjson2:fastjson2@2.0.26"}, {"ref": "3-org.apache.commons:commons-collections4@4.4"}, {"ref": "4-jakarta.annotation:jakarta.annotation-api@2.1.1"}, {"ref": "5-jakarta.xml.bind:jakarta.xml.bind-api@4.0.2", "dependsOn": ["7-jakarta.activation:jakarta.activation-api@2.1.3"]}, {"ref": "6-org.glassfish.jaxb:jaxb-runtime@4.0.5", "dependsOn": ["8-org.glassfish.jaxb:jaxb-core@4.0.5"]}, {"ref": "7-jakarta.activation:jakarta.activation-api@2.1.3"}, {"ref": "8-org.glassfish.jaxb:jaxb-core@4.0.5", "dependsOn": ["5-jakarta.xml.bind:jakarta.xml.bind-api@4.0.2", "7-jakarta.activation:jakarta.activation-api@2.1.3", "9-org.eclipse.angus:angus-activation@2.0.2", "10-org.glassfish.jaxb:txw2@4.0.5", "11-com.sun.istack:istack-commons-runtime@4.1.2"]}, {"ref": "9-org.eclipse.angus:angus-activation@2.0.2", "dependsOn": ["7-jakarta.activation:jakarta.activation-api@2.1.3"]}, {"ref": "10-org.glassfish.jaxb:txw2@4.0.5"}, {"ref": "11-com.sun.istack:istack-commons-runtime@4.1.2"}, {"ref": "12-com.derbysoft.next:next-propertyconnect-channel-api@1.0.0", "dependsOn": ["13-com.alibaba.fastjson2:fastjson2@2.0.26", "14-org.apache.commons:commons-collections4@4.4", "15-jakarta.annotation:jakarta.annotation-api@2.1.1", "16-jakarta.xml.bind:jakarta.xml.bind-api@4.0.2", "17-org.glassfish.jaxb:jaxb-runtime@4.0.5"]}, {"ref": "13-com.alibaba.fastjson2:fastjson2@2.0.26"}, {"ref": "14-org.apache.commons:commons-collections4@4.4"}, {"ref": "15-jakarta.annotation:jakarta.annotation-api@2.1.1"}, {"ref": "16-jakarta.xml.bind:jakarta.xml.bind-api@4.0.2", "dependsOn": ["18-jakarta.activation:jakarta.activation-api@2.1.3"]}, {"ref": "17-org.glassfish.jaxb:jaxb-runtime@4.0.5", "dependsOn": ["19-org.glassfish.jaxb:jaxb-core@4.0.5"]}, {"ref": "18-jakarta.activation:jakarta.activation-api@2.1.3"}, {"ref": "19-org.glassfish.jaxb:jaxb-core@4.0.5", "dependsOn": ["16-jakarta.xml.bind:jakarta.xml.bind-api@4.0.2", "18-jakarta.activation:jakarta.activation-api@2.1.3", "20-org.eclipse.angus:angus-activation@2.0.2", "21-org.glassfish.jaxb:txw2@4.0.5", "22-com.sun.istack:istack-commons-runtime@4.1.2"]}, {"ref": "20-org.eclipse.angus:angus-activation@2.0.2", "dependsOn": ["18-jakarta.activation:jakarta.activation-api@2.1.3"]}, {"ref": "21-org.glassfish.jaxb:txw2@4.0.5"}, {"ref": "22-com.sun.istack:istack-commons-runtime@4.1.2"}, {"ref": "23-com.derbysoft.next:next-propertyconnect-channel-common@1.0.0", "dependsOn": ["24-org.springframework.boot:spring-boot-starter-web@3.4.1", "25-org.springframework.boot:spring-boot-starter-validation@3.4.1", "26-org.mapstruct:<EMAIL>", "27-org.springdoc:springdoc-openapi-ui@1.7.0", "28-com.alibaba.fastjson2:fastjson2@2.0.26", "29-org.apache.commons:commons-collections4@4.4", "30-jakarta.annotation:jakarta.annotation-api@2.1.1", "31-jakarta.xml.bind:jakarta.xml.bind-api@4.0.2", "32-org.glassfish.jaxb:jaxb-runtime@4.0.5"]}, {"ref": "24-org.springframework.boot:spring-boot-starter-web@3.4.1", "dependsOn": ["33-org.springframework.boot:spring-boot-starter@3.4.1", "34-org.springframework.boot:spring-boot-starter-json@3.4.1", "35-org.springframework.boot:spring-boot-starter-tomcat@3.4.1", "36-org.springframework:spring-web@6.2.1", "37-org.springframework:spring-webmvc@6.2.1"]}, {"ref": "25-org.springframework.boot:spring-boot-starter-validation@3.4.1", "dependsOn": ["33-org.springframework.boot:spring-boot-starter@3.4.1", "38-org.apache.tomcat.embed:tomcat-embed-el@10.1.34", "39-org.hibernate.validator:<EMAIL>"]}, {"ref": "26-org.mapstruct:<EMAIL>"}, {"ref": "27-org.springdoc:springdoc-openapi-ui@1.7.0", "dependsOn": ["40-org.springdoc:springdoc-openapi-webmvc-core@1.7.0", "41-org.webjars:swagger-ui@4.18.2"]}, {"ref": "28-com.alibaba.fastjson2:fastjson2@2.0.26"}, {"ref": "29-org.apache.commons:commons-collections4@4.4"}, {"ref": "30-jakarta.annotation:jakarta.annotation-api@2.1.1"}, {"ref": "31-jakarta.xml.bind:jakarta.xml.bind-api@4.0.2", "dependsOn": ["42-jakarta.activation:jakarta.activation-api@2.1.3"]}, {"ref": "32-org.glassfish.jaxb:jaxb-runtime@4.0.5", "dependsOn": ["43-org.glassfish.jaxb:jaxb-core@4.0.5"]}, {"ref": "33-org.springframework.boot:spring-boot-starter@3.4.1", "dependsOn": ["44-org.springframework.boot:spring-boot@3.4.1", "45-org.springframework.boot:spring-boot-autoconfigure@3.4.1", "46-org.springframework.boot:spring-boot-starter-logging@3.4.1", "30-jakarta.annotation:jakarta.annotation-api@2.1.1", "47-org.springframework:spring-core@6.2.1", "48-org.yaml:snakeyaml@2.3"]}, {"ref": "34-org.springframework.boot:spring-boot-starter-json@3.4.1", "dependsOn": ["33-org.springframework.boot:spring-boot-starter@3.4.1", "36-org.springframework:spring-web@6.2.1", "49-com.fasterxml.jackson.core:jackson-databind@2.18.2", "50-com.fasterxml.jackson.datatype:jackson-datatype-jdk8@2.18.2", "51-com.fasterxml.jackson.datatype:jackson-datatype-jsr310@2.18.2", "52-com.fasterxml.jackson.module:jackson-module-parameter-names@2.18.2"]}, {"ref": "35-org.springframework.boot:spring-boot-starter-tomcat@3.4.1", "dependsOn": ["30-jakarta.annotation:jakarta.annotation-api@2.1.1", "53-org.apache.tomcat.embed:tomcat-embed-core@10.1.34", "38-org.apache.tomcat.embed:tomcat-embed-el@10.1.34", "54-org.apache.tomcat.embed:tomcat-embed-websocket@10.1.34"]}, {"ref": "36-org.springframework:spring-web@6.2.1", "dependsOn": ["55-org.springframework:spring-beans@6.2.1", "47-org.springframework:spring-core@6.2.1", "56-io.micrometer:micrometer-observation@1.14.2"]}, {"ref": "37-org.springframework:spring-webmvc@6.2.1", "dependsOn": ["57-org.springframework:spring-aop@6.2.1", "55-org.springframework:spring-beans@6.2.1", "58-org.springframework:spring-context@6.2.1", "47-org.springframework:spring-core@6.2.1", "59-org.springframework:spring-expression@6.2.1", "36-org.springframework:spring-web@6.2.1"]}, {"ref": "38-org.apache.tomcat.embed:tomcat-embed-el@10.1.34"}, {"ref": "39-org.hibernate.validator:<EMAIL>", "dependsOn": ["60-jakarta.validation:jakarta.validation-api@3.0.2", "61-org.jboss.logging:<EMAIL>", "62-com.fasterxml:classmate@1.7.0"]}, {"ref": "40-org.springdoc:springdoc-openapi-webmvc-core@1.7.0", "dependsOn": ["63-org.springdoc:springdoc-openapi-common@1.7.0", "37-org.springframework:spring-webmvc@6.2.1"]}, {"ref": "41-org.webjars:swagger-ui@4.18.2"}, {"ref": "42-jakarta.activation:jakarta.activation-api@2.1.3"}, {"ref": "43-org.glassfish.jaxb:jaxb-core@4.0.5", "dependsOn": ["31-jakarta.xml.bind:jakarta.xml.bind-api@4.0.2", "42-jakarta.activation:jakarta.activation-api@2.1.3", "64-org.eclipse.angus:angus-activation@2.0.2", "65-org.glassfish.jaxb:txw2@4.0.5", "66-com.sun.istack:istack-commons-runtime@4.1.2"]}, {"ref": "44-org.springframework.boot:spring-boot@3.4.1", "dependsOn": ["47-org.springframework:spring-core@6.2.1", "58-org.springframework:spring-context@6.2.1"]}, {"ref": "45-org.springframework.boot:spring-boot-autoconfigure@3.4.1", "dependsOn": ["44-org.springframework.boot:spring-boot@3.4.1"]}, {"ref": "46-org.springframework.boot:spring-boot-starter-logging@3.4.1", "dependsOn": ["67-ch.qos.logback:logback-classic@1.5.12", "68-org.apache.logging.log4j:log4j-to-slf4j@2.24.3", "69-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "47-org.springframework:spring-core@6.2.1"}, {"ref": "48-org.yaml:snakeyaml@2.3"}, {"ref": "49-com.fasterxml.jackson.core:jackson-databind@2.18.2", "dependsOn": ["70-com.fasterxml.jackson.core:jackson-annotations@2.18.2", "71-com.fasterxml.jackson.core:jackson-core@2.18.2"]}, {"ref": "50-com.fasterxml.jackson.datatype:jackson-datatype-jdk8@2.18.2", "dependsOn": ["71-com.fasterxml.jackson.core:jackson-core@2.18.2", "49-com.fasterxml.jackson.core:jackson-databind@2.18.2"]}, {"ref": "51-com.fasterxml.jackson.datatype:jackson-datatype-jsr310@2.18.2", "dependsOn": ["70-com.fasterxml.jackson.core:jackson-annotations@2.18.2", "71-com.fasterxml.jackson.core:jackson-core@2.18.2", "49-com.fasterxml.jackson.core:jackson-databind@2.18.2"]}, {"ref": "52-com.fasterxml.jackson.module:jackson-module-parameter-names@2.18.2", "dependsOn": ["71-com.fasterxml.jackson.core:jackson-core@2.18.2", "49-com.fasterxml.jackson.core:jackson-databind@2.18.2"]}, {"ref": "53-org.apache.tomcat.embed:tomcat-embed-core@10.1.34"}, {"ref": "54-org.apache.tomcat.embed:tomcat-embed-websocket@10.1.34", "dependsOn": ["53-org.apache.tomcat.embed:tomcat-embed-core@10.1.34"]}, {"ref": "55-org.springframework:spring-beans@6.2.1", "dependsOn": ["47-org.springframework:spring-core@6.2.1"]}, {"ref": "56-io.micrometer:micrometer-observation@1.14.2", "dependsOn": ["72-io.micrometer:micrometer-commons@1.14.2"]}, {"ref": "57-org.springframework:spring-aop@6.2.1", "dependsOn": ["55-org.springframework:spring-beans@6.2.1", "47-org.springframework:spring-core@6.2.1"]}, {"ref": "58-org.springframework:spring-context@6.2.1", "dependsOn": ["57-org.springframework:spring-aop@6.2.1", "55-org.springframework:spring-beans@6.2.1", "47-org.springframework:spring-core@6.2.1", "59-org.springframework:spring-expression@6.2.1", "56-io.micrometer:micrometer-observation@1.14.2"]}, {"ref": "59-org.springframework:spring-expression@6.2.1", "dependsOn": ["47-org.springframework:spring-core@6.2.1"]}, {"ref": "60-jakarta.validation:jakarta.validation-api@3.0.2"}, {"ref": "61-org.jboss.logging:<EMAIL>"}, {"ref": "62-com.fasterxml:classmate@1.7.0"}, {"ref": "63-org.springdoc:springdoc-openapi-common@1.7.0", "dependsOn": ["45-org.springframework.boot:spring-boot-autoconfigure@3.4.1", "36-org.springframework:spring-web@6.2.1", "73-io.swagger.core.v3:swagger-core@2.2.9"]}, {"ref": "64-org.eclipse.angus:angus-activation@2.0.2", "dependsOn": ["42-jakarta.activation:jakarta.activation-api@2.1.3"]}, {"ref": "65-org.glassfish.jaxb:txw2@4.0.5"}, {"ref": "66-com.sun.istack:istack-commons-runtime@4.1.2"}, {"ref": "67-ch.qos.logback:logback-classic@1.5.12", "dependsOn": ["74-ch.qos.logback:logback-core@1.5.12", "75-org.slf4j:slf4j-api@2.0.16"]}, {"ref": "68-org.apache.logging.log4j:log4j-to-slf4j@2.24.3", "dependsOn": ["76-org.apache.logging.log4j:log4j-api@2.24.3", "75-org.slf4j:slf4j-api@2.0.16"]}, {"ref": "69-org.slf4j:jul-to-slf4j@2.0.16", "dependsOn": ["75-org.slf4j:slf4j-api@2.0.16"]}, {"ref": "70-com.fasterxml.jackson.core:jackson-annotations@2.18.2"}, {"ref": "71-com.fasterxml.jackson.core:jackson-core@2.18.2"}, {"ref": "72-io.micrometer:micrometer-commons@1.14.2"}, {"ref": "73-io.swagger.core.v3:swagger-core@2.2.9", "dependsOn": ["31-jakarta.xml.bind:jakarta.xml.bind-api@4.0.2", "77-org.apache.commons:commons-lang3@3.17.0", "75-org.slf4j:slf4j-api@2.0.16", "70-com.fasterxml.jackson.core:jackson-annotations@2.18.2", "49-com.fasterxml.jackson.core:jackson-databind@2.18.2", "78-com.fasterxml.jackson.dataformat:jackson-dataformat-yaml@2.18.2", "51-com.fasterxml.jackson.datatype:jackson-datatype-jsr310@2.18.2", "79-io.swagger.core.v3:swagger-annotations@2.2.9", "48-org.yaml:snakeyaml@2.3", "80-io.swagger.core.v3:swagger-models@2.2.9", "60-jakarta.validation:jakarta.validation-api@3.0.2"]}, {"ref": "74-ch.qos.logback:logback-core@1.5.12"}, {"ref": "75-org.slf4j:slf4j-api@2.0.16"}, {"ref": "76-org.apache.logging.log4j:log4j-api@2.24.3"}, {"ref": "77-org.apache.commons:commons-lang3@3.17.0"}, {"ref": "78-com.fasterxml.jackson.dataformat:jackson-dataformat-yaml@2.18.2", "dependsOn": ["49-com.fasterxml.jackson.core:jackson-databind@2.18.2", "48-org.yaml:snakeyaml@2.3", "71-com.fasterxml.jackson.core:jackson-core@2.18.2"]}, {"ref": "79-io.swagger.core.v3:swagger-annotations@2.2.9"}, {"ref": "80-io.swagger.core.v3:swagger-models@2.2.9", "dependsOn": ["70-com.fasterxml.jackson.core:jackson-annotations@2.18.2"]}, {"ref": "81-com.derbysoft.next:next-propertyconnect-channel-task@1.0.0", "dependsOn": ["82-com.derbysoft.next:next-propertyconnect-channel-common@1.0.0", "83-org.springframework.boot:spring-boot-configuration-processor@3.4.1", "84-org.apache.groovy:groovy@4.0.24", "85-org.apache.groovy:groovy-xml@4.0.24", "86-org.springframework.boot:spring-boot-starter-web@3.4.1", "87-org.springframework.cloud:spring-cloud-starter-netflix-eureka-client@4.2.0", "88-com.derbysoft.next:next-commons-boot@3.0.1-SNAPSHOT", "89-org.springframework.boot:spring-boot-starter-data-mongodb@3.4.1", "90-org.springframework.boot:spring-boot-starter-actuator@3.4.1", "91-org.springframework.cloud:<EMAIL>", "92-com.google.guava:guava@31.1-jre", "93-org.springframework.cloud:spring-cloud-starter-bootstrap@4.2.0", "94-org.springframework.cloud:spring-cloud-starter-openfeign@4.2.0", "95-com.derbysoft.extension:aggregation-tool@1.0.1-SNAPSHOT", "96-com.derbysoft.extension:schedulecenter-support@1.0.0-SNAPSHOT", "97-com.derbysoft.schedulecenter:schedulecenter-taskframework@1.2.4", "98-org.mapstruct:<EMAIL>", "99-com.squareup.okhttp3:okhttp@4.12.0", "100-com.derbysoft.extension:springdoc-openapi@1.0.0-SNAPSHOT", "101-org.springframework.boot:spring-boot-starter-validation@3.4.1", "102-org.codehaus.gpars:gpars@1.2.1", "103-org.apache.poi:poi@5.2.5", "104-org.apache.poi:poi-ooxml@5.2.5", "105-software.amazon.awssdk:s3@2.29.52", "106-software.amazon.awssdk:sts@2.21.40", "107-commons-fileupload:commons-fileupload@1.5", "108-commons-io:commons-io@2.14.0", "109-com.alibaba.fastjson2:fastjson2@2.0.26", "110-org.apache.commons:commons-collections4@4.4", "111-jakarta.annotation:jakarta.annotation-api@2.1.1", "112-jakarta.xml.bind:jakarta.xml.bind-api@4.0.2", "113-org.glassfish.jaxb:jaxb-runtime@4.0.5"]}, {"ref": "82-com.derbysoft.next:next-propertyconnect-channel-common@1.0.0", "dependsOn": ["86-org.springframework.boot:spring-boot-starter-web@3.4.1", "101-org.springframework.boot:spring-boot-starter-validation@3.4.1", "98-org.mapstruct:<EMAIL>", "109-com.alibaba.fastjson2:fastjson2@2.0.26", "110-org.apache.commons:commons-collections4@4.4", "111-jakarta.annotation:jakarta.annotation-api@2.1.1", "112-jakarta.xml.bind:jakarta.xml.bind-api@4.0.2", "113-org.glassfish.jaxb:jaxb-runtime@4.0.5"]}, {"ref": "83-org.springframework.boot:spring-boot-configuration-processor@3.4.1"}, {"ref": "84-org.apache.groovy:groovy@4.0.24"}, {"ref": "85-org.apache.groovy:groovy-xml@4.0.24", "dependsOn": ["84-org.apache.groovy:groovy@4.0.24"]}, {"ref": "86-org.springframework.boot:spring-boot-starter-web@3.4.1", "dependsOn": ["114-org.springframework.boot:spring-boot-starter@3.4.1", "115-org.springframework.boot:spring-boot-starter-json@3.4.1", "116-org.springframework.boot:spring-boot-starter-tomcat@3.4.1", "117-org.springframework:spring-web@6.2.1", "118-org.springframework:spring-webmvc@6.2.1"]}, {"ref": "87-org.springframework.cloud:spring-cloud-starter-netflix-eureka-client@4.2.0", "dependsOn": ["119-org.springframework.cloud:spring-cloud-starter@4.2.0", "120-org.springframework.cloud:spring-cloud-netflix-eureka-client@4.2.0", "121-com.netflix.eureka:eureka-client@2.0.4", "122-org.springframework.cloud:spring-cloud-starter-loadbalancer@4.2.0"]}, {"ref": "88-com.derbysoft.next:next-commons-boot@3.0.1-SNAPSHOT", "dependsOn": ["123-com.derbysoft.next:next-commons-core@3.0.1-SNAPSHOT", "124-com.derbysoft.next:next-commons-dto@3.0.1-SNAPSHOT", "125-com.derbysoft.next:next-commons-hibernate@3.0.1-SNAPSHOT", "86-org.springframework.boot:spring-boot-starter-web@3.4.1", "126-org.springframework.boot:spring-boot-starter-data-jpa@3.4.1", "90-org.springframework.boot:spring-boot-starter-actuator@3.4.1", "127-org.apache.commons:commons-lang3@3.17.0", "99-com.squareup.okhttp3:okhttp@4.12.0", "128-com.google.code.gson:gson@2.11.0", "108-commons-io:commons-io@2.14.0", "129-com.derby.nuke:nuke-logback-kafka-appender@1.7.0", "130-org.springdoc:springdoc-openapi-starter-webmvc-ui@2.8.4", "131-com.derbysoft.servreg:client@1.1.12", "111-jakarta.annotation:jakarta.annotation-api@2.1.1", "112-jakarta.xml.bind:jakarta.xml.bind-api@4.0.2", "113-org.glassfish.jaxb:jaxb-runtime@4.0.5"]}, {"ref": "89-org.springframework.boot:spring-boot-starter-data-mongodb@3.4.1", "dependsOn": ["114-org.springframework.boot:spring-boot-starter@3.4.1", "132-org.mongodb:mongodb-driver-sync@5.2.1", "133-org.springframework.data:spring-data-mongodb@4.4.1"]}, {"ref": "90-org.springframework.boot:spring-boot-starter-actuator@3.4.1", "dependsOn": ["114-org.springframework.boot:spring-boot-starter@3.4.1", "134-org.springframework.boot:spring-boot-actuator-autoconfigure@3.4.1", "135-io.micrometer:micrometer-observation@1.14.2", "136-io.micrometer:micrometer-jakarta9@1.14.2"]}, {"ref": "91-org.springframework.cloud:<EMAIL>", "dependsOn": ["137-org.springframework.cloud:<EMAIL>", "138-org.springframework.cloud:<EMAIL>"]}, {"ref": "92-com.google.guava:guava@31.1-jre", "dependsOn": ["139-com.google.guava:failureaccess@1.0.1", "140-com.google.guava:listenablefuture@9999.0-empty-to-avoid-conflict-with-guava", "141-com.google.code.findbugs:jsr305@3.0.2", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "144-com.google.j2objc:j2objc-annotations@1.3"]}, {"ref": "93-org.springframework.cloud:spring-cloud-starter-bootstrap@4.2.0", "dependsOn": ["119-org.springframework.cloud:spring-cloud-starter@4.2.0"]}, {"ref": "94-org.springframework.cloud:spring-cloud-starter-openfeign@4.2.0", "dependsOn": ["119-org.springframework.cloud:spring-cloud-starter@4.2.0", "145-org.springframework.cloud:spring-cloud-openfeign-core@4.2.0", "117-org.springframework:spring-web@6.2.1", "146-org.springframework.cloud:spring-cloud-commons@4.2.0", "147-io.github.openfeign:feign-core@13.5", "148-io.github.openfeign:feign-slf4j@13.5"]}, {"ref": "95-com.derbysoft.extension:aggregation-tool@1.0.1-SNAPSHOT", "dependsOn": ["149-com.squareup:javapoet@1.13.0", "150-com.google.auto.service:auto-service@1.1.1", "151-org.springframework.boot:spring-boot-autoconfigure@3.4.1", "109-com.alibaba.fastjson2:fastjson2@2.0.26"]}, {"ref": "96-com.derbysoft.extension:schedulecenter-support@1.0.0-SNAPSHOT", "dependsOn": ["83-org.springframework.boot:spring-boot-configuration-processor@3.4.1", "114-org.springframework.boot:spring-boot-starter@3.4.1", "97-com.derbysoft.schedulecenter:schedulecenter-taskframework@1.2.4"]}, {"ref": "97-com.derbysoft.schedulecenter:schedulecenter-taskframework@1.2.4", "dependsOn": ["152-com.derbysoft.schedulecenter:schedulecenter-protocol@1.2.4", "153-org.slf4j:slf4j-api@2.0.16"]}, {"ref": "98-org.mapstruct:<EMAIL>"}, {"ref": "99-com.squareup.okhttp3:okhttp@4.12.0", "dependsOn": ["154-com.squareup.okio:okio@2.8.0", "155-org.jetbrains.kotlin:kotlin-stdlib-jdk8@1.9.25"]}, {"ref": "100-com.derbysoft.extension:springdoc-openapi@1.0.0-SNAPSHOT", "dependsOn": ["156-org.springdoc:springdoc-openapi-ui@1.7.0", "86-org.springframework.boot:spring-boot-starter-web@3.4.1"]}, {"ref": "101-org.springframework.boot:spring-boot-starter-validation@3.4.1", "dependsOn": ["114-org.springframework.boot:spring-boot-starter@3.4.1", "157-org.apache.tomcat.embed:tomcat-embed-el@10.1.34", "158-org.hibernate.validator:<EMAIL>"]}, {"ref": "102-org.codehaus.gpars:gpars@1.2.1", "dependsOn": ["159-org.multiverse:multiverse-core@0.7.0", "160-org.codehaus.jsr166-mirror:jsr166y@1.7.0"]}, {"ref": "103-org.apache.poi:poi@5.2.5", "dependsOn": ["161-commons-codec:commons-codec@1.17.1", "110-org.apache.commons:commons-collections4@4.4", "162-org.apache.commons:commons-math3@3.6.1", "108-commons-io:commons-io@2.14.0", "163-com.zaxxer:SparseBitSet@1.3", "164-org.apache.logging.log4j:log4j-api@2.24.3"]}, {"ref": "104-org.apache.poi:poi-ooxml@5.2.5", "dependsOn": ["103-org.apache.poi:poi@5.2.5", "165-org.apache.poi:poi-ooxml-lite@5.2.5", "166-org.apache.xmlbeans:xmlbeans@5.2.0", "167-org.apache.commons:commons-compress@1.26.1", "108-commons-io:commons-io@2.14.0", "168-com.github.virtuald:curvesapi@1.08", "164-org.apache.logging.log4j:log4j-api@2.24.3", "110-org.apache.commons:commons-collections4@4.4"]}, {"ref": "105-software.amazon.awssdk:s3@2.29.52", "dependsOn": ["169-software.amazon.awssdk:aws-xml-protocol@2.29.52", "170-software.amazon.awssdk:protocol-core@2.29.52", "171-software.amazon.awssdk:arns@2.29.52", "172-software.amazon.awssdk:profiles@2.29.52", "173-software.amazon.awssdk:crt-core@2.29.52", "174-software.amazon.awssdk:http-auth@2.29.52", "175-software.amazon.awssdk:identity-spi@2.29.52", "176-software.amazon.awssdk:http-auth-spi@2.29.52", "177-software.amazon.awssdk:http-auth-aws@2.29.52", "178-software.amazon.awssdk:checksums@2.29.52", "179-software.amazon.awssdk:checksums-spi@2.29.52", "180-software.amazon.awssdk:retries-spi@2.29.52", "181-software.amazon.awssdk:sdk-core@2.29.52", "182-software.amazon.awssdk:auth@2.29.52", "183-software.amazon.awssdk:http-client-spi@2.29.52", "184-software.amazon.awssdk:regions@2.29.52", "185-software.amazon.awssdk:annotations@2.29.52", "186-software.amazon.awssdk:utils@2.29.52", "187-software.amazon.awssdk:aws-core@2.29.52", "188-software.amazon.awssdk:metrics-spi@2.29.52", "189-software.amazon.awssdk:json-utils@2.29.52", "190-software.amazon.awssdk:endpoints-spi@2.29.52", "191-software.amazon.awssdk:apache-client@2.29.52", "192-software.amazon.awssdk:netty-nio-client@2.29.52"]}, {"ref": "106-software.amazon.awssdk:sts@2.21.40", "dependsOn": ["193-software.amazon.awssdk:aws-query-protocol@2.21.40", "170-software.amazon.awssdk:protocol-core@2.29.52", "172-software.amazon.awssdk:profiles@2.29.52", "177-software.amazon.awssdk:http-auth-aws@2.29.52", "181-software.amazon.awssdk:sdk-core@2.29.52", "182-software.amazon.awssdk:auth@2.29.52", "176-software.amazon.awssdk:http-auth-spi@2.29.52", "174-software.amazon.awssdk:http-auth@2.29.52", "175-software.amazon.awssdk:identity-spi@2.29.52", "183-software.amazon.awssdk:http-client-spi@2.29.52", "184-software.amazon.awssdk:regions@2.29.52", "185-software.amazon.awssdk:annotations@2.29.52", "186-software.amazon.awssdk:utils@2.29.52", "187-software.amazon.awssdk:aws-core@2.29.52", "188-software.amazon.awssdk:metrics-spi@2.29.52", "189-software.amazon.awssdk:json-utils@2.29.52", "190-software.amazon.awssdk:endpoints-spi@2.29.52", "191-software.amazon.awssdk:apache-client@2.29.52", "192-software.amazon.awssdk:netty-nio-client@2.29.52"]}, {"ref": "107-commons-fileupload:commons-fileupload@1.5", "dependsOn": ["108-commons-io:commons-io@2.14.0"]}, {"ref": "108-commons-io:commons-io@2.14.0"}, {"ref": "109-com.alibaba.fastjson2:fastjson2@2.0.26"}, {"ref": "110-org.apache.commons:commons-collections4@4.4"}, {"ref": "111-jakarta.annotation:jakarta.annotation-api@2.1.1"}, {"ref": "112-jakarta.xml.bind:jakarta.xml.bind-api@4.0.2", "dependsOn": ["194-jakarta.activation:jakarta.activation-api@2.1.3"]}, {"ref": "113-org.glassfish.jaxb:jaxb-runtime@4.0.5", "dependsOn": ["195-org.glassfish.jaxb:jaxb-core@4.0.5"]}, {"ref": "114-org.springframework.boot:spring-boot-starter@3.4.1", "dependsOn": ["196-org.springframework.boot:spring-boot@3.4.1", "151-org.springframework.boot:spring-boot-autoconfigure@3.4.1", "197-org.springframework.boot:spring-boot-starter-logging@3.4.1", "111-jakarta.annotation:jakarta.annotation-api@2.1.1", "198-org.springframework:spring-core@6.2.1", "199-org.yaml:snakeyaml@2.3"]}, {"ref": "115-org.springframework.boot:spring-boot-starter-json@3.4.1", "dependsOn": ["114-org.springframework.boot:spring-boot-starter@3.4.1", "117-org.springframework:spring-web@6.2.1", "200-com.fasterxml.jackson.core:jackson-databind@2.18.2", "201-com.fasterxml.jackson.datatype:jackson-datatype-jdk8@2.18.2", "202-com.fasterxml.jackson.datatype:jackson-datatype-jsr310@2.18.2", "203-com.fasterxml.jackson.module:jackson-module-parameter-names@2.18.2"]}, {"ref": "116-org.springframework.boot:spring-boot-starter-tomcat@3.4.1"}, {"ref": "117-org.springframework:spring-web@6.2.1", "dependsOn": ["204-org.springframework:spring-beans@6.2.1", "198-org.springframework:spring-core@6.2.1", "135-io.micrometer:micrometer-observation@1.14.2"]}, {"ref": "118-org.springframework:spring-webmvc@6.2.1", "dependsOn": ["205-org.springframework:spring-aop@6.2.1", "204-org.springframework:spring-beans@6.2.1", "206-org.springframework:spring-context@6.2.1", "198-org.springframework:spring-core@6.2.1", "207-org.springframework:spring-expression@6.2.1", "117-org.springframework:spring-web@6.2.1"]}, {"ref": "119-org.springframework.cloud:spring-cloud-starter@4.2.0", "dependsOn": ["114-org.springframework.boot:spring-boot-starter@3.4.1", "208-org.springframework.cloud:spring-cloud-context@4.2.0", "146-org.springframework.cloud:spring-cloud-commons@4.2.0", "209-org.bouncycastle:bcprov-jdk18on@1.78"]}, {"ref": "120-org.springframework.cloud:spring-cloud-netflix-eureka-client@4.2.0", "dependsOn": ["121-com.netflix.eureka:eureka-client@2.0.4", "210-org.apache.httpcomponents.client5:httpclient5@5.4.1"]}, {"ref": "121-com.netflix.eureka:eureka-client@2.0.4", "dependsOn": ["211-com.thoughtworks.xstream:xstream@1.4.20", "212-jakarta.ws.rs:jakarta.ws.rs-api@3.1.0", "213-jakarta.inject:jakarta.inject-api@2.0.1", "111-jakarta.annotation:jakarta.annotation-api@2.1.1", "214-com.netflix.spectator:spectator-api@1.7.3", "153-org.slf4j:slf4j-api@2.0.16", "215-org.apache.httpcomponents:httpclient@4.5.3", "216-commons-configuration:commons-configuration@1.10", "217-com.fasterxml.jackson.core:jackson-annotations@2.18.2", "218-com.fasterxml.jackson.core:jackson-core@2.18.2", "200-com.fasterxml.jackson.core:jackson-databind@2.18.2", "219-com.netflix.netflix-commons:netflix-eventbus@0.3.0", "220-javax.annotation:javax.annotation-api@1.2", "221-org.codehaus.jettison:jettison@1.5.4"]}, {"ref": "122-org.springframework.cloud:spring-cloud-starter-loadbalancer@4.2.0", "dependsOn": ["119-org.springframework.cloud:spring-cloud-starter@4.2.0", "222-org.springframework.cloud:spring-cloud-loadbalancer@4.2.0", "223-org.springframework.boot:spring-boot-starter-cache@3.4.1", "224-com.stoyanr:evictor@1.0.0"]}, {"ref": "123-com.derbysoft.next:next-commons-core@3.0.1-SNAPSHOT", "dependsOn": ["225-com.derbysoft.log:log-sdk@1.5.0", "226-ch.qos.logback:logback-classic@1.5.12", "153-org.slf4j:slf4j-api@2.0.16", "227-joda-time:joda-time@2.10.10", "127-org.apache.commons:commons-lang3@3.17.0", "228-org.apache.commons:commons-text@1.10.0", "108-commons-io:commons-io@2.14.0", "99-com.squareup.okhttp3:okhttp@4.12.0", "128-com.google.code.gson:gson@2.11.0", "92-com.google.guava:guava@31.1-jre", "109-com.alibaba.fastjson2:fastjson2@2.0.26", "200-com.fasterxml.jackson.core:jackson-databind@2.18.2", "229-com.fatboyindustrial.gson-javatime-serialisers:gson-javatime-serialisers@1.1.2", "111-jakarta.annotation:jakarta.annotation-api@2.1.1", "112-jakarta.xml.bind:jakarta.xml.bind-api@4.0.2", "113-org.glassfish.jaxb:jaxb-runtime@4.0.5"]}, {"ref": "124-com.derbysoft.next:next-commons-dto@3.0.1-SNAPSHOT", "dependsOn": ["128-com.google.code.gson:gson@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "118-org.springframework:spring-webmvc@6.2.1", "151-org.springframework.boot:spring-boot-autoconfigure@3.4.1", "230-jakarta.validation:jakarta.validation-api@3.0.2", "111-jakarta.annotation:jakarta.annotation-api@2.1.1", "112-jakarta.xml.bind:jakarta.xml.bind-api@4.0.2", "113-org.glassfish.jaxb:jaxb-runtime@4.0.5"]}, {"ref": "125-com.derbysoft.next:next-commons-hibernate@3.0.1-SNAPSHOT", "dependsOn": ["123-com.derbysoft.next:next-commons-core@3.0.1-SNAPSHOT", "124-com.derbysoft.next:next-commons-dto@3.0.1-SNAPSHOT", "231-commons-beanutils:commons-beanutils@1.9.4", "230-jakarta.validation:jakarta.validation-api@3.0.2", "86-org.springframework.boot:spring-boot-starter-web@3.4.1", "126-org.springframework.boot:spring-boot-starter-data-jpa@3.4.1", "232-org.apache.shardingsphere:shardingsphere-jdbc-core@5.4.1", "84-org.apache.groovy:groovy@4.0.24", "111-jakarta.annotation:jakarta.annotation-api@2.1.1", "112-jakarta.xml.bind:jakarta.xml.bind-api@4.0.2", "113-org.glassfish.jaxb:jaxb-runtime@4.0.5"]}, {"ref": "126-org.springframework.boot:spring-boot-starter-data-jpa@3.4.1", "dependsOn": ["114-org.springframework.boot:spring-boot-starter@3.4.1", "233-org.springframework.boot:spring-boot-starter-jdbc@3.4.1", "234-org.hibernate.orm:<EMAIL>", "235-org.springframework.data:spring-data-jpa@3.4.1", "236-org.springframework:spring-aspects@6.2.1"]}, {"ref": "127-org.apache.commons:commons-lang3@3.17.0"}, {"ref": "128-com.google.code.gson:gson@2.11.0", "dependsOn": ["143-com.google.errorprone:error_prone_annotations@2.11.0"]}, {"ref": "129-com.derby.nuke:nuke-logback-kafka-appender@1.7.0", "dependsOn": ["237-ch.qos.logback:logback-core@1.5.12", "226-ch.qos.logback:logback-classic@1.5.12", "238-javax.servlet:javax.servlet-api@3.1.0"]}, {"ref": "130-org.springdoc:springdoc-openapi-starter-webmvc-ui@2.8.4", "dependsOn": ["239-org.springdoc:springdoc-openapi-starter-webmvc-api@2.8.4", "240-org.webjars:swagger-ui@5.18.2", "241-org.webjars:webjars-locator-lite@1.0.1"]}, {"ref": "131-com.derbysoft.servreg:client@1.1.12", "dependsOn": ["242-com.derbysoft.servreg:registry@1.1.12", "199-org.yaml:snakeyaml@2.3", "127-org.apache.commons:commons-lang3@3.17.0", "243-org.jetbrains:annotations@RELEASE", "244-com.derbysoft:sdc-client@1.0.0-SNAPSHOT", "245-com.google.inject:guice@4.1.0", "161-commons-codec:commons-codec@1.17.1", "215-org.apache.httpcomponents:httpclient@4.5.3", "200-com.fasterxml.jackson.core:jackson-databind@2.18.2", "246-com.fasterxml.jackson.dataformat:jackson-dataformat-cbor@2.18.2", "153-org.slf4j:slf4j-api@2.0.16", "247-io.grpc:grpc-netty-shaded@1.38.0", "128-com.google.code.gson:gson@2.11.0", "248-io.grpc:grpc-protobuf@1.38.0", "249-com.google.protobuf:protobuf-java@3.19.6", "250-io.grpc:grpc-stub@1.38.0", "251-com.googlecode.protobuf-java-format:protobuf-java-format@1.2"]}, {"ref": "132-org.mongodb:mongodb-driver-sync@5.2.1", "dependsOn": ["252-org.mongodb:bson@5.2.1", "253-org.mongodb:mongodb-driver-core@5.2.1"]}, {"ref": "133-org.springframework.data:spring-data-mongodb@4.4.1", "dependsOn": ["254-org.springframework:spring-tx@6.2.1", "206-org.springframework:spring-context@6.2.1", "204-org.springframework:spring-beans@6.2.1", "198-org.springframework:spring-core@6.2.1", "207-org.springframework:spring-expression@6.2.1", "255-org.springframework.data:spring-data-commons@3.4.1", "253-org.mongodb:mongodb-driver-core@5.2.1", "153-org.slf4j:slf4j-api@2.0.16"]}, {"ref": "134-org.springframework.boot:spring-boot-actuator-autoconfigure@3.4.1", "dependsOn": ["256-org.springframework.boot:spring-boot-actuator@3.4.1", "196-org.springframework.boot:spring-boot@3.4.1", "151-org.springframework.boot:spring-boot-autoconfigure@3.4.1", "200-com.fasterxml.jackson.core:jackson-databind@2.18.2", "202-com.fasterxml.jackson.datatype:jackson-datatype-jsr310@2.18.2"]}, {"ref": "135-io.micrometer:micrometer-observation@1.14.2", "dependsOn": ["257-io.micrometer:micrometer-commons@1.14.2"]}, {"ref": "136-io.micrometer:micrometer-jakarta9@1.14.2", "dependsOn": ["258-io.micrometer:micrometer-core@1.14.2", "257-io.micrometer:micrometer-commons@1.14.2", "135-io.micrometer:micrometer-observation@1.14.2"]}, {"ref": "137-org.springframework.cloud:<EMAIL>", "dependsOn": ["119-org.springframework.cloud:spring-cloud-starter@4.2.0", "259-org.springframework.boot:spring-boot-starter-aop@3.4.1", "260-org.springframework.cloud:<EMAIL>"]}, {"ref": "138-org.springframework.cloud:<EMAIL>", "dependsOn": ["260-org.springframework.cloud:<EMAIL>", "117-org.springframework:spring-web@6.2.1", "146-org.springframework.cloud:spring-cloud-commons@4.2.0", "261-io.zipkin.zipkin2:zipkin@2.27.1", "262-io.zipkin.reporter2:zipkin-reporter@3.4.3", "263-io.zipkin.reporter2:zipkin-reporter-brave@3.4.3", "264-io.zipkin.reporter2:zipkin-sender-kafka@3.4.3", "265-io.zipkin.reporter2:zipkin-sender-activemq-client@3.4.3", "266-io.zipkin.reporter2:zipkin-sender-amqp-client@3.4.3"]}, {"ref": "139-com.google.guava:failureaccess@1.0.1"}, {"ref": "140-com.google.guava:listenablefuture@9999.0-empty-to-avoid-conflict-with-guava"}, {"ref": "141-com.google.code.findbugs:jsr305@3.0.2"}, {"ref": "142-org.checkerframework:checker-qual@3.12.0"}, {"ref": "143-com.google.errorprone:error_prone_annotations@2.11.0"}, {"ref": "144-com.google.j2objc:j2objc-annotations@1.3"}, {"ref": "145-org.springframework.cloud:spring-cloud-openfeign-core@4.2.0", "dependsOn": ["151-org.springframework.boot:spring-boot-autoconfigure@3.4.1", "267-io.github.openfeign:feign-form-spring@13.5"]}, {"ref": "146-org.springframework.cloud:spring-cloud-commons@4.2.0", "dependsOn": ["268-org.springframework.security:spring-security-crypto@6.4.2"]}, {"ref": "147-io.github.openfeign:feign-core@13.5"}, {"ref": "148-io.github.openfeign:feign-slf4j@13.5", "dependsOn": ["147-io.github.openfeign:feign-core@13.5", "153-org.slf4j:slf4j-api@2.0.16"]}, {"ref": "149-com.squareup:javapoet@1.13.0"}, {"ref": "150-com.google.auto.service:auto-service@1.1.1", "dependsOn": ["269-com.google.auto.service:auto-service-annotations@1.1.1", "270-com.google.auto:auto-common@1.2.1", "92-com.google.guava:guava@31.1-jre"]}, {"ref": "151-org.springframework.boot:spring-boot-autoconfigure@3.4.1", "dependsOn": ["196-org.springframework.boot:spring-boot@3.4.1"]}, {"ref": "152-com.derbysoft.schedulecenter:schedulecenter-protocol@1.2.4", "dependsOn": ["271-com.alibaba:<PERSON><PERSON><PERSON>@1.2.76", "108-commons-io:commons-io@2.14.0"]}, {"ref": "153-org.slf4j:slf4j-api@2.0.16"}, {"ref": "154-com.squareup.okio:okio@2.8.0", "dependsOn": ["272-org.jetbrains.kotlin:kotlin-stdlib@1.9.25", "273-org.jetbrains.kotlin:kotlin-stdlib-common@1.9.25"]}, {"ref": "155-org.jetbrains.kotlin:kotlin-stdlib-jdk8@1.9.25", "dependsOn": ["272-org.jetbrains.kotlin:kotlin-stdlib@1.9.25", "274-org.jetbrains.kotlin:kotlin-stdlib-jdk7@1.9.25"]}, {"ref": "156-org.springdoc:springdoc-openapi-ui@1.7.0", "dependsOn": ["275-org.springdoc:springdoc-openapi-webmvc-core@1.7.0", "240-org.webjars:swagger-ui@5.18.2"]}, {"ref": "157-org.apache.tomcat.embed:tomcat-embed-el@10.1.34"}, {"ref": "158-org.hibernate.validator:<EMAIL>", "dependsOn": ["230-jakarta.validation:jakarta.validation-api@3.0.2", "276-org.jboss.logging:<EMAIL>", "277-com.fasterxml:classmate@1.7.0"]}, {"ref": "159-org.multiverse:multiverse-core@0.7.0"}, {"ref": "160-org.codehaus.jsr166-mirror:jsr166y@1.7.0"}, {"ref": "161-commons-codec:commons-codec@1.17.1"}, {"ref": "162-org.apache.commons:commons-math3@3.6.1"}, {"ref": "163-com.zaxxer:SparseBitSet@1.3"}, {"ref": "164-org.apache.logging.log4j:log4j-api@2.24.3"}, {"ref": "165-org.apache.poi:poi-ooxml-lite@5.2.5", "dependsOn": ["166-org.apache.xmlbeans:xmlbeans@5.2.0"]}, {"ref": "166-org.apache.xmlbeans:xmlbeans@5.2.0", "dependsOn": ["164-org.apache.logging.log4j:log4j-api@2.24.3"]}, {"ref": "167-org.apache.commons:commons-compress@1.26.1", "dependsOn": ["161-commons-codec:commons-codec@1.17.1", "108-commons-io:commons-io@2.14.0", "127-org.apache.commons:commons-lang3@3.17.0"]}, {"ref": "168-com.github.virtuald:curvesapi@1.08"}, {"ref": "169-software.amazon.awssdk:aws-xml-protocol@2.29.52", "dependsOn": ["193-software.amazon.awssdk:aws-query-protocol@2.21.40", "170-software.amazon.awssdk:protocol-core@2.29.52", "187-software.amazon.awssdk:aws-core@2.29.52", "181-software.amazon.awssdk:sdk-core@2.29.52", "185-software.amazon.awssdk:annotations@2.29.52", "183-software.amazon.awssdk:http-client-spi@2.29.52", "186-software.amazon.awssdk:utils@2.29.52"]}, {"ref": "170-software.amazon.awssdk:protocol-core@2.29.52", "dependsOn": ["181-software.amazon.awssdk:sdk-core@2.29.52", "185-software.amazon.awssdk:annotations@2.29.52", "186-software.amazon.awssdk:utils@2.29.52", "183-software.amazon.awssdk:http-client-spi@2.29.52"]}, {"ref": "171-software.amazon.awssdk:arns@2.29.52", "dependsOn": ["185-software.amazon.awssdk:annotations@2.29.52", "186-software.amazon.awssdk:utils@2.29.52"]}, {"ref": "172-software.amazon.awssdk:profiles@2.29.52", "dependsOn": ["186-software.amazon.awssdk:utils@2.29.52", "185-software.amazon.awssdk:annotations@2.29.52"]}, {"ref": "173-software.amazon.awssdk:crt-core@2.29.52", "dependsOn": ["185-software.amazon.awssdk:annotations@2.29.52", "186-software.amazon.awssdk:utils@2.29.52"]}, {"ref": "174-software.amazon.awssdk:http-auth@2.29.52", "dependsOn": ["185-software.amazon.awssdk:annotations@2.29.52", "186-software.amazon.awssdk:utils@2.29.52", "183-software.amazon.awssdk:http-client-spi@2.29.52", "176-software.amazon.awssdk:http-auth-spi@2.29.52", "175-software.amazon.awssdk:identity-spi@2.29.52"]}, {"ref": "175-software.amazon.awssdk:identity-spi@2.29.52", "dependsOn": ["185-software.amazon.awssdk:annotations@2.29.52", "186-software.amazon.awssdk:utils@2.29.52"]}, {"ref": "176-software.amazon.awssdk:http-auth-spi@2.29.52", "dependsOn": ["185-software.amazon.awssdk:annotations@2.29.52", "186-software.amazon.awssdk:utils@2.29.52", "183-software.amazon.awssdk:http-client-spi@2.29.52", "278-org.reactivestreams:reactive-streams@1.0.4", "175-software.amazon.awssdk:identity-spi@2.29.52"]}, {"ref": "177-software.amazon.awssdk:http-auth-aws@2.29.52", "dependsOn": ["185-software.amazon.awssdk:annotations@2.29.52", "186-software.amazon.awssdk:utils@2.29.52", "175-software.amazon.awssdk:identity-spi@2.29.52", "183-software.amazon.awssdk:http-client-spi@2.29.52", "176-software.amazon.awssdk:http-auth-spi@2.29.52", "179-software.amazon.awssdk:checksums-spi@2.29.52", "178-software.amazon.awssdk:checksums@2.29.52"]}, {"ref": "178-software.amazon.awssdk:checksums@2.29.52", "dependsOn": ["185-software.amazon.awssdk:annotations@2.29.52", "179-software.amazon.awssdk:checksums-spi@2.29.52", "186-software.amazon.awssdk:utils@2.29.52"]}, {"ref": "179-software.amazon.awssdk:checksums-spi@2.29.52", "dependsOn": ["185-software.amazon.awssdk:annotations@2.29.52"]}, {"ref": "180-software.amazon.awssdk:retries-spi@2.29.52", "dependsOn": ["185-software.amazon.awssdk:annotations@2.29.52", "186-software.amazon.awssdk:utils@2.29.52"]}, {"ref": "181-software.amazon.awssdk:sdk-core@2.29.52", "dependsOn": ["185-software.amazon.awssdk:annotations@2.29.52", "183-software.amazon.awssdk:http-client-spi@2.29.52", "188-software.amazon.awssdk:metrics-spi@2.29.52", "190-software.amazon.awssdk:endpoints-spi@2.29.52", "176-software.amazon.awssdk:http-auth-spi@2.29.52", "177-software.amazon.awssdk:http-auth-aws@2.29.52", "179-software.amazon.awssdk:checksums-spi@2.29.52", "178-software.amazon.awssdk:checksums@2.29.52", "175-software.amazon.awssdk:identity-spi@2.29.52", "186-software.amazon.awssdk:utils@2.29.52", "172-software.amazon.awssdk:profiles@2.29.52", "180-software.amazon.awssdk:retries-spi@2.29.52", "279-software.amazon.awssdk:retries@2.29.52", "153-org.slf4j:slf4j-api@2.0.16", "278-org.reactivestreams:reactive-streams@1.0.4"]}, {"ref": "182-software.amazon.awssdk:auth@2.29.52", "dependsOn": ["185-software.amazon.awssdk:annotations@2.29.52", "186-software.amazon.awssdk:utils@2.29.52", "181-software.amazon.awssdk:sdk-core@2.29.52", "175-software.amazon.awssdk:identity-spi@2.29.52", "184-software.amazon.awssdk:regions@2.29.52", "172-software.amazon.awssdk:profiles@2.29.52", "183-software.amazon.awssdk:http-client-spi@2.29.52", "189-software.amazon.awssdk:json-utils@2.29.52", "177-software.amazon.awssdk:http-auth-aws@2.29.52", "280-software.amazon.awssdk:http-auth-aws-eventstream@2.29.52", "174-software.amazon.awssdk:http-auth@2.29.52", "176-software.amazon.awssdk:http-auth-spi@2.29.52", "281-software.amazon.eventstream:eventstream@1.0.1"]}, {"ref": "183-software.amazon.awssdk:http-client-spi@2.29.52", "dependsOn": ["185-software.amazon.awssdk:annotations@2.29.52", "186-software.amazon.awssdk:utils@2.29.52", "188-software.amazon.awssdk:metrics-spi@2.29.52", "278-org.reactivestreams:reactive-streams@1.0.4"]}, {"ref": "184-software.amazon.awssdk:regions@2.29.52", "dependsOn": ["185-software.amazon.awssdk:annotations@2.29.52", "186-software.amazon.awssdk:utils@2.29.52", "181-software.amazon.awssdk:sdk-core@2.29.52", "172-software.amazon.awssdk:profiles@2.29.52", "189-software.amazon.awssdk:json-utils@2.29.52", "153-org.slf4j:slf4j-api@2.0.16"]}, {"ref": "185-software.amazon.awssdk:annotations@2.29.52"}, {"ref": "186-software.amazon.awssdk:utils@2.29.52", "dependsOn": ["278-org.reactivestreams:reactive-streams@1.0.4", "185-software.amazon.awssdk:annotations@2.29.52", "153-org.slf4j:slf4j-api@2.0.16"]}, {"ref": "187-software.amazon.awssdk:aws-core@2.29.52", "dependsOn": ["185-software.amazon.awssdk:annotations@2.29.52", "184-software.amazon.awssdk:regions@2.29.52", "182-software.amazon.awssdk:auth@2.29.52", "176-software.amazon.awssdk:http-auth-spi@2.29.52", "175-software.amazon.awssdk:identity-spi@2.29.52", "174-software.amazon.awssdk:http-auth@2.29.52", "172-software.amazon.awssdk:profiles@2.29.52", "181-software.amazon.awssdk:sdk-core@2.29.52", "183-software.amazon.awssdk:http-client-spi@2.29.52", "188-software.amazon.awssdk:metrics-spi@2.29.52", "190-software.amazon.awssdk:endpoints-spi@2.29.52", "186-software.amazon.awssdk:utils@2.29.52", "180-software.amazon.awssdk:retries-spi@2.29.52", "279-software.amazon.awssdk:retries@2.29.52", "281-software.amazon.eventstream:eventstream@1.0.1"]}, {"ref": "188-software.amazon.awssdk:metrics-spi@2.29.52", "dependsOn": ["185-software.amazon.awssdk:annotations@2.29.52", "186-software.amazon.awssdk:utils@2.29.52"]}, {"ref": "189-software.amazon.awssdk:json-utils@2.29.52", "dependsOn": ["186-software.amazon.awssdk:utils@2.29.52", "185-software.amazon.awssdk:annotations@2.29.52", "282-software.amazon.awssdk:third-party-jackson-core@2.29.52"]}, {"ref": "190-software.amazon.awssdk:endpoints-spi@2.29.52", "dependsOn": ["185-software.amazon.awssdk:annotations@2.29.52"]}, {"ref": "191-software.amazon.awssdk:apache-client@2.29.52", "dependsOn": ["183-software.amazon.awssdk:http-client-spi@2.29.52", "188-software.amazon.awssdk:metrics-spi@2.29.52", "186-software.amazon.awssdk:utils@2.29.52", "185-software.amazon.awssdk:annotations@2.29.52", "215-org.apache.httpcomponents:httpclient@4.5.3", "283-org.apache.httpcomponents:httpcore@4.4.16", "161-commons-codec:commons-codec@1.17.1"]}, {"ref": "192-software.amazon.awssdk:netty-nio-client@2.29.52", "dependsOn": ["185-software.amazon.awssdk:annotations@2.29.52", "183-software.amazon.awssdk:http-client-spi@2.29.52", "186-software.amazon.awssdk:utils@2.29.52", "188-software.amazon.awssdk:metrics-spi@2.29.52", "284-io.netty:<EMAIL>", "285-io.netty:<EMAIL>", "286-io.netty:<EMAIL>", "287-io.netty:<EMAIL>", "288-io.netty:<EMAIL>", "289-io.netty:<EMAIL>", "290-io.netty:<EMAIL>", "291-io.netty:<EMAIL>", "292-io.netty:<EMAIL>", "278-org.reactivestreams:reactive-streams@1.0.4", "153-org.slf4j:slf4j-api@2.0.16"]}, {"ref": "193-software.amazon.awssdk:aws-query-protocol@2.21.40", "dependsOn": ["170-software.amazon.awssdk:protocol-core@2.29.52", "187-software.amazon.awssdk:aws-core@2.29.52", "181-software.amazon.awssdk:sdk-core@2.29.52", "185-software.amazon.awssdk:annotations@2.29.52", "183-software.amazon.awssdk:http-client-spi@2.29.52", "186-software.amazon.awssdk:utils@2.29.52"]}, {"ref": "194-jakarta.activation:jakarta.activation-api@2.1.3"}, {"ref": "195-org.glassfish.jaxb:jaxb-core@4.0.5", "dependsOn": ["112-jakarta.xml.bind:jakarta.xml.bind-api@4.0.2", "194-jakarta.activation:jakarta.activation-api@2.1.3", "293-org.eclipse.angus:angus-activation@2.0.2", "294-org.glassfish.jaxb:txw2@4.0.5", "295-com.sun.istack:istack-commons-runtime@4.1.2"]}, {"ref": "196-org.springframework.boot:spring-boot@3.4.1", "dependsOn": ["198-org.springframework:spring-core@6.2.1", "206-org.springframework:spring-context@6.2.1"]}, {"ref": "197-org.springframework.boot:spring-boot-starter-logging@3.4.1", "dependsOn": ["226-ch.qos.logback:logback-classic@1.5.12", "296-org.apache.logging.log4j:log4j-to-slf4j@2.24.3", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "198-org.springframework:spring-core@6.2.1"}, {"ref": "199-org.yaml:snakeyaml@2.3"}, {"ref": "200-com.fasterxml.jackson.core:jackson-databind@2.18.2", "dependsOn": ["217-com.fasterxml.jackson.core:jackson-annotations@2.18.2", "218-com.fasterxml.jackson.core:jackson-core@2.18.2"]}, {"ref": "201-com.fasterxml.jackson.datatype:jackson-datatype-jdk8@2.18.2", "dependsOn": ["218-com.fasterxml.jackson.core:jackson-core@2.18.2", "200-com.fasterxml.jackson.core:jackson-databind@2.18.2"]}, {"ref": "202-com.fasterxml.jackson.datatype:jackson-datatype-jsr310@2.18.2", "dependsOn": ["217-com.fasterxml.jackson.core:jackson-annotations@2.18.2", "218-com.fasterxml.jackson.core:jackson-core@2.18.2", "200-com.fasterxml.jackson.core:jackson-databind@2.18.2"]}, {"ref": "203-com.fasterxml.jackson.module:jackson-module-parameter-names@2.18.2", "dependsOn": ["218-com.fasterxml.jackson.core:jackson-core@2.18.2", "200-com.fasterxml.jackson.core:jackson-databind@2.18.2"]}, {"ref": "204-org.springframework:spring-beans@6.2.1", "dependsOn": ["198-org.springframework:spring-core@6.2.1"]}, {"ref": "205-org.springframework:spring-aop@6.2.1", "dependsOn": ["204-org.springframework:spring-beans@6.2.1", "198-org.springframework:spring-core@6.2.1"]}, {"ref": "206-org.springframework:spring-context@6.2.1", "dependsOn": ["205-org.springframework:spring-aop@6.2.1", "204-org.springframework:spring-beans@6.2.1", "198-org.springframework:spring-core@6.2.1", "207-org.springframework:spring-expression@6.2.1", "135-io.micrometer:micrometer-observation@1.14.2"]}, {"ref": "207-org.springframework:spring-expression@6.2.1", "dependsOn": ["198-org.springframework:spring-core@6.2.1"]}, {"ref": "208-org.springframework.cloud:spring-cloud-context@4.2.0", "dependsOn": ["268-org.springframework.security:spring-security-crypto@6.4.2"]}, {"ref": "209-org.bouncycastle:bcprov-jdk18on@1.78"}, {"ref": "210-org.apache.httpcomponents.client5:httpclient5@5.4.1", "dependsOn": ["298-org.apache.httpcomponents.core5:httpcore5@5.3.1", "299-org.apache.httpcomponents.core5:httpcore5-h2@5.3.1", "153-org.slf4j:slf4j-api@2.0.16"]}, {"ref": "211-com.thoughtworks.xstream:xstream@1.4.20", "dependsOn": ["300-io.github.x-stream:mxparser@1.2.2"]}, {"ref": "212-jakarta.ws.rs:jakarta.ws.rs-api@3.1.0"}, {"ref": "213-jakarta.inject:jakarta.inject-api@2.0.1"}, {"ref": "214-com.netflix.spectator:spectator-api@1.7.3", "dependsOn": ["153-org.slf4j:slf4j-api@2.0.16"]}, {"ref": "215-org.apache.httpcomponents:httpclient@4.5.3", "dependsOn": ["283-org.apache.httpcomponents:httpcore@4.4.16", "161-commons-codec:commons-codec@1.17.1"]}, {"ref": "216-commons-configuration:commons-configuration@1.10", "dependsOn": ["301-commons-lang:commons-lang@2.6"]}, {"ref": "217-com.fasterxml.jackson.core:jackson-annotations@2.18.2"}, {"ref": "218-com.fasterxml.jackson.core:jackson-core@2.18.2"}, {"ref": "219-com.netflix.netflix-commons:netflix-eventbus@0.3.0", "dependsOn": ["153-org.slf4j:slf4j-api@2.0.16", "302-com.netflix.netflix-commons:netflix-infix@0.3.0", "303-com.netflix.servo:servo-core@0.5.3", "304-org.apache.commons:commons-math@2.2"]}, {"ref": "220-javax.annotation:javax.annotation-api@1.2"}, {"ref": "221-org.codehaus.jettison:jettison@1.5.4"}, {"ref": "222-org.springframework.cloud:spring-cloud-loadbalancer@4.2.0", "dependsOn": ["146-org.springframework.cloud:spring-cloud-commons@4.2.0", "208-org.springframework.cloud:spring-cloud-context@4.2.0", "305-io.projectreactor:reactor-core@3.7.1", "306-io.projectreactor.addons:reactor-extra@3.5.2"]}, {"ref": "223-org.springframework.boot:spring-boot-starter-cache@3.4.1", "dependsOn": ["114-org.springframework.boot:spring-boot-starter@3.4.1", "307-org.springframework:spring-context-support@6.2.1"]}, {"ref": "224-com.stoyanr:evictor@1.0.0"}, {"ref": "225-com.derbysoft.log:log-sdk@1.5.0", "dependsOn": ["153-org.slf4j:slf4j-api@2.0.16"]}, {"ref": "226-ch.qos.logback:logback-classic@1.5.12", "dependsOn": ["237-ch.qos.logback:logback-core@1.5.12", "153-org.slf4j:slf4j-api@2.0.16"]}, {"ref": "227-joda-time:joda-time@2.10.10"}, {"ref": "228-org.apache.commons:commons-text@1.10.0", "dependsOn": ["127-org.apache.commons:commons-lang3@3.17.0"]}, {"ref": "229-com.fatboyindustrial.gson-javatime-serialisers:gson-javatime-serialisers@1.1.2", "dependsOn": ["128-com.google.code.gson:gson@2.11.0"]}, {"ref": "230-jakarta.validation:jakarta.validation-api@3.0.2"}, {"ref": "231-commons-beanutils:commons-beanutils@1.9.4", "dependsOn": ["308-commons-logging:commons-logging@1.2", "309-commons-collections:commons-collections@3.2.2"]}, {"ref": "232-org.apache.shardingsphere:shardingsphere-jdbc-core@5.4.1", "dependsOn": ["310-org.apache.shardingsphere:shardingsphere-transaction-core@5.4.1", "311-org.apache.shardingsphere:shardingsphere-global-clock-core@5.4.1", "312-org.apache.shardingsphere:shardingsphere-global-clock-tso-core@5.4.1", "313-org.apache.shardingsphere:shardingsphere-parser-sql-sql92@5.4.1", "314-org.apache.shardingsphere:shardingsphere-parser-sql-mysql@5.4.1", "315-org.apache.shardingsphere:shardingsphere-parser-sql-postgresql@5.4.1", "316-org.apache.shardingsphere:shardingsphere-parser-sql-oracle@5.4.1", "317-org.apache.shardingsphere:shardingsphere-parser-sql-sqlserver@5.4.1", "318-org.apache.shardingsphere:shardingsphere-parser-sql-opengauss@5.4.1", "319-org.apache.shardingsphere:shardingsphere-mysql-dialect-exception@5.4.1", "320-org.apache.shardingsphere:shardingsphere-postgresql-dialect-exception@5.4.1", "321-org.apache.shardingsphere:shardingsphere-authority-core@5.4.1", "322-org.apache.shardingsphere:shardingsphere-single-core@5.4.1", "323-org.apache.shardingsphere:shardingsphere-traffic-core@5.4.1", "324-org.apache.shardingsphere:shardingsphere-infra-context@5.4.1", "325-org.apache.shardingsphere:shardingsphere-standalone-mode-core@5.4.1", "326-org.apache.shardingsphere:shardingsphere-standalone-mode-repository-jdbc@5.4.1", "327-org.apache.shardingsphere:shardingsphere-cluster-mode-core@5.4.1", "328-org.apache.shardingsphere:shardingsphere-sharding-core@5.4.1", "329-org.apache.shardingsphere:shardingsphere-broadcast-core@5.4.1", "330-org.apache.shardingsphere:shardingsphere-readwrite-splitting-core@5.4.1", "331-org.apache.shardingsphere:shardingsphere-encrypt-core@5.4.1", "332-org.apache.shardingsphere:shardingsphere-mask-core@5.4.1", "333-org.apache.shardingsphere:shardingsphere-shadow-core@5.4.1", "334-org.apache.shardingsphere:shardingsphere-sql-federation-core@5.4.1", "335-org.apache.shardingsphere:shardingsphere-sql-parser-core@5.4.1", "336-org.apache.shardingsphere:shardingsphere-logging-core@5.4.1", "337-com.h2database:h2@2.3.232", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "233-org.springframework.boot:spring-boot-starter-jdbc@3.4.1", "dependsOn": ["114-org.springframework.boot:spring-boot-starter@3.4.1", "339-com.zaxxer:HikariCP@5.1.0", "340-org.springframework:spring-jdbc@6.2.1"]}, {"ref": "234-org.hibernate.orm:<EMAIL>", "dependsOn": ["341-jakarta.persistence:jakarta.persistence-api@3.1.0", "342-jakarta.transaction:jakarta.transaction-api@2.0.1", "276-org.jboss.logging:<EMAIL>", "343-org.hibernate.common:<EMAIL>", "344-io.smallrye:jandex@3.2.0", "277-com.fasterxml:classmate@1.7.0", "345-net.bytebuddy:byte-buddy@1.15.11", "112-jakarta.xml.bind:jakarta.xml.bind-api@4.0.2", "113-org.glassfish.jaxb:jaxb-runtime@4.0.5", "213-jakarta.inject:jakarta.inject-api@2.0.1", "346-org.antlr:antlr4-runtime@4.13.0"]}, {"ref": "235-org.springframework.data:spring-data-jpa@3.4.1", "dependsOn": ["255-org.springframework.data:spring-data-commons@3.4.1", "347-org.springframework:spring-orm@6.2.1", "206-org.springframework:spring-context@6.2.1", "205-org.springframework:spring-aop@6.2.1", "254-org.springframework:spring-tx@6.2.1", "204-org.springframework:spring-beans@6.2.1", "198-org.springframework:spring-core@6.2.1", "346-org.antlr:antlr4-runtime@4.13.0", "111-jakarta.annotation:jakarta.annotation-api@2.1.1", "153-org.slf4j:slf4j-api@2.0.16"]}, {"ref": "236-org.springframework:spring-aspects@6.2.1", "dependsOn": ["348-org.aspectj:aspectjweaver@********"]}, {"ref": "237-ch.qos.logback:logback-core@1.5.12"}, {"ref": "238-javax.servlet:javax.servlet-api@3.1.0"}, {"ref": "239-org.springdoc:springdoc-openapi-starter-webmvc-api@2.8.4", "dependsOn": ["349-org.springdoc:springdoc-openapi-starter-common@2.8.4", "118-org.springframework:spring-webmvc@6.2.1"]}, {"ref": "240-org.webjars:swagger-ui@5.18.2"}, {"ref": "241-org.webjars:webjars-locator-lite@1.0.1", "dependsOn": ["350-org.jspecify:jspecify@1.0.0"]}, {"ref": "242-com.derbysoft.servreg:registry@1.1.12", "dependsOn": ["351-com.derbysoft.servreg:model@1.1.12", "161-commons-codec:commons-codec@1.17.1", "215-org.apache.httpcomponents:httpclient@4.5.3", "200-com.fasterxml.jackson.core:jackson-databind@2.18.2", "246-com.fasterxml.jackson.dataformat:jackson-dataformat-cbor@2.18.2", "153-org.slf4j:slf4j-api@2.0.16", "247-io.grpc:grpc-netty-shaded@1.38.0", "128-com.google.code.gson:gson@2.11.0", "248-io.grpc:grpc-protobuf@1.38.0", "249-com.google.protobuf:protobuf-java@3.19.6", "250-io.grpc:grpc-stub@1.38.0", "251-com.googlecode.protobuf-java-format:protobuf-java-format@1.2"]}, {"ref": "243-org.jetbrains:annotations@RELEASE"}, {"ref": "244-com.derbysoft:sdc-client@1.0.0-SNAPSHOT", "dependsOn": ["200-com.fasterxml.jackson.core:jackson-databind@2.18.2", "108-commons-io:commons-io@2.14.0"]}, {"ref": "245-com.google.inject:guice@4.1.0", "dependsOn": ["352-javax.inject:javax.inject@1", "353-aopalliance:aopalliance@1.0"]}, {"ref": "246-com.fasterxml.jackson.dataformat:jackson-dataformat-cbor@2.18.2", "dependsOn": ["200-com.fasterxml.jackson.core:jackson-databind@2.18.2", "218-com.fasterxml.jackson.core:jackson-core@2.18.2"]}, {"ref": "247-io.grpc:grpc-netty-shaded@1.38.0", "dependsOn": ["354-io.grpc:grpc-core@1.38.0"]}, {"ref": "248-io.grpc:grpc-protobuf@1.38.0", "dependsOn": ["355-io.grpc:grpc-api@1.38.0", "141-com.google.code.findbugs:jsr305@3.0.2", "356-com.google.api.grpc:proto-google-common-protos@2.0.1", "357-io.grpc:grpc-protobuf-lite@1.38.0", "92-com.google.guava:guava@31.1-jre", "143-com.google.errorprone:error_prone_annotations@2.11.0", "358-org.codehaus.mojo:animal-sniffer-annotations@1.19"]}, {"ref": "249-com.google.protobuf:protobuf-java@3.19.6"}, {"ref": "250-io.grpc:grpc-stub@1.38.0", "dependsOn": ["355-io.grpc:grpc-api@1.38.0", "92-com.google.guava:guava@31.1-jre", "143-com.google.errorprone:error_prone_annotations@2.11.0", "358-org.codehaus.mojo:animal-sniffer-annotations@1.19", "141-com.google.code.findbugs:jsr305@3.0.2"]}, {"ref": "251-com.googlecode.protobuf-java-format:protobuf-java-format@1.2"}, {"ref": "252-org.mongodb:bson@5.2.1"}, {"ref": "253-org.mongodb:mongodb-driver-core@5.2.1", "dependsOn": ["252-org.mongodb:bson@5.2.1", "359-org.mongodb:bson-record-codec@5.2.1"]}, {"ref": "254-org.springframework:spring-tx@6.2.1", "dependsOn": ["204-org.springframework:spring-beans@6.2.1", "198-org.springframework:spring-core@6.2.1"]}, {"ref": "255-org.springframework.data:spring-data-commons@3.4.1", "dependsOn": ["198-org.springframework:spring-core@6.2.1", "204-org.springframework:spring-beans@6.2.1", "153-org.slf4j:slf4j-api@2.0.16"]}, {"ref": "256-org.springframework.boot:spring-boot-actuator@3.4.1", "dependsOn": ["196-org.springframework.boot:spring-boot@3.4.1"]}, {"ref": "257-io.micrometer:micrometer-commons@1.14.2"}, {"ref": "258-io.micrometer:micrometer-core@1.14.2", "dependsOn": ["257-io.micrometer:micrometer-commons@1.14.2", "135-io.micrometer:micrometer-observation@1.14.2", "360-org.hdrhistogram:HdrHistogram@2.2.2", "361-org.latencyutils:LatencyUtils@2.0.3"]}, {"ref": "259-org.springframework.boot:spring-boot-starter-aop@3.4.1", "dependsOn": ["114-org.springframework.boot:spring-boot-starter@3.4.1", "205-org.springframework:spring-aop@6.2.1", "348-org.aspectj:aspectjweaver@********"]}, {"ref": "260-org.springframework.cloud:<EMAIL>", "dependsOn": ["146-org.springframework.cloud:spring-cloud-commons@4.2.0", "206-org.springframework:spring-context@6.2.1", "362-org.aspectj:aspectjrt@********", "363-io.zipkin.brave:brave@6.0.3", "364-io.zipkin.brave:brave-context-slf4j@6.0.3", "365-io.zipkin.brave:brave-instrumentation-messaging@6.0.3", "366-io.zipkin.brave:brave-instrumentation-rpc@6.0.3", "367-io.zipkin.brave:brave-instrumentation-spring-web@6.0.3", "368-io.zipkin.brave:brave-instrumentation-spring-rabbit@6.0.3", "369-io.zipkin.brave:brave-instrumentation-kafka-clients@6.0.3", "370-io.zipkin.brave:brave-instrumentation-kafka-streams@6.0.3", "371-io.zipkin.brave:brave-instrumentation-httpclient@6.0.3", "372-io.zipkin.brave:brave-instrumentation-httpasyncclient@6.0.3", "373-io.zipkin.brave:brave-instrumentation-spring-webmvc@6.0.3", "374-io.zipkin.brave:brave-instrumentation-jms@6.0.3", "375-io.zipkin.reporter2:zipkin-reporter-metrics-micrometer@3.4.3"]}, {"ref": "261-io.zipkin.zipkin2:zipkin@2.27.1"}, {"ref": "262-io.zipkin.reporter2:zipkin-reporter@3.4.3", "dependsOn": ["261-io.zipkin.zipkin2:zipkin@2.27.1"]}, {"ref": "263-io.zipkin.reporter2:zipkin-reporter-brave@3.4.3", "dependsOn": ["262-io.zipkin.reporter2:zipkin-reporter@3.4.3"]}, {"ref": "264-io.zipkin.reporter2:zipkin-sender-kafka@3.4.3", "dependsOn": ["262-io.zipkin.reporter2:zipkin-reporter@3.4.3"]}, {"ref": "265-io.zipkin.reporter2:zipkin-sender-activemq-client@3.4.3", "dependsOn": ["262-io.zipkin.reporter2:zipkin-reporter@3.4.3"]}, {"ref": "266-io.zipkin.reporter2:zipkin-sender-amqp-client@3.4.3", "dependsOn": ["262-io.zipkin.reporter2:zipkin-reporter@3.4.3"]}, {"ref": "267-io.github.openfeign:feign-form-spring@13.5", "dependsOn": ["376-io.github.openfeign:feign-form@13.5", "117-org.springframework:spring-web@6.2.1", "107-commons-fileupload:commons-fileupload@1.5"]}, {"ref": "268-org.springframework.security:spring-security-crypto@6.4.2"}, {"ref": "269-com.google.auto.service:auto-service-annotations@1.1.1"}, {"ref": "270-com.google.auto:auto-common@1.2.1", "dependsOn": ["92-com.google.guava:guava@31.1-jre"]}, {"ref": "271-com.alibaba:<PERSON><PERSON><PERSON>@1.2.76"}, {"ref": "272-org.jetbrains.kotlin:kotlin-stdlib@1.9.25", "dependsOn": ["243-org.jetbrains:annotations@RELEASE"]}, {"ref": "273-org.jetbrains.kotlin:kotlin-stdlib-common@1.9.25"}, {"ref": "274-org.jetbrains.kotlin:kotlin-stdlib-jdk7@1.9.25", "dependsOn": ["272-org.jetbrains.kotlin:kotlin-stdlib@1.9.25"]}, {"ref": "275-org.springdoc:springdoc-openapi-webmvc-core@1.7.0", "dependsOn": ["377-org.springdoc:springdoc-openapi-common@1.7.0", "118-org.springframework:spring-webmvc@6.2.1"]}, {"ref": "276-org.jboss.logging:<EMAIL>"}, {"ref": "277-com.fasterxml:classmate@1.7.0"}, {"ref": "278-org.reactivestreams:reactive-streams@1.0.4"}, {"ref": "279-software.amazon.awssdk:retries@2.29.52", "dependsOn": ["180-software.amazon.awssdk:retries-spi@2.29.52", "185-software.amazon.awssdk:annotations@2.29.52", "186-software.amazon.awssdk:utils@2.29.52"]}, {"ref": "280-software.amazon.awssdk:http-auth-aws-eventstream@2.29.52", "dependsOn": ["185-software.amazon.awssdk:annotations@2.29.52", "281-software.amazon.eventstream:eventstream@1.0.1"]}, {"ref": "281-software.amazon.eventstream:eventstream@1.0.1"}, {"ref": "282-software.amazon.awssdk:third-party-jackson-core@2.29.52"}, {"ref": "283-org.apache.httpcomponents:httpcore@4.4.16"}, {"ref": "284-io.netty:<EMAIL>", "dependsOn": ["288-io.netty:<EMAIL>", "289-io.netty:<EMAIL>", "287-io.netty:<EMAIL>", "286-io.netty:<EMAIL>", "290-io.netty:<EMAIL>"]}, {"ref": "285-io.netty:<EMAIL>", "dependsOn": ["288-io.netty:<EMAIL>", "289-io.netty:<EMAIL>", "287-io.netty:<EMAIL>", "286-io.netty:<EMAIL>", "290-io.netty:<EMAIL>", "284-io.netty:<EMAIL>"]}, {"ref": "286-io.netty:<EMAIL>", "dependsOn": ["288-io.netty:<EMAIL>", "289-io.netty:<EMAIL>", "287-io.netty:<EMAIL>"]}, {"ref": "287-io.netty:<EMAIL>", "dependsOn": ["288-io.netty:<EMAIL>", "289-io.netty:<EMAIL>", "292-io.netty:<EMAIL>"]}, {"ref": "288-io.netty:<EMAIL>"}, {"ref": "289-io.netty:<EMAIL>", "dependsOn": ["288-io.netty:<EMAIL>"]}, {"ref": "290-io.netty:<EMAIL>", "dependsOn": ["288-io.netty:<EMAIL>", "292-io.netty:<EMAIL>", "289-io.netty:<EMAIL>", "287-io.netty:<EMAIL>", "378-io.netty:<EMAIL>", "286-io.netty:<EMAIL>"]}, {"ref": "291-io.netty:<EMAIL>", "dependsOn": ["288-io.netty:<EMAIL>", "289-io.netty:<EMAIL>", "287-io.netty:<EMAIL>", "378-io.netty:<EMAIL>"]}, {"ref": "292-io.netty:<EMAIL>", "dependsOn": ["288-io.netty:<EMAIL>"]}, {"ref": "293-org.eclipse.angus:angus-activation@2.0.2", "dependsOn": ["194-jakarta.activation:jakarta.activation-api@2.1.3"]}, {"ref": "294-org.glassfish.jaxb:txw2@4.0.5"}, {"ref": "295-com.sun.istack:istack-commons-runtime@4.1.2"}, {"ref": "296-org.apache.logging.log4j:log4j-to-slf4j@2.24.3", "dependsOn": ["164-org.apache.logging.log4j:log4j-api@2.24.3", "153-org.slf4j:slf4j-api@2.0.16"]}, {"ref": "297-org.slf4j:jul-to-slf4j@2.0.16", "dependsOn": ["153-org.slf4j:slf4j-api@2.0.16"]}, {"ref": "298-org.apache.httpcomponents.core5:httpcore5@5.3.1"}, {"ref": "299-org.apache.httpcomponents.core5:httpcore5-h2@5.3.1", "dependsOn": ["298-org.apache.httpcomponents.core5:httpcore5@5.3.1"]}, {"ref": "300-io.github.x-stream:mxparser@1.2.2", "dependsOn": ["379-xmlpull:xmlpull@*******"]}, {"ref": "301-commons-lang:commons-lang@2.6"}, {"ref": "302-com.netflix.netflix-commons:netflix-infix@0.3.0", "dependsOn": ["153-org.slf4j:slf4j-api@2.0.16", "380-commons-jxpath:commons-jxpath@1.3", "227-joda-time:joda-time@2.10.10", "381-org.antlr:antlr-runtime@3.4", "92-com.google.guava:guava@31.1-jre", "128-com.google.code.gson:gson@2.11.0"]}, {"ref": "303-com.netflix.servo:servo-core@0.5.3", "dependsOn": ["153-org.slf4j:slf4j-api@2.0.16", "92-com.google.guava:guava@31.1-jre"]}, {"ref": "304-org.apache.commons:commons-math@2.2"}, {"ref": "305-io.projectreactor:reactor-core@3.7.1", "dependsOn": ["278-org.reactivestreams:reactive-streams@1.0.4"]}, {"ref": "306-io.projectreactor.addons:reactor-extra@3.5.2", "dependsOn": ["305-io.projectreactor:reactor-core@3.7.1"]}, {"ref": "307-org.springframework:spring-context-support@6.2.1", "dependsOn": ["204-org.springframework:spring-beans@6.2.1", "206-org.springframework:spring-context@6.2.1", "198-org.springframework:spring-core@6.2.1"]}, {"ref": "308-commons-logging:commons-logging@1.2"}, {"ref": "309-commons-collections:commons-collections@3.2.2"}, {"ref": "310-org.apache.shardingsphere:shardingsphere-transaction-core@5.4.1", "dependsOn": ["382-org.apache.shardingsphere:shardingsphere-transaction-api@5.4.1", "383-org.apache.shardingsphere:shardingsphere-infra-executor@5.4.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "311-org.apache.shardingsphere:shardingsphere-global-clock-core@5.4.1", "dependsOn": ["384-org.apache.shardingsphere:shardingsphere-global-clock-api@5.4.1", "385-org.apache.shardingsphere:shardingsphere-infra-common@5.4.1", "382-org.apache.shardingsphere:shardingsphere-transaction-api@5.4.1", "386-org.apache.shardingsphere:shardingsphere-mode-core@5.4.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "312-org.apache.shardingsphere:shardingsphere-global-clock-tso-core@5.4.1", "dependsOn": ["384-org.apache.shardingsphere:shardingsphere-global-clock-api@5.4.1", "387-org.apache.shardingsphere:shardingsphere-global-clock-tso-spi@5.4.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "313-org.apache.shardingsphere:shardingsphere-parser-sql-sql92@5.4.1", "dependsOn": ["388-org.apache.shardingsphere:shardingsphere-infra-database-sql92@5.4.1", "389-org.apache.shardingsphere:shardingsphere-parser-sql-engine@5.4.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "314-org.apache.shardingsphere:shardingsphere-parser-sql-mysql@5.4.1", "dependsOn": ["390-org.apache.shardingsphere:shardingsphere-infra-database-mysql@5.4.1", "389-org.apache.shardingsphere:shardingsphere-parser-sql-engine@5.4.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "315-org.apache.shardingsphere:shardingsphere-parser-sql-postgresql@5.4.1", "dependsOn": ["391-org.apache.shardingsphere:shardingsphere-infra-database-postgresql@5.4.1", "389-org.apache.shardingsphere:shardingsphere-parser-sql-engine@5.4.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "316-org.apache.shardingsphere:shardingsphere-parser-sql-oracle@5.4.1", "dependsOn": ["392-org.apache.shardingsphere:shardingsphere-infra-database-oracle@5.4.1", "389-org.apache.shardingsphere:shardingsphere-parser-sql-engine@5.4.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "317-org.apache.shardingsphere:shardingsphere-parser-sql-sqlserver@5.4.1", "dependsOn": ["393-org.apache.shardingsphere:shardingsphere-infra-database-sqlserver@5.4.1", "389-org.apache.shardingsphere:shardingsphere-parser-sql-engine@5.4.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "318-org.apache.shardingsphere:shardingsphere-parser-sql-opengauss@5.4.1", "dependsOn": ["394-org.apache.shardingsphere:shardingsphere-infra-database-opengauss@5.4.1", "389-org.apache.shardingsphere:shardingsphere-parser-sql-engine@5.4.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "319-org.apache.shardingsphere:shardingsphere-mysql-dialect-exception@5.4.1", "dependsOn": ["395-org.apache.shardingsphere:shardingsphere-infra-exception-dialect-core@5.4.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "320-org.apache.shardingsphere:shardingsphere-postgresql-dialect-exception@5.4.1", "dependsOn": ["395-org.apache.shardingsphere:shardingsphere-infra-exception-dialect-core@5.4.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "321-org.apache.shardingsphere:shardingsphere-authority-core@5.4.1", "dependsOn": ["396-org.apache.shardingsphere:shardingsphere-authority-api@5.4.1", "383-org.apache.shardingsphere:shardingsphere-infra-executor@5.4.1", "397-org.apache.shardingsphere:shardingsphere-mode-api@5.4.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "322-org.apache.shardingsphere:shardingsphere-single-core@5.4.1", "dependsOn": ["398-org.apache.shardingsphere:shardingsphere-single-api@5.4.1", "399-org.apache.shardingsphere:shardingsphere-sql-federation-api@5.4.1", "400-org.apache.shardingsphere:shardingsphere-infra-route@5.4.1", "324-org.apache.shardingsphere:shardingsphere-infra-context@5.4.1", "386-org.apache.shardingsphere:shardingsphere-mode-core@5.4.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "323-org.apache.shardingsphere:shardingsphere-traffic-core@5.4.1", "dependsOn": ["385-org.apache.shardingsphere:shardingsphere-infra-common@5.4.1", "401-org.apache.shardingsphere:shardingsphere-infra-binder@5.4.1", "402-org.apache.shardingsphere:shardingsphere-traffic-api@5.4.1", "327-org.apache.shardingsphere:shardingsphere-cluster-mode-core@5.4.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "324-org.apache.shardingsphere:shardingsphere-infra-context@5.4.1", "dependsOn": ["401-org.apache.shardingsphere:shardingsphere-infra-binder@5.4.1", "403-org.apache.shardingsphere:shardingsphere-infra-session@5.4.1", "383-org.apache.shardingsphere:shardingsphere-infra-executor@5.4.1", "404-org.apache.shardingsphere:shardingsphere-parser-distsql-engine@5.4.1", "321-org.apache.shardingsphere:shardingsphere-authority-core@5.4.1", "398-org.apache.shardingsphere:shardingsphere-single-api@5.4.1", "310-org.apache.shardingsphere:shardingsphere-transaction-core@5.4.1", "405-org.apache.shardingsphere:shardingsphere-sql-translator-core@5.4.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "325-org.apache.shardingsphere:shardingsphere-standalone-mode-core@5.4.1", "dependsOn": ["386-org.apache.shardingsphere:shardingsphere-mode-core@5.4.1", "406-org.apache.shardingsphere:shardingsphere-standalone-mode-repository-api@5.4.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "326-org.apache.shardingsphere:shardingsphere-standalone-mode-repository-jdbc@5.4.1", "dependsOn": ["406-org.apache.shardingsphere:shardingsphere-standalone-mode-repository-api@5.4.1", "407-javax.xml.bind:jaxb-api@2.3.0", "408-com.sun.xml.bind:jaxb-core@4.0.5", "409-com.sun.xml.bind:jaxb-impl@4.0.5", "410-javax.activation:javax.activation-api@1.2.0", "339-com.zaxxer:HikariCP@5.1.0", "337-com.h2database:h2@2.3.232", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "327-org.apache.shardingsphere:shardingsphere-cluster-mode-core@5.4.1", "dependsOn": ["386-org.apache.shardingsphere:shardingsphere-mode-core@5.4.1", "411-org.apache.shardingsphere:shardingsphere-cluster-mode-repository-api@5.4.1", "321-org.apache.shardingsphere:shardingsphere-authority-core@5.4.1", "322-org.apache.shardingsphere:shardingsphere-single-core@5.4.1", "412-org.apache.shardingsphere:shardingsphere-metadata-core@5.4.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "328-org.apache.shardingsphere:shardingsphere-sharding-core@5.4.1", "dependsOn": ["413-org.apache.shardingsphere:shardingsphere-sharding-api@5.4.1", "414-org.apache.shardingsphere:shardingsphere-data-pipeline-api@5.4.1", "401-org.apache.shardingsphere:shardingsphere-infra-binder@5.4.1", "400-org.apache.shardingsphere:shardingsphere-infra-route@5.4.1", "415-org.apache.shardingsphere:shardingsphere-infra-rewrite@5.4.1", "416-org.apache.shardingsphere:shardingsphere-infra-merge@5.4.1", "386-org.apache.shardingsphere:shardingsphere-mode-core@5.4.1", "417-org.apache.shardingsphere:shardingsphere-time-service-core@5.4.1", "418-org.apache.shardingsphere:shardingsphere-system-time-service@5.4.1", "334-org.apache.shardingsphere:shardingsphere-sql-federation-core@5.4.1", "419-org.apache.shardingsphere:shardingsphere-infra-expr-core@5.4.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "329-org.apache.shardingsphere:shardingsphere-broadcast-core@5.4.1", "dependsOn": ["420-org.apache.shardingsphere:shardingsphere-broadcast-api@5.4.1", "334-org.apache.shardingsphere:shardingsphere-sql-federation-core@5.4.1", "401-org.apache.shardingsphere:shardingsphere-infra-binder@5.4.1", "400-org.apache.shardingsphere:shardingsphere-infra-route@5.4.1", "386-org.apache.shardingsphere:shardingsphere-mode-core@5.4.1", "419-org.apache.shardingsphere:shardingsphere-infra-expr-core@5.4.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "330-org.apache.shardingsphere:shardingsphere-readwrite-splitting-core@5.4.1", "dependsOn": ["421-org.apache.shardingsphere:shardingsphere-readwrite-splitting-api@5.4.1", "400-org.apache.shardingsphere:shardingsphere-infra-route@5.4.1", "386-org.apache.shardingsphere:shardingsphere-mode-core@5.4.1", "310-org.apache.shardingsphere:shardingsphere-transaction-core@5.4.1", "419-org.apache.shardingsphere:shardingsphere-infra-expr-core@5.4.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "331-org.apache.shardingsphere:shardingsphere-encrypt-core@5.4.1", "dependsOn": ["422-org.apache.shardingsphere:shardingsphere-encrypt-api@5.4.1", "415-org.apache.shardingsphere:shardingsphere-infra-rewrite@5.4.1", "416-org.apache.shardingsphere:shardingsphere-infra-merge@5.4.1", "386-org.apache.shardingsphere:shardingsphere-mode-core@5.4.1", "161-commons-codec:commons-codec@1.17.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "332-org.apache.shardingsphere:shardingsphere-mask-core@5.4.1", "dependsOn": ["423-org.apache.shardingsphere:shardingsphere-mask-api@5.4.1", "416-org.apache.shardingsphere:shardingsphere-infra-merge@5.4.1", "386-org.apache.shardingsphere:shardingsphere-mode-core@5.4.1", "161-commons-codec:commons-codec@1.17.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "333-org.apache.shardingsphere:shardingsphere-shadow-core@5.4.1", "dependsOn": ["424-org.apache.shardingsphere:shardingsphere-shadow-api@5.4.1", "401-org.apache.shardingsphere:shardingsphere-infra-binder@5.4.1", "400-org.apache.shardingsphere:shardingsphere-infra-route@5.4.1", "415-org.apache.shardingsphere:shardingsphere-infra-rewrite@5.4.1", "386-org.apache.shardingsphere:shardingsphere-mode-core@5.4.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "334-org.apache.shardingsphere:shardingsphere-sql-federation-core@5.4.1", "dependsOn": ["399-org.apache.shardingsphere:shardingsphere-sql-federation-api@5.4.1", "383-org.apache.shardingsphere:shardingsphere-infra-executor@5.4.1", "416-org.apache.shardingsphere:shardingsphere-infra-merge@5.4.1", "324-org.apache.shardingsphere:shardingsphere-infra-context@5.4.1", "385-org.apache.shardingsphere:shardingsphere-infra-common@5.4.1", "401-org.apache.shardingsphere:shardingsphere-infra-binder@5.4.1", "128-com.google.code.gson:gson@2.11.0", "335-org.apache.shardingsphere:shardingsphere-sql-parser-core@5.4.1", "425-org.apache.calcite:calcite-core@1.35.0", "426-com.jayway.jsonpath:json-path@2.9.0", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "335-org.apache.shardingsphere:shardingsphere-sql-parser-core@5.4.1", "dependsOn": ["427-org.apache.shardingsphere:shardingsphere-sql-parser-api@5.4.1", "397-org.apache.shardingsphere:shardingsphere-mode-api@5.4.1", "428-org.apache.shardingsphere:shardingsphere-infra-parser@5.4.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "336-org.apache.shardingsphere:shardingsphere-logging-core@5.4.1", "dependsOn": ["429-org.apache.shardingsphere:shardingsphere-logging-api@5.4.1", "397-org.apache.shardingsphere:shardingsphere-mode-api@5.4.1", "226-ch.qos.logback:logback-classic@1.5.12", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "337-com.h2database:h2@2.3.232"}, {"ref": "338-org.slf4j:jcl-over-slf4j@2.0.16", "dependsOn": ["153-org.slf4j:slf4j-api@2.0.16"]}, {"ref": "339-com.zaxxer:HikariCP@5.1.0", "dependsOn": ["153-org.slf4j:slf4j-api@2.0.16"]}, {"ref": "340-org.springframework:spring-jdbc@6.2.1", "dependsOn": ["204-org.springframework:spring-beans@6.2.1", "198-org.springframework:spring-core@6.2.1", "254-org.springframework:spring-tx@6.2.1"]}, {"ref": "341-jakarta.persistence:jakarta.persistence-api@3.1.0"}, {"ref": "342-jakarta.transaction:jakarta.transaction-api@2.0.1"}, {"ref": "343-org.hibernate.common:<EMAIL>"}, {"ref": "344-io.smallrye:jandex@3.2.0"}, {"ref": "345-net.bytebuddy:byte-buddy@1.15.11"}, {"ref": "346-org.antlr:antlr4-runtime@4.13.0"}, {"ref": "347-org.springframework:spring-orm@6.2.1", "dependsOn": ["204-org.springframework:spring-beans@6.2.1", "198-org.springframework:spring-core@6.2.1", "340-org.springframework:spring-jdbc@6.2.1", "254-org.springframework:spring-tx@6.2.1"]}, {"ref": "348-org.aspectj:aspectjweaver@********"}, {"ref": "349-org.springdoc:springdoc-openapi-starter-common@2.8.4", "dependsOn": ["151-org.springframework.boot:spring-boot-autoconfigure@3.4.1", "430-io.swagger.core.v3:swagger-core-jakarta@2.2.28"]}, {"ref": "350-org.jspecify:jspecify@1.0.0"}, {"ref": "351-com.derbysoft.servreg:model@1.1.12", "dependsOn": ["161-commons-codec:commons-codec@1.17.1", "215-org.apache.httpcomponents:httpclient@4.5.3", "200-com.fasterxml.jackson.core:jackson-databind@2.18.2", "246-com.fasterxml.jackson.dataformat:jackson-dataformat-cbor@2.18.2", "153-org.slf4j:slf4j-api@2.0.16", "247-io.grpc:grpc-netty-shaded@1.38.0", "128-com.google.code.gson:gson@2.11.0", "248-io.grpc:grpc-protobuf@1.38.0", "249-com.google.protobuf:protobuf-java@3.19.6", "250-io.grpc:grpc-stub@1.38.0", "251-com.googlecode.protobuf-java-format:protobuf-java-format@1.2"]}, {"ref": "352-javax.inject:javax.inject@1"}, {"ref": "353-aopalliance:aopalliance@1.0"}, {"ref": "354-io.grpc:grpc-core@1.38.0", "dependsOn": ["355-io.grpc:grpc-api@1.38.0", "431-com.google.android:annotations@*******", "358-org.codehaus.mojo:animal-sniffer-annotations@1.19", "143-com.google.errorprone:error_prone_annotations@2.11.0", "92-com.google.guava:guava@31.1-jre", "432-io.perfmark:perfmark-api@0.23.0", "141-com.google.code.findbugs:jsr305@3.0.2"]}, {"ref": "355-io.grpc:grpc-api@1.38.0", "dependsOn": ["433-io.grpc:grpc-context@1.38.0", "141-com.google.code.findbugs:jsr305@3.0.2", "92-com.google.guava:guava@31.1-jre", "143-com.google.errorprone:error_prone_annotations@2.11.0", "358-org.codehaus.mojo:animal-sniffer-annotations@1.19"]}, {"ref": "356-com.google.api.grpc:proto-google-common-protos@2.0.1"}, {"ref": "357-io.grpc:grpc-protobuf-lite@1.38.0", "dependsOn": ["355-io.grpc:grpc-api@1.38.0", "141-com.google.code.findbugs:jsr305@3.0.2", "92-com.google.guava:guava@31.1-jre", "143-com.google.errorprone:error_prone_annotations@2.11.0", "358-org.codehaus.mojo:animal-sniffer-annotations@1.19"]}, {"ref": "358-org.codehaus.mojo:animal-sniffer-annotations@1.19"}, {"ref": "359-org.mongodb:bson-record-codec@5.2.1", "dependsOn": ["252-org.mongodb:bson@5.2.1"]}, {"ref": "360-org.hdrhistogram:HdrHistogram@2.2.2"}, {"ref": "361-org.latencyutils:LatencyUtils@2.0.3"}, {"ref": "362-org.aspectj:aspectjrt@********"}, {"ref": "363-io.zipkin.brave:brave@6.0.3"}, {"ref": "364-io.zipkin.brave:brave-context-slf4j@6.0.3", "dependsOn": ["363-io.zipkin.brave:brave@6.0.3"]}, {"ref": "365-io.zipkin.brave:brave-instrumentation-messaging@6.0.3", "dependsOn": ["363-io.zipkin.brave:brave@6.0.3"]}, {"ref": "366-io.zipkin.brave:brave-instrumentation-rpc@6.0.3", "dependsOn": ["363-io.zipkin.brave:brave@6.0.3"]}, {"ref": "367-io.zipkin.brave:brave-instrumentation-spring-web@6.0.3", "dependsOn": ["434-io.zipkin.brave:brave-instrumentation-http@6.0.3", "363-io.zipkin.brave:brave@6.0.3"]}, {"ref": "368-io.zipkin.brave:brave-instrumentation-spring-rabbit@6.0.3", "dependsOn": ["365-io.zipkin.brave:brave-instrumentation-messaging@6.0.3", "363-io.zipkin.brave:brave@6.0.3"]}, {"ref": "369-io.zipkin.brave:brave-instrumentation-kafka-clients@6.0.3", "dependsOn": ["365-io.zipkin.brave:brave-instrumentation-messaging@6.0.3", "363-io.zipkin.brave:brave@6.0.3"]}, {"ref": "370-io.zipkin.brave:brave-instrumentation-kafka-streams@6.0.3", "dependsOn": ["365-io.zipkin.brave:brave-instrumentation-messaging@6.0.3", "369-io.zipkin.brave:brave-instrumentation-kafka-clients@6.0.3", "363-io.zipkin.brave:brave@6.0.3"]}, {"ref": "371-io.zipkin.brave:brave-instrumentation-httpclient@6.0.3", "dependsOn": ["434-io.zipkin.brave:brave-instrumentation-http@6.0.3", "363-io.zipkin.brave:brave@6.0.3"]}, {"ref": "372-io.zipkin.brave:brave-instrumentation-httpasyncclient@6.0.3", "dependsOn": ["434-io.zipkin.brave:brave-instrumentation-http@6.0.3", "363-io.zipkin.brave:brave@6.0.3"]}, {"ref": "373-io.zipkin.brave:brave-instrumentation-spring-webmvc@6.0.3", "dependsOn": ["435-io.zipkin.brave:brave-instrumentation-servlet@6.0.3", "363-io.zipkin.brave:brave@6.0.3"]}, {"ref": "374-io.zipkin.brave:brave-instrumentation-jms@6.0.3", "dependsOn": ["365-io.zipkin.brave:brave-instrumentation-messaging@6.0.3", "363-io.zipkin.brave:brave@6.0.3"]}, {"ref": "375-io.zipkin.reporter2:zipkin-reporter-metrics-micrometer@3.4.3", "dependsOn": ["262-io.zipkin.reporter2:zipkin-reporter@3.4.3"]}, {"ref": "376-io.github.openfeign:feign-form@13.5", "dependsOn": ["147-io.github.openfeign:feign-core@13.5"]}, {"ref": "377-org.springdoc:springdoc-openapi-common@1.7.0", "dependsOn": ["151-org.springframework.boot:spring-boot-autoconfigure@3.4.1", "117-org.springframework:spring-web@6.2.1", "436-io.swagger.core.v3:swagger-core@2.2.9"]}, {"ref": "378-io.netty:<EMAIL>", "dependsOn": ["288-io.netty:<EMAIL>", "289-io.netty:<EMAIL>", "287-io.netty:<EMAIL>"]}, {"ref": "379-xmlpull:xmlpull@*******"}, {"ref": "380-commons-jxpath:commons-jxpath@1.3"}, {"ref": "381-org.antlr:antlr-runtime@3.4", "dependsOn": ["437-org.antlr:stringtemplate@3.2.1", "438-antlr:antlr@2.7.7"]}, {"ref": "382-org.apache.shardingsphere:shardingsphere-transaction-api@5.4.1", "dependsOn": ["385-org.apache.shardingsphere:shardingsphere-infra-common@5.4.1", "403-org.apache.shardingsphere:shardingsphere-infra-session@5.4.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "383-org.apache.shardingsphere:shardingsphere-infra-executor@5.4.1", "dependsOn": ["385-org.apache.shardingsphere:shardingsphere-infra-common@5.4.1", "415-org.apache.shardingsphere:shardingsphere-infra-rewrite@5.4.1", "439-com.alibaba:transmittable-thread-local@2.14.2", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "384-org.apache.shardingsphere:shardingsphere-global-clock-api@5.4.1", "dependsOn": ["385-org.apache.shardingsphere:shardingsphere-infra-common@5.4.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "385-org.apache.shardingsphere:shardingsphere-infra-common@5.4.1", "dependsOn": ["440-org.apache.shardingsphere:shardingsphere-infra-util@5.4.1", "390-org.apache.shardingsphere:shardingsphere-infra-database-mysql@5.4.1", "441-org.apache.shardingsphere:shardingsphere-infra-database-mariadb@5.4.1", "391-org.apache.shardingsphere:shardingsphere-infra-database-postgresql@5.4.1", "394-org.apache.shardingsphere:shardingsphere-infra-database-opengauss@5.4.1", "392-org.apache.shardingsphere:shardingsphere-infra-database-oracle@5.4.1", "393-org.apache.shardingsphere:shardingsphere-infra-database-sqlserver@5.4.1", "442-org.apache.shardingsphere:shardingsphere-infra-database-h2@5.4.1", "388-org.apache.shardingsphere:shardingsphere-infra-database-sql92@5.4.1", "443-org.apache.shardingsphere:shardingsphere-infra-data-source-pool-hikari@5.4.1", "389-org.apache.shardingsphere:shardingsphere-parser-sql-engine@5.4.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "386-org.apache.shardingsphere:shardingsphere-mode-core@5.4.1", "dependsOn": ["324-org.apache.shardingsphere:shardingsphere-infra-context@5.4.1", "397-org.apache.shardingsphere:shardingsphere-mode-api@5.4.1", "412-org.apache.shardingsphere:shardingsphere-metadata-core@5.4.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "387-org.apache.shardingsphere:shardingsphere-global-clock-tso-spi@5.4.1", "dependsOn": ["311-org.apache.shardingsphere:shardingsphere-global-clock-core@5.4.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "388-org.apache.shardingsphere:shardingsphere-infra-database-sql92@5.4.1", "dependsOn": ["444-org.apache.shardingsphere:shardingsphere-infra-database-core@5.4.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "389-org.apache.shardingsphere:shardingsphere-parser-sql-engine@5.4.1", "dependsOn": ["445-org.apache.shardingsphere:shardingsphere-parser-sql-spi@5.4.1", "446-org.apache.shardingsphere:shardingsphere-parser-sql-statement@5.4.1", "346-org.antlr:antlr4-runtime@4.13.0", "447-com.github.ben-manes.caffeine:caffeine@3.1.8", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "390-org.apache.shardingsphere:shardingsphere-infra-database-mysql@5.4.1", "dependsOn": ["444-org.apache.shardingsphere:shardingsphere-infra-database-core@5.4.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "391-org.apache.shardingsphere:shardingsphere-infra-database-postgresql@5.4.1", "dependsOn": ["444-org.apache.shardingsphere:shardingsphere-infra-database-core@5.4.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "392-org.apache.shardingsphere:shardingsphere-infra-database-oracle@5.4.1", "dependsOn": ["444-org.apache.shardingsphere:shardingsphere-infra-database-core@5.4.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "393-org.apache.shardingsphere:shardingsphere-infra-database-sqlserver@5.4.1", "dependsOn": ["444-org.apache.shardingsphere:shardingsphere-infra-database-core@5.4.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "394-org.apache.shardingsphere:shardingsphere-infra-database-opengauss@5.4.1", "dependsOn": ["444-org.apache.shardingsphere:shardingsphere-infra-database-core@5.4.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "395-org.apache.shardingsphere:shardingsphere-infra-exception-dialect-core@5.4.1", "dependsOn": ["385-org.apache.shardingsphere:shardingsphere-infra-common@5.4.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "396-org.apache.shardingsphere:shardingsphere-authority-api@5.4.1", "dependsOn": ["385-org.apache.shardingsphere:shardingsphere-infra-common@5.4.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "397-org.apache.shardingsphere:shardingsphere-mode-api@5.4.1", "dependsOn": ["385-org.apache.shardingsphere:shardingsphere-infra-common@5.4.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "398-org.apache.shardingsphere:shardingsphere-single-api@5.4.1", "dependsOn": ["385-org.apache.shardingsphere:shardingsphere-infra-common@5.4.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "399-org.apache.shardingsphere:shardingsphere-sql-federation-api@5.4.1", "dependsOn": ["385-org.apache.shardingsphere:shardingsphere-infra-common@5.4.1", "383-org.apache.shardingsphere:shardingsphere-infra-executor@5.4.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "400-org.apache.shardingsphere:shardingsphere-infra-route@5.4.1", "dependsOn": ["401-org.apache.shardingsphere:shardingsphere-infra-binder@5.4.1", "403-org.apache.shardingsphere:shardingsphere-infra-session@5.4.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "401-org.apache.shardingsphere:shardingsphere-infra-binder@5.4.1", "dependsOn": ["395-org.apache.shardingsphere:shardingsphere-infra-exception-dialect-core@5.4.1", "389-org.apache.shardingsphere:shardingsphere-parser-sql-engine@5.4.1", "428-org.apache.shardingsphere:shardingsphere-infra-parser@5.4.1", "385-org.apache.shardingsphere:shardingsphere-infra-common@5.4.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "402-org.apache.shardingsphere:shardingsphere-traffic-api@5.4.1", "dependsOn": ["385-org.apache.shardingsphere:shardingsphere-infra-common@5.4.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "403-org.apache.shardingsphere:shardingsphere-infra-session@5.4.1", "dependsOn": ["401-org.apache.shardingsphere:shardingsphere-infra-binder@5.4.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "404-org.apache.shardingsphere:shardingsphere-parser-distsql-engine@5.4.1", "dependsOn": ["448-org.apache.shardingsphere:shardingsphere-parser-distsql-statement@5.4.1", "389-org.apache.shardingsphere:shardingsphere-parser-sql-engine@5.4.1", "346-org.antlr:antlr4-runtime@4.13.0", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "405-org.apache.shardingsphere:shardingsphere-sql-translator-core@5.4.1", "dependsOn": ["449-org.apache.shardingsphere:shardingsphere-sql-translator-api@5.4.1", "397-org.apache.shardingsphere:shardingsphere-mode-api@5.4.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "406-org.apache.shardingsphere:shardingsphere-standalone-mode-repository-api@5.4.1", "dependsOn": ["386-org.apache.shardingsphere:shardingsphere-mode-core@5.4.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "407-javax.xml.bind:jaxb-api@2.3.0"}, {"ref": "408-com.sun.xml.bind:jaxb-core@4.0.5", "dependsOn": ["112-jakarta.xml.bind:jakarta.xml.bind-api@4.0.2", "293-org.eclipse.angus:angus-activation@2.0.2"]}, {"ref": "409-com.sun.xml.bind:jaxb-impl@4.0.5", "dependsOn": ["408-com.sun.xml.bind:jaxb-core@4.0.5"]}, {"ref": "410-javax.activation:javax.activation-api@1.2.0"}, {"ref": "411-org.apache.shardingsphere:shardingsphere-cluster-mode-repository-api@5.4.1", "dependsOn": ["385-org.apache.shardingsphere:shardingsphere-infra-common@5.4.1", "386-org.apache.shardingsphere:shardingsphere-mode-core@5.4.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "412-org.apache.shardingsphere:shardingsphere-metadata-core@5.4.1", "dependsOn": ["324-org.apache.shardingsphere:shardingsphere-infra-context@5.4.1", "397-org.apache.shardingsphere:shardingsphere-mode-api@5.4.1", "396-org.apache.shardingsphere:shardingsphere-authority-api@5.4.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "413-org.apache.shardingsphere:shardingsphere-sharding-api@5.4.1", "dependsOn": ["401-org.apache.shardingsphere:shardingsphere-infra-binder@5.4.1", "385-org.apache.shardingsphere:shardingsphere-infra-common@5.4.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "414-org.apache.shardingsphere:shardingsphere-data-pipeline-api@5.4.1", "dependsOn": ["385-org.apache.shardingsphere:shardingsphere-infra-common@5.4.1", "161-commons-codec:commons-codec@1.17.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "415-org.apache.shardingsphere:shardingsphere-infra-rewrite@5.4.1", "dependsOn": ["400-org.apache.shardingsphere:shardingsphere-infra-route@5.4.1", "405-org.apache.shardingsphere:shardingsphere-sql-translator-core@5.4.1", "450-org.apache.shardingsphere:shardingsphere-sql-translator-native-provider@5.4.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "416-org.apache.shardingsphere:shardingsphere-infra-merge@5.4.1", "dependsOn": ["401-org.apache.shardingsphere:shardingsphere-infra-binder@5.4.1", "383-org.apache.shardingsphere:shardingsphere-infra-executor@5.4.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "417-org.apache.shardingsphere:shardingsphere-time-service-core@5.4.1", "dependsOn": ["451-org.apache.shardingsphere:shardingsphere-time-service-api@5.4.1", "385-org.apache.shardingsphere:shardingsphere-infra-common@5.4.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "418-org.apache.shardingsphere:shardingsphere-system-time-service@5.4.1", "dependsOn": ["451-org.apache.shardingsphere:shardingsphere-time-service-api@5.4.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "419-org.apache.shardingsphere:shardingsphere-infra-expr-core@5.4.1", "dependsOn": ["452-org.apache.shardingsphere:shardingsphere-infra-expr-spi@5.4.1", "453-org.apache.shardingsphere:shardingsphere-infra-expr-groovy@5.4.1", "454-org.apache.shardingsphere:shardingsphere-infra-expr-literal@5.4.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "420-org.apache.shardingsphere:shardingsphere-broadcast-api@5.4.1", "dependsOn": ["401-org.apache.shardingsphere:shardingsphere-infra-binder@5.4.1", "385-org.apache.shardingsphere:shardingsphere-infra-common@5.4.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "421-org.apache.shardingsphere:shardingsphere-readwrite-splitting-api@5.4.1", "dependsOn": ["385-org.apache.shardingsphere:shardingsphere-infra-common@5.4.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "422-org.apache.shardingsphere:shardingsphere-encrypt-api@5.4.1", "dependsOn": ["385-org.apache.shardingsphere:shardingsphere-infra-common@5.4.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "423-org.apache.shardingsphere:shardingsphere-mask-api@5.4.1", "dependsOn": ["385-org.apache.shardingsphere:shardingsphere-infra-common@5.4.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "424-org.apache.shardingsphere:shardingsphere-shadow-api@5.4.1", "dependsOn": ["385-org.apache.shardingsphere:shardingsphere-infra-common@5.4.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "425-org.apache.calcite:calcite-core@1.35.0", "dependsOn": ["455-org.apache.calcite:calcite-linq4j@1.35.0", "456-org.locationtech.jts:jts-core@1.19.0", "457-org.locationtech.jts.io:jts-io-common@1.19.0", "458-org.locationtech.proj4j:proj4j@1.2.2", "217-com.fasterxml.jackson.core:jackson-annotations@2.18.2", "143-com.google.errorprone:error_prone_annotations@2.11.0", "92-com.google.guava:guava@31.1-jre", "459-org.apache.calcite.avatica:avatica-core@1.23.0", "460-org.apiguardian:apiguardian-api@1.1.2", "142-org.checkerframework:checker-qual@3.12.0", "153-org.slf4j:slf4j-api@2.0.16", "218-com.fasterxml.jackson.core:jackson-core@2.18.2", "200-com.fasterxml.jackson.core:jackson-databind@2.18.2", "461-com.fasterxml.jackson.dataformat:jackson-dataformat-yaml@2.18.2", "462-com.google.uzaygezen:uzaygezen-core@0.2", "463-com.yahoo.datasketches:sketches-core@0.9.0", "161-commons-codec:commons-codec@1.17.1", "464-net.hydromatic:aggdesigner-algorithm@6.0", "465-org.apache.commons:commons-dbcp2@2.12.0", "127-org.apache.commons:commons-lang3@3.17.0", "162-org.apache.commons:commons-math3@3.6.1", "108-commons-io:commons-io@2.14.0", "466-org.codehaus.janino:commons-compiler@3.1.12", "467-org.codehaus.janino:janino@3.1.12"]}, {"ref": "426-com.jayway.jsonpath:json-path@2.9.0"}, {"ref": "427-org.apache.shardingsphere:shardingsphere-sql-parser-api@5.4.1", "dependsOn": ["385-org.apache.shardingsphere:shardingsphere-infra-common@5.4.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "428-org.apache.shardingsphere:shardingsphere-infra-parser@5.4.1", "dependsOn": ["404-org.apache.shardingsphere:shardingsphere-parser-distsql-engine@5.4.1", "447-com.github.ben-manes.caffeine:caffeine@3.1.8", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "429-org.apache.shardingsphere:shardingsphere-logging-api@5.4.1", "dependsOn": ["385-org.apache.shardingsphere:shardingsphere-infra-common@5.4.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "430-io.swagger.core.v3:swagger-core-jakarta@2.2.28", "dependsOn": ["127-org.apache.commons:commons-lang3@3.17.0", "153-org.slf4j:slf4j-api@2.0.16", "468-io.swagger.core.v3:swagger-annotations-jakarta@2.2.28", "469-io.swagger.core.v3:swagger-models-jakarta@2.2.28", "199-org.yaml:snakeyaml@2.3", "112-jakarta.xml.bind:jakarta.xml.bind-api@4.0.2", "230-jakarta.validation:jakarta.validation-api@3.0.2", "217-com.fasterxml.jackson.core:jackson-annotations@2.18.2", "200-com.fasterxml.jackson.core:jackson-databind@2.18.2", "461-com.fasterxml.jackson.dataformat:jackson-dataformat-yaml@2.18.2", "202-com.fasterxml.jackson.datatype:jackson-datatype-jsr310@2.18.2"]}, {"ref": "431-com.google.android:annotations@*******"}, {"ref": "432-io.perfmark:perfmark-api@0.23.0"}, {"ref": "433-io.grpc:grpc-context@1.38.0"}, {"ref": "434-io.zipkin.brave:brave-instrumentation-http@6.0.3", "dependsOn": ["363-io.zipkin.brave:brave@6.0.3"]}, {"ref": "435-io.zipkin.brave:brave-instrumentation-servlet@6.0.3", "dependsOn": ["434-io.zipkin.brave:brave-instrumentation-http@6.0.3", "363-io.zipkin.brave:brave@6.0.3"]}, {"ref": "436-io.swagger.core.v3:swagger-core@2.2.9", "dependsOn": ["112-jakarta.xml.bind:jakarta.xml.bind-api@4.0.2", "127-org.apache.commons:commons-lang3@3.17.0", "153-org.slf4j:slf4j-api@2.0.16", "217-com.fasterxml.jackson.core:jackson-annotations@2.18.2", "200-com.fasterxml.jackson.core:jackson-databind@2.18.2", "461-com.fasterxml.jackson.dataformat:jackson-dataformat-yaml@2.18.2", "202-com.fasterxml.jackson.datatype:jackson-datatype-jsr310@2.18.2", "470-io.swagger.core.v3:swagger-annotations@2.2.9", "199-org.yaml:snakeyaml@2.3", "471-io.swagger.core.v3:swagger-models@2.2.9", "230-jakarta.validation:jakarta.validation-api@3.0.2"]}, {"ref": "437-org.antlr:stringtemplate@3.2.1", "dependsOn": ["438-antlr:antlr@2.7.7"]}, {"ref": "438-antlr:antlr@2.7.7"}, {"ref": "439-com.alibaba:transmittable-thread-local@2.14.2"}, {"ref": "440-org.apache.shardingsphere:shardingsphere-infra-util@5.4.1", "dependsOn": ["472-org.apache.shardingsphere:shardingsphere-infra-spi@5.4.1", "199-org.yaml:snakeyaml@2.3", "200-com.fasterxml.jackson.core:jackson-databind@2.18.2", "202-com.fasterxml.jackson.datatype:jackson-datatype-jsr310@2.18.2", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "441-org.apache.shardingsphere:shardingsphere-infra-database-mariadb@5.4.1", "dependsOn": ["444-org.apache.shardingsphere:shardingsphere-infra-database-core@5.4.1", "390-org.apache.shardingsphere:shardingsphere-infra-database-mysql@5.4.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "442-org.apache.shardingsphere:shardingsphere-infra-database-h2@5.4.1", "dependsOn": ["444-org.apache.shardingsphere:shardingsphere-infra-database-core@5.4.1", "390-org.apache.shardingsphere:shardingsphere-infra-database-mysql@5.4.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "443-org.apache.shardingsphere:shardingsphere-infra-data-source-pool-hikari@5.4.1", "dependsOn": ["473-org.apache.shardingsphere:shardingsphere-infra-data-source-pool-core@5.4.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "444-org.apache.shardingsphere:shardingsphere-infra-database-core@5.4.1", "dependsOn": ["474-org.apache.shardingsphere:shardingsphere-infra-exception-core@5.4.1", "440-org.apache.shardingsphere:shardingsphere-infra-util@5.4.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "445-org.apache.shardingsphere:shardingsphere-parser-sql-spi@5.4.1", "dependsOn": ["440-org.apache.shardingsphere:shardingsphere-infra-util@5.4.1", "444-org.apache.shardingsphere:shardingsphere-infra-database-core@5.4.1", "346-org.antlr:antlr4-runtime@4.13.0", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "446-org.apache.shardingsphere:shardingsphere-parser-sql-statement@5.4.1", "dependsOn": ["445-org.apache.shardingsphere:shardingsphere-parser-sql-spi@5.4.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "447-com.github.ben-manes.caffeine:caffeine@3.1.8", "dependsOn": ["143-com.google.errorprone:error_prone_annotations@2.11.0"]}, {"ref": "448-org.apache.shardingsphere:shardingsphere-parser-distsql-statement@5.4.1", "dependsOn": ["385-org.apache.shardingsphere:shardingsphere-infra-common@5.4.1", "446-org.apache.shardingsphere:shardingsphere-parser-sql-statement@5.4.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "449-org.apache.shardingsphere:shardingsphere-sql-translator-api@5.4.1", "dependsOn": ["385-org.apache.shardingsphere:shardingsphere-infra-common@5.4.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "450-org.apache.shardingsphere:shardingsphere-sql-translator-native-provider@5.4.1", "dependsOn": ["449-org.apache.shardingsphere:shardingsphere-sql-translator-api@5.4.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "451-org.apache.shardingsphere:shardingsphere-time-service-api@5.4.1", "dependsOn": ["385-org.apache.shardingsphere:shardingsphere-infra-common@5.4.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "452-org.apache.shardingsphere:shardingsphere-infra-expr-spi@5.4.1", "dependsOn": ["440-org.apache.shardingsphere:shardingsphere-infra-util@5.4.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "453-org.apache.shardingsphere:shardingsphere-infra-expr-groovy@5.4.1", "dependsOn": ["452-org.apache.shardingsphere:shardingsphere-infra-expr-spi@5.4.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "454-org.apache.shardingsphere:shardingsphere-infra-expr-literal@5.4.1", "dependsOn": ["452-org.apache.shardingsphere:shardingsphere-infra-expr-spi@5.4.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "455-org.apache.calcite:calcite-linq4j@1.35.0", "dependsOn": ["460-org.apiguardian:apiguardian-api@1.1.2", "142-org.checkerframework:checker-qual@3.12.0", "92-com.google.guava:guava@31.1-jre", "459-org.apache.calcite.avatica:avatica-core@1.23.0"]}, {"ref": "456-org.locationtech.jts:jts-core@1.19.0"}, {"ref": "457-org.locationtech.jts.io:jts-io-common@1.19.0", "dependsOn": ["475-com.googlecode.json-simple:json-simple@1.1.1", "456-org.locationtech.jts:jts-core@1.19.0"]}, {"ref": "458-org.locationtech.proj4j:proj4j@1.2.2"}, {"ref": "459-org.apache.calcite.avatica:avatica-core@1.23.0", "dependsOn": ["476-org.apache.calcite.avatica:avatica-metrics@1.23.0", "217-com.fasterxml.jackson.core:jackson-annotations@2.18.2", "200-com.fasterxml.jackson.core:jackson-databind@2.18.2", "249-com.google.protobuf:protobuf-java@3.19.6", "218-com.fasterxml.jackson.core:jackson-core@2.18.2", "210-org.apache.httpcomponents.client5:httpclient5@5.4.1", "298-org.apache.httpcomponents.core5:httpcore5@5.3.1", "153-org.slf4j:slf4j-api@2.0.16"]}, {"ref": "460-org.apiguardian:apiguardian-api@1.1.2"}, {"ref": "461-com.fasterxml.jackson.dataformat:jackson-dataformat-yaml@2.18.2", "dependsOn": ["200-com.fasterxml.jackson.core:jackson-databind@2.18.2", "199-org.yaml:snakeyaml@2.3", "218-com.fasterxml.jackson.core:jackson-core@2.18.2"]}, {"ref": "462-com.google.uzaygezen:uzaygezen-core@0.2", "dependsOn": ["127-org.apache.commons:commons-lang3@3.17.0", "92-com.google.guava:guava@31.1-jre"]}, {"ref": "463-com.yahoo.datasketches:sketches-core@0.9.0", "dependsOn": ["477-com.yahoo.datasketches:memory@0.9.0"]}, {"ref": "464-net.hydromatic:aggdesigner-algorithm@6.0", "dependsOn": ["301-commons-lang:commons-lang@2.6", "308-commons-logging:commons-logging@1.2"]}, {"ref": "465-org.apache.commons:commons-dbcp2@2.12.0", "dependsOn": ["478-org.apache.commons:commons-pool2@2.12.0", "342-jakarta.transaction:jakarta.transaction-api@2.0.1"]}, {"ref": "466-org.codehaus.janino:commons-compiler@3.1.12"}, {"ref": "467-org.codehaus.janino:janino@3.1.12", "dependsOn": ["466-org.codehaus.janino:commons-compiler@3.1.12"]}, {"ref": "468-io.swagger.core.v3:swagger-annotations-jakarta@2.2.28"}, {"ref": "469-io.swagger.core.v3:swagger-models-jakarta@2.2.28", "dependsOn": ["217-com.fasterxml.jackson.core:jackson-annotations@2.18.2"]}, {"ref": "470-io.swagger.core.v3:swagger-annotations@2.2.9"}, {"ref": "471-io.swagger.core.v3:swagger-models@2.2.9", "dependsOn": ["217-com.fasterxml.jackson.core:jackson-annotations@2.18.2"]}, {"ref": "472-org.apache.shardingsphere:shardingsphere-infra-spi@5.4.1", "dependsOn": ["474-org.apache.shardingsphere:shardingsphere-infra-exception-core@5.4.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "473-org.apache.shardingsphere:shardingsphere-infra-data-source-pool-core@5.4.1", "dependsOn": ["444-org.apache.shardingsphere:shardingsphere-infra-database-core@5.4.1", "92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "474-org.apache.shardingsphere:shardingsphere-infra-exception-core@5.4.1", "dependsOn": ["92-com.google.guava:guava@31.1-jre", "142-org.checkerframework:checker-qual@3.12.0", "143-com.google.errorprone:error_prone_annotations@2.11.0", "127-org.apache.commons:commons-lang3@3.17.0", "110-org.apache.commons:commons-collections4@4.4", "153-org.slf4j:slf4j-api@2.0.16", "338-org.slf4j:jcl-over-slf4j@2.0.16", "297-org.slf4j:jul-to-slf4j@2.0.16"]}, {"ref": "475-com.googlecode.json-simple:json-simple@1.1.1"}, {"ref": "476-org.apache.calcite.avatica:avatica-metrics@1.23.0", "dependsOn": ["153-org.slf4j:slf4j-api@2.0.16"]}, {"ref": "477-com.yahoo.datasketches:memory@0.9.0"}, {"ref": "478-org.apache.commons:commons-pool2@2.12.0"}, {"ref": "479-node-script@1.0.0"}]}