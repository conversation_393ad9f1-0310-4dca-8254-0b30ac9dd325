#!/usr/bin/env python3
import csv
import sys
from collections import defaultdict

def analyze_jacoco_coverage(csv_file):
    """分析JaCoCo覆盖率报告"""
    
    total_instruction_missed = 0
    total_instruction_covered = 0
    total_line_missed = 0
    total_line_covered = 0
    total_method_missed = 0
    total_method_covered = 0
    
    # 按包分组统计
    package_stats = defaultdict(lambda: {
        'instruction_missed': 0,
        'instruction_covered': 0,
        'line_missed': 0,
        'line_covered': 0,
        'method_missed': 0,
        'method_covered': 0,
        'classes': []
    })
    
    # 低覆盖率的类
    low_coverage_classes = []
    
    with open(csv_file, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        
        for row in reader:
            # 跳过MapStruct生成的类
            if 'Mapper' in row['CLASS'] and ('Impl' in row['CLASS'] or '$' in row['CLASS']):
                continue
                
            instruction_missed = int(row['INSTRUCTION_MISSED'])
            instruction_covered = int(row['INSTRUCTION_COVERED'])
            line_missed = int(row['LINE_MISSED'])
            line_covered = int(row['LINE_COVERED'])
            method_missed = int(row['METHOD_MISSED'])
            method_covered = int(row['METHOD_COVERED'])
            
            total_instruction_missed += instruction_missed
            total_instruction_covered += instruction_covered
            total_line_missed += line_missed
            total_line_covered += line_covered
            total_method_missed += method_missed
            total_method_covered += method_covered
            
            # 计算类级别覆盖率
            total_instructions = instruction_missed + instruction_covered
            total_lines = line_missed + line_covered
            total_methods = method_missed + method_covered
            
            if total_instructions > 0:
                instruction_coverage = (instruction_covered / total_instructions) * 100
                line_coverage = (line_covered / total_lines) * 100 if total_lines > 0 else 0
                method_coverage = (method_covered / total_methods) * 100 if total_methods > 0 else 0
                
                # 找出覆盖率低于80%的类
                if instruction_coverage < 80 and total_instructions > 10:  # 只关注有一定代码量的类
                    low_coverage_classes.append({
                        'package': row['PACKAGE'],
                        'class': row['CLASS'],
                        'instruction_coverage': instruction_coverage,
                        'line_coverage': line_coverage,
                        'method_coverage': method_coverage,
                        'total_instructions': total_instructions,
                        'total_lines': total_lines,
                        'total_methods': total_methods
                    })
            
            # 按包统计
            package = row['PACKAGE']
            package_stats[package]['instruction_missed'] += instruction_missed
            package_stats[package]['instruction_covered'] += instruction_covered
            package_stats[package]['line_missed'] += line_missed
            package_stats[package]['line_covered'] += line_covered
            package_stats[package]['method_missed'] += method_missed
            package_stats[package]['method_covered'] += method_covered
            package_stats[package]['classes'].append(row['CLASS'])
    
    # 计算总体覆盖率
    total_instructions = total_instruction_missed + total_instruction_covered
    total_lines = total_line_missed + total_line_covered
    total_methods = total_method_missed + total_method_covered
    
    overall_instruction_coverage = (total_instruction_covered / total_instructions) * 100 if total_instructions > 0 else 0
    overall_line_coverage = (total_line_covered / total_lines) * 100 if total_lines > 0 else 0
    overall_method_coverage = (total_method_covered / total_methods) * 100 if total_methods > 0 else 0
    
    print("=== JaCoCo 覆盖率分析报告 ===")
    print(f"总体指令覆盖率: {overall_instruction_coverage:.2f}%")
    print(f"总体行覆盖率: {overall_line_coverage:.2f}%")
    print(f"总体方法覆盖率: {overall_method_coverage:.2f}%")
    print()
    
    print("=== 覆盖率低于80%的类 (按指令覆盖率排序) ===")
    low_coverage_classes.sort(key=lambda x: x['instruction_coverage'])
    
    for i, cls in enumerate(low_coverage_classes[:20]):  # 显示前20个最需要改进的类
        print(f"{i+1:2d}. {cls['class']}")
        print(f"    包: {cls['package']}")
        print(f"    指令覆盖率: {cls['instruction_coverage']:.1f}% ({cls['total_instructions']} 指令)")
        print(f"    行覆盖率: {cls['line_coverage']:.1f}% ({cls['total_lines']} 行)")
        print(f"    方法覆盖率: {cls['method_coverage']:.1f}% ({cls['total_methods']} 方法)")
        print()
    
    print(f"总共有 {len(low_coverage_classes)} 个类的覆盖率低于80%")
    
    # 按包统计覆盖率
    print("\n=== 按包统计覆盖率 ===")
    package_coverage = []
    for package, stats in package_stats.items():
        total_pkg_instructions = stats['instruction_missed'] + stats['instruction_covered']
        if total_pkg_instructions > 0:
            pkg_coverage = (stats['instruction_covered'] / total_pkg_instructions) * 100
            package_coverage.append((package, pkg_coverage, total_pkg_instructions))
    
    package_coverage.sort(key=lambda x: x[1])  # 按覆盖率排序
    
    for package, coverage, instructions in package_coverage[:10]:  # 显示覆盖率最低的10个包
        print(f"{coverage:5.1f}% - {package} ({instructions} 指令)")

if __name__ == "__main__":
    csv_file = "next-propertyconnect-channel-task/target/site/jacoco/jacoco.csv"
    analyze_jacoco_coverage(csv_file)
