2025-05-27T03:19:30.474Z: Executing: C:\Users\<USER>\AppData\Roaming\npm\node_modules\snyk\wrapper_dist\snyk-win.exe sbom --format=cyclonedx1.5+json --all-projects -d -- -Dscope=runtime
2025-05-27T03:19:30Z main - Using log level: debug
2025-05-27T03:19:31Z main - > request [0xc000834c80]: GET https://api.snyk.io/rest/self?version=2024-04-22
2025-05-27T03:19:31Z main - > request [0xc000834c80]: header: map[Authorization:[Bearer ***] Session-Token:[Bearer ***]]
2025-05-27T03:19:31Z main - < response [0xc000834c80]: 200 OK
2025-05-27T03:19:31Z main - < response [0xc000834c80]: header: map[Akamai-Cache-Status:[Miss from child] Akamai-Grn:[0.5c9e3617.1748315970.93d456ed] Content-Length:[500] Content-Type:[application/vnd.api+json; charset=utf-8] Date:[Tue, 27 May 2025 03:19:31 GMT] Deprecation:[2024-05-31] Server:[envoy] Snyk-Request-Id:[************************************] Snyk-Version-Lifecycle-Stage:[ga] Snyk-Version-Requested:[2024-04-22] Snyk-Version-Served:[2024-04-22] Strict-Transport-Security:[max-age=31536000; preload] Sunset:[2024-10-19] Vary:[Accept-Encoding] X-Content-Type-Options:[nosniff] X-Envoy-Upstream-Service-Time:[11] X-Frame-Options:[SAMEORIGIN]]
2025-05-27T03:19:31Z main - > request [0xc000dc5400]: GET https://api.snyk.io/rest/self?version=2024-04-22
2025-05-27T03:19:31Z main - > request [0xc000dc5400]: header: map[Authorization:[Bearer ***] Session-Token:[Bearer ***] Snyk-Interaction-Id:[urn:snyk:interaction:************************************] Snyk-Request-Id:[************************************] User-Agent:[snyk-cli/1.1297.1 (windows;amd64) TS_BINARY_WRAPPER/1.1297.1] X-Snyk-Cli-Version:[1.1297.1]]
2025-05-27T03:19:31Z main - < response [0xc000dc5400]: 200 OK
2025-05-27T03:19:31Z main - < response [0xc000dc5400]: header: map[Akamai-Cache-Status:[Miss from child] Akamai-Grn:[0.5c9e3617.1748315971.93d467b6] Content-Length:[500] Content-Type:[application/vnd.api+json; charset=utf-8] Date:[Tue, 27 May 2025 03:19:31 GMT] Deprecation:[2024-05-31] Server:[envoy] Snyk-Request-Id:[************************************] Snyk-Version-Lifecycle-Stage:[ga] Snyk-Version-Requested:[2024-04-22] Snyk-Version-Served:[2024-04-22] Strict-Transport-Security:[max-age=31536000; preload] Sunset:[2024-10-19] Vary:[Accept-Encoding] X-Content-Type-Options:[nosniff] X-Envoy-Upstream-Service-Time:[14] X-Frame-Options:[SAMEORIGIN]]
2025-05-27T03:19:31Z main - Version:               1.1297.1 
2025-05-27T03:19:31Z main - Platform:              windows amd64  TS_BINARY_WRAPPER/1.1297.1
2025-05-27T03:19:31Z main - API:                   https://api.snyk.io
2025-05-27T03:19:31Z main - Region:                snyk-us-01
2025-05-27T03:19:31Z main - Cache:                 C:\Users\<USER>\AppData\Local/snyk/snyk-cli
2025-05-27T03:19:31Z main - Organization:          ************************************
2025-05-27T03:19:31Z main - Insecure HTTPS:        false
2025-05-27T03:19:31Z main - Analytics:             enabled
2025-05-27T03:19:31Z main - Authorization:         e841a5f0***c2e1f353  (type=oauth; expiry=2025-05-27 04:19:20.0934317 +0000 UTC)
2025-05-27T03:19:31Z main - Interaction:           urn:snyk:interaction:************************************
2025-05-27T03:19:31Z main - Features:              
2025-05-27T03:19:31Z main -   preview:             disabled
2025-05-27T03:19:31Z main -   fips:                Not available
2025-05-27T03:19:31Z main - Checks:                
2025-05-27T03:19:31Z main -   Configuration:       all good
2025-05-27T03:19:31Z main - Running sbom
2025-05-27T03:19:31Z sbom:1 - Workflow Start
2025-05-27T03:19:31Z sbom:1 - SBOM workflow start
2025-05-27T03:19:31Z sbom:1 - Getting preferred organization ID
2025-05-27T03:19:32Z main - > request [0xc0010c3040]: GET https://api.snyk.io/rest/self?version=2024-04-22
2025-05-27T03:19:32Z main - > request [0xc0010c3040]: header: map[Authorization:[Bearer ***] Session-Token:[Bearer ***] Snyk-Interaction-Id:[urn:snyk:interaction:************************************] Snyk-Request-Id:[************************************] User-Agent:[snyk-cli/1.1297.1 (windows;amd64) TS_BINARY_WRAPPER/1.1297.1] X-Snyk-Cli-Version:[1.1297.1]]
2025-05-27T03:19:32Z main - < response [0xc0010c3040]: 200 OK
2025-05-27T03:19:32Z main - < response [0xc0010c3040]: header: map[Akamai-Cache-Status:[Miss from child] Akamai-Grn:[0.5c9e3617.1748315972.93d47b3a] Content-Length:[500] Content-Type:[application/vnd.api+json; charset=utf-8] Date:[Tue, 27 May 2025 03:19:32 GMT] Deprecation:[2024-05-31] Server:[envoy] Snyk-Request-Id:[************************************] Snyk-Version-Lifecycle-Stage:[ga] Snyk-Version-Requested:[2024-04-22] Snyk-Version-Served:[2024-04-22] Strict-Transport-Security:[max-age=31536000; preload] Sunset:[2024-10-19] Vary:[Accept-Encoding] X-Content-Type-Options:[nosniff] X-Envoy-Upstream-Service-Time:[9] X-Frame-Options:[SAMEORIGIN]]
2025-05-27T03:19:32Z sbom:1 - Invoking depgraph workflow
2025-05-27T03:19:32Z depgraph:2 - Workflow Start
2025-05-27T03:19:32Z depgraph:2 - DepGraph workflow start
2025-05-27T03:19:32Z depgraph:2 - Target directory: C:\Users\<USER>\Documents\IdeaProjects\next-propertyconnect-channel
2025-05-27T03:19:32Z depgraph:2 - Debug: true
2025-05-27T03:19:32Z legacycli:3 - Workflow Start
2025-05-27T03:19:32Z legacycli:3 - Arguments:[test --print-graph --json --all-projects --fail-fast C:\Users\<USER>\Documents\IdeaProjects\next-propertyconnect-channel --debug]
2025-05-27T03:19:32Z legacycli:3 - Use StdIO:false
2025-05-27T03:19:32Z legacycli:3 - Working directory:
2025-05-27T03:19:32Z legacycli:3 - Init start
2025-05-27T03:19:32Z legacycli:3 - Init-Lock acquired: true (C:\Users\<USER>\AppData\Local/snyk/snyk-cli/1.1297.1.lock)
2025-05-27T03:19:32Z legacycli:3 - Validating sha256 of C:\Users\<USER>\AppData\Local/snyk/snyk-cli/1.1297.1/snyk-win.exe
2025-05-27T03:19:32Z legacycli:3 - expected:  bc4103ce95167d5da25895a1d6e21a9c2371bb15ef42535565042558222eb8d3
2025-05-27T03:19:32Z legacycli:3 - actual:    bc4103ce95167d5da25895a1d6e21a9c2371bb15ef42535565042558222eb8d3
2025-05-27T03:19:32Z legacycli:3 - Extraction not required
2025-05-27T03:19:32Z legacycli:3 - Init end
2025-05-27T03:19:32Z legacycli:3 - Creating new Certificate Authority
2025-05-27T03:19:32Z legacycli:3 - Temporary CertificateLocation: C:\Users\<USER>\AppData\Local/snyk/snyk-cli/1.1297.1/tmp/pid36104\snyk-cli-cert-3631176830.crt
2025-05-27T03:19:32Z legacycli:3 - Enabled Proxy Authentication Mechanism: Anyauth
2025-05-27T03:19:32Z legacycli:3 - starting proxy
2025-05-27T03:19:32Z legacycli:3 - Wrapper proxy is listening on port: 6987
2025-05-27T03:19:33Z main - > request [0xc0007b4140]: GET https://api.snyk.io/rest/self?version=2024-04-22
2025-05-27T03:19:33Z main - > request [0xc0007b4140]: header: map[Authorization:[Bearer ***] Session-Token:[Bearer ***] Snyk-Interaction-Id:[urn:snyk:interaction:************************************] Snyk-Request-Id:[************************************] User-Agent:[snyk-cli/1.1297.1 (windows;amd64) TS_BINARY_WRAPPER/1.1297.1] X-Snyk-Cli-Version:[1.1297.1]]
2025-05-27T03:19:33Z main - < response [0xc0007b4140]: 200 OK
2025-05-27T03:19:33Z main - < response [0xc0007b4140]: header: map[Akamai-Cache-Status:[Miss from child] Akamai-Grn:[0.5c9e3617.1748315972.93d49655] Content-Length:[500] Content-Type:[application/vnd.api+json; charset=utf-8] Date:[Tue, 27 May 2025 03:19:33 GMT] Deprecation:[2024-05-31] Server:[envoy] Snyk-Request-Id:[************************************] Snyk-Version-Lifecycle-Stage:[ga] Snyk-Version-Requested:[2024-04-22] Snyk-Version-Served:[2024-04-22] Strict-Transport-Security:[max-age=31536000; preload] Sunset:[2024-10-19] Vary:[Accept-Encoding] X-Content-Type-Options:[nosniff] X-Envoy-Upstream-Service-Time:[9] X-Frame-Options:[SAMEORIGIN]]
2025-05-27T03:19:33Z legacycli:3 - Launching:
2025-05-27T03:19:33Z legacycli:3 - C:\Users\<USER>\AppData\Local/snyk/snyk-cli/1.1297.1/snyk-win.exe
2025-05-27T03:19:33Z legacycli:3 - With Arguments:
2025-05-27T03:19:33Z legacycli:3 - test, --print-graph, --json, --all-projects, --fail-fast, C:\Users\<USER>\Documents\IdeaProjects\next-propertyconnect-channel, --debug, --, -Dscope=runtime
2025-05-27T03:19:33Z legacycli:3 - With Environment:
2025-05-27T03:19:33Z legacycli:3 - NODE_EXTRA_CA_CERTS = C:\Users\<USER>\AppData\Local/snyk/snyk-cli/1.1297.1/tmp/pid36104\snyk-cli-cert-3631176830.crt
2025-05-27T03:19:33Z legacycli:3 - HTTPS_PROXY = http://***@127.0.0.1:6987
2025-05-27T03:19:33Z legacycli:3 - HTTP_PROXY = http://***@127.0.0.1:6987
2025-05-27T03:19:33Z legacycli:3 - NO_PROXY = localhost,127.0.0.1,::1
2025-05-27T03:19:33Z legacycli:3 - SNYK_SYSTEM_HTTPS_PROXY =
2025-05-27T03:19:33Z legacycli:3 - SNYK_SYSTEM_HTTP_PROXY =
2025-05-27T03:19:33Z legacycli:3 - SNYK_SYSTEM_NO_PROXY =
2025-05-27T03:19:33Z legacycli:3 - SNYK_API = https://api.snyk.io
2025-05-27T03:19:34Z legacycli:3 - [001] INFO: Running 1 CONNECT handlers
2025-05-27T03:19:34Z legacycli:3 - HandleConnect - basic authentication result: &{0 <nil> 0x805a00}api.snyk.io:443
2025-05-27T03:19:34Z legacycli:3 - [001] INFO: on 0th handler: &{2 <nil> 0x805600} api.snyk.io:443
2025-05-27T03:19:34Z legacycli:3 - [001] INFO: Assuming CONNECT is TLS, mitm proxying it
2025-05-27T03:19:34Z legacycli:3 - [001] INFO: signing for api.snyk.io
2025-05-27T03:19:34Z legacycli:3 - [002] INFO: req api.snyk.io:443
2025-05-27T03:19:34Z legacycli:3 - > request [0xc0010c2f00]: GET https://api.snyk.io:443/v1/cli-config/feature-flags/enablePnpmCli?org=
2025-05-27T03:19:34Z legacycli:3 - > request [0xc0010c2f00]: header: map[Accept:[application/json] Authorization:[Bearer ***] Connection:[close] Session-Token:[Bearer ***] Snyk-Interaction-Id:[urn:snyk:interaction:************************************] Snyk-Request-Id:[************************************] User-Agent:[snyk-cli/1.1297.1 (windows;amd64) TS_BINARY_WRAPPER/1.1297.1] X-Snyk-Cli-Version:[1.1297.1]]
2025-05-27T03:19:34Z legacycli:3 - [002] INFO: Sending request GET https://api.snyk.io:443/v1/cli-config/feature-flags/enablePnpmCli?org=
2025-05-27T03:19:34Z legacycli:3 - [002] INFO: resp 403 Forbidden
2025-05-27T03:19:34Z legacycli:3 - < response [0xc0010c2f00]: 403 Forbidden
2025-05-27T03:19:34Z legacycli:3 - < response [0xc0010c2f00]: header: map[Akamai-Cache-Status:[Miss from child] Akamai-Grn:[0.5c9e3617.1748315974.93d4d22e] Content-Length:[107] Content-Security-Policy:[base-uri https://app.snyk.io; script-src 'self' 'nonce-WjP6VvCdTEuxFQTCoXhTlA==' 'unsafe-inline' 'unsafe-eval' 'strict-dynamic' 'report-sample'; img-src https: data:; object-src 'none'; frame-ancestors https://app.snyk.io https://learn.snyk.io https://login.snyk.io https://security.snyk.io; report-to csp-report-group; report-uri https://web-reports.snyk.io/csp?version=bc14206829245c170ad364aaf99bd4a193d5b071;] Content-Type:[application/json; charset=utf-8] Date:[Tue, 27 May 2025 03:19:34 GMT] Report-To:[{"group":"csp-report-group","max_age":1800,"endpoints":[{"url":"https://web-reports.snyk.io/csp?version=bc14206829245c170ad364aaf99bd4a193d5b071"}],"include_subdomains":true}] Snyk-Request-Id:[************************************] Strict-Transport-Security:[max-age=31536000; preload] Vary:[Accept-Encoding] X-Content-Type-Options:[nosniff] X-Frame-Options:[SAMEORIGIN]]
2025-05-27T03:19:34Z legacycli:3 - < response [0xc0010c2f00]: body: {"ok":false,"userMessage":"Org next-eMMLmEbjtVVYrWRQ9QJA5Y doesn't have 'enable-pnpm-cli' feature enabled"}
2025-05-27T03:19:34Z legacycli:3 - [001] INFO: Exiting on EOF
2025-05-27T03:19:33.693Z snyk test <ref *1> {
  _: [
    'C:\\Users\\<USER>\\Documents\\IdeaProjects\\next-propertyconnect-channel',
    [Circular *1]
  ],
  'print-graph': true,
  json: true,
  'fail-fast': true,
  debug: true,
  _doubleDashArgs: [ '-Dscope=runtime' ],
  allProjects: true
}
2025-05-27T03:19:34.240Z snyk using proxy: http://***@127.0.0.1:6987
2025-05-27T03:19:34.245Z snyk Using a custom Snyk API 'SNYK_API' environment variable 'https://api.snyk.io'
2025-05-27T03:19:36.682Z snyk-test auto detect manifest files, found 5 [
  'C:\\Users\\<USER>\\Documents\\IdeaProjects\\next-propertyconnect-channel\\pom.xml',
  'C:\\Users\\<USER>\\Documents\\IdeaProjects\\next-propertyconnect-channel\\next-propertyconnect-channel-api\\pom.xml',
  'C:\\Users\\<USER>\\Documents\\IdeaProjects\\next-propertyconnect-channel\\next-propertyconnect-channel-common\\pom.xml',
  'C:\\Users\\<USER>\\Documents\\IdeaProjects\\next-propertyconnect-channel\\next-propertyconnect-channel-task\\pom.xml',
  'C:\\Users\\<USER>\\Documents\\IdeaProjects\\next-propertyconnect-channel\\next-propertyconnect-channel-task\\src\\main\\resources\\tool\\package.json'
]
2025-05-27T03:19:36.683Z snyk-yarn-workspaces Processing potential Yarn workspaces (5)
2025-05-27T03:19:36.683Z snyk-yarn-workspaces Processing C:\Users\<USER>\Documents\IdeaProjects\next-propertyconnect-channel\next-propertyconnect-channel-task\src\main\resources\tool as a potential Yarn workspace
2025-05-27T03:19:36.684Z snyk-yarn-workspaces C:\Users\<USER>\Documents\IdeaProjects\next-propertyconnect-channel\next-propertyconnect-channel-task\src\main\resources\tool\package.json is not part of any detected workspace, skipping
2025-05-27T03:19:36.684Z snyk-yarn-workspaces No yarn workspaces detected in any of the 5 target files.
2025-05-27T03:19:36.685Z snyk-test Not part of a workspace: C:\Users\<USER>\Documents\IdeaProjects\next-propertyconnect-channel\pom.xml, C:\Users\<USER>\Documents\IdeaProjects\next-propertyconnect-channel\next-propertyconnect-channel-api\pom.xml, C:\Users\<USER>\Documents\IdeaProjects\next-propertyconnect-channel\next-propertyconnect-channel-common\pom.xml, C:\Users\<USER>\Documents\IdeaProjects\next-propertyconnect-channel\next-propertyconnect-channel-task\pom.xml, C:\Users\<USER>\Documents\IdeaProjects\next-propertyconnect-channel\next-propertyconnect-channel-task\src\main\resources\tool\package.json}
2025-05-27T03:19:36.686Z snyk-mvn-plugin Maven command: mvn org.apache.maven.plugins:maven-dependency-plugin:3.6.1:tree -DoutputType=dot --batch-mode --non-recursive --file="pom.xml" -Dverbose -Dscope=runtime
2025-05-27T03:19:36.686Z snyk-mvn-plugin Maven working directory: C:\Users\<USER>\Documents\IdeaProjects\next-propertyconnect-channel
2025-05-27T03:19:36.686Z snyk-mvn-plugin Verbose enabled: true
2025-05-27T03:19:43.115Z snyk-mvn-plugin Child process exited with code: 0
2025-05-27T03:19:43.625Z snyk-mvn-plugin Child process exited with code: 0
2025-05-27T03:19:43.629Z snyk-mvn-plugin Maven command: mvn org.apache.maven.plugins:maven-dependency-plugin:3.6.1:tree -DoutputType=dot --batch-mode --non-recursive --file="next-propertyconnect-channel-api\pom.xml" -Dverbose -Dscope=runtime
2025-05-27T03:19:43.630Z snyk-mvn-plugin Maven working directory: C:\Users\<USER>\Documents\IdeaProjects\next-propertyconnect-channel
2025-05-27T03:19:43.630Z snyk-mvn-plugin Verbose enabled: true
2025-05-27T03:19:46.797Z snyk-mvn-plugin Child process exited with code: 0
2025-05-27T03:19:47.317Z snyk-mvn-plugin Child process exited with code: 0
2025-05-27T03:19:47.318Z snyk-mvn-plugin Maven command: mvn org.apache.maven.plugins:maven-dependency-plugin:3.6.1:tree -DoutputType=dot --batch-mode --non-recursive --file="next-propertyconnect-channel-common\pom.xml" -Dverbose -Dscope=runtime
2025-05-27T03:19:47.318Z snyk-mvn-plugin Maven working directory: C:\Users\<USER>\Documents\IdeaProjects\next-propertyconnect-channel
2025-05-27T03:19:47.318Z snyk-mvn-plugin Verbose enabled: true
2025-05-27T03:19:50.577Z snyk-mvn-plugin Child process exited with code: 0
2025-05-27T03:19:51.150Z snyk-mvn-plugin Child process exited with code: 0
2025-05-27T03:19:51.155Z snyk-mvn-plugin Maven command: mvn org.apache.maven.plugins:maven-dependency-plugin:3.6.1:tree -DoutputType=dot --batch-mode --non-recursive --file="next-propertyconnect-channel-task\pom.xml" -Dverbose -Dscope=runtime
2025-05-27T03:19:51.155Z snyk-mvn-plugin Maven working directory: C:\Users\<USER>\Documents\IdeaProjects\next-propertyconnect-channel
2025-05-27T03:19:51.155Z snyk-mvn-plugin Verbose enabled: true
2025-05-27T03:20:01.571Z snyk-mvn-plugin Child process exited with code: 0
2025-05-27T03:20:02.097Z snyk-mvn-plugin Child process exited with code: 0
2025-05-27T04:10:36.758Z snyk-test Found 5 projects from 5 detected manifests
2025-05-27T04:10:36.759Z snyk Potential policy locations found: [
  'C:\\Users\\<USER>\\Documents\\IdeaProjects\\next-propertyconnect-channel'
]
2025-05-27T04:10:37.841Z snyk:prune rootPkg {
  name: 'com.derbysoft.next:next-propertyconnect-channel',
  version: '1.0.0'
}
2025-05-27T04:10:37.841Z snyk:prune shouldPrune: false
2025-05-27T04:10:37.841Z snyk Potential policy locations found: [
  'C:\\Users\\<USER>\\Documents\\IdeaProjects\\next-propertyconnect-channel\\next-propertyconnect-channel-api'
]
2025-05-27T04:10:38.701Z snyk:prune rootPkg {
  name: 'com.derbysoft.next:next-propertyconnect-channel-api',
  version: '1.0.0'
}
2025-05-27T04:10:38.701Z snyk:prune shouldPrune: false
2025-05-27T04:10:38.701Z snyk Potential policy locations found: [
  'C:\\Users\\<USER>\\Documents\\IdeaProjects\\next-propertyconnect-channel\\next-propertyconnect-channel-common'
]
2025-05-27T04:10:39.543Z snyk:prune rootPkg {
  name: 'com.derbysoft.next:next-propertyconnect-channel-common',
  version: '1.0.0'
}
2025-05-27T04:10:39.543Z snyk:prune shouldPrune: false
2025-05-27T04:10:39.543Z snyk Potential policy locations found: [
  'C:\\Users\\<USER>\\Documents\\IdeaProjects\\next-propertyconnect-channel\\next-propertyconnect-channel-task'
]
2025-05-27T04:10:40.393Z snyk:prune rootPkg {
  name: 'com.derbysoft.next:next-propertyconnect-channel-task',
  version: '1.0.0'
}
2025-05-27T04:10:40.393Z snyk:prune shouldPrune: true
2025-05-27T04:10:40.393Z snyk:prune Trying to prune the graph
2025-05-27T04:10:40.517Z snyk Potential policy locations found: [
  'C:\\Users\\<USER>\\Documents\\IdeaProjects\\next-propertyconnect-channel\\next-propertyconnect-channel-task\\src\\main\\resources\\tool'
]
2025-05-27T04:10:41.354Z snyk:run-test converting dep-tree to dep-graph {
  name: 'node-script',
  targetFile: 'next-propertyconnect-channel-task\\src\\main\\resources\\tool\\package.json'
}
2025-05-27T04:10:41.354Z snyk:run-test done converting dep-tree to dep-graph { uniquePkgsCount: 1 }
2025-05-27T04:10:41.355Z snyk:prune rootPkg { name: 'node-script', version: '1.0.0' }
2025-05-27T04:10:41.355Z snyk:prune shouldPrune: false
2025-05-27T04:10:41.763Z snyk analytics {
  "args": [],
  "command": "test",
  "metadata": {
    "upgradable-snyk-protect-paths": 0,
    "local": true,
    "allProjects": {
      "scannedProjects": 5,
      "targetFiles": [
        "C:\\Users\\<USER>\\Documents\\IdeaProjects\\next-propertyconnect-channel\\pom.xml",
        "C:\\Users\\<USER>\\Documents\\IdeaProjects\\next-propertyconnect-channel\\next-propertyconnect-channel-api\\pom.xml",
        "C:\\Users\\<USER>\\Documents\\IdeaProjects\\next-propertyconnect-channel\\next-propertyconnect-channel-common\\pom.xml",
        "C:\\Users\\<USER>\\Documents\\IdeaProjects\\next-propertyconnect-channel\\next-propertyconnect-channel-task\\pom.xml",
        "C:\\Users\\<USER>\\Documents\\IdeaProjects\\next-propertyconnect-channel\\next-propertyconnect-channel-task\\src\\main\\resources\\tool\\package.json"
      ],
      "packageManagers": [
        "maven",
        "maven",
        "maven",
        "maven",
        "npm"
      ],
      "ignore": [
        "node_modules",
        ".build"
      ]
    },
    "pluginName": "custom-auto-detect",
    "policies": 5,
    "policyLocations": [
      "C:\\Users\\<USER>\\Documents\\IdeaProjects\\next-propertyconnect-channel",
      "C:\\Users\<USER>\86727\\Documents\\IdeaProjects\\next-propertyconnect-channel\\next-propertyconnect-channel-api",
      "C:\\Users\\<USER>\\Documents\\IdeaProjects\\next-propertyconnect-channel\\next-propertyconnect-channel-common",
      "C:\\Users\\<USER>\\Documents\\IdeaProjects\\next-propertyconnect-channel\\next-propertyconnect-channel-task",
      "C:\\Users\\<USER>\\Documents\\IdeaProjects\\next-propertyconnect-channel\\next-propertyconnect-channel-task\\src\\main\\resources\\tool"
    ],
    "packageManager": [
      "maven",
      "maven",
      "maven",
      "maven",
      "npm"
    ],
    "packageName": [
      "com.derbysoft.next:next-propertyconnect-channel",
      "com.derbysoft.next:next-propertyconnect-channel-api",
      "com.derbysoft.next:next-propertyconnect-channel-common",
      "com.derbysoft.next:next-propertyconnect-channel-task",
      "node-script"
    ],
    "packageVersion": [
      "1.0.0",
      "1.0.0",
      "1.0.0",
      "1.0.0",
      "1.0.0"
    ],
    "package": [
      "com.derbysoft.next:next-propertyconnect-channel@1.0.0",
      "com.derbysoft.next:next-propertyconnect-channel-api@1.0.0",
      "com.derbysoft.next:next-propertyconnect-channel-common@1.0.0",
      "com.derbysoft.next:next-propertyconnect-channel-task@1.0.0",
      "node-script@1.0.0"
    ],
    "prune.graphToTreeDuration": 9,
    "prune.treeToGraphDuration": 108
  },
  "os": "Windows 10",
  "osPlatform": "win32",
  "osRelease": "10.0.26100",
  "osArch": "x64",
  "version": "1.1297.1",
  "nodeVersion": "v18.5.0",
  "standalone": true,
  "integrationName": "",
  "integrationVersion": "1.1297.1",
  "integrationEnvironment": "",
  "integrationEnvironmentVersion": "",
  "id": "1f0f35e281ce65b17745524ce8c3171fcfe77df9",
  "ci": false,
  "durationMs": 3067666,
  "metrics": {
    "network_time": {
      "type": "timer",
      "values": [
        743
      ],
      "total": 743
    },
    "cpu_time": {
      "type": "synthetic",
      "values": [
        3066923
      ],
      "total": 3066923
    }
  }
}
2025-05-27T04:10:41.765Z snyk sending request to: https://api.snyk.io/v1/analytics/cli
2025-05-27T04:10:41.765Z snyk request body size: 2609
2025-05-27T04:10:41.765Z snyk gzipped request body size: 706
2025-05-27T04:10:41.766Z snyk using proxy: http://***@127.0.0.1:6987
2025-05-27T04:10:42Z legacycli:3 - Workflow End
2025-05-27T04:10:42Z depgraph:2 - DepGraph workflow done (extracted 5 dependency graphs)
2025-05-27T04:10:42Z depgraph:2 - Workflow End
2025-05-27T04:10:42Z sbom:1 - Generating documents for 5 depgraph(s)
2025-05-27T04:10:42Z sbom:1 - Document subject: { Name: "next-propertyconnect-channel", Version: "" }
2025-05-27T04:10:42Z sbom:1 - Converting depgraphs remotely (url: https://api.snyk.io/hidden/orgs/************************************/sbom?version=2022-03-31~experimental&format=cyclonedx1.5%2Bjson)
2025-05-27T04:10:43Z main - > request [0xc0006f9900]: POST https://api.snyk.io/hidden/orgs/************************************/sbom?version=2022-03-31~experimental&format=cyclonedx1.5%2Bjson
2025-05-27T04:10:43Z main - > request [0xc0006f9900]: header: map[Authorization:[Bearer ***] Content-Type:[application/json] Session-Token:[Bearer ***] Snyk-Interaction-Id:[urn:snyk:interaction:************************************] Snyk-Request-Id:[************************************] User-Agent:[snyk-cli/1.1297.1 (windows;amd64) TS_BINARY_WRAPPER/1.1297.1] X-Snyk-Cli-Version:[1.1297.1]]
2025-05-27T04:10:43Z main - < response [0xc0006f9900]: 200 OK
2025-05-27T04:10:43Z main - < response [0xc0006f9900]: header: map[Akamai-Cache-Status:[Miss from child] Akamai-Grn:[0.5c9e3617.1748319042.9557add6] Content-Type:[application/vnd.cyclonedx+json] Date:[Tue, 27 May 2025 04:10:43 GMT] Deprecation:[2024-10-15] Server:[envoy] Snyk-Request-Id:[************************************] Snyk-Version-Lifecycle-Stage:[experimental] Snyk-Version-Requested:[2022-03-31~experimental] Snyk-Version-Served:[2022-03-31~experimental] Strict-Transport-Security:[max-age=31536000; preload] Sunset:[2022-04-30] X-Content-Type-Options:[nosniff] X-Envoy-Upstream-Service-Time:[99] X-Frame-Options:[SAMEORIGIN]]
2025-05-27T04:10:44Z sbom:1 - Successfully converted depGraph to SBOM
2025-05-27T04:10:44Z sbom:1 - Successfully generated SBOM document.
2025-05-27T04:10:44Z sbom:1 - Workflow End
2025-05-27T04:10:44Z findings.filter:4 - Workflow Start
2025-05-27T04:10:44Z findings.filter:4 - Workflow End
2025-05-27T04:10:44Z output:5 - Workflow Start
2025-05-27T04:10:44Z output:5 - No findings to process
2025-05-27T04:10:44Z output:5 - Processing 'did://sbom/sbom#273402000' based on 'unknown' of type 'application/vnd.cyclonedx+json'
2025-05-27T04:10:44Z output:5 - Workflow End
2025-05-27T04:10:44Z main - Deriving Exit Code 0 (cause: <nil>)
2025-05-27T04:10:44Z main - Sending Analytics
2025-05-27T04:10:44Z main - > request [0xc000f1eb40]: POST https://api.snyk.io/v1/analytics/cli?org=************************************
2025-05-27T04:10:44Z main - > request [0xc000f1eb40]: header: map[Authorization:[Bearer ***] Content-Type:[application/json; charset=utf-8] Session-Token:[Bearer ***] Snyk-Interaction-Id:[urn:snyk:interaction:************************************] Snyk-Request-Id:[************************************] User-Agent:[snyk-cli/1.1297.1 (windows;amd64) TS_BINARY_WRAPPER/1.1297.1] X-Snyk-Cli-Version:[1.1297.1]]
2025-05-27T04:10:44Z main - < response [0xc000f1eb40]: 200 OK
2025-05-27T04:10:44Z main - < response [0xc000f1eb40]: header: map[Akamai-Cache-Status:[Miss from child] Akamai-Grn:[0.5c9e3617.1748319044.9557dc03] Content-Length:[11] Content-Security-Policy:[base-uri https://app.snyk.io; script-src 'self' 'nonce-r2BHClKgAROycI1D6DQkxQ==' 'unsafe-inline' 'unsafe-eval' 'strict-dynamic' 'report-sample'; img-src https: data:; object-src 'none'; frame-ancestors https://app.snyk.io https://learn.snyk.io https://login.snyk.io https://security.snyk.io; report-to csp-report-group; report-uri https://web-reports.snyk.io/csp?version=bc14206829245c170ad364aaf99bd4a193d5b071;] Content-Type:[application/json; charset=utf-8] Date:[Tue, 27 May 2025 04:10:44 GMT] Report-To:[{"group":"csp-report-group","max_age":1800,"endpoints":[{"url":"https://web-reports.snyk.io/csp?version=bc14206829245c170ad364aaf99bd4a193d5b071"}],"include_subdomains":true}] Snyk-Request-Id:[************************************] Strict-Transport-Security:[max-age=31536000; preload] Vary:[X-HTTP-Method-Override, Accept-Encoding] X-Content-Type-Options:[nosniff] X-Frame-Options:[SAMEORIGIN]]
2025-05-27T04:10:44Z main - Analytics successfully send
2025-05-27T04:10:44Z main - Sending Instrumentation
2025-05-27T04:10:44Z analytics.report:6 - Workflow Start
2025-05-27T04:10:45Z main - > request [0xc0012a43c0]: GET https://api.snyk.io/rest/self?version=2024-04-22
2025-05-27T04:10:45Z main - > request [0xc0012a43c0]: header: map[Authorization:[Bearer ***] Session-Token:[Bearer ***] Snyk-Interaction-Id:[urn:snyk:interaction:************************************] Snyk-Request-Id:[************************************] User-Agent:[snyk-cli/1.1297.1 (windows;amd64) TS_BINARY_WRAPPER/1.1297.1] X-Snyk-Cli-Version:[1.1297.1]]
2025-05-27T04:10:45Z main - < response [0xc0012a43c0]: 200 OK
2025-05-27T04:10:45Z main - < response [0xc0012a43c0]: header: map[Akamai-Cache-Status:[Miss from child] Akamai-Grn:[0.5c9e3617.1748319045.9557ea8d] Content-Length:[500] Content-Type:[application/vnd.api+json; charset=utf-8] Date:[Tue, 27 May 2025 04:10:45 GMT] Deprecation:[2024-05-31] Server:[envoy] Snyk-Request-Id:[************************************] Snyk-Version-Lifecycle-Stage:[ga] Snyk-Version-Requested:[2024-04-22] Snyk-Version-Served:[2024-04-22] Strict-Transport-Security:[max-age=31536000; preload] Sunset:[2024-10-19] Vary:[Accept-Encoding] X-Content-Type-Options:[nosniff] X-Envoy-Upstream-Service-Time:[10] X-Frame-Options:[SAMEORIGIN]]
2025-05-27T04:10:45Z analytics.report:6 - adding command line input
2025-05-27T04:10:45Z analytics.report:6 - [0] Processing element
2025-05-27T04:10:45Z analytics.report:6 - [0] Schema Version: 2
2025-05-27T04:10:45Z analytics.report:6 - [0] Data: {"data":{"attributes":{"interaction":{"categories":["sbom","format","all-projects","debug"],"errors":[],"extension":{"exitcode":0,"legacycli::metadata__allProjects__ignore":"[node_modules .build]","legacycli::metadata__allProjects__packageManagers":"[maven maven maven maven npm]","legacycli::metadata__allProjects__scannedProjects":5,"legacycli::metadata__allProjects__targetFiles":"[REDACTED\\Documents\\IdeaProjects\\next-propertyconnect-channel\\pom.xml REDACTED\\Documents\\IdeaProjects\\next-propertyconnect-channel\\next-propertyconnect-channel-api\\pom.xml REDACTED\\Documents\\IdeaProjects\\next-propertyconnect-channel\\next-propertyconnect-channel-common\\pom.xml REDACTED\\Documents\\IdeaProjects\\next-propertyconnect-channel\\next-propertyconnect-channel-task\\pom.xml REDACTED\\Documents\\IdeaProjects\\next-propertyconnect-channel\\next-propertyconnect-channel-task\\src\\main\\resources\\tool\\package.json]","legacycli::metadata__local":true,"legacycli::metadata__package":"[com.derbysoft.next:next-propertyconnect-channel@1.0.0 com.derbysoft.next:next-propertyconnect-channel-api@1.0.0 com.derbysoft.next:next-propertyconnect-channel-common@1.0.0 com.derbysoft.next:next-propertyconnect-channel-task@1.0.0 node-script@1.0.0]","legacycli::metadata__packageManager":"[maven maven maven maven npm]","legacycli::metadata__packageName":"[com.derbysoft.next:next-propertyconnect-channel com.derbysoft.next:next-propertyconnect-channel-api com.derbysoft.next:next-propertyconnect-channel-common com.derbysoft.next:next-propertyconnect-channel-task node-script]","legacycli::metadata__packageVersion":"[1.0.0 1.0.0 1.0.0 1.0.0 1.0.0]","legacycli::metadata__pluginName":"custom-auto-detect","legacycli::metadata__policies":5,"legacycli::metadata__policyLocations":"[REDACTED\\Documents\\IdeaProjects\\next-propertyconnect-channel REDACTED\\Documents\\IdeaProjects\\next-propertyconnect-channel\\next-propertyconnect-channel-api REDACTED\\Documents\\IdeaProjects\\next-propertyconnect-channel\\next-propertyconnect-channel-common REDACTED\\Documents\\IdeaProjects\\next-propertyconnect-channel\\next-propertyconnect-channel-task REDACTED\\Documents\\IdeaProjects\\next-propertyconnect-channel\\next-propertyconnect-channel-task\\src\\main\\resources\\tool]","legacycli::metadata__prune.graphToTreeDuration":9,"legacycli::metadata__prune.treeToGraphDuration":108,"legacycli::metadata__upgradable-snyk-protect-paths":0},"id":"urn:snyk:interaction:************************************","results":[],"stage":"dev","status":"success","target":{"id":"pkg:git/bitbucket.org/derbysoft/next-propertyconnect-channel@e26d8be2316e449e00c750781706bdf805432281?branch=develop-java17"},"timestamp_ms":1748315970590,"type":""},"runtime":{"application":{"name":"snyk-cli","version":"1.1297.1"},"integration":{"name":"TS_BINARY_WRAPPER","version":"1.1297.1"},"performance":{"duration_ms":3073699},"platform":{"arch":"amd64","os":"windows"}}},"type":""}}
2025-05-27T04:10:45Z main - > request [0xc0012a5e00]: POST https://api.snyk.io/hidden/orgs/************************************/analytics?version=2024-03-07~experimental
2025-05-27T04:10:45Z main - > request [0xc0012a5e00]: header: map[Authorization:[Bearer ***] Content-Type:[application/json] Session-Token:[Bearer ***] Snyk-Interaction-Id:[urn:snyk:interaction:************************************] Snyk-Request-Id:[************************************] User-Agent:[snyk-cli/1.1297.1 (windows;amd64) TS_BINARY_WRAPPER/1.1297.1] X-Snyk-Cli-Version:[1.1297.1]]
2025-05-27T04:10:45Z main - < response [0xc0012a5e00]: 201 Created
2025-05-27T04:10:45Z main - < response [0xc0012a5e00]: header: map[Akamai-Cache-Status:[Miss from child] Akamai-Grn:[0.5c9e3617.1748319045.9557f888] Content-Length:[2] Content-Type:[application/json] Date:[Tue, 27 May 2025 04:10:45 GMT] Deprecation:[2024-10-15] Server:[envoy] Snyk-Request-Id:[************************************] Snyk-Version-Lifecycle-Stage:[experimental] Snyk-Version-Requested:[2024-03-07~experimental] Snyk-Version-Served:[2024-03-07~experimental] Strict-Transport-Security:[max-age=31536000; preload] Sunset:[2024-04-06] X-Content-Type-Options:[nosniff] X-Envoy-Upstream-Service-Time:[1] X-Frame-Options:[SAMEORIGIN]]
2025-05-27T04:10:45Z analytics.report:6 - Workflow End
2025-05-27T04:10:45Z main - Instrumentation successfully sent
2025-05-27T04:10:45Z internal.cleanup:7 - Workflow Start
2025-05-27T04:10:45Z internal.cleanup:7 - Deleted temporary certificate file: C:\Users\<USER>\AppData\Local/snyk/snyk-cli/1.1297.1/tmp/pid36104\snyk-cli-cert-3631176830.crt
2025-05-27T04:10:45Z internal.cleanup:7 - Deleted temporary directory: C:\Users\<USER>\AppData\Local/snyk/snyk-cli/1.1297.1/tmp/pid36104
2025-05-27T04:10:45Z internal.cleanup:7 - Workflow End
2025-05-27T04:10:45Z main - Interaction:           urn:snyk:interaction:************************************
2025-05-27T04:10:45Z main - Exit Code:             0
2025-05-27T04:10:45.814Z: {
  status: 0,
  signal: null,
  output: [ null, null, null ],
  pid: 36104,
  stdout: null,
  stderr: null
}
