package com.derbysoft.next.propertyconnect.channel.task.controller.api

import com.derbysoft.next.propertyconnect.channel.task.service.ChannelHotelCRUDService
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelHotelDTO
import com.fasterxml.jackson.databind.ObjectMapper
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.http.MediaType
import org.springframework.test.context.ContextConfiguration
import org.springframework.test.web.servlet.MockMvc
import spock.lang.Specification
import spock.mock.DetachedMockFactory

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*

/**
 * Phase 3 - Controller Layer Tests
 * Comprehensive Spock tests for ChannelHotelController
 * Tests REST endpoints, request validation, error handling, response mapping
 */
@WebMvcTest
@ContextConfiguration(classes = [TestConfig])
class ChannelHotelControllerSpec extends Specification {

    @Autowired
    MockMvc mockMvc

    @Autowired
    ChannelHotelCRUDService mockChannelHotelService

    @Autowired
    ObjectMapper objectMapper

    @TestConfiguration
    static class TestConfig {
        def mockFactory = new DetachedMockFactory()

        @Bean
        ChannelHotelCRUDService channelHotelCRUDService() {
            return mockFactory.Mock(ChannelHotelCRUDService)
        }
    }

    def "should create channel hotel successfully"() {
        given: "a valid channel hotel request"
        def channelHotelDTO = new ChannelHotelDTO().tap {
            supplierId = "SUPPLIER_001"
            channelId = "CHANNEL_001"
            hotelId = "HOTEL_001"
            channelHotelId = "CH_HOTEL_001"
        }

        and: "service returns created hotel"
        mockChannelHotelService.createChannelHotel(_) >> channelHotelDTO.tap {
            id = "GENERATED_ID_001"
        }

        when: "posting to create channel hotel endpoint"
        def response = mockMvc.perform(post("/api/channel-hotels")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(channelHotelDTO)))

        then: "should return 201 Created with hotel data"
        response.andExpect(status().isCreated())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath('$.id').value("GENERATED_ID_001"))
                .andExpect(jsonPath('$.supplierId').value("SUPPLIER_001"))
                .andExpect(jsonPath('$.channelId').value("CHANNEL_001"))
                .andExpect(jsonPath('$.hotelId').value("HOTEL_001"))

        and: "service should be called once"
        1 * mockChannelHotelService.createChannelHotel(_)
    }

    def "should return 400 for invalid channel hotel request"() {
        given: "an invalid channel hotel request (missing required fields)"
        def invalidRequest = [
            supplierId: "", // Empty supplier ID
            channelId: "CHANNEL_001"
            // Missing hotelId
        ]

        when: "posting invalid request"
        def response = mockMvc.perform(post("/api/channel-hotels")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(invalidRequest)))

        then: "should return 400 Bad Request"
        response.andExpect(status().isBadRequest())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath('$.error').exists())
                .andExpect(jsonPath('$.message').exists())

        and: "service should not be called"
        0 * mockChannelHotelService.createChannelHotel(_)
    }

    def "should retrieve channel hotel by ID successfully"() {
        given: "an existing channel hotel ID"
        def hotelId = "HOTEL_001"
        def channelHotelDTO = new ChannelHotelDTO().tap {
            id = "GENERATED_ID_001"
            supplierId = "SUPPLIER_001"
            channelId = "CHANNEL_001"
            hotelId = hotelId
            channelHotelId = "CH_HOTEL_001"
        }

        and: "service returns the hotel"
        mockChannelHotelService.getChannelHotel(hotelId) >> channelHotelDTO

        when: "getting channel hotel by ID"
        def response = mockMvc.perform(get("/api/channel-hotels/{id}", hotelId))

        then: "should return 200 OK with hotel data"
        response.andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath('$.id').value("GENERATED_ID_001"))
                .andExpect(jsonPath('$.supplierId').value("SUPPLIER_001"))
                .andExpect(jsonPath('$.hotelId').value(hotelId))

        and: "service should be called once"
        1 * mockChannelHotelService.getChannelHotel(hotelId)
    }

    def "should return 404 when channel hotel not found"() {
        given: "a non-existent hotel ID"
        def nonExistentId = "NON_EXISTENT_HOTEL"

        and: "service returns null"
        mockChannelHotelService.getChannelHotel(nonExistentId) >> null

        when: "getting non-existent channel hotel"
        def response = mockMvc.perform(get("/api/channel-hotels/{id}", nonExistentId))

        then: "should return 404 Not Found"
        response.andExpect(status().isNotFound())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath('$.error').value("NOT_FOUND"))
                .andExpect(jsonPath('$.message').exists())

        and: "service should be called once"
        1 * mockChannelHotelService.getChannelHotel(nonExistentId)
    }

    def "should update channel hotel successfully"() {
        given: "an existing hotel ID and update request"
        def hotelId = "HOTEL_001"
        def updateRequest = new ChannelHotelDTO().tap {
            supplierId = "SUPPLIER_001"
            channelId = "CHANNEL_001"
            hotelId = hotelId
            channelHotelId = "CH_HOTEL_001_UPDATED"
        }

        and: "service returns updated hotel"
        mockChannelHotelService.updateChannelHotel(hotelId, _) >> updateRequest.tap {
            id = "GENERATED_ID_001"
        }

        when: "putting update to channel hotel endpoint"
        def response = mockMvc.perform(put("/api/channel-hotels/{id}", hotelId)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(updateRequest)))

        then: "should return 200 OK with updated data"
        response.andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath('$.id').value("GENERATED_ID_001"))
                .andExpect(jsonPath('$.channelHotelId').value("CH_HOTEL_001_UPDATED"))

        and: "service should be called once"
        1 * mockChannelHotelService.updateChannelHotel(hotelId, _)
    }

    def "should delete channel hotel successfully"() {
        given: "an existing hotel ID"
        def hotelId = "HOTEL_001"

        and: "service successfully deletes hotel"
        mockChannelHotelService.deleteChannelHotel(hotelId) >> true

        when: "deleting channel hotel"
        def response = mockMvc.perform(delete("/api/channel-hotels/{id}", hotelId))

        then: "should return 204 No Content"
        response.andExpect(status().isNoContent())

        and: "service should be called once"
        1 * mockChannelHotelService.deleteChannelHotel(hotelId)
    }

    def "should return 404 when deleting non-existent hotel"() {
        given: "a non-existent hotel ID"
        def nonExistentId = "NON_EXISTENT_HOTEL"

        and: "service returns false (not found)"
        mockChannelHotelService.deleteChannelHotel(nonExistentId) >> false

        when: "deleting non-existent hotel"
        def response = mockMvc.perform(delete("/api/channel-hotels/{id}", nonExistentId))

        then: "should return 404 Not Found"
        response.andExpect(status().isNotFound())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath('$.error').value("NOT_FOUND"))

        and: "service should be called once"
        1 * mockChannelHotelService.deleteChannelHotel(nonExistentId)
    }

    def "should handle service exceptions gracefully"() {
        given: "a hotel ID that causes service exception"
        def hotelId = "ERROR_HOTEL"

        and: "service throws exception"
        mockChannelHotelService.getChannelHotel(hotelId) >> {
            throw new RuntimeException("Database connection failed")
        }

        when: "getting hotel that causes exception"
        def response = mockMvc.perform(get("/api/channel-hotels/{id}", hotelId))

        then: "should return 500 Internal Server Error"
        response.andExpect(status().isInternalServerError())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath('$.error').value("INTERNAL_SERVER_ERROR"))
                .andExpect(jsonPath('$.message').exists())

        and: "service should be called once"
        1 * mockChannelHotelService.getChannelHotel(hotelId)
    }

    def "should validate request content type"() {
        given: "a valid channel hotel request with wrong content type"
        def channelHotelDTO = new ChannelHotelDTO().tap {
            supplierId = "SUPPLIER_001"
            channelId = "CHANNEL_001"
            hotelId = "HOTEL_001"
        }

        when: "posting with wrong content type"
        def response = mockMvc.perform(post("/api/channel-hotels")
                .contentType(MediaType.TEXT_PLAIN)
                .content(objectMapper.writeValueAsString(channelHotelDTO)))

        then: "should return 415 Unsupported Media Type"
        response.andExpect(status().isUnsupportedMediaType())

        and: "service should not be called"
        0 * mockChannelHotelService.createChannelHotel(_)
    }

    def "should handle malformed JSON gracefully"() {
        given: "malformed JSON request"
        def malformedJson = '{"supplierId":"SUPPLIER_001","channelId":"CHANNEL_001",}'

        when: "posting malformed JSON"
        def response = mockMvc.perform(post("/api/channel-hotels")
                .contentType(MediaType.APPLICATION_JSON)
                .content(malformedJson))

        then: "should return 400 Bad Request"
        response.andExpect(status().isBadRequest())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath('$.error').exists())

        and: "service should not be called"
        0 * mockChannelHotelService.createChannelHotel(_)
    }

    def "should list channel hotels with pagination"() {
        given: "pagination parameters"
        def page = 0
        def size = 10
        def channelHotels = (1..5).collect { i ->
            new ChannelHotelDTO().tap {
                id = "HOTEL_${i.toString().padLeft(3, '0')}"
                supplierId = "SUPPLIER_001"
                channelId = "CHANNEL_001"
                hotelId = "HOTEL_${i.toString().padLeft(3, '0')}"
            }
        }

        and: "service returns paginated results"
        mockChannelHotelService.listChannelHotels(page, size) >> [
            content: channelHotels,
            totalElements: 25,
            totalPages: 3,
            number: page,
            size: size
        ]

        when: "getting channel hotels with pagination"
        def response = mockMvc.perform(get("/api/channel-hotels")
                .param("page", page.toString())
                .param("size", size.toString()))

        then: "should return 200 OK with paginated data"
        response.andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath('$.content').isArray())
                .andExpect(jsonPath('$.content.length()').value(5))
                .andExpect(jsonPath('$.totalElements').value(25))
                .andExpect(jsonPath('$.totalPages').value(3))
                .andExpect(jsonPath('$.number').value(page))
                .andExpect(jsonPath('$.size').value(size))

        and: "service should be called once"
        1 * mockChannelHotelService.listChannelHotels(page, size)
    }

    def "should search channel hotels by criteria"() {
        given: "search criteria"
        def supplierId = "SUPPLIER_001"
        def channelId = "CHANNEL_001"
        def searchResults = [
            new ChannelHotelDTO().tap {
                id = "HOTEL_001"
                supplierId = supplierId
                channelId = channelId
                hotelId = "HOTEL_001"
            },
            new ChannelHotelDTO().tap {
                id = "HOTEL_002"
                supplierId = supplierId
                channelId = channelId
                hotelId = "HOTEL_002"
            }
        ]

        and: "service returns search results"
        mockChannelHotelService.searchChannelHotels(supplierId, channelId) >> searchResults

        when: "searching channel hotels"
        def response = mockMvc.perform(get("/api/channel-hotels/search")
                .param("supplierId", supplierId)
                .param("channelId", channelId))

        then: "should return 200 OK with search results"
        response.andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath('$').isArray())
                .andExpect(jsonPath('$.length()').value(2))
                .andExpect(jsonPath('$[0].supplierId').value(supplierId))
                .andExpect(jsonPath('$[0].channelId').value(channelId))
                .andExpect(jsonPath('$[1].supplierId').value(supplierId))
                .andExpect(jsonPath('$[1].channelId').value(channelId))

        and: "service should be called once"
        1 * mockChannelHotelService.searchChannelHotels(supplierId, channelId)
    }
}
