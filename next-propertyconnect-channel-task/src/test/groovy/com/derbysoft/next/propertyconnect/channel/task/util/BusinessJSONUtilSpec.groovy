package com.derbysoft.next.propertyconnect.channel.task.util

import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelHotelDTO
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelProductsDTO
import com.fasterxml.jackson.databind.ObjectMapper
import spock.lang.Specification
import spock.lang.Shared
import spock.lang.Unroll

/**
 * Phase 1 - Mapping & Utility Tests
 * Comprehensive Spock tests for BusinessJSONUtil
 * Tests JSON serialization/deserialization, null safety, error handling
 */
class BusinessJSONUtilSpec extends Specification {

    @Shared
    BusinessJSONUtil jsonUtil

    @Shared
    ObjectMapper objectMapper

    def setupSpec() {
        jsonUtil = new BusinessJSONUtil()
        objectMapper = new ObjectMapper()
    }

    def "should serialize simple object to JSON"() {
        given: "a simple object"
        def data = [name: "Test", value: 123, active: true]

        when: "serializing to JSON"
        def json = jsonUtil.toJson(data)

        then: "should produce valid JSON"
        json != null
        json.contains('"name":"Test"')
        json.contains('"value":123')
        json.contains('"active":true')
    }

    def "should deserialize JSON to object"() {
        given: "a JSON string"
        def json = '{"name":"Test","value":123,"active":true}'

        when: "deserializing from JSON"
        def result = jsonUtil.fromJson(json, Map.class)

        then: "should produce correct object"
        result != null
        result.name == "Test"
        result.value == 123
        result.active == true
    }

    def "should handle null input gracefully"() {
        when: "serializing null"
        def jsonResult = jsonUtil.toJson(null)

        and: "deserializing null"
        def objectResult = jsonUtil.fromJson(null, Map.class)

        then: "should handle nulls without throwing exceptions"
        jsonResult == null || jsonResult == "null"
        objectResult == null
    }

    def "should handle empty string input"() {
        when: "deserializing empty string"
        def result = jsonUtil.fromJson("", Map.class)

        then: "should handle gracefully"
        result == null || result == [:]
    }

    @Unroll
    def "should serialize complex object: #objectType"() {
        given: "a complex object"
        def complexObject = createComplexObject(objectType)

        when: "serializing to JSON"
        def json = jsonUtil.toJson(complexObject)

        then: "should produce valid JSON"
        json != null
        json.length() > 0
        !json.contains("null") || objectType == "withNulls"

        where:
        objectType << ["channelHotel", "channelProducts", "withNulls", "nested"]
    }

    @Unroll
    def "should deserialize complex JSON: #jsonType"() {
        given: "a complex JSON string"
        def json = createComplexJson(jsonType)

        when: "deserializing from JSON"
        def result = jsonUtil.fromJson(json, getTargetClass(jsonType))

        then: "should produce correct object"
        result != null
        validateDeserializedObject(result, jsonType)

        where:
        jsonType << ["channelHotel", "channelProducts", "simpleMap", "arrayData"]
    }

    def "should handle malformed JSON gracefully"() {
        given: "malformed JSON strings"
        def malformedJsons = [
            '{"name":"Test",}',  // Trailing comma
            '{"name":Test}',     // Unquoted value
            '{name:"Test"}',     // Unquoted key
            '{"name":"Test"',    // Missing closing brace
            'not json at all'    // Not JSON
        ]

        when: "deserializing malformed JSON"
        def results = malformedJsons.collect { json ->
            try {
                jsonUtil.fromJson(json, Map.class)
            } catch (Exception e) {
                return "ERROR: ${e.class.simpleName}"
            }
        }

        then: "should handle errors gracefully"
        results.every { it == null || it.toString().startsWith("ERROR:") }
    }

    def "should preserve data types during round-trip"() {
        given: "object with various data types"
        def originalData = [
            stringValue: "test string",
            intValue: 42,
            longValue: 123456789L,
            doubleValue: 3.14159d,
            booleanValue: true,
            nullValue: null,
            listValue: [1, 2, 3],
            mapValue: [nested: "value"]
        ]

        when: "performing round-trip serialization"
        def json = jsonUtil.toJson(originalData)
        def deserializedData = jsonUtil.fromJson(json, Map.class)

        then: "should preserve all data types"
        deserializedData != null
        deserializedData.stringValue == "test string"
        deserializedData.intValue == 42
        deserializedData.doubleValue == 3.14159d
        deserializedData.booleanValue == true
        deserializedData.nullValue == null
        deserializedData.listValue == [1, 2, 3]
        deserializedData.mapValue.nested == "value"
    }

    def "should handle large objects efficiently"() {
        given: "a large object"
        def largeObject = createLargeObject(1000)

        when: "serializing large object"
        def startTime = System.currentTimeMillis()
        def json = jsonUtil.toJson(largeObject)
        def serializationTime = System.currentTimeMillis() - startTime

        and: "deserializing large object"
        startTime = System.currentTimeMillis()
        def result = jsonUtil.fromJson(json, Map.class)
        def deserializationTime = System.currentTimeMillis() - startTime

        then: "should handle efficiently"
        json != null
        result != null
        serializationTime < 1000   // Should complete within 1 second
        deserializationTime < 1000 // Should complete within 1 second
        result.size() == 1000
    }

    def "should handle concurrent serialization operations"() {
        given: "multiple objects for concurrent processing"
        def objects = (1..10).collect { i ->
            [id: i, name: "Object ${i}", data: (1..10).collect { "item${it}" }]
        }

        when: "serializing concurrently"
        def results = objects.parallelStream()
            .map { obj -> jsonUtil.toJson(obj) }
            .collect()

        then: "should handle concurrent operations"
        results.size() == 10
        results.every { it != null && it.length() > 0 }
        results.every { it.contains('"name":"Object ') }
    }

    def "should handle special characters in JSON"() {
        given: "object with special characters"
        def specialData = [
            unicodeText: "测试数据 🚀",
            quotesText: 'Text with "quotes" and \'apostrophes\'',
            newlineText: "Line 1\nLine 2\rLine 3",
            tabText: "Column1\tColumn2\tColumn3",
            backslashText: "Path\\to\\file"
        ]

        when: "performing round-trip with special characters"
        def json = jsonUtil.toJson(specialData)
        def result = jsonUtil.fromJson(json, Map.class)

        then: "should preserve special characters"
        result != null
        result.unicodeText == "测试数据 🚀"
        result.quotesText.contains('"quotes"')
        result.newlineText.contains('\n')
        result.tabText.contains('\t')
        result.backslashText.contains('\\')
    }

    // Helper methods for test data creation
    private Object createComplexObject(String type) {
        switch (type) {
            case "channelHotel":
                return new ChannelHotelDTO().tap {
                    supplierId = "SUPPLIER_001"
                    channelId = "CHANNEL_001"
                    hotelId = "HOTEL_001"
                }
            case "channelProducts":
                return new ChannelProductsDTO().tap {
                    supplierId = "SUPPLIER_001"
                    hotelId = "HOTEL_001"
                    channelProducts = [
                        new ChannelProductsDTO.Product().tap {
                            roomName = "Test Room"
                            rateName = "Test Rate"
                        }
                    ]
                }
            case "withNulls":
                return [name: "Test", nullField: null, value: 123]
            case "nested":
                return [
                    level1: [
                        level2: [
                            level3: "deep value"
                        ]
                    ]
                ]
            default:
                return [type: type, value: "test"]
        }
    }

    private String createComplexJson(String type) {
        switch (type) {
            case "channelHotel":
                return '{"supplierId":"SUPPLIER_001","channelId":"CHANNEL_001","hotelId":"HOTEL_001"}'
            case "channelProducts":
                return '{"supplierId":"SUPPLIER_001","hotelId":"HOTEL_001","channelProducts":[{"roomName":"Test Room","rateName":"Test Rate"}]}'
            case "simpleMap":
                return '{"key1":"value1","key2":"value2"}'
            case "arrayData":
                return '[{"id":1,"name":"Item 1"},{"id":2,"name":"Item 2"}]'
            default:
                return '{"type":"' + type + '","value":"test"}'
        }
    }

    private Class<?> getTargetClass(String jsonType) {
        switch (jsonType) {
            case "channelHotel":
                return ChannelHotelDTO.class
            case "channelProducts":
                return ChannelProductsDTO.class
            case "arrayData":
                return List.class
            default:
                return Map.class
        }
    }

    private boolean validateDeserializedObject(Object result, String jsonType) {
        switch (jsonType) {
            case "channelHotel":
                return result instanceof ChannelHotelDTO && 
                       ((ChannelHotelDTO) result).supplierId == "SUPPLIER_001"
            case "channelProducts":
                return result instanceof ChannelProductsDTO && 
                       ((ChannelProductsDTO) result).supplierId == "SUPPLIER_001"
            case "simpleMap":
                return result instanceof Map && 
                       ((Map) result).containsKey("key1")
            case "arrayData":
                return result instanceof List && 
                       ((List) result).size() == 2
            default:
                return result != null
        }
    }

    private Map<String, Object> createLargeObject(int size) {
        def largeObject = [:]
        (1..size).each { i ->
            largeObject["key${i}"] = [
                id: i,
                name: "Item ${i}",
                description: "Description for item ${i}",
                tags: ["tag1", "tag2", "tag3"],
                metadata: [created: new Date(), version: 1.0]
            ]
        }
        return largeObject
    }
}
