package com.derbysoft.next.propertyconnect.channel.task.service.impl.bookingcom

import com.derbysoft.next.propertyconnect.channel.task.service.AccountSettingService
import com.derbysoft.next.propertyconnect.channel.task.service.impl.bookingcom.BookingcomClient
import com.derbysoft.next.propertyconnect.channel.task.service.impl.bookingcom.BookingcomServiceImpl
import feign.FeignException
import org.springframework.test.util.ReflectionTestUtils
import spock.lang.Specification
import spock.lang.Shared
import spock.lang.Unroll

/**
 * Phase 2 - External Service Logic Tests  
 * Comprehensive Spock tests for BookingcomServiceImpl
 * Tests Groovy script execution, HTTP client mocking, error handling
 */
class BookingcomServiceImplSpec extends Specification {

    @Shared
    BookingcomServiceImpl bookingcomService

    AccountSettingService mockAccountSettingService = Mock()
    BookingcomClient mockBookingcomClient = Mock()

    def setup() {
        bookingcomService = new BookingcomServiceImpl()
        ReflectionTestUtils.setField(bookingcomService, "accountSettingService", mockAccountSettingService)
        ReflectionTestUtils.setField(bookingcomService, "bookingcomClient", mockBookingcomClient)
        ReflectionTestUtils.setField(bookingcomService, "hotelLimit", 30000)
    }

    def "should return correct channel name"() {
        expect: "channel name should be BOOKINGCOM"
        bookingcomService.channel() == "BOOKINGCOM"
    }

    def "should return correct request span"() {
        expect: "request span should be 30"
        bookingcomService.requestSpan() == 30
    }

    def "should create room rate request successfully"() {
        given: "a hotel ID"
        def hotelId = "TEST_HOTEL_001"

        and: "mock account settings"
        mockAccountSettingService.getAccountSettings(hotelId) >> [
            username: "test_user",
            password: "test_pass",
            hotelId: hotelId
        ]

        when: "creating room rate request"
        def result = bookingcomService.createRoomRateReq(hotelId)

        then: "should return valid request"
        result != null
        result.containsKey("hotelId")
        result.hotelId == hotelId
        1 * mockAccountSettingService.getAccountSettings(hotelId)
    }

    def "should create Booking.com request authentication"() {
        given: "username and password"
        def username = "test_user"
        def password = "test_pass"

        when: "creating authentication"
        def result = bookingcomService.createBcomReqAuth(username, password)

        then: "should return authentication object"
        result != null
        result.containsKey("username")
        result.containsKey("password")
        result.username == username
        result.password == password
    }

    @Unroll
    def "should handle authentication creation with different inputs: #scenario"() {
        when: "creating authentication"
        def result = bookingcomService.createBcomReqAuth(username, password)

        then: "should handle different scenarios"
        if (expectedValid) {
            result != null
            result.username == username
            result.password == password
        } else {
            result == null || result.isEmpty()
        }

        where:
        scenario              | username      | password      | expectedValid
        "valid credentials"   | "test_user"   | "test_pass"   | true
        "null username"       | null          | "test_pass"   | false
        "null password"       | "test_user"   | null          | false
        "empty username"      | ""            | "test_pass"   | false
        "empty password"      | "test_user"   | ""            | false
        "both null"           | null          | null          | false
        "both empty"          | ""            | ""            | false
    }

    def "should handle hotel limit validation"() {
        given: "hotel limit is set"
        def currentLimit = ReflectionTestUtils.getField(bookingcomService, "hotelLimit")

        expect: "hotel limit should be configured"
        currentLimit == 30000
    }

    def "should retrieve hotel information successfully"() {
        given: "a valid hotel request"
        def request = [
            hotelId: "TEST_HOTEL_001",
            auth: [username: "test_user", password: "test_pass"]
        ]

        and: "mock client returns hotel data"
        mockBookingcomClient.getHotelInfo(_) >> [
            hotelId: "TEST_HOTEL_001",
            hotelName: "Test Hotel",
            address: "123 Test Street",
            city: "Test City",
            country: "Test Country",
            starRating: 4,
            facilities: ["WiFi", "Pool", "Gym"],
            status: "ACTIVE"
        ]

        when: "retrieving hotel information"
        def result = bookingcomService.getHotelInfo(request)

        then: "should return hotel data"
        result != null
        result.hotelId == "TEST_HOTEL_001"
        result.hotelName == "Test Hotel"
        result.starRating == 4
        result.facilities.contains("WiFi")
        result.status == "ACTIVE"
        1 * mockBookingcomClient.getHotelInfo(_)
    }

    def "should handle HTTP client errors gracefully"() {
        given: "a hotel request"
        def request = [
            hotelId: "TEST_HOTEL_001",
            auth: [username: "test_user", password: "test_pass"]
        ]

        and: "mock client throws exception"
        mockBookingcomClient.getHotelInfo(_) >> {
            throw FeignException.badRequest("Invalid request", null, null)
        }

        when: "retrieving hotel information"
        def result = bookingcomService.getHotelInfo(request)

        then: "should handle exception"
        thrown(FeignException)
        1 * mockBookingcomClient.getHotelInfo(_)
    }

    def "should retrieve room types successfully"() {
        given: "a valid room types request"
        def request = [
            hotelId: "TEST_HOTEL_001",
            auth: [username: "test_user", password: "test_pass"]
        ]

        and: "mock client returns room types"
        mockBookingcomClient.getRoomTypes(_) >> [
            roomTypes: [
                [
                    roomTypeId: "RT_001",
                    roomTypeName: "Deluxe Room",
                    bedType: "KING",
                    maxOccupancy: 2,
                    roomSize: 35,
                    amenities: ["WiFi", "AC", "Minibar"]
                ],
                [
                    roomTypeId: "RT_002",
                    roomTypeName: "Standard Room", 
                    bedType: "QUEEN",
                    maxOccupancy: 2,
                    roomSize: 25,
                    amenities: ["WiFi", "AC"]
                ]
            ],
            totalCount: 2,
            status: "SUCCESS"
        ]

        when: "retrieving room types"
        def result = bookingcomService.getRoomTypes(request)

        then: "should return room types data"
        result != null
        result.roomTypes.size() == 2
        result.totalCount == 2
        result.status == "SUCCESS"
        
        with(result.roomTypes[0]) {
            roomTypeId == "RT_001"
            roomTypeName == "Deluxe Room"
            bedType == "KING"
            maxOccupancy == 2
            roomSize == 35
        }
        
        1 * mockBookingcomClient.getRoomTypes(_)
    }

    def "should retrieve rate plans successfully"() {
        given: "a valid rate plans request"
        def request = [
            hotelId: "TEST_HOTEL_001",
            auth: [username: "test_user", password: "test_pass"]
        ]

        and: "mock client returns rate plans"
        mockBookingcomClient.getRatePlans(_) >> [
            ratePlans: [
                [
                    ratePlanId: "RP_001",
                    ratePlanName: "Best Available Rate",
                    rateType: "BAR",
                    paymentType: "HOTEL_COLLECT",
                    cancellationPolicy: "FREE_CANCELLATION_24H",
                    mealPlan: "ROOM_ONLY"
                ],
                [
                    ratePlanId: "RP_002",
                    ratePlanName: "Advance Purchase",
                    rateType: "ADVANCE_PURCHASE", 
                    paymentType: "PREPAID",
                    cancellationPolicy: "NON_REFUNDABLE",
                    mealPlan: "BREAKFAST_INCLUDED"
                ]
            ],
            totalCount: 2,
            status: "SUCCESS"
        ]

        when: "retrieving rate plans"
        def result = bookingcomService.getRatePlans(request)

        then: "should return rate plans data"
        result != null
        result.ratePlans.size() == 2
        result.totalCount == 2
        result.status == "SUCCESS"
        
        with(result.ratePlans[0]) {
            ratePlanId == "RP_001"
            ratePlanName == "Best Available Rate"
            rateType == "BAR"
            paymentType == "HOTEL_COLLECT"
            mealPlan == "ROOM_ONLY"
        }
        
        1 * mockBookingcomClient.getRatePlans(_)
    }

    def "should handle timeout scenarios"() {
        given: "a request that will timeout"
        def request = [
            hotelId: "TEST_HOTEL_001",
            auth: [username: "test_user", password: "test_pass"]
        ]

        and: "mock client simulates timeout"
        mockBookingcomClient.getHotelInfo(_) >> {
            Thread.sleep(5000) // Simulate long response time
            throw new RuntimeException("Request timeout")
        }

        when: "making request with timeout"
        def result = bookingcomService.getHotelInfoWithTimeout(request, 2000)

        then: "should handle timeout"
        thrown(RuntimeException)
        1 * mockBookingcomClient.getHotelInfo(_)
    }

    def "should handle rate limiting scenarios"() {
        given: "multiple rapid requests"
        def requests = (1..10).collect { i ->
            [
                hotelId: "HOTEL_${i.toString().padLeft(3, '0')}",
                auth: [username: "test_user", password: "test_pass"]
            ]
        }

        and: "mock client simulates rate limiting"
        def requestCount = 0
        mockBookingcomClient.getHotelInfo(_) >> {
            requestCount++
            if (requestCount > 5) {
                throw FeignException.tooManyRequests("Rate limit exceeded", null, null)
            }
            return [hotelId: "TEST_HOTEL", status: "SUCCESS"]
        }

        when: "making multiple requests"
        def results = []
        def exceptions = []
        requests.each { request ->
            try {
                results << bookingcomService.getHotelInfo(request)
            } catch (FeignException e) {
                exceptions << e
            }
        }

        then: "should handle rate limiting"
        results.size() <= 5
        exceptions.size() >= 5
        exceptions.every { it instanceof FeignException }
    }

    def "should validate request structure"() {
        given: "various request structures"
        def validRequest = [
            hotelId: "TEST_HOTEL_001",
            auth: [username: "test_user", password: "test_pass"]
        ]
        
        def invalidRequests = [
            null,
            [:],
            [hotelId: "TEST_HOTEL_001"], // Missing auth
            [auth: [username: "test_user", password: "test_pass"]], // Missing hotelId
            [hotelId: "", auth: [username: "test_user", password: "test_pass"]], // Empty hotelId
            [hotelId: "TEST_HOTEL_001", auth: [:]] // Empty auth
        ]

        expect: "valid request should pass validation"
        bookingcomService.validateRequest(validRequest) == true

        and: "invalid requests should fail validation"
        invalidRequests.every { request ->
            bookingcomService.validateRequest(request) == false
        }
    }

    def "should handle concurrent requests safely"() {
        given: "multiple requests for concurrent processing"
        def requests = (1..15).collect { i ->
            [
                hotelId: "HOTEL_${i.toString().padLeft(3, '0')}",
                auth: [username: "test_user", password: "test_pass"]
            ]
        }

        and: "mock client returns data for each request"
        mockBookingcomClient.getHotelInfo(_) >> { args ->
            def request = args[0]
            return [
                hotelId: request.hotelId,
                hotelName: "Hotel ${request.hotelId}",
                status: "SUCCESS"
            ]
        }

        when: "processing requests concurrently"
        def results = requests.parallelStream()
            .map { request -> bookingcomService.getHotelInfo(request) }
            .collect()

        then: "should handle all requests successfully"
        results.size() == 15
        results.every { it != null && it.status == "SUCCESS" }
        results.every { it.hotelName.startsWith("Hotel HOTEL_") }
        (15.._) * mockBookingcomClient.getHotelInfo(_)
    }
}
