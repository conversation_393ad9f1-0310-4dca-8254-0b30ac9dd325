package com.derbysoft.next.propertyconnect.channel.task

import spock.lang.Specification
import spock.lang.Unroll

/**
 * Simple Spock test to verify framework setup
 */
class SimpleSpockTestSpec extends Specification {

    def "should verify Spock framework is working"() {
        given: "a simple calculation"
        def a = 2
        def b = 3

        when: "performing addition"
        def result = a + b

        then: "result should be correct"
        result == 5
    }

    @Unroll
    def "should test parameterized scenarios: #input gives #expected"() {
        expect: "calculation should be correct"
        input * 2 == expected

        where:
        input | expected
        1     | 2
        2     | 4
        3     | 6
        5     | 10
    }

    def "should test string operations"() {
        given: "a string"
        def text = "Hello Spock"

        expect: "string operations work correctly"
        text.length() == 11
        text.startsWith("Hello")
        text.endsWith("Spock")
        text.contains("lo Sp")
    }

    def "should test collections"() {
        given: "a list"
        def numbers = [1, 2, 3, 4, 5]

        expect: "collection operations work"
        numbers.size() == 5
        numbers.contains(3)
        numbers.first() == 1
        numbers.last() == 5
        numbers.sum() == 15
    }

    def "should test exception handling"() {
        when: "dividing by zero"
        def result = 10 / 0

        then: "should throw ArithmeticException"
        thrown(ArithmeticException)
    }

    def "should test mock behavior"() {
        given: "a mock list"
        def mockList = Mock(List)

        when: "calling size method"
        def size = mockList.size()

        then: "mock should return stubbed value"
        1 * mockList.size() >> 42
        size == 42
    }
}
