package com.derbysoft.next.propertyconnect.channel.task.service.impl.agoda

import com.derbysoft.next.propertyconnect.channel.task.service.AccountSettingService
import com.derbysoft.next.propertyconnect.channel.task.service.impl.agoda.AgodaClient
import com.derbysoft.next.propertyconnect.channel.task.service.impl.agoda.AgodaServiceImpl
import feign.FeignException
import feign.RetryableException
import spock.lang.Specification
import spock.lang.Shared
import spock.lang.Unroll
import spock.lang.Timeout

import java.util.concurrent.TimeUnit

/**
 * Phase 2 - External Service Logic Tests
 * Comprehensive Spock tests for AgodaServiceImpl
 * Tests HTTP client mocking, timeout handling, retry logic, rate limiting
 */
class AgodaServiceImplSpec extends Specification {

    @Shared
    AgodaServiceImpl agodaService

    AccountSettingService mockAccountSettingService = Mock()
    AgodaClient mockAgodaClient = Mock()

    def setup() {
        agodaService = new AgodaServiceImpl(mockAccountSettingService, mockAgodaClient)
    }

    def cleanup() {
        // Reset any static state if needed
    }

    def "should return correct channel name"() {
        expect: "channel name should be AGODA"
        agodaService.channel() == "AGODA"
    }

    def "should create Agoda request successfully"() {
        given: "a hotel ID"
        def hotelId = "TEST_HOTEL_001"

        and: "mock account settings"
        mockAccountSettingService.getAccountSettings(hotelId) >> [
            username: "test_user",
            password: "test_pass",
            endpoint: "https://api.agoda.com"
        ]

        when: "creating Agoda request"
        def result = agodaService.createAgodaRequest(hotelId)

        then: "should return valid request object"
        result != null
        result.containsKey("hotelId")
        result.hotelId == hotelId
        1 * mockAccountSettingService.getAccountSettings(hotelId)
    }

    def "should handle null hotel ID gracefully"() {
        when: "creating request with null hotel ID"
        def result = agodaService.createAgodaRequest(null)

        then: "should handle gracefully"
        result == null || result.isEmpty()
        0 * mockAccountSettingService.getAccountSettings(_)
    }

    def "should handle empty hotel ID gracefully"() {
        when: "creating request with empty hotel ID"
        def result = agodaService.createAgodaRequest("")

        then: "should handle gracefully"
        result == null || result.isEmpty()
        0 * mockAccountSettingService.getAccountSettings(_)
    }

    @Unroll
    def "should handle account settings retrieval: #scenario"() {
        given: "a hotel ID"
        def hotelId = "TEST_HOTEL_001"

        and: "mock account settings response"
        mockAccountSettingService.getAccountSettings(hotelId) >> accountSettings

        when: "creating Agoda request"
        def result = agodaService.createAgodaRequest(hotelId)

        then: "should handle different account settings scenarios"
        if (expectedResult) {
            result != null
            result.hotelId == hotelId
        } else {
            result == null || result.isEmpty()
        }
        1 * mockAccountSettingService.getAccountSettings(hotelId)

        where:
        scenario              | accountSettings                                    | expectedResult
        "valid settings"      | [username: "user", password: "pass"]             | true
        "null settings"       | null                                              | false
        "empty settings"      | [:]                                               | false
        "missing username"    | [password: "pass"]                                | false
        "missing password"    | [username: "user"]                                | false
        "partial settings"    | [username: "user", password: "pass", extra: "x"] | true
    }

    def "should handle HTTP client timeout"() {
        given: "a hotel ID and request"
        def hotelId = "TEST_HOTEL_001"
        def request = [hotelId: hotelId, action: "getRooms"]

        and: "mock account settings"
        mockAccountSettingService.getAccountSettings(hotelId) >> [
            username: "test_user",
            password: "test_pass"
        ]

        and: "mock client throws timeout exception"
        mockAgodaClient.getRooms(_) >> { 
            throw new RetryableException(
                500, "Connection timeout", null, null, null
            )
        }

        when: "calling service method that uses HTTP client"
        def result = agodaService.getRooms(request)

        then: "should handle timeout gracefully"
        thrown(RetryableException)
        1 * mockAgodaClient.getRooms(_)
    }

    def "should handle HTTP client service unavailable"() {
        given: "a hotel ID and request"
        def hotelId = "TEST_HOTEL_001"
        def request = [hotelId: hotelId, action: "getRooms"]

        and: "mock client throws service unavailable exception"
        mockAgodaClient.getRooms(_) >> { 
            throw FeignException.serviceUnavailable("Service unavailable", null, null)
        }

        when: "calling service method"
        def result = agodaService.getRooms(request)

        then: "should handle service unavailable"
        thrown(FeignException)
        1 * mockAgodaClient.getRooms(_)
    }

    @Timeout(value = 5, unit = TimeUnit.SECONDS)
    def "should handle retry logic with exponential backoff"() {
        given: "a hotel ID and request"
        def hotelId = "TEST_HOTEL_001"
        def request = [hotelId: hotelId, action: "getRooms"]

        and: "mock client fails first two times, succeeds third time"
        def callCount = 0
        mockAgodaClient.getRooms(_) >> {
            callCount++
            if (callCount <= 2) {
                throw new RetryableException(
                    503, "Service temporarily unavailable", null, null, null
                )
            }
            return [
                rooms: [
                    [roomId: "ROOM_001", roomName: "Deluxe Room"],
                    [roomId: "ROOM_002", roomName: "Standard Room"]
                ]
            ]
        }

        when: "calling service method with retry"
        def result = agodaService.getRoomsWithRetry(request, 3, 100)

        then: "should succeed after retries"
        result != null
        result.rooms.size() == 2
        callCount == 3
    }

    def "should handle rate limiting"() {
        given: "multiple concurrent requests"
        def hotelId = "TEST_HOTEL_001"
        def requests = (1..10).collect { i ->
            [hotelId: hotelId, action: "getRooms", requestId: i]
        }

        and: "mock client with rate limiting simulation"
        def requestTimes = []
        mockAgodaClient.getRooms(_) >> { args ->
            requestTimes << System.currentTimeMillis()
            if (requestTimes.size() > 5) {
                // Simulate rate limiting after 5 requests
                throw FeignException.tooManyRequests("Rate limit exceeded", null, null)
            }
            return [rooms: [[roomId: "ROOM_001", roomName: "Test Room"]]]
        }

        when: "making multiple requests"
        def results = []
        def exceptions = []
        requests.each { request ->
            try {
                results << agodaService.getRooms(request)
            } catch (FeignException e) {
                exceptions << e
            }
        }

        then: "should handle rate limiting"
        results.size() <= 5
        exceptions.size() >= 5
        exceptions.every { it instanceof FeignException }
    }

    def "should handle successful room retrieval"() {
        given: "a valid request"
        def request = [hotelId: "TEST_HOTEL_001", action: "getRooms"]

        and: "mock client returns room data"
        mockAgodaClient.getRooms(_) >> [
            rooms: [
                [
                    roomId: "ROOM_001",
                    roomName: "Deluxe Room",
                    bedType: "KING",
                    maxOccupancy: 2,
                    amenities: ["WiFi", "AC", "TV"]
                ],
                [
                    roomId: "ROOM_002", 
                    roomName: "Standard Room",
                    bedType: "QUEEN",
                    maxOccupancy: 2,
                    amenities: ["WiFi", "AC"]
                ]
            ],
            totalCount: 2,
            status: "SUCCESS"
        ]

        when: "retrieving rooms"
        def result = agodaService.getRooms(request)

        then: "should return room data"
        result != null
        result.rooms.size() == 2
        result.totalCount == 2
        result.status == "SUCCESS"
        
        with(result.rooms[0]) {
            roomId == "ROOM_001"
            roomName == "Deluxe Room"
            bedType == "KING"
            maxOccupancy == 2
            amenities.contains("WiFi")
        }
        
        1 * mockAgodaClient.getRooms(_)
    }

    def "should handle successful rate plan retrieval"() {
        given: "a valid request"
        def request = [hotelId: "TEST_HOTEL_001", action: "getRatePlans"]

        and: "mock client returns rate plan data"
        mockAgodaClient.getRatePlans(_) >> [
            ratePlans: [
                [
                    rateId: "RATE_001",
                    rateName: "Best Available Rate",
                    rateType: "BAR",
                    paymentType: "PREPAID",
                    cancellationPolicy: "FREE_CANCELLATION"
                ],
                [
                    rateId: "RATE_002",
                    rateName: "Non-Refundable Rate", 
                    rateType: "NRF",
                    paymentType: "PREPAID",
                    cancellationPolicy: "NON_REFUNDABLE"
                ]
            ],
            totalCount: 2,
            status: "SUCCESS"
        ]

        when: "retrieving rate plans"
        def result = agodaService.getRatePlans(request)

        then: "should return rate plan data"
        result != null
        result.ratePlans.size() == 2
        result.totalCount == 2
        result.status == "SUCCESS"
        
        with(result.ratePlans[0]) {
            rateId == "RATE_001"
            rateName == "Best Available Rate"
            rateType == "BAR"
            paymentType == "PREPAID"
        }
        
        1 * mockAgodaClient.getRatePlans(_)
    }

    def "should handle concurrent requests safely"() {
        given: "multiple requests for concurrent processing"
        def requests = (1..20).collect { i ->
            [hotelId: "HOTEL_${i.toString().padLeft(3, '0')}", action: "getRooms"]
        }

        and: "mock client returns data for each request"
        mockAgodaClient.getRooms(_) >> { args ->
            def request = args[0]
            return [
                rooms: [[roomId: "ROOM_001", roomName: "Room for ${request.hotelId}"]],
                hotelId: request.hotelId,
                status: "SUCCESS"
            ]
        }

        when: "processing requests concurrently"
        def results = requests.parallelStream()
            .map { request -> agodaService.getRooms(request) }
            .collect()

        then: "should handle all requests successfully"
        results.size() == 20
        results.every { it != null && it.status == "SUCCESS" }
        results.every { it.rooms.size() == 1 }
        (20.._) * mockAgodaClient.getRooms(_)
    }

    @Unroll
    def "should validate request parameters: #scenario"() {
        given: "a request with specific parameters"
        def request = requestData

        when: "validating and processing request"
        def isValid = agodaService.validateRequest(request)
        def result = isValid ? agodaService.processRequest(request) : null

        then: "should validate correctly"
        isValid == expectedValid
        if (expectedValid) {
            result != null
        } else {
            result == null
        }

        where:
        scenario              | requestData                                    | expectedValid
        "valid request"       | [hotelId: "HOTEL_001", action: "getRooms"]   | true
        "missing hotelId"     | [action: "getRooms"]                         | false
        "missing action"      | [hotelId: "HOTEL_001"]                       | false
        "empty hotelId"       | [hotelId: "", action: "getRooms"]            | false
        "null hotelId"        | [hotelId: null, action: "getRooms"]          | false
        "invalid action"      | [hotelId: "HOTEL_001", action: "invalid"]    | false
        "extra parameters"    | [hotelId: "HOTEL_001", action: "getRooms", extra: "value"] | true
    }
}
