package com.derbysoft.next.propertyconnect.channel.task.domain.dto

import com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.RemoteChannelService
import com.fasterxml.jackson.databind.ObjectMapper
import spock.lang.Specification
import spock.lang.Shared
import spock.lang.Unroll

import java.time.LocalDateTime

/**
 * Phase 4 - Data Layer & DTO/Entity Tests
 * Comprehensive Spock tests for ChannelProductsDTO
 * Tests DTO validation, serialization/deserialization, equals/hashCode
 */
class ChannelProductsDTOSpec extends Specification {

    @Shared
    ObjectMapper objectMapper = new ObjectMapper()

    def "should create ChannelProductsDTO with all fields"() {
        given: "all required fields"
        def supplierId = "SUPPLIER_001"
        def hotelId = "HOTEL_001"
        def channelHotelId = "CH_HOTEL_001"
        def hotelName = "Test Hotel"
        def channelId = "CHANNEL_001"
        def retrieveDate = "2023-12-01"
        def operateDate = LocalDateTime.now()
        def extensions = [customField: "customValue"]

        when: "creating ChannelProductsDTO"
        def dto = new ChannelProductsDTO().tap {
            supplierId = supplierId
            hotelId = hotelId
            channelHotelId = channelHotelId
            hotelName = hotelName
            channelId = channelId
            retrieveDate = retrieveDate
            operateDate = operateDate
            extensions = extensions
        }

        then: "all fields should be set correctly"
        dto.supplierId == supplierId
        dto.hotelId == hotelId
        dto.channelHotelId == channelHotelId
        dto.hotelName == hotelName
        dto.channelId == channelId
        dto.retrieveDate == retrieveDate
        dto.operateDate == operateDate
        dto.extensions == extensions
    }

    def "should support method chaining with Lombok @Accessors"() {
        when: "using method chaining"
        def dto = new ChannelProductsDTO()
                .setSupplierId("SUPPLIER_001")
                .setHotelId("HOTEL_001")
                .setChannelId("CHANNEL_001")
                .setHotelName("Test Hotel")

        then: "all fields should be set"
        dto.supplierId == "SUPPLIER_001"
        dto.hotelId == "HOTEL_001"
        dto.channelId == "CHANNEL_001"
        dto.hotelName == "Test Hotel"
    }

    def "should handle null values gracefully"() {
        when: "creating DTO with null values"
        def dto = new ChannelProductsDTO().tap {
            supplierId = null
            hotelId = null
            channelProducts = null
            extensions = null
        }

        then: "should accept null values"
        dto.supplierId == null
        dto.hotelId == null
        dto.channelProducts == null
        dto.extensions == null
    }

    def "should serialize to JSON correctly"() {
        given: "a ChannelProductsDTO with data"
        def dto = new ChannelProductsDTO().tap {
            supplierId = "SUPPLIER_001"
            hotelId = "HOTEL_001"
            channelId = "CHANNEL_001"
            hotelName = "Test Hotel"
            channelProducts = [
                new ChannelProductsDTO.Product().tap {
                    roomName = "Deluxe Room"
                    rateName = "Standard Rate"
                    status = "ACTIVE"
                }
            ]
        }

        when: "serializing to JSON"
        def json = objectMapper.writeValueAsString(dto)

        then: "should produce valid JSON"
        json != null
        json.contains('"supplierId":"SUPPLIER_001"')
        json.contains('"hotelId":"HOTEL_001"')
        json.contains('"channelId":"CHANNEL_001"')
        json.contains('"hotelName":"Test Hotel"')
        json.contains('"roomName":"Deluxe Room"')
    }

    def "should deserialize from JSON correctly"() {
        given: "a JSON string"
        def json = '''
        {
            "supplierId": "SUPPLIER_001",
            "hotelId": "HOTEL_001",
            "channelId": "CHANNEL_001",
            "hotelName": "Test Hotel",
            "channelProducts": [
                {
                    "roomName": "Deluxe Room",
                    "rateName": "Standard Rate",
                    "status": "ACTIVE"
                }
            ]
        }
        '''

        when: "deserializing from JSON"
        def dto = objectMapper.readValue(json, ChannelProductsDTO.class)

        then: "should create correct object"
        dto != null
        dto.supplierId == "SUPPLIER_001"
        dto.hotelId == "HOTEL_001"
        dto.channelId == "CHANNEL_001"
        dto.hotelName == "Test Hotel"
        dto.channelProducts.size() == 1
        dto.channelProducts[0].roomName == "Deluxe Room"
        dto.channelProducts[0].rateName == "Standard Rate"
        dto.channelProducts[0].status == "ACTIVE"
    }

    def "should create Product with all fields"() {
        given: "all product fields"
        def supplierId = "SUPPLIER_001"
        def channelId = "CHANNEL_001"
        def roomName = "Deluxe Room"
        def rateName = "Standard Rate"
        def status = "ACTIVE"
        def mapped = true
        def availStatus = true
        def maxOccupancy = 2
        def predictionScore = 0.95d

        when: "creating Product"
        def product = new ChannelProductsDTO.Product().tap {
            supplierId = supplierId
            channelId = channelId
            roomName = roomName
            rateName = rateName
            status = status
            mapped = mapped
            availStatus = availStatus
            maxOccupancy = maxOccupancy
            predictionScore = predictionScore
        }

        then: "all fields should be set correctly"
        product.supplierId == supplierId
        product.channelId == channelId
        product.roomName == roomName
        product.rateName == rateName
        product.status == status
        product.mapped == mapped
        product.availStatus == availStatus
        product.maxOccupancy == maxOccupancy
        product.predictionScore == predictionScore
    }

    def "should implement equals and hashCode correctly for Product"() {
        given: "two identical products"
        def product1 = new ChannelProductsDTO.Product().tap {
            supplierId = "SUPPLIER_001"
            channelId = "CHANNEL_001"
            roomId = "ROOM_001"
            rateId = "RATE_001"
            roomName = "Deluxe Room"
            rateName = "Standard Rate"
        }

        def product2 = new ChannelProductsDTO.Product().tap {
            supplierId = "SUPPLIER_001"
            channelId = "CHANNEL_001"
            roomId = "ROOM_001"
            rateId = "RATE_001"
            roomName = "Deluxe Room"
            rateName = "Standard Rate"
        }

        and: "a different product"
        def product3 = new ChannelProductsDTO.Product().tap {
            supplierId = "SUPPLIER_002"
            channelId = "CHANNEL_001"
            roomId = "ROOM_001"
            rateId = "RATE_001"
            roomName = "Deluxe Room"
            rateName = "Standard Rate"
        }

        expect: "equals and hashCode should work correctly"
        product1.equals(product2)
        product1.hashCode() == product2.hashCode()
        !product1.equals(product3)
        product1.hashCode() != product3.hashCode()
    }

    @Unroll
    def "should validate Product code pattern: #scenario"() {
        given: "a product with specific room and rate IDs"
        def product = new ChannelProductsDTO.Product().tap {
            channelRoomId = roomId
            channelRateId = rateId
        }

        when: "getting code pattern and code"
        def codePattern = product.codePattern()
        def code = product.getCode()

        then: "should return correct pattern and code"
        codePattern == "RoomCode/RateCode"
        code == expectedCode

        where:
        scenario              | roomId      | rateId      | expectedCode
        "valid IDs"           | "ROOM_001"  | "RATE_001"  | "ROOM_001/RATE_001"
        "null room ID"        | null        | "RATE_001"  | "null/RATE_001"
        "null rate ID"        | "ROOM_001"  | null        | "ROOM_001/null"
        "both null"           | null        | null        | "null/null"
        "empty room ID"       | ""          | "RATE_001"  | "/RATE_001"
        "empty rate ID"       | "ROOM_001"  | ""          | "ROOM_001/"
    }

    @Unroll
    def "should validate Product name combination: #scenario"() {
        given: "a product with specific room and rate names"
        def product = new ChannelProductsDTO.Product().tap {
            channelRoomName = roomName
            channelRateName = rateName
        }

        when: "getting name"
        def name = product.getName()

        then: "should return correct name combination"
        name == expectedName

        where:
        scenario              | roomName      | rateName      | expectedName
        "valid names"         | "Deluxe Room" | "Standard Rate" | "Deluxe Room/Standard Rate"
        "null room name"      | null          | "Standard Rate" | "null/Standard Rate"
        "null rate name"      | "Deluxe Room" | null          | "Deluxe Room/null"
        "both null"           | null          | null          | "null/null"
        "empty room name"     | ""            | "Standard Rate" | "/Standard Rate"
        "empty rate name"     | "Deluxe Room" | ""            | "Deluxe Room/"
    }

    def "should handle nested candidate products"() {
        given: "a product with candidate products"
        def candidateProduct = new ChannelProductsDTO.Product().tap {
            supplierId = "CANDIDATE_SUPPLIER"
            roomName = "Candidate Room"
            rateName = "Candidate Rate"
            predictionScore = 0.75d
        }

        def mainProduct = new ChannelProductsDTO.Product().tap {
            supplierId = "MAIN_SUPPLIER"
            roomName = "Main Room"
            rateName = "Main Rate"
            candidateProducts = [candidateProduct]
        }

        when: "accessing nested data"
        def candidates = mainProduct.candidateProducts

        then: "should access nested candidate products"
        candidates != null
        candidates.size() == 1
        candidates[0].supplierId == "CANDIDATE_SUPPLIER"
        candidates[0].roomName == "Candidate Room"
        candidates[0].predictionScore == 0.75d
    }

    def "should handle RemoteChannelService.Operation enum"() {
        given: "a product with operation"
        def product = new ChannelProductsDTO.Product().tap {
            operation = RemoteChannelService.Operation.SaveProperty
        }

        expect: "operation should be set correctly"
        product.operation == RemoteChannelService.Operation.SaveProperty
        product.operation.name() == "SaveProperty"
    }

    def "should handle extensions map correctly"() {
        given: "a product with extensions"
        def extensions = [
            "customField1": "value1",
            "customField2": 123,
            "customField3": true,
            "nestedObject": [
                "nestedField": "nestedValue"
            ]
        ]

        def product = new ChannelProductsDTO.Product().tap {
            extensions = extensions
        }

        expect: "extensions should be handled correctly"
        product.extensions != null
        product.extensions.size() == 4
        product.extensions["customField1"] == "value1"
        product.extensions["customField2"] == 123
        product.extensions["customField3"] == true
        product.extensions["nestedObject"]["nestedField"] == "nestedValue"
    }

    def "should handle round-trip serialization for complex Product"() {
        given: "a complex product with all fields"
        def originalProduct = new ChannelProductsDTO.Product().tap {
            supplierId = "SUPPLIER_001"
            channelId = "CHANNEL_001"
            hotelId = "HOTEL_001"
            channelHotelId = "CH_HOTEL_001"
            roomId = "ROOM_001"
            channelRoomId = "CH_ROOM_001"
            roomName = "Deluxe Room"
            channelRoomName = "Channel Deluxe Room"
            rateId = "RATE_001"
            channelRateId = "CH_RATE_001"
            rateName = "Standard Rate"
            channelRateName = "Channel Standard Rate"
            status = "ACTIVE"
            mapped = true
            availStatus = true
            bedType = "KING"
            mealPlan = "BB"
            payType = "PREPAID"
            maxOccupancy = 2
            predictionScore = 0.88d
            operation = RemoteChannelService.Operation.SaveRoomTypes
            extensions = [customField: "customValue"]
        }

        when: "performing round-trip serialization"
        def json = objectMapper.writeValueAsString(originalProduct)
        def deserializedProduct = objectMapper.readValue(json, ChannelProductsDTO.Product.class)

        then: "all fields should be preserved"
        deserializedProduct.supplierId == originalProduct.supplierId
        deserializedProduct.channelId == originalProduct.channelId
        deserializedProduct.roomName == originalProduct.roomName
        deserializedProduct.rateName == originalProduct.rateName
        deserializedProduct.status == originalProduct.status
        deserializedProduct.mapped == originalProduct.mapped
        deserializedProduct.availStatus == originalProduct.availStatus
        deserializedProduct.bedType == originalProduct.bedType
        deserializedProduct.mealPlan == originalProduct.mealPlan
        deserializedProduct.payType == originalProduct.payType
        deserializedProduct.maxOccupancy == originalProduct.maxOccupancy
        deserializedProduct.predictionScore == originalProduct.predictionScore
        deserializedProduct.extensions["customField"] == "customValue"
    }
}
