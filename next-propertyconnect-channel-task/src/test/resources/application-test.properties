# Test configuration for next-propertyconnect-channel-task

# Spring Boot Test Configuration
# Note: spring.profiles.active should not be set in profile-specific files
spring.main.banner-mode=off
logging.level.org.springframework=WARN
logging.level.org.mongodb=WARN

# MongoDB Test Configuration - Use embedded MongoDB
spring.data.mongodb.database=test_channel_task
spring.data.mongodb.auto-index-creation=true
# Embedded MongoDB configuration
de.flapdoodle.embed.mongo.version=4.4.18

# Disable Eureka for tests
eureka.client.enabled=false
eureka.client.register-with-eureka=false
eureka.client.fetch-registry=false

# Disable Feign clients for unit tests
feign.client.config.default.connect-timeout=1000
feign.client.config.default.read-timeout=1000

# Test API URLs (mock endpoints)
app.pc.apigateway-url=http://localhost:8080/test/
app.apilayer.url=http://localhost:8080/test/
app.synchronizer.url=http://localhost:8080/test/
app.aimapping.url=http://localhost:8080/test/

# Test tokens
app.apilayer.token=test-token
app.openai.api-key=test-openai-key
app.openai.url=localhost:8080/test/openai

# Test channel configurations
app.bookingcom.base-url=http://localhost:8080/test/bookingcom/
app.expedia.base-url=http://localhost:8080/test/expedia/
app.agoda.base-url=http://localhost:8080/test/agoda/
app.ctrip.base-url=http://localhost:8080/test/ctrip/
app.airbnb.base-url=http://localhost:8080/test/airbnb/
app.fliggy.base-url=http://localhost:8080/test/fliggy/
app.hotelbeds.base-url=localhost:8080/test/hotelbeds
app.synxis.adapter-base-url=localhost:8080/test/synxis
app.synxis.base-url=localhost:8080/test/synxis

# Test hotel setup configuration
app.task.hotel-setup.block-ari-refresh=TEST_BLOCKED_CHANNEL
app.task.hotel-setup.alarm-emails=<EMAIL>

# Test ARI refresh configuration
app.task.ari-refresh.default.hotel-batch-size=10
app.task.ari-refresh.default.max-split-span=30
app.task.ari-refresh.default.days-before=1
app.task.ari-refresh.default.task-batch=5
app.task.ari-refresh.default.task-time-gap=1000
app.task.ari-refresh.default.delay-execution-time=5000

# Test mapping configuration
app.aimapping.default-threshold=0.8

# Disable actuator endpoints for tests
management.endpoints.enabled-by-default=false
management.endpoint.health.enabled=true

# Test logging configuration
app.log.enable=false
app.log.perf.app-name=test-pcchannel
app.log.stream.app-name=test-pcchannel

# Test cache configuration
spring.cache.type=simple

# Test Jackson configuration
spring.jackson.default-property-inclusion=NON_NULL
spring.jackson.serialization.WRITE_EMPTY_JSON_ARRAYS=false

# Test server configuration
server.port=0
server.servlet.context-path=/



# Test security configuration (if applicable)
spring.security.user.name=test
spring.security.user.password=test
spring.security.user.roles=USER

# Test async configuration
spring.task.execution.pool.core-size=2
spring.task.execution.pool.max-size=4
spring.task.execution.pool.queue-capacity=100

# Test validation configuration
spring.validation.enabled=true

# Test multipart configuration
spring.servlet.multipart.enabled=true
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB

# Test retry configuration
app.retry.max-attempts=3
app.retry.delay=1000

# Test timeout configuration
app.timeout.connect=5000
app.timeout.read=10000

# Test circuit breaker configuration (if using)
resilience4j.circuitbreaker.instances.default.failure-rate-threshold=50
resilience4j.circuitbreaker.instances.default.wait-duration-in-open-state=30000
resilience4j.circuitbreaker.instances.default.sliding-window-size=10

# Test rate limiting configuration (if using)
app.rate-limit.requests-per-minute=100

# Test S3 configuration (mock)
app.s3.bucket-name=test-bucket
app.s3.region=us-east-1
app.s3.access-key=test-access-key
app.s3.secret-key=test-secret-key

# Test email configuration (mock)
app.email.from=<EMAIL>
app.email.smtp.host=localhost
app.email.smtp.port=587

# Test webhook configuration
app.webhook.timeout=5000
app.webhook.retry-attempts=3
