package com.derbysoft.next.propertyconenct.channel.common.utils;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Comprehensive unit tests for DateTimeUtil
 * Tests date/time formatting and parsing functionality
 */
@ExtendWith(MockitoExtension.class)
class DateTimeUtilTest {

    @Test
    void testISO_LOCAL_DATE_TIME_WITH_MILLISECOND_FormatterExists() {
        // When
        DateTimeFormatter formatter = DateTimeUtil.ISO_LOCAL_DATE_TIME_WITH_MILLISECOND;

        // Then
        assertNotNull(formatter);
    }

    @Test
    void testISO_LOCAL_DATE_TIME_WITH_MILLISECOND_FormatPattern() {
        // Given
        LocalDateTime dateTime = LocalDateTime.of(2023, 12, 25, 10, 30, 45, 123000000);

        // When
        String formatted = dateTime.format(DateTimeUtil.ISO_LOCAL_DATE_TIME_WITH_MILLISECOND);

        // Then
        assertEquals("2023-12-25T10:30:45.123", formatted);
    }

    @Test
    void testISO_LOCAL_DATE_TIME_WITH_MILLISECOND_ParseFormatted() {
        // Given
        String dateTimeString = "2023-12-25T10:30:45.123";

        // When
        LocalDateTime parsed = LocalDateTime.parse(dateTimeString, DateTimeUtil.ISO_LOCAL_DATE_TIME_WITH_MILLISECOND);

        // Then
        assertEquals(2023, parsed.getYear());
        assertEquals(12, parsed.getMonthValue());
        assertEquals(25, parsed.getDayOfMonth());
        assertEquals(10, parsed.getHour());
        assertEquals(30, parsed.getMinute());
        assertEquals(45, parsed.getSecond());
        assertEquals(123000000, parsed.getNano());
    }

    @Test
    void testISO_LOCAL_DATE_TIME_WITH_MILLISECOND_RoundTripFormatting() {
        // Given
        LocalDateTime original = LocalDateTime.of(2023, 6, 15, 14, 25, 30, 456000000);

        // When
        String formatted = original.format(DateTimeUtil.ISO_LOCAL_DATE_TIME_WITH_MILLISECOND);
        LocalDateTime parsed = LocalDateTime.parse(formatted, DateTimeUtil.ISO_LOCAL_DATE_TIME_WITH_MILLISECOND);

        // Then
        assertEquals(original, parsed);
    }

    @Test
    void testISO_LOCAL_DATE_TIME_WITH_MILLISECOND_WithZeroMilliseconds() {
        // Given
        LocalDateTime dateTime = LocalDateTime.of(2023, 1, 1, 0, 0, 0, 0);

        // When
        String formatted = dateTime.format(DateTimeUtil.ISO_LOCAL_DATE_TIME_WITH_MILLISECOND);

        // Then
        assertEquals("2023-01-01T00:00:00.000", formatted);
    }

    @Test
    void testISO_LOCAL_DATE_TIME_WITH_MILLISECOND_WithMaxMilliseconds() {
        // Given
        LocalDateTime dateTime = LocalDateTime.of(2023, 1, 1, 0, 0, 0, 999000000);

        // When
        String formatted = dateTime.format(DateTimeUtil.ISO_LOCAL_DATE_TIME_WITH_MILLISECOND);

        // Then
        assertEquals("2023-01-01T00:00:00.999", formatted);
    }

    @Test
    void testISO_LOCAL_DATE_TIME_WITH_MILLISECOND_WithLeapYear() {
        // Given
        LocalDateTime dateTime = LocalDateTime.of(2024, 2, 29, 12, 0, 0, 500000000);

        // When
        String formatted = dateTime.format(DateTimeUtil.ISO_LOCAL_DATE_TIME_WITH_MILLISECOND);

        // Then
        assertEquals("2024-02-29T12:00:00.500", formatted);
    }

    @Test
    void testISO_LOCAL_DATE_TIME_WITH_MILLISECOND_WithEndOfYear() {
        // Given
        LocalDateTime dateTime = LocalDateTime.of(2023, 12, 31, 23, 59, 59, 999000000);

        // When
        String formatted = dateTime.format(DateTimeUtil.ISO_LOCAL_DATE_TIME_WITH_MILLISECOND);

        // Then
        assertEquals("2023-12-31T23:59:59.999", formatted);
    }

    @Test
    void testISO_LOCAL_DATE_TIME_WITH_MILLISECOND_WithStartOfYear() {
        // Given
        LocalDateTime dateTime = LocalDateTime.of(2023, 1, 1, 0, 0, 0, 1000000);

        // When
        String formatted = dateTime.format(DateTimeUtil.ISO_LOCAL_DATE_TIME_WITH_MILLISECOND);

        // Then
        assertEquals("2023-01-01T00:00:00.001", formatted);
    }

    @Test
    void testISO_LOCAL_DATE_TIME_WITH_MILLISECOND_ParseInvalidFormat_ThrowsException() {
        // Given
        String invalidFormat = "2023-12-25 10:30:45.123";

        // When & Then
        assertThrows(DateTimeParseException.class, () ->
            LocalDateTime.parse(invalidFormat, DateTimeUtil.ISO_LOCAL_DATE_TIME_WITH_MILLISECOND));
    }

    @Test
    void testISO_LOCAL_DATE_TIME_WITH_MILLISECOND_ParseMissingMilliseconds_ThrowsException() {
        // Given
        String missingMilliseconds = "2023-12-25T10:30:45";

        // When & Then
        assertThrows(DateTimeParseException.class, () ->
            LocalDateTime.parse(missingMilliseconds, DateTimeUtil.ISO_LOCAL_DATE_TIME_WITH_MILLISECOND));
    }

    @Test
    void testISO_LOCAL_DATE_TIME_WITH_MILLISECOND_ParseExtraDigits_ThrowsException() {
        // Given
        String extraDigits = "2023-12-25T10:30:45.1234";

        // When & Then
        assertThrows(DateTimeParseException.class, () ->
            LocalDateTime.parse(extraDigits, DateTimeUtil.ISO_LOCAL_DATE_TIME_WITH_MILLISECOND));
    }

    @Test
    void testISO_LOCAL_DATE_TIME_WITH_MILLISECOND_ParseInvalidDate_ThrowsException() {
        // Given
        String invalidDate = "2023-13-25T10:30:45.123";

        // When & Then
        assertThrows(DateTimeParseException.class, () ->
            LocalDateTime.parse(invalidDate, DateTimeUtil.ISO_LOCAL_DATE_TIME_WITH_MILLISECOND));
    }

    @Test
    void testISO_LOCAL_DATE_TIME_WITH_MILLISECOND_ParseInvalidTime_ThrowsException() {
        // Given
        String invalidTime = "2023-12-25T25:30:45.123";

        // When & Then
        assertThrows(DateTimeParseException.class, () ->
            LocalDateTime.parse(invalidTime, DateTimeUtil.ISO_LOCAL_DATE_TIME_WITH_MILLISECOND));
    }

    @Test
    void testISO_LOCAL_DATE_TIME_WITH_MILLISECOND_WithDifferentYears() {
        // Test various years
        int[] years = {1999, 2000, 2001, 2023, 2024, 2100};
        
        for (int year : years) {
            // Given
            LocalDateTime dateTime = LocalDateTime.of(year, 6, 15, 12, 30, 45, 123000000);

            // When
            String formatted = dateTime.format(DateTimeUtil.ISO_LOCAL_DATE_TIME_WITH_MILLISECOND);
            LocalDateTime parsed = LocalDateTime.parse(formatted, DateTimeUtil.ISO_LOCAL_DATE_TIME_WITH_MILLISECOND);

            // Then
            assertEquals(dateTime, parsed);
            assertTrue(formatted.startsWith(String.valueOf(year)));
        }
    }

    @Test
    void testISO_LOCAL_DATE_TIME_WITH_MILLISECOND_WithDifferentMonths() {
        // Test all months
        for (int month = 1; month <= 12; month++) {
            // Given
            LocalDateTime dateTime = LocalDateTime.of(2023, month, 15, 12, 30, 45, 123000000);

            // When
            String formatted = dateTime.format(DateTimeUtil.ISO_LOCAL_DATE_TIME_WITH_MILLISECOND);
            LocalDateTime parsed = LocalDateTime.parse(formatted, DateTimeUtil.ISO_LOCAL_DATE_TIME_WITH_MILLISECOND);

            // Then
            assertEquals(dateTime, parsed);
            assertEquals(month, parsed.getMonthValue());
        }
    }

    @Test
    void testISO_LOCAL_DATE_TIME_WITH_MILLISECOND_WithDifferentDays() {
        // Test various days
        int[] days = {1, 15, 28, 30, 31};
        
        for (int day : days) {
            // Given
            LocalDateTime dateTime = LocalDateTime.of(2023, 1, day, 12, 30, 45, 123000000);

            // When
            String formatted = dateTime.format(DateTimeUtil.ISO_LOCAL_DATE_TIME_WITH_MILLISECOND);
            LocalDateTime parsed = LocalDateTime.parse(formatted, DateTimeUtil.ISO_LOCAL_DATE_TIME_WITH_MILLISECOND);

            // Then
            assertEquals(dateTime, parsed);
            assertEquals(day, parsed.getDayOfMonth());
        }
    }

    @Test
    void testISO_LOCAL_DATE_TIME_WITH_MILLISECOND_WithDifferentHours() {
        // Test various hours
        int[] hours = {0, 1, 12, 23};
        
        for (int hour : hours) {
            // Given
            LocalDateTime dateTime = LocalDateTime.of(2023, 6, 15, hour, 30, 45, 123000000);

            // When
            String formatted = dateTime.format(DateTimeUtil.ISO_LOCAL_DATE_TIME_WITH_MILLISECOND);
            LocalDateTime parsed = LocalDateTime.parse(formatted, DateTimeUtil.ISO_LOCAL_DATE_TIME_WITH_MILLISECOND);

            // Then
            assertEquals(dateTime, parsed);
            assertEquals(hour, parsed.getHour());
        }
    }

    @Test
    void testISO_LOCAL_DATE_TIME_WITH_MILLISECOND_WithDifferentMinutes() {
        // Test various minutes
        int[] minutes = {0, 1, 30, 59};
        
        for (int minute : minutes) {
            // Given
            LocalDateTime dateTime = LocalDateTime.of(2023, 6, 15, 12, minute, 45, 123000000);

            // When
            String formatted = dateTime.format(DateTimeUtil.ISO_LOCAL_DATE_TIME_WITH_MILLISECOND);
            LocalDateTime parsed = LocalDateTime.parse(formatted, DateTimeUtil.ISO_LOCAL_DATE_TIME_WITH_MILLISECOND);

            // Then
            assertEquals(dateTime, parsed);
            assertEquals(minute, parsed.getMinute());
        }
    }

    @Test
    void testISO_LOCAL_DATE_TIME_WITH_MILLISECOND_WithDifferentSeconds() {
        // Test various seconds
        int[] seconds = {0, 1, 30, 59};
        
        for (int second : seconds) {
            // Given
            LocalDateTime dateTime = LocalDateTime.of(2023, 6, 15, 12, 30, second, 123000000);

            // When
            String formatted = dateTime.format(DateTimeUtil.ISO_LOCAL_DATE_TIME_WITH_MILLISECOND);
            LocalDateTime parsed = LocalDateTime.parse(formatted, DateTimeUtil.ISO_LOCAL_DATE_TIME_WITH_MILLISECOND);

            // Then
            assertEquals(dateTime, parsed);
            assertEquals(second, parsed.getSecond());
        }
    }

    @Test
    void testISO_LOCAL_DATE_TIME_WITH_MILLISECOND_WithDifferentMilliseconds() {
        // Test various milliseconds
        int[] milliseconds = {0, 1, 123, 500, 999};
        
        for (int ms : milliseconds) {
            // Given
            LocalDateTime dateTime = LocalDateTime.of(2023, 6, 15, 12, 30, 45, ms * 1000000);

            // When
            String formatted = dateTime.format(DateTimeUtil.ISO_LOCAL_DATE_TIME_WITH_MILLISECOND);
            LocalDateTime parsed = LocalDateTime.parse(formatted, DateTimeUtil.ISO_LOCAL_DATE_TIME_WITH_MILLISECOND);

            // Then
            assertEquals(dateTime, parsed);
            assertEquals(ms * 1000000, parsed.getNano());
        }
    }

    @Test
    void testISO_LOCAL_DATE_TIME_WITH_MILLISECOND_FormatterIsImmutable() {
        // Given
        DateTimeFormatter formatter1 = DateTimeUtil.ISO_LOCAL_DATE_TIME_WITH_MILLISECOND;
        DateTimeFormatter formatter2 = DateTimeUtil.ISO_LOCAL_DATE_TIME_WITH_MILLISECOND;

        // When & Then
        assertSame(formatter1, formatter2);
    }

    @Test
    void testISO_LOCAL_DATE_TIME_WITH_MILLISECOND_ThreadSafety() {
        // Given
        LocalDateTime dateTime = LocalDateTime.of(2023, 6, 15, 12, 30, 45, 123000000);
        
        // When
        Thread[] threads = new Thread[10];
        String[] results = new String[10];
        Exception[] exceptions = new Exception[10];

        for (int i = 0; i < 10; i++) {
            final int index = i;
            threads[i] = new Thread(() -> {
                try {
                    results[index] = dateTime.format(DateTimeUtil.ISO_LOCAL_DATE_TIME_WITH_MILLISECOND);
                } catch (Exception e) {
                    exceptions[index] = e;
                }
            });
            threads[i].start();
        }

        // Wait for all threads to complete
        for (Thread thread : threads) {
            try {
                thread.join();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }

        // Then
        for (int i = 0; i < 10; i++) {
            assertNull(exceptions[i], "Thread " + i + " should not have thrown an exception");
            assertNotNull(results[i], "Thread " + i + " should have returned a result");
            assertEquals("2023-06-15T12:30:45.123", results[i]);
        }
    }

    @Test
    void testDateTimeUtil_IsUtilityClass() {
        // When & Then
        // Verify that DateTimeUtil is a utility class (cannot be instantiated)
        // This is enforced by Lombok's @UtilityClass annotation
        assertTrue(DateTimeUtil.class.getModifiers() != 0);
    }
}
