package com.derbysoft.next.propertyconenct.channel.common.mapstruct;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.time.format.DateTimeParseException;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Comprehensive unit tests for DateTimeMapper
 * Tests date/time mapping functionality between LocalDateTime and String
 */
@ExtendWith(MockitoExtension.class)
class DateTimeMapperTest {

    private TestDateTimeMapper dateTimeMapper;

    @BeforeEach
    void setUp() {
        dateTimeMapper = new TestDateTimeMapper();
    }

    @Test
    void testDateTimeToString_WithValidDateTime_ReturnsFormattedString() {
        // Given
        LocalDateTime dateTime = LocalDateTime.of(2023, 12, 25, 10, 30, 45, 123000000);

        // When
        String result = dateTimeMapper.dateTimeToString(dateTime);

        // Then
        assertEquals("2023-12-25T10:30:45.123", result);
    }

    @Test
    void testDateTimeToString_WithNullDateTime_ReturnsNull() {
        // Given
        LocalDateTime dateTime = null;

        // When
        String result = dateTimeMapper.dateTimeToString(dateTime);

        // Then
        assertNull(result);
    }

    @Test
    void testDateTimeToString_WithZeroMilliseconds_ReturnsCorrectFormat() {
        // Given
        LocalDateTime dateTime = LocalDateTime.of(2023, 1, 1, 0, 0, 0, 0);

        // When
        String result = dateTimeMapper.dateTimeToString(dateTime);

        // Then
        assertEquals("2023-01-01T00:00:00.000", result);
    }

    @Test
    void testDateTimeToString_WithMaxMilliseconds_ReturnsCorrectFormat() {
        // Given
        LocalDateTime dateTime = LocalDateTime.of(2023, 1, 1, 0, 0, 0, 999000000);

        // When
        String result = dateTimeMapper.dateTimeToString(dateTime);

        // Then
        assertEquals("2023-01-01T00:00:00.999", result);
    }

    @Test
    void testDateTimeToString_WithLeapYear_ReturnsCorrectFormat() {
        // Given
        LocalDateTime dateTime = LocalDateTime.of(2024, 2, 29, 12, 0, 0, 500000000);

        // When
        String result = dateTimeMapper.dateTimeToString(dateTime);

        // Then
        assertEquals("2024-02-29T12:00:00.500", result);
    }

    @Test
    void testStringToDateTime_WithValidString_ReturnsLocalDateTime() {
        // Given
        String dateTimeString = "2023-12-25T10:30:45.123";

        // When
        LocalDateTime result = dateTimeMapper.stringToDateTime(dateTimeString);

        // Then
        assertNotNull(result);
        assertEquals(2023, result.getYear());
        assertEquals(12, result.getMonthValue());
        assertEquals(25, result.getDayOfMonth());
        assertEquals(10, result.getHour());
        assertEquals(30, result.getMinute());
        assertEquals(45, result.getSecond());
        assertEquals(123000000, result.getNano());
    }

    @Test
    void testStringToDateTime_WithNullString_ReturnsNull() {
        // Given
        String dateTimeString = null;

        // When
        LocalDateTime result = dateTimeMapper.stringToDateTime(dateTimeString);

        // Then
        assertNull(result);
    }

    @Test
    void testStringToDateTime_WithZeroMilliseconds_ReturnsCorrectDateTime() {
        // Given
        String dateTimeString = "2023-01-01T00:00:00.000";

        // When
        LocalDateTime result = dateTimeMapper.stringToDateTime(dateTimeString);

        // Then
        assertNotNull(result);
        assertEquals(LocalDateTime.of(2023, 1, 1, 0, 0, 0, 0), result);
    }

    @Test
    void testStringToDateTime_WithMaxMilliseconds_ReturnsCorrectDateTime() {
        // Given
        String dateTimeString = "2023-01-01T00:00:00.999";

        // When
        LocalDateTime result = dateTimeMapper.stringToDateTime(dateTimeString);

        // Then
        assertNotNull(result);
        assertEquals(LocalDateTime.of(2023, 1, 1, 0, 0, 0, 999000000), result);
    }

    @Test
    void testStringToDateTime_WithLeapYear_ReturnsCorrectDateTime() {
        // Given
        String dateTimeString = "2024-02-29T12:00:00.500";

        // When
        LocalDateTime result = dateTimeMapper.stringToDateTime(dateTimeString);

        // Then
        assertNotNull(result);
        assertEquals(LocalDateTime.of(2024, 2, 29, 12, 0, 0, 500000000), result);
    }

    @Test
    void testStringToDateTime_WithInvalidFormat_ThrowsException() {
        // Given
        String invalidFormat = "2023-12-25 10:30:45.123";

        // When & Then
        assertThrows(DateTimeParseException.class, () ->
            dateTimeMapper.stringToDateTime(invalidFormat));
    }

    @Test
    void testStringToDateTime_WithMissingMilliseconds_ThrowsException() {
        // Given
        String missingMilliseconds = "2023-12-25T10:30:45";

        // When & Then
        assertThrows(DateTimeParseException.class, () ->
            dateTimeMapper.stringToDateTime(missingMilliseconds));
    }

    @Test
    void testStringToDateTime_WithExtraDigits_ThrowsException() {
        // Given
        String extraDigits = "2023-12-25T10:30:45.1234";

        // When & Then
        assertThrows(DateTimeParseException.class, () ->
            dateTimeMapper.stringToDateTime(extraDigits));
    }

    @Test
    void testStringToDateTime_WithInvalidDate_ThrowsException() {
        // Given
        String invalidDate = "2023-13-25T10:30:45.123";

        // When & Then
        assertThrows(DateTimeParseException.class, () ->
            dateTimeMapper.stringToDateTime(invalidDate));
    }

    @Test
    void testStringToDateTime_WithInvalidTime_ThrowsException() {
        // Given
        String invalidTime = "2023-12-25T25:30:45.123";

        // When & Then
        assertThrows(DateTimeParseException.class, () ->
            dateTimeMapper.stringToDateTime(invalidTime));
    }

    @Test
    void testRoundTripConversion_WithValidDateTime_ReturnsOriginal() {
        // Given
        LocalDateTime original = LocalDateTime.of(2023, 6, 15, 14, 25, 30, 456000000);

        // When
        String stringified = dateTimeMapper.dateTimeToString(original);
        LocalDateTime converted = dateTimeMapper.stringToDateTime(stringified);

        // Then
        assertEquals(original, converted);
    }

    @Test
    void testRoundTripConversion_WithValidString_ReturnsOriginal() {
        // Given
        String original = "2023-06-15T14:25:30.456";

        // When
        LocalDateTime dateTime = dateTimeMapper.stringToDateTime(original);
        String converted = dateTimeMapper.dateTimeToString(dateTime);

        // Then
        assertEquals(original, converted);
    }

    @Test
    void testRoundTripConversion_WithMultipleValues_AllSucceed() {
        // Given
        LocalDateTime[] dateTimes = {
            LocalDateTime.of(2023, 1, 1, 0, 0, 0, 0),
            LocalDateTime.of(2023, 6, 15, 12, 30, 45, 123000000),
            LocalDateTime.of(2023, 12, 31, 23, 59, 59, 999000000),
            LocalDateTime.of(2024, 2, 29, 6, 15, 30, 500000000)
        };

        for (LocalDateTime original : dateTimes) {
            // When
            String stringified = dateTimeMapper.dateTimeToString(original);
            LocalDateTime converted = dateTimeMapper.stringToDateTime(stringified);

            // Then
            assertEquals(original, converted);
        }
    }

    @Test
    void testDateTimeToString_WithDifferentYears_AllFormatCorrectly() {
        // Given
        int[] years = {1999, 2000, 2001, 2023, 2024, 2100};

        for (int year : years) {
            LocalDateTime dateTime = LocalDateTime.of(year, 6, 15, 12, 30, 45, 123000000);

            // When
            String result = dateTimeMapper.dateTimeToString(dateTime);

            // Then
            assertTrue(result.startsWith(String.valueOf(year)));
            assertEquals(23, result.length()); // yyyy-MM-ddTHH:mm:ss.SSS
        }
    }

    @Test
    void testDateTimeToString_WithDifferentMonths_AllFormatCorrectly() {
        // Given
        for (int month = 1; month <= 12; month++) {
            LocalDateTime dateTime = LocalDateTime.of(2023, month, 15, 12, 30, 45, 123000000);

            // When
            String result = dateTimeMapper.dateTimeToString(dateTime);

            // Then
            String expectedMonth = String.format("%02d", month);
            assertTrue(result.contains("-" + expectedMonth + "-"));
        }
    }

    @Test
    void testStringToDateTime_WithDifferentFormats_OnlyValidOnesSucceed() {
        // Given
        String[] validFormats = {
            "2023-01-01T00:00:00.000",
            "2023-12-31T23:59:59.999",
            "2024-02-29T12:30:45.500"
        };

        String[] invalidFormats = {
            "2023-01-01 00:00:00.000",
            "2023/01/01T00:00:00.000",
            "2023-01-01T00:00:00",
            "2023-01-01T00:00:00.0000"
        };

        // When & Then
        for (String valid : validFormats) {
            assertDoesNotThrow(() -> dateTimeMapper.stringToDateTime(valid));
        }

        for (String invalid : invalidFormats) {
            assertThrows(DateTimeParseException.class, () -> dateTimeMapper.stringToDateTime(invalid));
        }
    }

    @Test
    void testDateTimeToString_WithEdgeCases_HandlesCorrectly() {
        // Given
        LocalDateTime[] edgeCases = {
            LocalDateTime.of(2000, 1, 1, 0, 0, 0, 1000000), // 1 millisecond
            LocalDateTime.of(2000, 12, 31, 23, 59, 59, 998000000), // 998 milliseconds
            LocalDateTime.of(1999, 1, 1, 0, 0, 0, 0), // Year 1999
            LocalDateTime.of(2100, 12, 31, 23, 59, 59, 999000000) // Year 2100
        };

        for (LocalDateTime dateTime : edgeCases) {
            // When
            String result = dateTimeMapper.dateTimeToString(dateTime);

            // Then
            assertNotNull(result);
            assertEquals(23, result.length());
            assertTrue(result.matches("\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}\\.\\d{3}"));
        }
    }

    @Test
    void testStringToDateTime_WithEdgeCases_HandlesCorrectly() {
        // Given
        String[] edgeCases = {
            "0001-01-01T00:00:00.000",
            "9999-12-31T23:59:59.999",
            "2000-01-01T00:00:00.001",
            "2000-12-31T23:59:59.998"
        };

        for (String dateTimeString : edgeCases) {
            // When
            LocalDateTime result = dateTimeMapper.stringToDateTime(dateTimeString);

            // Then
            assertNotNull(result);
            assertTrue(result.getNano() % 1000000 == 0); // Should be millisecond precision
        }
    }

    @Test
    void testDateTimeMapper_ThreadSafety_HandlesCorrectly() {
        // Given
        LocalDateTime dateTime = LocalDateTime.of(2023, 6, 15, 12, 30, 45, 123000000);
        
        // When
        Thread[] threads = new Thread[10];
        String[] results = new String[10];
        Exception[] exceptions = new Exception[10];

        for (int i = 0; i < 10; i++) {
            final int index = i;
            threads[i] = new Thread(() -> {
                try {
                    results[index] = dateTimeMapper.dateTimeToString(dateTime);
                } catch (Exception e) {
                    exceptions[index] = e;
                }
            });
            threads[i].start();
        }

        // Wait for all threads to complete
        for (Thread thread : threads) {
            try {
                thread.join();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }

        // Then
        for (int i = 0; i < 10; i++) {
            assertNull(exceptions[i], "Thread " + i + " should not have thrown an exception");
            assertNotNull(results[i], "Thread " + i + " should have returned a result");
            assertEquals("2023-06-15T12:30:45.123", results[i]);
        }
    }

    @Test
    void testDateTimeMapper_IsInterface_CanBeImplemented() {
        // When & Then
        assertTrue(DateTimeMapper.class.isInterface());
        assertTrue(dateTimeMapper instanceof DateTimeMapper);
    }

    @Test
    void testDateTimeMapper_DefaultMethods_AreAccessible() {
        // When & Then
        assertDoesNotThrow(() -> {
            dateTimeMapper.dateTimeToString(LocalDateTime.now());
            dateTimeMapper.stringToDateTime("2023-01-01T00:00:00.000");
        });
    }

    /**
     * Test implementation of DateTimeMapper for testing purposes
     */
    private static class TestDateTimeMapper implements DateTimeMapper {
        // Uses default interface methods
    }
}
