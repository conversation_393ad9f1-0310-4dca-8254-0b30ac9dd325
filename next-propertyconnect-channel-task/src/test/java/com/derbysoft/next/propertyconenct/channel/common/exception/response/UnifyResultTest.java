package com.derbysoft.next.propertyconenct.channel.common.exception.response;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;
import java.util.function.Supplier;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Comprehensive unit tests for UnifyResult
 * Tests response wrapper functionality with complex JSON handling
 */
@ExtendWith(MockitoExtension.class)
class UnifyResultTest {

    @Test
    void testConstructorWithHeaderAndPayload() {
        // Given
        Header header = new Header();
        String payload = "test payload";
        
        // When
        UnifyResult<String> result = new UnifyResult<>(header, payload);
        
        // Then
        assertEquals(header, result.getHeader());
        assertEquals(payload, result.object);
        assertNull(result.list);
    }

    @Test
    void testConstructorWithHeaderAndCollectionPayload() {
        // Given
        Header header = new Header();
        List<String> payload = Arrays.asList("item1", "item2", "item3");
        
        // When
        UnifyResult<List<String>> result = new UnifyResult<>(header, payload);
        
        // Then
        assertEquals(header, result.getHeader());
        assertNull(result.object);
        assertEquals(payload, result.list);
        assertEquals(payload, result.getList());
    }

    @Test
    void testConstructorWithHeaderAndNullPayload() {
        // Given
        Header header = new Header();
        
        // When
        UnifyResult<String> result = new UnifyResult<>(header, null);
        
        // Then
        assertEquals(header, result.getHeader());
        assertNull(result.object);
        assertNull(result.list);
    }

    @Test
    void testConstructorWithHeaderAndMapPayload() {
        // Given
        Header header = new Header();
        Map<String, String> payload = new HashMap<>();
        payload.put("key1", "value1");
        payload.put("key2", "value2");
        
        // When
        UnifyResult<Map<String, String>> result = new UnifyResult<>(header, payload);
        
        // Then
        assertEquals(header, result.getHeader());
        assertEquals(payload, result.object);
        assertNull(result.list);
        assertEquals(payload, result.getMap());
    }

    @Test
    void testGetMapWithNonMapObject_ReturnsNull() {
        // Given
        Header header = new Header();
        String payload = "not a map";
        
        // When
        UnifyResult<String> result = new UnifyResult<>(header, payload);
        
        // Then
        assertNull(result.getMap());
    }

    @Test
    void testFromWithHeaderAndSupplier() {
        // Given
        Header header = new Header();
        Supplier<String> supplier = () -> "supplied value";
        
        // When
        UnifyResult<String> result = UnifyResult.from(header, supplier);
        
        // Then
        assertEquals(header, result.getHeader());
        assertEquals("supplied value", result.object);
    }

    @Test
    void testFromWithSupplierOnly() {
        // Given
        Supplier<Integer> supplier = () -> 42;
        
        // When
        UnifyResult<Integer> result = UnifyResult.from(supplier);
        
        // Then
        assertNotNull(result.getHeader());
        assertEquals(42, result.object);
    }

    @Test
    void testFromWithRunnable() {
        // Given
        final List<String> sideEffect = new ArrayList<>();
        Runnable runnable = () -> sideEffect.add("executed");
        
        // When
        UnifyResult<Void> result = UnifyResult.from(runnable);
        
        // Then
        assertNotNull(result.getHeader());
        assertNull(result.object);
        assertEquals(1, sideEffect.size());
        assertEquals("executed", sideEffect.get(0));
    }

    @Test
    void testToUnifyResult() {
        // Given
        String data = "test data";
        
        // When
        UnifyResult<String> result = UnifyResult.toUnifyResult(data);
        
        // Then
        assertNotNull(result.getHeader());
        assertEquals(data, result.object);
    }

    @Test
    void testToUnifyResultWithNull() {
        // Given & When
        UnifyResult<Object> result = UnifyResult.toUnifyResult(null);
        
        // Then
        assertNotNull(result.getHeader());
        assertNull(result.object);
    }

    @Test
    void testToUnifyResultWithCollection() {
        // Given
        List<String> data = Arrays.asList("a", "b", "c");
        
        // When
        UnifyResult<List<String>> result = UnifyResult.toUnifyResult(data);
        
        // Then
        assertNotNull(result.getHeader());
        assertNull(result.object);
        assertEquals(data, result.list);
    }

    @Test
    void testSetHeader() {
        // Given
        Header originalHeader = new Header();
        Header newHeader = new Header();
        UnifyResult<String> result = new UnifyResult<>(originalHeader, "test");
        
        // When
        result.setHeader(newHeader);
        
        // Then
        assertEquals(newHeader, result.getHeader());
    }

    @Test
    void testWithComplexMap() {
        // Given
        Header header = new Header();
        Map<String, Object> complexMap = new HashMap<>();
        complexMap.put("string", "value");
        complexMap.put("number", 123);
        complexMap.put("boolean", true);
        complexMap.put("nested", Map.of("inner", "value"));
        
        // When
        UnifyResult<Map<String, Object>> result = new UnifyResult<>(header, complexMap);
        
        // Then
        assertEquals(header, result.getHeader());
        assertEquals(complexMap, result.object);
        assertEquals(complexMap, result.getMap());
        assertNull(result.list);
    }

    @Test
    void testWithCustomObjects() {
        // Given
        TestData testData = new TestData("test", 123);
        Header header = new Header();
        
        // When
        UnifyResult<TestData> result = new UnifyResult<>(header, testData);
        
        // Then
        assertEquals(header, result.getHeader());
        assertEquals(testData, result.object);
        assertNull(result.getMap()); // Should be null since TestData is not a Map
    }

    @Test
    void testWithEmptyCollection() {
        // Given
        List<String> emptyList = new ArrayList<>();
        Header header = new Header();
        
        // When
        UnifyResult<List<String>> result = new UnifyResult<>(header, emptyList);
        
        // Then
        assertEquals(header, result.getHeader());
        assertNull(result.object);
        assertEquals(emptyList, result.list);
        assertTrue(result.getList().isEmpty());
    }

    @Test
    void testWithSet() {
        // Given
        Set<String> set = new HashSet<>(Arrays.asList("a", "b", "c"));
        Header header = new Header();
        
        // When
        UnifyResult<Set<String>> result = new UnifyResult<>(header, set);
        
        // Then
        assertEquals(header, result.getHeader());
        assertNull(result.object);
        assertEquals(set, result.list);
        assertEquals(3, result.getList().size());
    }

    @Test
    void testSupplierExceptionHandling() {
        // Given
        Supplier<String> faultySupplier = () -> {
            throw new RuntimeException("Supplier failed");
        };
        
        // When & Then
        assertThrows(RuntimeException.class, () -> {
            UnifyResult.from(faultySupplier);
        });
    }

    @Test
    void testRunnableExceptionHandling() {
        // Given
        Runnable faultyRunnable = () -> {
            throw new RuntimeException("Runnable failed");
        };
        
        // When & Then
        assertThrows(RuntimeException.class, () -> {
            UnifyResult.from(faultyRunnable);
        });
    }

    @Test
    void testWithNestedCollections() {
        // Given
        List<List<String>> nestedList = Arrays.asList(
            Arrays.asList("a1", "a2"),
            Arrays.asList("b1", "b2"),
            Arrays.asList("c1", "c2")
        );
        Header header = new Header();
        
        // When
        UnifyResult<List<List<String>>> result = new UnifyResult<>(header, nestedList);
        
        // Then
        assertEquals(header, result.getHeader());
        assertNull(result.object);
        assertEquals(nestedList, result.list);
        assertEquals(3, result.getList().size());
    }

    @Test
    void testWithMapOfCollections() {
        // Given
        Map<String, List<String>> mapOfLists = new HashMap<>();
        mapOfLists.put("list1", Arrays.asList("a", "b"));
        mapOfLists.put("list2", Arrays.asList("c", "d"));
        Header header = new Header();
        
        // When
        UnifyResult<Map<String, List<String>>> result = new UnifyResult<>(header, mapOfLists);
        
        // Then
        assertEquals(header, result.getHeader());
        assertEquals(mapOfLists, result.object);
        assertEquals(mapOfLists, result.getMap());
        assertNull(result.list);
    }

    @Test
    void testMapTypeFilter() {
        // Given
        UnifyResult.MapTypeFilter filter = new UnifyResult.MapTypeFilter();
        Map<String, String> map = new HashMap<>();
        String notMap = "not a map";
        
        // When & Then
        assertTrue(filter.equals(map));
        assertFalse(filter.equals(notMap));
        assertFalse(filter.equals(null));
    }

    @Test
    void testWithDifferentMapTypes() {
        // Given
        Header header = new Header();
        
        // Test with HashMap
        HashMap<String, String> hashMap = new HashMap<>();
        hashMap.put("key", "value");
        UnifyResult<HashMap<String, String>> hashMapResult = new UnifyResult<>(header, hashMap);
        
        // Test with LinkedHashMap
        LinkedHashMap<String, String> linkedHashMap = new LinkedHashMap<>();
        linkedHashMap.put("key", "value");
        UnifyResult<LinkedHashMap<String, String>> linkedHashMapResult = new UnifyResult<>(header, linkedHashMap);
        
        // Test with TreeMap
        TreeMap<String, String> treeMap = new TreeMap<>();
        treeMap.put("key", "value");
        UnifyResult<TreeMap<String, String>> treeMapResult = new UnifyResult<>(header, treeMap);
        
        // When & Then
        assertEquals(hashMap, hashMapResult.getMap());
        assertEquals(linkedHashMap, linkedHashMapResult.getMap());
        assertEquals(treeMap, treeMapResult.getMap());
    }

    @Test
    void testWithImmutableCollections() {
        // Given
        Header header = new Header();
        List<String> immutableList = List.of("a", "b", "c");
        Set<String> immutableSet = Set.of("x", "y", "z");
        Map<String, String> immutableMap = Map.of("key", "value");
        
        // When
        UnifyResult<List<String>> listResult = new UnifyResult<>(header, immutableList);
        UnifyResult<Set<String>> setResult = new UnifyResult<>(header, immutableSet);
        UnifyResult<Map<String, String>> mapResult = new UnifyResult<>(header, immutableMap);
        
        // Then
        assertEquals(immutableList, listResult.list);
        assertEquals(immutableSet, setResult.list);
        assertEquals(immutableMap, mapResult.object);
        assertEquals(immutableMap, mapResult.getMap());
    }

    @Test
    void testWithComplexSupplier() {
        // Given
        Supplier<Map<String, Object>> complexSupplier = () -> {
            Map<String, Object> result = new HashMap<>();
            result.put("timestamp", System.currentTimeMillis());
            result.put("data", Arrays.asList("item1", "item2"));
            result.put("metadata", Map.of("version", "1.0"));
            return result;
        };
        
        // When
        UnifyResult<Map<String, Object>> result = UnifyResult.from(complexSupplier);
        
        // Then
        assertNotNull(result.getHeader());
        assertNotNull(result.object);
        assertNotNull(result.getMap());
        assertTrue(result.getMap().containsKey("timestamp"));
        assertTrue(result.getMap().containsKey("data"));
        assertTrue(result.getMap().containsKey("metadata"));
    }

    @Test
    void testThreadSafety() {
        // Given
        Header header = new Header();
        String payload = "thread safe test";
        
        // When
        Thread[] threads = new Thread[10];
        UnifyResult<String>[] results = new UnifyResult[10];
        Exception[] exceptions = new Exception[10];

        for (int i = 0; i < 10; i++) {
            final int index = i;
            threads[i] = new Thread(() -> {
                try {
                    results[index] = new UnifyResult<>(header, payload + index);
                } catch (Exception e) {
                    exceptions[index] = e;
                }
            });
            threads[i].start();
        }

        // Wait for all threads to complete
        for (Thread thread : threads) {
            try {
                thread.join();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }

        // Then
        for (int i = 0; i < 10; i++) {
            assertNull(exceptions[i], "Thread " + i + " should not have thrown an exception");
            assertNotNull(results[i], "Thread " + i + " should have returned a result");
            assertEquals(payload + i, results[i].object);
        }
    }

    @Test
    void testStaticMethodsThreadSafety() {
        // Given
        String testData = "static method test";
        
        // When
        Thread[] threads = new Thread[10];
        UnifyResult<String>[] results = new UnifyResult[10];
        Exception[] exceptions = new Exception[10];

        for (int i = 0; i < 10; i++) {
            final int index = i;
            threads[i] = new Thread(() -> {
                try {
                    results[index] = UnifyResult.toUnifyResult(testData + index);
                } catch (Exception e) {
                    exceptions[index] = e;
                }
            });
            threads[i].start();
        }

        // Wait for all threads to complete
        for (Thread thread : threads) {
            try {
                thread.join();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }

        // Then
        for (int i = 0; i < 10; i++) {
            assertNull(exceptions[i], "Thread " + i + " should not have thrown an exception");
            assertNotNull(results[i], "Thread " + i + " should have returned a result");
            assertEquals(testData + i, results[i].object);
        }
    }

    @Test
    void testWithLargeCollections() {
        // Given
        Header header = new Header();
        List<Integer> largeList = new ArrayList<>();
        for (int i = 0; i < 10000; i++) {
            largeList.add(i);
        }
        
        // When
        UnifyResult<List<Integer>> result = new UnifyResult<>(header, largeList);
        
        // Then
        assertEquals(header, result.getHeader());
        assertNull(result.object);
        assertEquals(largeList, result.list);
        assertEquals(10000, result.getList().size());
    }

    @Test
    void testWithLargeMaps() {
        // Given
        Header header = new Header();
        Map<String, Integer> largeMap = new HashMap<>();
        for (int i = 0; i < 10000; i++) {
            largeMap.put("key" + i, i);
        }
        
        // When
        UnifyResult<Map<String, Integer>> result = new UnifyResult<>(header, largeMap);
        
        // Then
        assertEquals(header, result.getHeader());
        assertEquals(largeMap, result.object);
        assertEquals(largeMap, result.getMap());
        assertNull(result.list);
        assertEquals(10000, result.getMap().size());
    }

    // Test data class for custom object testing
    private static class TestData {
        private final String name;
        private final int value;

        public TestData(String name, int value) {
            this.name = name;
            this.value = value;
        }

        public String getName() {
            return name;
        }

        public int getValue() {
            return value;
        }

        @Override
        public boolean equals(Object obj) {
            if (this == obj) return true;
            if (obj == null || getClass() != obj.getClass()) return false;
            TestData testData = (TestData) obj;
            return value == testData.value && Objects.equals(name, testData.name);
        }

        @Override
        public int hashCode() {
            return Objects.hash(name, value);
        }
    }
}
