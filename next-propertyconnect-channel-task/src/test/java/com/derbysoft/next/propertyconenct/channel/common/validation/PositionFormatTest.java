package com.derbysoft.next.propertyconenct.channel.common.validation;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import jakarta.validation.ValidatorFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.annotation.Annotation;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Comprehensive unit tests for PositionFormat
 * Tests validation annotation functionality for geographic position coordinates
 */
@ExtendWith(MockitoExtension.class)
class PositionFormatTest {

    private Validator validator;

    @BeforeEach
    void setUp() {
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        validator = factory.getValidator();
    }

    @Test
    void testPositionFormat_IsAnnotation() {
        // When & Then
        assertTrue(PositionFormat.class.isAnnotation());
    }

    @Test
    void testPositionFormat_ValidLongitudes_PassValidation() {
        // Given
        String[] validLongitudes = {
            "0",
            "0.0",
            "180",
            "180.0",
            "-180",
            "-180.0",
            "179.999999999999999",
            "-179.999999999999999",
            "90",
            "-90",
            "45.123456789012345",
            "-45.123456789012345",
            "1",
            "-1",
            "0.000000000000001",
            "-0.000000000000001",
            "179",
            "-179",
            "100.5",
            "-100.5"
        };

        for (String validLongitude : validLongitudes) {
            // When
            TestClass testObj = new TestClass();
            testObj.longitude = validLongitude;
            Set<ConstraintViolation<TestClass>> violations = validator.validate(testObj);

            // Then
            assertTrue(violations.isEmpty(), 
                "Longitude '" + validLongitude + "' should be valid but got violations: " + violations);
        }
    }

    @Test
    void testPositionFormat_ValidLatitudes_PassValidation() {
        // Given
        String[] validLatitudes = {
            "0",
            "0.0",
            "90",
            "90.0",
            "-90",
            "-90.0",
            "89.999999999999999",
            "-89.999999999999999",
            "45",
            "-45",
            "30.123456789012345",
            "-30.123456789012345",
            "1",
            "-1",
            "0.000000000000001",
            "-0.000000000000001",
            "89",
            "-89",
            "60.5",
            "-60.5"
        };

        for (String validLatitude : validLatitudes) {
            // When
            TestClass testObj = new TestClass();
            testObj.latitude = validLatitude;
            Set<ConstraintViolation<TestClass>> violations = validator.validate(testObj);

            // Then
            assertTrue(violations.isEmpty(), 
                "Latitude '" + validLatitude + "' should be valid but got violations: " + violations);
        }
    }

    @Test
    void testPositionFormat_InvalidLongitudes_FailValidation() {
        // Given
        String[] invalidLongitudes = {
            "180.1",
            "180.000000000000001",
            "-180.1",
            "-180.000000000000001",
            "181",
            "-181",
            "200",
            "-200",
            "360",
            "-360",
            "999",
            "-999",
            "180.1234567890123456", // too many decimal places
            "abc",
            "180.abc",
            "180.",
            ".180",
            "180.0.0",
            "180,0", // comma instead of dot
            "180 0", // space
            "+180.1", // explicit plus sign with invalid value
            "1e2", // scientific notation
            "180°", // degree symbol
            "N180", // with direction
            "180W" // with direction
        };

        for (String invalidLongitude : invalidLongitudes) {
            // When
            TestClass testObj = new TestClass();
            testObj.longitude = invalidLongitude;
            Set<ConstraintViolation<TestClass>> violations = validator.validate(testObj);

            // Then
            assertFalse(violations.isEmpty(), 
                "Longitude '" + invalidLongitude + "' should be invalid but passed validation");
            
            ConstraintViolation<TestClass> violation = violations.iterator().next();
            assertTrue(violation.getMessage().contains("Invalid position format"));
        }
    }

    @Test
    void testPositionFormat_InvalidLatitudes_FailValidation() {
        // Given
        // Note: The PositionFormat regex allows 0-179 range, so we test values outside this range
        String[] invalidLatitudes = {
            "181", // above 180
            "-181", // below -180
            "200", // way above 180
            "-200", // way below -180
            "999",
            "-999",
            "180.1", // above 180 with decimal
            "-180.1", // below -180 with decimal
            "180.000000000000001", // above 180 with many decimals
            "-180.000000000000001", // below -180 with many decimals
            "90.1234567890123456", // too many decimal places
            "abc",
            "90.abc",
            "90.",
            ".90",
            "90.0.0",
            "90,0", // comma instead of dot
            "90 0", // space
            "+90.1", // explicit plus sign
            "1e2", // scientific notation
            "90°", // degree symbol
            "N90", // with direction
            "90N" // with direction
        };

        for (String invalidLatitude : invalidLatitudes) {
            // When
            TestClass testObj = new TestClass();
            testObj.latitude = invalidLatitude;
            Set<ConstraintViolation<TestClass>> violations = validator.validate(testObj);

            // Then
            assertFalse(violations.isEmpty(),
                "Latitude '" + invalidLatitude + "' should be invalid but passed validation");

            ConstraintViolation<TestClass> violation = violations.iterator().next();
            assertTrue(violation.getMessage().contains("Invalid position format"));
        }
    }

    @Test
    void testPositionFormat_NullValue_PassesValidation() {
        // Given
        TestClass testObj = new TestClass();
        testObj.longitude = null;
        testObj.latitude = null;

        // When
        Set<ConstraintViolation<TestClass>> violations = validator.validate(testObj);

        // Then
        assertTrue(violations.isEmpty(), "Null values should pass validation");
    }

    @Test
    void testPositionFormat_EmptyString_FailsValidation() {
        // Given
        TestClass testObj = new TestClass();
        testObj.longitude = "";

        // When
        Set<ConstraintViolation<TestClass>> violations = validator.validate(testObj);

        // Then
        assertFalse(violations.isEmpty(), "Empty string should fail validation");
    }

    @Test
    void testPositionFormat_WhitespaceOnly_FailsValidation() {
        // Given
        TestClass testObj = new TestClass();
        testObj.longitude = "   ";

        // When
        Set<ConstraintViolation<TestClass>> violations = validator.validate(testObj);

        // Then
        assertFalse(violations.isEmpty(), "Whitespace-only string should fail validation");
    }

    @Test
    void testPositionFormat_BoundaryValues() {
        // Given
        String[] boundaryValues = {
            "180.0", // max longitude
            "-180.0", // min longitude
            "90.0", // max latitude
            "-90.0", // min latitude
            "179.999999999999999", // just under max longitude
            "-179.999999999999999", // just under min longitude
            "89.999999999999999", // just under max latitude
            "-89.999999999999999" // just under min latitude
        };

        for (String boundaryValue : boundaryValues) {
            // Test longitude separately
            TestClass longitudeTestObj = new TestClass();
            longitudeTestObj.longitude = boundaryValue;
            Set<ConstraintViolation<TestClass>> longitudeViolations = validator.validate(longitudeTestObj);

            // Test latitude separately
            TestClass latitudeTestObj = new TestClass();
            latitudeTestObj.latitude = boundaryValue;
            Set<ConstraintViolation<TestClass>> latitudeViolations = validator.validate(latitudeTestObj);

            // Then
            // All boundary values should be valid for longitude (range -180 to 180)
            assertTrue(longitudeViolations.isEmpty(),
                "Boundary value '" + boundaryValue + "' should be valid for longitude");

            // All boundary values should also be valid for latitude since they're all within 0-179 range
            // The PositionFormat regex allows 0-179, so all our boundary values are valid
            assertTrue(latitudeViolations.isEmpty(),
                "Boundary value '" + boundaryValue + "' should be valid for latitude (regex allows 0-179 range)");
        }
    }

    @Test
    void testPositionFormat_AnnotationProperties() throws Exception {
        // Given
        PositionFormat annotation = TestClass.class.getDeclaredField("longitude").getAnnotation(PositionFormat.class);

        // When & Then
        assertNotNull(annotation);
        assertEquals("", annotation.message());
        assertEquals(0, annotation.groups().length);
        assertEquals(0, annotation.payload().length);
    }

    @Test
    void testPositionFormat_PatternRegex() throws Exception {
        // Given
        PositionFormat annotation = TestClass.class.getDeclaredField("longitude").getAnnotation(PositionFormat.class);
        
        // When
        // Get the Pattern annotation that PositionFormat is composed of
        Annotation[] annotations = PositionFormat.class.getAnnotations();
        jakarta.validation.constraints.Pattern patternAnnotation = null;
        for (Annotation ann : annotations) {
            if (ann instanceof jakarta.validation.constraints.Pattern) {
                patternAnnotation = (jakarta.validation.constraints.Pattern) ann;
                break;
            }
        }

        // Then
        assertNotNull(patternAnnotation);
        assertEquals("^-?((180(\\.0{1,15})?)|((1[0-7]?\\d|\\d{1,2})(\\.\\d{1,15})?))$", patternAnnotation.regexp());
        assertEquals("Invalid position format", patternAnnotation.message());
    }

    @Test
    void testPositionFormat_TargetElements() {
        // When
        java.lang.annotation.Target target = PositionFormat.class.getAnnotation(java.lang.annotation.Target.class);

        // Then
        assertNotNull(target);
        assertEquals(2, target.value().length);
        assertTrue(java.util.Arrays.asList(target.value()).contains(java.lang.annotation.ElementType.FIELD));
        assertTrue(java.util.Arrays.asList(target.value()).contains(java.lang.annotation.ElementType.ANNOTATION_TYPE));
    }

    @Test
    void testPositionFormat_RetentionPolicy() {
        // When
        java.lang.annotation.Retention retention = PositionFormat.class.getAnnotation(java.lang.annotation.Retention.class);

        // Then
        assertNotNull(retention);
        assertEquals(java.lang.annotation.RetentionPolicy.RUNTIME, retention.value());
    }

    @Test
    void testPositionFormat_IsDocumented() {
        // When
        java.lang.annotation.Documented documented = PositionFormat.class.getAnnotation(java.lang.annotation.Documented.class);

        // Then
        assertNotNull(documented);
    }

    @Test
    void testPositionFormat_IsConstraint() {
        // When
        jakarta.validation.Constraint constraint = PositionFormat.class.getAnnotation(jakarta.validation.Constraint.class);

        // Then
        assertNotNull(constraint);
        assertEquals(0, constraint.validatedBy().length);
    }

    @Test
    void testPositionFormat_DecimalPrecision() {
        // Given
        String[] precisionTests = {
            "0.1", // 1 decimal place
            "0.12", // 2 decimal places
            "0.123456789012345", // 15 decimal places (max)
            "180.000000000000000", // 15 zeros after decimal for 180
            "90.000000000000000", // 15 zeros after decimal for 90
            "-180.000000000000000", // 15 zeros after decimal for -180
            "-90.000000000000000" // 15 zeros after decimal for -90
        };

        for (String precisionTest : precisionTests) {
            // When
            TestClass testObj = new TestClass();
            testObj.longitude = precisionTest;
            Set<ConstraintViolation<TestClass>> violations = validator.validate(testObj);

            // Then
            assertTrue(violations.isEmpty(), 
                "Precision test '" + precisionTest + "' should be valid but got violations: " + violations);
        }
    }

    @Test
    void testPositionFormat_ExcessiveDecimalPrecision_FailsValidation() {
        // Given
        String[] excessivePrecisionTests = {
            "0.1234567890123456", // 16 decimal places (too many)
            "180.0000000000000001", // 16 zeros after decimal for 180
            "90.0000000000000001" // 16 zeros after decimal for 90
        };

        for (String excessivePrecisionTest : excessivePrecisionTests) {
            // When
            TestClass testObj = new TestClass();
            testObj.longitude = excessivePrecisionTest;
            Set<ConstraintViolation<TestClass>> violations = validator.validate(testObj);

            // Then
            assertFalse(violations.isEmpty(), 
                "Excessive precision test '" + excessivePrecisionTest + "' should be invalid but passed validation");
        }
    }

    @Test
    void testPositionFormat_RealWorldCoordinates() {
        // Given
        String[][] realWorldCoordinates = {
            {"40.7589", "-73.9851"}, // New York City
            {"51.5074", "-0.1278"}, // London
            {"35.6762", "139.6503"}, // Tokyo
            {"-33.8688", "151.2093"}, // Sydney
            {"48.8566", "2.3522"}, // Paris
            {"55.7558", "37.6176"}, // Moscow
            {"-22.9068", "-43.1729"}, // Rio de Janeiro
            {"1.3521", "103.8198"}, // Singapore
            {"25.2048", "55.2708"}, // Dubai
            {"-26.2041", "28.0473"} // Johannesburg
        };

        for (String[] coordinate : realWorldCoordinates) {
            // When
            TestClass testObj = new TestClass();
            testObj.latitude = coordinate[0];
            testObj.longitude = coordinate[1];
            Set<ConstraintViolation<TestClass>> violations = validator.validate(testObj);

            // Then
            assertTrue(violations.isEmpty(), 
                "Real world coordinate [" + coordinate[0] + ", " + coordinate[1] + "] should be valid but got violations: " + violations);
        }
    }

    @Test
    void testPositionFormat_ThreadSafety() {
        // Given
        String validPosition = "40.7589";
        
        // When
        Thread[] threads = new Thread[10];
        boolean[] results = new boolean[10];
        Exception[] exceptions = new Exception[10];

        for (int i = 0; i < 10; i++) {
            final int index = i;
            threads[i] = new Thread(() -> {
                try {
                    TestClass testObj = new TestClass();
                    testObj.longitude = validPosition;
                    testObj.latitude = validPosition;
                    Set<ConstraintViolation<TestClass>> violations = validator.validate(testObj);
                    results[index] = violations.isEmpty();
                } catch (Exception e) {
                    exceptions[index] = e;
                }
            });
            threads[i].start();
        }

        // Wait for all threads to complete
        for (Thread thread : threads) {
            try {
                thread.join();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }

        // Then
        for (int i = 0; i < 10; i++) {
            assertNull(exceptions[i], "Thread " + i + " should not have thrown an exception");
            assertTrue(results[i], "Thread " + i + " should have validated successfully");
        }
    }

    @Test
    void testPositionFormat_AnnotationTypeValidation() {
        // Given
        // Test that PositionFormat can be used as meta-annotation
        // by checking if it has the correct target elements
        java.lang.annotation.Target target = PositionFormat.class.getAnnotation(java.lang.annotation.Target.class);

        // When & Then
        assertNotNull(target);
        assertTrue(java.util.Arrays.asList(target.value()).contains(java.lang.annotation.ElementType.ANNOTATION_TYPE));
    }

    // Test class for validation
    private static class TestClass {
        @PositionFormat
        public String longitude;

        @PositionFormat
        public String latitude;
    }
}
