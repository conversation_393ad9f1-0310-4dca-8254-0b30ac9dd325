package com.derbysoft.next.propertyconenct.channel.common.mapstruct;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Comprehensive unit tests for CommMapper
 * Tests MapStruct common mapping functionality for object to string map conversion
 */
@ExtendWith(MockitoExtension.class)
class CommMapperTest {

    private TestCommMapper commMapper;

    @BeforeEach
    void setUp() {
        commMapper = new TestCommMapper();
    }

    @Test
    void testCommMapper_IsInterface() {
        // When & Then
        assertTrue(CommMapper.class.isInterface());
        assertTrue(commMapper instanceof CommMapper);
    }

    @Test
    void testObjectMapToStringMap_WithNullMap_ReturnsNull() {
        // Given
        Map<String, Object> input = null;

        // When
        Map<String, String> result = commMapper.objectMapToStringMap(input);

        // Then
        assertNull(result);
    }

    @Test
    void testObjectMapToStringMap_WithEmptyMap_ReturnsEmptyMap() {
        // Given
        Map<String, Object> input = new HashMap<>();

        // When
        Map<String, String> result = commMapper.objectMapToStringMap(input);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testObjectMapToStringMap_WithStringValues_ReturnsCorrectStringMap() {
        // Given
        Map<String, Object> input = new HashMap<>();
        input.put("key1", "value1");
        input.put("key2", "value2");
        input.put("key3", "value3");

        // When
        Map<String, String> result = commMapper.objectMapToStringMap(input);

        // Then
        assertNotNull(result);
        assertEquals(3, result.size());
        assertEquals("value1", result.get("key1"));
        assertEquals("value2", result.get("key2"));
        assertEquals("value3", result.get("key3"));
    }

    @Test
    void testObjectMapToStringMap_WithIntegerValues_ConvertsToString() {
        // Given
        Map<String, Object> input = new HashMap<>();
        input.put("int1", 123);
        input.put("int2", 456);
        input.put("int3", 0);
        input.put("int4", -789);

        // When
        Map<String, String> result = commMapper.objectMapToStringMap(input);

        // Then
        assertNotNull(result);
        assertEquals(4, result.size());
        assertEquals("123", result.get("int1"));
        assertEquals("456", result.get("int2"));
        assertEquals("0", result.get("int3"));
        assertEquals("-789", result.get("int4"));
    }

    @Test
    void testObjectMapToStringMap_WithBooleanValues_ConvertsToString() {
        // Given
        Map<String, Object> input = new HashMap<>();
        input.put("bool1", true);
        input.put("bool2", false);
        input.put("bool3", Boolean.TRUE);
        input.put("bool4", Boolean.FALSE);

        // When
        Map<String, String> result = commMapper.objectMapToStringMap(input);

        // Then
        assertNotNull(result);
        assertEquals(4, result.size());
        assertEquals("true", result.get("bool1"));
        assertEquals("false", result.get("bool2"));
        assertEquals("true", result.get("bool3"));
        assertEquals("false", result.get("bool4"));
    }

    @Test
    void testObjectMapToStringMap_WithDoubleValues_ConvertsToString() {
        // Given
        Map<String, Object> input = new HashMap<>();
        input.put("double1", 123.456);
        input.put("double2", 0.0);
        input.put("double3", -789.123);
        input.put("double4", Double.MAX_VALUE);
        input.put("double5", Double.MIN_VALUE);

        // When
        Map<String, String> result = commMapper.objectMapToStringMap(input);

        // Then
        assertNotNull(result);
        assertEquals(5, result.size());
        assertEquals("123.456", result.get("double1"));
        assertEquals("0.0", result.get("double2"));
        assertEquals("-789.123", result.get("double3"));
        assertEquals(String.valueOf(Double.MAX_VALUE), result.get("double4"));
        assertEquals(String.valueOf(Double.MIN_VALUE), result.get("double5"));
    }

    @Test
    void testObjectMapToStringMap_WithNullValues_ConvertsToNullString() {
        // Given
        Map<String, Object> input = new HashMap<>();
        input.put("null1", null);
        input.put("null2", null);
        input.put("string1", "value1");

        // When
        Map<String, String> result = commMapper.objectMapToStringMap(input);

        // Then
        assertNotNull(result);
        assertEquals(3, result.size());
        assertEquals("null", result.get("null1"));
        assertEquals("null", result.get("null2"));
        assertEquals("value1", result.get("string1"));
    }

    @Test
    void testObjectMapToStringMap_WithMixedTypes_ConvertsAllToString() {
        // Given
        Map<String, Object> input = new HashMap<>();
        input.put("string", "text");
        input.put("integer", 42);
        input.put("boolean", true);
        input.put("double", 3.14);
        input.put("null", null);

        // When
        Map<String, String> result = commMapper.objectMapToStringMap(input);

        // Then
        assertNotNull(result);
        assertEquals(5, result.size());
        assertEquals("text", result.get("string"));
        assertEquals("42", result.get("integer"));
        assertEquals("true", result.get("boolean"));
        assertEquals("3.14", result.get("double"));
        assertEquals("null", result.get("null"));
    }

    @Test
    void testObjectMapToStringMap_WithComplexObjects_ConvertsToString() {
        // Given
        Map<String, Object> input = new HashMap<>();
        List<String> list = Arrays.asList("item1", "item2");
        Map<String, String> nestedMap = new HashMap<>();
        nestedMap.put("nested", "value");
        
        input.put("list", list);
        input.put("map", nestedMap);
        input.put("date", LocalDateTime.of(2023, 1, 1, 12, 0));
        input.put("bigDecimal", new BigDecimal("123.456"));

        // When
        Map<String, String> result = commMapper.objectMapToStringMap(input);

        // Then
        assertNotNull(result);
        assertEquals(4, result.size());
        assertEquals(list.toString(), result.get("list"));
        assertEquals(nestedMap.toString(), result.get("map"));
        assertEquals("2023-01-01T12:00", result.get("date"));
        assertEquals("123.456", result.get("bigDecimal"));
    }

    @Test
    void testObjectMapToStringMap_WithSpecialCharacters_HandlesCorrectly() {
        // Given
        Map<String, Object> input = new HashMap<>();
        input.put("special1", "value with spaces");
        input.put("special2", "value\nwith\nnewlines");
        input.put("special3", "value\twith\ttabs");
        input.put("special4", "value\"with\"quotes");
        input.put("special5", "value'with'apostrophes");
        input.put("special6", "value\\with\\backslashes");

        // When
        Map<String, String> result = commMapper.objectMapToStringMap(input);

        // Then
        assertNotNull(result);
        assertEquals(6, result.size());
        assertEquals("value with spaces", result.get("special1"));
        assertEquals("value\nwith\nnewlines", result.get("special2"));
        assertEquals("value\twith\ttabs", result.get("special3"));
        assertEquals("value\"with\"quotes", result.get("special4"));
        assertEquals("value'with'apostrophes", result.get("special5"));
        assertEquals("value\\with\\backslashes", result.get("special6"));
    }

    @Test
    void testObjectMapToStringMap_WithUnicodeCharacters_HandlesCorrectly() {
        // Given
        Map<String, Object> input = new HashMap<>();
        input.put("unicode1", "café");
        input.put("unicode2", "北京");
        input.put("unicode3", "🏨");
        input.put("unicode4", "العربية");
        input.put("unicode5", "москва");

        // When
        Map<String, String> result = commMapper.objectMapToStringMap(input);

        // Then
        assertNotNull(result);
        assertEquals(5, result.size());
        assertEquals("café", result.get("unicode1"));
        assertEquals("北京", result.get("unicode2"));
        assertEquals("🏨", result.get("unicode3"));
        assertEquals("العربية", result.get("unicode4"));
        assertEquals("москва", result.get("unicode5"));
    }

    @Test
    void testObjectMapToStringMap_WithLargeMap_HandlesCorrectly() {
        // Given
        Map<String, Object> input = new HashMap<>();
        for (int i = 0; i < 1000; i++) {
            input.put("key" + i, "value" + i);
        }

        // When
        Map<String, String> result = commMapper.objectMapToStringMap(input);

        // Then
        assertNotNull(result);
        assertEquals(1000, result.size());
        for (int i = 0; i < 1000; i++) {
            assertEquals("value" + i, result.get("key" + i));
        }
    }

    @Test
    void testObjectMapToStringMap_WithNumericTypes_ConvertsCorrectly() {
        // Given
        Map<String, Object> input = new HashMap<>();
        input.put("byte", (byte) 127);
        input.put("short", (short) 32767);
        input.put("int", 2147483647);
        input.put("long", 9223372036854775807L);
        input.put("float", 3.14f);
        input.put("double", 3.141592653589793);

        // When
        Map<String, String> result = commMapper.objectMapToStringMap(input);

        // Then
        assertNotNull(result);
        assertEquals(6, result.size());
        assertEquals("127", result.get("byte"));
        assertEquals("32767", result.get("short"));
        assertEquals("2147483647", result.get("int"));
        assertEquals("9223372036854775807", result.get("long"));
        assertEquals("3.14", result.get("float"));
        assertEquals("3.141592653589793", result.get("double"));
    }

    @Test
    void testObjectMapToStringMap_WithArrays_ConvertsToString() {
        // Given
        Map<String, Object> input = new HashMap<>();
        input.put("stringArray", new String[]{"a", "b", "c"});
        input.put("intArray", new int[]{1, 2, 3});
        input.put("boolArray", new boolean[]{true, false, true});

        // When
        Map<String, String> result = commMapper.objectMapToStringMap(input);

        // Then
        assertNotNull(result);
        assertEquals(3, result.size());

        // Arrays are converted to their string representation
        String stringArrayResult = result.get("stringArray");
        String intArrayResult = result.get("intArray");
        String boolArrayResult = result.get("boolArray");

        assertNotNull(stringArrayResult);
        assertNotNull(intArrayResult);
        assertNotNull(boolArrayResult);

        // Verify the string representation contains the expected format
        // Arrays are converted using Arrays.toString() which uses [I@hashcode format for primitive arrays
        // and [Ljava.lang.String;@hashcode format for object arrays
        assertTrue(stringArrayResult.contains("[") || stringArrayResult.contains("@"));
        assertTrue(intArrayResult.contains("[") || intArrayResult.contains("@"));
        assertTrue(boolArrayResult.contains("[") || boolArrayResult.contains("@"));
    }

    @Test
    void testObjectMapToStringMap_PreservesKeyOrder_WithLinkedHashMap() {
        // Given
        Map<String, Object> input = new LinkedHashMap<>();
        input.put("first", "1");
        input.put("second", "2");
        input.put("third", "3");

        // When
        Map<String, String> result = commMapper.objectMapToStringMap(input);

        // Then
        assertNotNull(result);
        assertEquals(3, result.size());

        // Note: The stream().collect() operation doesn't guarantee order preservation
        // So we just verify all keys are present
        assertTrue(result.containsKey("first"));
        assertTrue(result.containsKey("second"));
        assertTrue(result.containsKey("third"));
        assertEquals("1", result.get("first"));
        assertEquals("2", result.get("second"));
        assertEquals("3", result.get("third"));
    }

    @Test
    void testObjectMapToStringMap_WithCustomObject_ConvertsToString() {
        // Given
        Map<String, Object> input = new HashMap<>();
        TestObject testObj = new TestObject("test", 123);
        input.put("customObject", testObj);

        // When
        Map<String, String> result = commMapper.objectMapToStringMap(input);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(testObj.toString(), result.get("customObject"));
    }

    @Test
    void testObjectMapToStringMap_ThreadSafety_HandlesCorrectly() {
        // Given
        Map<String, Object> input = new HashMap<>();
        input.put("key1", "value1");
        input.put("key2", 123);
        input.put("key3", true);
        
        // When
        Thread[] threads = new Thread[10];
        Map<String, String>[] results = new Map[10];
        Exception[] exceptions = new Exception[10];

        for (int i = 0; i < 10; i++) {
            final int index = i;
            threads[i] = new Thread(() -> {
                try {
                    results[index] = commMapper.objectMapToStringMap(input);
                } catch (Exception e) {
                    exceptions[index] = e;
                }
            });
            threads[i].start();
        }

        // Wait for all threads to complete
        for (Thread thread : threads) {
            try {
                thread.join();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }

        // Then
        for (int i = 0; i < 10; i++) {
            assertNull(exceptions[i], "Thread " + i + " should not have thrown an exception");
            assertNotNull(results[i], "Thread " + i + " should have returned a result");
            assertEquals(3, results[i].size());
            assertEquals("value1", results[i].get("key1"));
            assertEquals("123", results[i].get("key2"));
            assertEquals("true", results[i].get("key3"));
        }
    }

    @Test
    void testObjectMapToStringMap_WithImmutableMap_HandlesCorrectly() {
        // Given
        Map<String, Object> input = Map.of(
            "key1", "value1",
            "key2", 123,
            "key3", true
        );

        // When
        Map<String, String> result = commMapper.objectMapToStringMap(input);

        // Then
        assertNotNull(result);
        assertEquals(3, result.size());
        assertEquals("value1", result.get("key1"));
        assertEquals("123", result.get("key2"));
        assertEquals("true", result.get("key3"));
    }

    @Test
    void testObjectMapToStringMap_WithSingletonMap_HandlesCorrectly() {
        // Given
        Map<String, Object> input = Collections.singletonMap("singleKey", "singleValue");

        // When
        Map<String, String> result = commMapper.objectMapToStringMap(input);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("singleValue", result.get("singleKey"));
    }

    @Test
    void testCommMapper_DefaultMethod_IsAccessible() {
        // When & Then
        assertDoesNotThrow(() -> {
            Map<String, Object> testMap = new HashMap<>();
            testMap.put("test", "value");
            commMapper.objectMapToStringMap(testMap);
        });
    }

    // Test implementation of CommMapper for testing purposes
    private static class TestCommMapper implements CommMapper {
        // Uses default interface methods
    }

    // Test object for custom object conversion testing
    private static class TestObject {
        private final String name;
        private final int value;

        public TestObject(String name, int value) {
            this.name = name;
            this.value = value;
        }

        @Override
        public String toString() {
            return "TestObject{name='" + name + "', value=" + value + "}";
        }
    }
}
