package com.derbysoft.next.propertyconenct.channel.common.validation;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import jakarta.validation.ValidatorFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.annotation.Annotation;
import java.lang.reflect.Method;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Comprehensive unit tests for BusinessIdFormat
 * Tests validation annotation functionality including pattern matching and constraint validation
 */
@ExtendWith(MockitoExtension.class)
class BusinessIdFormatTest {

    private Validator validator;

    @BeforeEach
    void setUp() {
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        validator = factory.getValidator();
    }

    @Test
    void testBusinessIdFormat_IsAnnotation() {
        // When & Then
        assertTrue(BusinessIdFormat.class.isAnnotation());
    }

    @Test
    void testBusinessIdFormat_HasCorrectRetentionPolicy() throws Exception {
        // When
        BusinessIdFormat annotation = TestClass.class.getDeclaredField("validId").getAnnotation(BusinessIdFormat.class);

        // Then
        assertNotNull(annotation);
    }

    @Test
    void testBusinessIdFormat_ValidIds_PassValidation() {
        // Given
        String[] validIds = {
            "PROPERTYCONNECT-CNFLAPDSKD",
            "HOTEL123",
            "BK1",
            "OCBNB-FLGY",
            "test_id",
            "ID-WITH-DASHES",
            "ID_WITH_UNDERSCORES",
            "123456",
            "abc",
            "ABC",
            "MixedCase123",
            "id.with.dots",
            "id with spaces",
            "<EMAIL>",
            "id#hash",
            "id%percent",
            "id+plus",
            "id=equals",
            "id?question",
            "id!exclamation",
            "id~tilde",
            "id`backtick",
            "id|pipe",
            "id\\backslash",
            "id/slash",
            "id:colon",
            "id;semicolon",
            "id,comma",
            "id.period",
            "id-dash",
            "id_underscore",
            "id(parenthesis)",
            "id[bracket]",
            "id*asterisk"
        };

        for (String validId : validIds) {
            // When
            TestClass testObj = new TestClass();
            testObj.validId = validId;
            Set<ConstraintViolation<TestClass>> violations = validator.validate(testObj);

            // Then
            assertTrue(violations.isEmpty(), 
                "ID '" + validId + "' should be valid but got violations: " + violations);
        }
    }

    @Test
    void testBusinessIdFormat_InvalidIds_FailValidation() {
        // Given
        String[] invalidIds = {
            "id${injection}",
            "id<script>",
            "id>greater",
            "id&ampersand",
            "id'quote",
            "id\"doublequote",
            "${malicious}",
            "<script>alert('xss')</script>",
            "id&lt;script&gt;",
            "id'OR'1'='1",
            "id\"OR\"1\"=\"1",
            "id${env:JAVA_HOME}",
            "id<img src=x>",
            "id>redirect",
            "id&entity;",
            "id'single",
            "id\"double"
        };

        for (String invalidId : invalidIds) {
            // When
            TestClass testObj = new TestClass();
            testObj.validId = invalidId;
            Set<ConstraintViolation<TestClass>> violations = validator.validate(testObj);

            // Then
            assertFalse(violations.isEmpty(), 
                "ID '" + invalidId + "' should be invalid but passed validation");
            
            ConstraintViolation<TestClass> violation = violations.iterator().next();
            assertTrue(violation.getMessage().contains("should not contain special characters") ||
                      violation.getMessage().contains("'Id' should not contain special characters"));
        }
    }

    @Test
    void testBusinessIdFormat_NullValue_PassesValidation() {
        // Given
        TestClass testObj = new TestClass();
        testObj.validId = null;

        // When
        Set<ConstraintViolation<TestClass>> violations = validator.validate(testObj);

        // Then
        assertTrue(violations.isEmpty(), "Null values should pass validation");
    }

    @Test
    void testBusinessIdFormat_EmptyString_PassesValidation() {
        // Given
        TestClass testObj = new TestClass();
        testObj.validId = "";

        // When
        Set<ConstraintViolation<TestClass>> violations = validator.validate(testObj);

        // Then
        assertTrue(violations.isEmpty(), "Empty string should pass validation");
    }

    @Test
    void testBusinessIdFormat_WhitespaceOnly_PassesValidation() {
        // Given
        TestClass testObj = new TestClass();
        testObj.validId = "   ";

        // When
        Set<ConstraintViolation<TestClass>> violations = validator.validate(testObj);

        // Then
        assertTrue(violations.isEmpty(), "Whitespace-only string should pass validation");
    }

    @Test
    void testBusinessIdFormat_AnnotationProperties() throws Exception {
        // Given
        BusinessIdFormat annotation = TestClass.class.getDeclaredField("validId").getAnnotation(BusinessIdFormat.class);

        // When & Then
        assertNotNull(annotation);
        assertEquals("", annotation.message());
        assertEquals(0, annotation.groups().length);
        assertEquals(0, annotation.payload().length);
    }

    @Test
    void testBusinessIdFormat_PatternRegex() throws Exception {
        // Given
        BusinessIdFormat annotation = TestClass.class.getDeclaredField("validId").getAnnotation(BusinessIdFormat.class);
        
        // When
        // Get the Pattern annotation that BusinessIdFormat is composed of
        Annotation[] annotations = BusinessIdFormat.class.getAnnotations();
        jakarta.validation.constraints.Pattern patternAnnotation = null;
        for (Annotation ann : annotations) {
            if (ann instanceof jakarta.validation.constraints.Pattern) {
                patternAnnotation = (jakarta.validation.constraints.Pattern) ann;
                break;
            }
        }

        // Then
        assertNotNull(patternAnnotation);
        assertEquals("^[^${}<>&'\"]*$", patternAnnotation.regexp());
        assertEquals("'Id' should not contain special characters", patternAnnotation.message());
    }

    @Test
    void testBusinessIdFormat_TargetElements() {
        // When
        java.lang.annotation.Target target = BusinessIdFormat.class.getAnnotation(java.lang.annotation.Target.class);

        // Then
        assertNotNull(target);
        assertEquals(3, target.value().length);
        assertTrue(java.util.Arrays.asList(target.value()).contains(java.lang.annotation.ElementType.FIELD));
        assertTrue(java.util.Arrays.asList(target.value()).contains(java.lang.annotation.ElementType.ANNOTATION_TYPE));
        assertTrue(java.util.Arrays.asList(target.value()).contains(java.lang.annotation.ElementType.PARAMETER));
    }

    @Test
    void testBusinessIdFormat_RetentionPolicy() {
        // When
        java.lang.annotation.Retention retention = BusinessIdFormat.class.getAnnotation(java.lang.annotation.Retention.class);

        // Then
        assertNotNull(retention);
        assertEquals(java.lang.annotation.RetentionPolicy.RUNTIME, retention.value());
    }

    @Test
    void testBusinessIdFormat_IsDocumented() {
        // When
        java.lang.annotation.Documented documented = BusinessIdFormat.class.getAnnotation(java.lang.annotation.Documented.class);

        // Then
        assertNotNull(documented);
    }

    @Test
    void testBusinessIdFormat_IsConstraint() {
        // When
        jakarta.validation.Constraint constraint = BusinessIdFormat.class.getAnnotation(jakarta.validation.Constraint.class);

        // Then
        assertNotNull(constraint);
        assertEquals(0, constraint.validatedBy().length);
    }

    @Test
    void testBusinessIdFormat_EdgeCases() {
        // Given
        String[] edgeCases = {
            "a", // single character
            "1", // single digit
            "_", // single underscore
            "-", // single dash
            ".", // single dot
            " ", // single space
            "very_long_id_with_many_characters_that_should_still_be_valid_123456789",
            "ID.WITH.MANY.DOTS",
            "ID-WITH-MANY-DASHES",
            "ID_WITH_MANY_UNDERSCORES",
            "ID WITH MANY SPACES",
            "123456789012345678901234567890", // long numeric
            "abcdefghijklmnopqrstuvwxyz", // all lowercase
            "ABCDEFGHIJKLMNOPQRSTUVWXYZ", // all uppercase
            "0123456789", // all digits
            "!@#%^*()_+-=[]|;:,./~`" // special chars except forbidden ones (removed < > { })
        };

        for (String edgeCase : edgeCases) {
            // When
            TestClass testObj = new TestClass();
            testObj.validId = edgeCase;
            Set<ConstraintViolation<TestClass>> violations = validator.validate(testObj);

            // Then
            assertTrue(violations.isEmpty(),
                "Edge case '" + edgeCase + "' should be valid but got violations: " + violations);
        }
    }

    @Test
    void testBusinessIdFormat_ForbiddenCharactersCombinations() {
        // Given
        String[] forbiddenCombinations = {
            "valid${invalid}",
            "valid<invalid>",
            "valid&invalid",
            "valid'invalid",
            "valid\"invalid",
            "${}<>&'\"", // all forbidden chars
            "start${middle}end",
            "start<middle>end",
            "start&middle&end",
            "start'middle'end",
            "start\"middle\"end"
        };

        for (String forbiddenCombo : forbiddenCombinations) {
            // When
            TestClass testObj = new TestClass();
            testObj.validId = forbiddenCombo;
            Set<ConstraintViolation<TestClass>> violations = validator.validate(testObj);

            // Then
            assertFalse(violations.isEmpty(), 
                "Forbidden combination '" + forbiddenCombo + "' should be invalid but passed validation");
        }
    }

    @Test
    void testBusinessIdFormat_UnicodeCharacters() {
        // Given
        String[] unicodeIds = {
            "café", // accented characters
            "naïve", // diaeresis
            "résumé", // acute accents
            "北京", // Chinese characters
            "東京", // Japanese characters
            "москва", // Cyrillic characters
            "العربية", // Arabic characters
            "हिन्दी", // Hindi characters
            "🏨hotel", // emoji
            "test™", // trademark symbol
            "test©", // copyright symbol
            "test®" // registered symbol
        };

        for (String unicodeId : unicodeIds) {
            // When
            TestClass testObj = new TestClass();
            testObj.validId = unicodeId;
            Set<ConstraintViolation<TestClass>> violations = validator.validate(testObj);

            // Then
            assertTrue(violations.isEmpty(), 
                "Unicode ID '" + unicodeId + "' should be valid but got violations: " + violations);
        }
    }

    @Test
    void testBusinessIdFormat_MethodParameterValidation() throws Exception {
        // Given
        Method method = TestService.class.getMethod("processId", String.class);
        BusinessIdFormat annotation = method.getParameters()[0].getAnnotation(BusinessIdFormat.class);

        // When & Then
        assertNotNull(annotation);
    }

    @Test
    void testBusinessIdFormat_AnnotationTypeValidation() {
        // Given
        // Test that BusinessIdFormat can be used as meta-annotation
        // by checking if it has the correct target elements
        java.lang.annotation.Target target = BusinessIdFormat.class.getAnnotation(java.lang.annotation.Target.class);

        // When & Then
        assertNotNull(target);
        assertTrue(java.util.Arrays.asList(target.value()).contains(java.lang.annotation.ElementType.ANNOTATION_TYPE));
    }

    @Test
    void testBusinessIdFormat_ThreadSafety() {
        // Given
        String validId = "THREAD_SAFE_ID";
        
        // When
        Thread[] threads = new Thread[10];
        boolean[] results = new boolean[10];
        Exception[] exceptions = new Exception[10];

        for (int i = 0; i < 10; i++) {
            final int index = i;
            threads[i] = new Thread(() -> {
                try {
                    TestClass testObj = new TestClass();
                    testObj.validId = validId;
                    Set<ConstraintViolation<TestClass>> violations = validator.validate(testObj);
                    results[index] = violations.isEmpty();
                } catch (Exception e) {
                    exceptions[index] = e;
                }
            });
            threads[i].start();
        }

        // Wait for all threads to complete
        for (Thread thread : threads) {
            try {
                thread.join();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }

        // Then
        for (int i = 0; i < 10; i++) {
            assertNull(exceptions[i], "Thread " + i + " should not have thrown an exception");
            assertTrue(results[i], "Thread " + i + " should have validated successfully");
        }
    }

    // Test classes for validation
    private static class TestClass {
        @BusinessIdFormat
        public String validId;
    }

    private static class TestService {
        public void processId(@BusinessIdFormat String id) {
            // Method for testing parameter validation
        }
    }
}
