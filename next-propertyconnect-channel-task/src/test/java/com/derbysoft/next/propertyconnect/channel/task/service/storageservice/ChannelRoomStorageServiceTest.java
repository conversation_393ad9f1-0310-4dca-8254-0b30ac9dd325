package com.derbysoft.next.propertyconnect.channel.task.service.storageservice;

import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelHotelDTO;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelRoomInfo;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ItemStatus;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.SyncStatus;
import com.derbysoft.next.propertyconnect.channel.task.domain.entity.ChannelHotelRoomPO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ChannelRoomStorageServiceTest {

    @Mock
    private MongoTemplate mongoTemplate;

    private ChannelRoomStorageService channelRoomStorageService;

    @BeforeEach
    void setUp() {
        channelRoomStorageService = new ChannelRoomStorageService(mongoTemplate);
    }

    @Test
    void testSaveIncrementalChannelProduct_WithNullRoomsInfo() {
        // Given
        ChannelHotelDTO channelHotel = new ChannelHotelDTO();
        channelHotel.setChannelId("test-channel");
        channelHotel.setChannelHotelId("test-hotel");
        channelHotel.setRoomsInfo(null);

        // When
        ChannelHotelDTO result = channelRoomStorageService.saveIncrementalChannelProduct(channelHotel);

        // Then
        assertNotNull(result);
        assertEquals(channelHotel, result);
        verifyNoInteractions(mongoTemplate);
    }

    @Test
    void testSaveIncrementalChannelProduct_WithEmptyRoomsInfo() {
        // Given
        ChannelHotelDTO channelHotel = new ChannelHotelDTO();
        channelHotel.setChannelId("test-channel");
        channelHotel.setChannelHotelId("test-hotel");
        channelHotel.setRoomsInfo(Collections.emptyList());

        // When
        ChannelHotelDTO result = channelRoomStorageService.saveIncrementalChannelProduct(channelHotel);

        // Then
        assertNotNull(result);
        assertEquals(channelHotel, result);
        verifyNoInteractions(mongoTemplate);
    }

    @Test
    void testSaveIncrementalChannelProduct_WithRoomsInfo() {
        // Given
        ChannelHotelDTO channelHotel = createTestChannelHotelDTO();
        ChannelRoomInfo roomInfo1 = createTestRoomInfo("room1", "Room 1");
        ChannelRoomInfo roomInfo2 = createTestRoomInfo("room2", "Room 2");
        channelHotel.setRoomsInfo(Arrays.asList(roomInfo1, roomInfo2));

        // Mock MongoDB operations
        ChannelHotelRoomPO existingPO1 = createTestRoomPO("room1");
        ChannelHotelRoomPO existingPO2 = null; // Second room doesn't exist

        when(mongoTemplate.findOne(any(Query.class), eq(ChannelHotelRoomPO.class)))
                .thenReturn(existingPO1, existingPO2);

        ChannelHotelRoomPO savedPO1 = createTestRoomPO("room1");
        ChannelHotelRoomPO savedPO2 = createTestRoomPO("room2");
        when(mongoTemplate.save(any(ChannelHotelRoomPO.class)))
                .thenReturn(savedPO1, savedPO2);

        // When
        ChannelHotelDTO result = channelRoomStorageService.saveIncrementalChannelProduct(channelHotel);

        // Then
        assertNotNull(result);
        assertEquals(2, result.getRoomsInfo().size());
        verify(mongoTemplate, times(2)).findOne(any(Query.class), eq(ChannelHotelRoomPO.class));
        verify(mongoTemplate, times(2)).save(any(ChannelHotelRoomPO.class));
    }

    @Test
    void testGetChannelHotel_WithExistingRooms() {
        // Given
        String channelId = "test-channel";
        String channelHotelId = "test-hotel";

        ChannelHotelRoomPO roomPO1 = createTestRoomPO("room1");
        ChannelHotelRoomPO roomPO2 = createTestRoomPO("room2");
        List<ChannelHotelRoomPO> roomPOs = Arrays.asList(roomPO1, roomPO2);

        when(mongoTemplate.find(any(Query.class), eq(ChannelHotelRoomPO.class)))
                .thenReturn(roomPOs);

        // When
        ChannelHotelDTO result = channelRoomStorageService.getChannelHotel(channelId, channelHotelId);

        // Then
        assertNotNull(result);
        assertNotNull(result.getRoomsInfo());
        assertEquals(2, result.getRoomsInfo().size());
        assertNull(result.getRoomInfo()); // Should be null after processing
        verify(mongoTemplate).find(any(Query.class), eq(ChannelHotelRoomPO.class));
    }

    @Test
    void testGetChannelHotel_WithNoRooms() {
        // Given
        String channelId = "test-channel";
        String channelHotelId = "test-hotel";

        when(mongoTemplate.find(any(Query.class), eq(ChannelHotelRoomPO.class)))
                .thenReturn(Collections.emptyList());

        // When
        ChannelHotelDTO result = channelRoomStorageService.getChannelHotel(channelId, channelHotelId);

        // Then
        assertNull(result);
        verify(mongoTemplate).find(any(Query.class), eq(ChannelHotelRoomPO.class));
    }

    @Test
    void testDeleteChannelHotel() {
        // Given
        String channelId = "test-channel";
        String channelHotelId = "test-hotel";

        // When
        channelRoomStorageService.deleteChannelHotel(channelId, channelHotelId);

        // Then
        verify(mongoTemplate).remove(any(Query.class), eq(ChannelHotelRoomPO.class));
    }

    @Test
    void testGetChannelHotel_SingleRoom() {
        // Given
        String channelId = "test-channel";
        String channelHotelId = "test-hotel";

        ChannelHotelRoomPO roomPO = createTestRoomPO("room1");
        when(mongoTemplate.find(any(Query.class), eq(ChannelHotelRoomPO.class)))
                .thenReturn(Arrays.asList(roomPO));

        // When
        ChannelHotelDTO result = channelRoomStorageService.getChannelHotel(channelId, channelHotelId);

        // Then
        assertNotNull(result);
        verify(mongoTemplate).find(any(Query.class), eq(ChannelHotelRoomPO.class));
    }

    private ChannelHotelDTO createTestChannelHotelDTO() {
        ChannelHotelDTO dto = new ChannelHotelDTO();
        dto.setChannelId("test-channel");
        dto.setChannelHotelId("test-hotel");
        dto.setSupplierId("test-supplier");
        return dto;
    }

    private ChannelRoomInfo createTestRoomInfo(String code, String name) {
        ChannelRoomInfo roomInfo = new ChannelRoomInfo(code);
        roomInfo.setName(name);
        roomInfo.setStatus(ItemStatus.Actived);
        roomInfo.setSyncStatus(SyncStatus.SYNCED);
        
        // Add occupancy info
        ChannelRoomInfo.Occupancy occupancy = new ChannelRoomInfo.Occupancy();
        occupancy.setMaxOccupancy(2);
        occupancy.setMaxAdult(2);
        occupancy.setMaxChild(1);
        roomInfo.setOccupancy(occupancy);
        
        return roomInfo;
    }

    private ChannelHotelRoomPO createTestRoomPO(String code) {
        return ChannelHotelRoomPO.builder()
                .id("test-id")
                .channelId("test-channel")
                .channelHotelId("test-hotel")
                .code(code)
                .status(ItemStatus.Actived)
                .syncStatus(SyncStatus.SYNCED)
                .roomInfo(new java.util.HashMap<>(Map.of("code", code, "name", "Test Room")))
                .build();
    }
}
