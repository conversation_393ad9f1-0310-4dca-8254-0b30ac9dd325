package com.derbysoft.next.propertyconnect.channel.task.service.impl.groovy;

import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelHotelDTO;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelHotelInfo;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.SyncStatus;
import com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.RemoteChannelService;
import com.derbysoft.next.propertyconnect.channel.task.service.impl.hoteltonight.HotelTonightAdapterService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Comprehensive unit tests for HotelTonightAdapterService Groovy script
 * Tests property customization and procedure customization
 */
@ExtendWith(MockitoExtension.class)
class HotelTonightAdapterServiceTest {

    private HotelTonightAdapterService hotelTonightService;

    @BeforeEach
    void setUp() {
        hotelTonightService = new HotelTonightAdapterService();
    }

    @Test
    void testChannel_ReturnsCorrectChannelName() {
        // When
        String result = hotelTonightService.channel();

        // Then
        assertEquals("HOTELTONIGHT", result);
    }

    @Test
    void testCustomizeProperty_SetsMarkUpExtension() {
        // Given
        ChannelHotelDTO dto = createTestChannelHotelDTO();

        // When
        hotelTonightService.customizeProperty(dto);

        // Then
        assertNotNull(dto.getHotelInfo().getExtensions());
        assertEquals("0", dto.getHotelInfo().getExtensions().get("mark_up"));
    }

    @Test
    void testCustomizeProperty_WithExistingExtensions_ReplacesExtensions() {
        // Given
        ChannelHotelDTO dto = createTestChannelHotelDTO();
        
        Map<String, Object> existingExtensions = new HashMap<>();
        existingExtensions.put("existing_key", "existing_value");
        existingExtensions.put("another_key", "another_value");
        dto.getHotelInfo().setExtensions(existingExtensions);

        // When
        hotelTonightService.customizeProperty(dto);

        // Then
        Map<String, Object> extensions = dto.getHotelInfo().getExtensions();
        assertNotNull(extensions);
        assertEquals(1, extensions.size());
        assertEquals("0", extensions.get("mark_up"));
        assertFalse(extensions.containsKey("existing_key"));
        assertFalse(extensions.containsKey("another_key"));
    }

    @Test
    void testCustomizeProperty_WithNullExtensions_CreatesNewExtensions() {
        // Given
        ChannelHotelDTO dto = createTestChannelHotelDTO();
        dto.getHotelInfo().setExtensions(null);

        // When
        hotelTonightService.customizeProperty(dto);

        // Then
        assertNotNull(dto.getHotelInfo().getExtensions());
        assertEquals("0", dto.getHotelInfo().getExtensions().get("mark_up"));
    }

    @Test
    void testCustomizeProperty_WithNullHotelInfo_ThrowsException() {
        // Given
        ChannelHotelDTO dto = new ChannelHotelDTO();
        dto.setHotelInfo(null);

        // When & Then
        assertThrows(NullPointerException.class, () -> {
            hotelTonightService.customizeProperty(dto);
        });
    }

    @Test
    void testCustomizeProcedure_ReturnsCorrectOperations() {
        // When
        List<RemoteChannelService.Operation> result = hotelTonightService.customizeProcedure();

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.contains(RemoteChannelService.Operation.SaveProperty));
        assertTrue(result.contains(RemoteChannelService.Operation.TriggerARIRefresh));
    }

    @Test
    void testCustomizeProcedure_ReturnsOperationsInCorrectOrder() {
        // When
        List<RemoteChannelService.Operation> result = hotelTonightService.customizeProcedure();

        // Then
        assertEquals(RemoteChannelService.Operation.SaveProperty, result.get(0));
        assertEquals(RemoteChannelService.Operation.TriggerARIRefresh, result.get(1));
    }

    @Test
    void testCustomizeProcedure_ReturnsImmutableList() {
        // When
        List<RemoteChannelService.Operation> result = hotelTonightService.customizeProcedure();

        // Then
        assertNotNull(result);
        // The list should be immutable or at least consistent
        assertEquals(2, result.size());
    }

    @Test
    void testCustomizeProperty_MarkUpValueIsString() {
        // Given
        ChannelHotelDTO dto = createTestChannelHotelDTO();

        // When
        hotelTonightService.customizeProperty(dto);

        // Then
        Object markUpValue = dto.getHotelInfo().getExtensions().get("mark_up");
        assertNotNull(markUpValue);
        assertTrue(markUpValue instanceof String);
        assertEquals("0", markUpValue);
    }

    @Test
    void testCustomizeProperty_DoesNotModifyOtherHotelInfoProperties() {
        // Given
        ChannelHotelDTO dto = createTestChannelHotelDTO();
        String originalId = dto.getHotelInfo().getId();
        String originalName = dto.getHotelInfo().getName();
        SyncStatus originalStatus = dto.getHotelInfo().getSyncStatus();

        // When
        hotelTonightService.customizeProperty(dto);

        // Then
        assertEquals(originalId, dto.getHotelInfo().getId());
        assertEquals(originalName, dto.getHotelInfo().getName());
        assertEquals(originalStatus, dto.getHotelInfo().getSyncStatus());
    }

    @Test
    void testCustomizeProperty_MultipleCallsProduceSameResult() {
        // Given
        ChannelHotelDTO dto = createTestChannelHotelDTO();

        // When
        hotelTonightService.customizeProperty(dto);
        Map<String, Object> firstResult = new HashMap<>(dto.getHotelInfo().getExtensions());
        
        hotelTonightService.customizeProperty(dto);
        Map<String, Object> secondResult = dto.getHotelInfo().getExtensions();

        // Then
        assertEquals(firstResult, secondResult);
        assertEquals("0", secondResult.get("mark_up"));
    }

    @Test
    void testCustomizeProperty_WithDifferentChannelHotelDTOs_ProducesSameExtensions() {
        // Given
        ChannelHotelDTO dto1 = createTestChannelHotelDTO();
        dto1.setChannelHotelId("HOTEL_001");
        
        ChannelHotelDTO dto2 = createTestChannelHotelDTO();
        dto2.setChannelHotelId("HOTEL_002");

        // When
        hotelTonightService.customizeProperty(dto1);
        hotelTonightService.customizeProperty(dto2);

        // Then
        assertEquals(dto1.getHotelInfo().getExtensions(), dto2.getHotelInfo().getExtensions());
        assertEquals("0", dto1.getHotelInfo().getExtensions().get("mark_up"));
        assertEquals("0", dto2.getHotelInfo().getExtensions().get("mark_up"));
    }

    @Test
    void testCustomizeProcedure_DoesNotIncludeOtherOperations() {
        // When
        List<RemoteChannelService.Operation> result = hotelTonightService.customizeProcedure();

        // Then
        assertFalse(result.contains(RemoteChannelService.Operation.SaveRoomTypes));
        assertFalse(result.contains(RemoteChannelService.Operation.SaveRatePlans));
        assertFalse(result.contains(RemoteChannelService.Operation.SaveProducts));
    }

    @Test
    void testCustomizeProcedure_ConsistentResults() {
        // When
        List<RemoteChannelService.Operation> result1 = hotelTonightService.customizeProcedure();
        List<RemoteChannelService.Operation> result2 = hotelTonightService.customizeProcedure();

        // Then
        assertEquals(result1, result2);
        assertEquals(result1.size(), result2.size());
        for (int i = 0; i < result1.size(); i++) {
            assertEquals(result1.get(i), result2.get(i));
        }
    }

    private ChannelHotelDTO createTestChannelHotelDTO() {
        ChannelHotelDTO dto = new ChannelHotelDTO();
        dto.setChannelId("HOTELTONIGHT");
        dto.setChannelHotelId("HOTEL_123");
        
        ChannelHotelInfo hotelInfo = new ChannelHotelInfo();
        hotelInfo.setId("HOTEL_123");
        hotelInfo.setName("Test Hotel");
        hotelInfo.setSyncStatus(SyncStatus.DRAFT);
        
        Map<String, Object> extensions = new HashMap<>();
        hotelInfo.setExtensions(extensions);
        
        dto.setHotelInfo(hotelInfo);
        
        return dto;
    }
}
