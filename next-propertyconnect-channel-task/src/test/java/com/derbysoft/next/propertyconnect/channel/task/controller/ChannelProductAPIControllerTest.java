package com.derbysoft.next.propertyconnect.channel.task.controller;

import com.derbysoft.next.propertyconenct.channel.common.exception.response.UnifyResult;
import com.derbysoft.next.propertyconnect.channel.task.controller.vo.ChannelProductVO;
import com.derbysoft.next.propertyconnect.channel.task.service.ChannelProductAPIService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * Comprehensive unit tests for ChannelProductAPIController
 * Tests all controller endpoints and error scenarios
 */
@ExtendWith(MockitoExtension.class)
class ChannelProductAPIControllerTest {

    @Mock
    private ChannelProductAPIService channelProductAPIService;

    @InjectMocks
    private ChannelProductAPIController channelProductAPIController;

    private ChannelProductVO testChannelProductVO;

    @BeforeEach
    void setUp() {
        testChannelProductVO = new ChannelProductVO();
        testChannelProductVO.setChannelId("TEST_CHANNEL");
        testChannelProductVO.setChannelHotelId("TEST_HOTEL");
        
        List<ChannelProductVO.Product> products = new ArrayList<>();
        ChannelProductVO.Product product = new ChannelProductVO.Product();
        product.setRoomId("ROOM_001");
        product.setRoomName("Standard Room");
        product.setRateId("RATE_001");
        product.setRateName("Standard Rate");
        products.add(product);
        
        testChannelProductVO.setChannelProducts(products);
    }

    @Test
    void testGetChannelProducts_Success() {
        // Given
        String channelId = "BOOKING";
        String channelHotelId = "HOTEL_123";
        String supplierId = "SUPPLIER_456";
        String hotelSystemConnectionId = "CONNECTION_789";
        Boolean refresh = false;
        Boolean ignoreError = false;
        
        when(channelProductAPIService.getChannelProducts(
            channelId, channelHotelId, supplierId, hotelSystemConnectionId, refresh, ignoreError))
            .thenReturn(testChannelProductVO);

        // When
        UnifyResult<ChannelProductVO> result = channelProductAPIController.getChannelProducts(
            channelId, channelHotelId, supplierId, hotelSystemConnectionId, refresh, ignoreError);

        // Then
        assertNotNull(result);
        assertEquals(testChannelProductVO, result.getObject());
        verify(channelProductAPIService).getChannelProducts(
            channelId, channelHotelId, supplierId, hotelSystemConnectionId, refresh, ignoreError);
    }

    @Test
    void testGetChannelProducts_WithDefaultParameters_Success() {
        // Given
        String channelId = "AGODA";
        String channelHotelId = "AGODA_HOTEL_456";
        String supplierId = "AGODA_SUPPLIER_789";

        when(channelProductAPIService.getChannelProducts(
            eq(channelId), eq(channelHotelId), eq(supplierId),
            any(), any(), any()))
            .thenReturn(testChannelProductVO);

        // When
        UnifyResult<ChannelProductVO> result = channelProductAPIController.getChannelProducts(
            channelId, channelHotelId, supplierId, null, null, null);

        // Then
        assertNotNull(result);
        assertEquals(testChannelProductVO, result.getObject());
        verify(channelProductAPIService).getChannelProducts(
            channelId, channelHotelId, supplierId, null, null, null);
    }

    @Test
    void testGetChannelProductsWithExtra_Success() {
        // Given
        String channelId = "CTRIP";
        String channelHotelId = "CTRIP_HOTEL_404";
        String supplierId = "CTRIP_SUPPLIER_505";

        when(channelProductAPIService.getChannelProductsWithExtra(channelId, channelHotelId, supplierId))
            .thenReturn(testChannelProductVO);

        // When
        UnifyResult<ChannelProductVO> result = channelProductAPIController.getChannelProductsWithExtra(
            channelId, channelHotelId, supplierId);

        // Then
        assertNotNull(result);
        assertEquals(testChannelProductVO, result.getObject());
        verify(channelProductAPIService).getChannelProductsWithExtra(channelId, channelHotelId, supplierId);
    }

    @Test
    void testUploadChannelProducts_Success() {
        // Given
        String channelId = "HOTELTONIGHT";
        String channelHotelId = "HT_HOTEL_606";
        MockMultipartFile file = new MockMultipartFile(
            "file", "products.xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            "test content".getBytes());

        when(channelProductAPIService.uploadChannelProducts(channelId, channelHotelId, file))
            .thenReturn(testChannelProductVO);

        // When
        UnifyResult<ChannelProductVO> result = channelProductAPIController.uploadChannelProducts(
            channelId, channelHotelId, file);

        // Then
        assertNotNull(result);
        assertEquals(testChannelProductVO, result.getObject());
        verify(channelProductAPIService).uploadChannelProducts(channelId, channelHotelId, file);
    }

    @Test
    void testGetChannelProducts_ServiceThrowsException_PropagatesException() {
        // Given
        String channelId = "ERROR_CHANNEL";
        String channelHotelId = "ERROR_HOTEL";
        String supplierId = "ERROR_SUPPLIER";
        
        when(channelProductAPIService.getChannelProducts(
            any(), any(), any(), any(), any(), any()))
            .thenThrow(new RuntimeException("Service error"));

        // When & Then
        assertThrows(RuntimeException.class, () -> {
            channelProductAPIController.getChannelProducts(
                channelId, channelHotelId, supplierId, null, null, null);
        });
    }

    @Test
    void testGetChannelProducts_ServiceReturnsNull_HandlesGracefully() {
        // Given
        String channelId = "NULL_CHANNEL";
        String channelHotelId = "NULL_HOTEL";
        String supplierId = "NULL_SUPPLIER";
        
        when(channelProductAPIService.getChannelProducts(
            any(), any(), any(), any(), any(), any()))
            .thenReturn(null);

        // When
        UnifyResult<ChannelProductVO> result = channelProductAPIController.getChannelProducts(
            channelId, channelHotelId, supplierId, null, null, null);

        // Then
        assertNotNull(result);
        assertNull(result.getObject());
    }

    @Test
    void testGetChannelProductsWithExtra_ServiceReturnsNull_HandlesGracefully() {
        // Given
        String channelId = "NULL_CHANNEL";
        String channelHotelId = "NULL_HOTEL";
        String supplierId = "NULL_SUPPLIER";

        when(channelProductAPIService.getChannelProductsWithExtra(channelId, channelHotelId, supplierId))
            .thenReturn(null);

        // When
        UnifyResult<ChannelProductVO> result = channelProductAPIController.getChannelProductsWithExtra(
            channelId, channelHotelId, supplierId);

        // Then
        assertNotNull(result);
        assertNull(result.getObject());
    }

    @Test
    void testUploadChannelProducts_ServiceThrowsException_PropagatesException() {
        // Given
        String channelId = "ERROR_CHANNEL";
        String channelHotelId = "ERROR_HOTEL";
        MultipartFile file = new MockMultipartFile("file", "test.xlsx", "application/excel", "content".getBytes());
        
        when(channelProductAPIService.uploadChannelProducts(channelId, channelHotelId, file))
            .thenThrow(new RuntimeException("Upload error"));

        // When & Then
        assertThrows(RuntimeException.class, () -> {
            channelProductAPIController.uploadChannelProducts(channelId, channelHotelId, file);
        });
    }

    @Test
    void testMultipleOperations_VerifyIndependentCalls() {
        // Given
        String channelId = "MULTI_OP_CHANNEL";
        String channelHotelId = "MULTI_OP_HOTEL";
        String supplierId = "MULTI_OP_SUPPLIER";
        MultipartFile file = new MockMultipartFile("file", "test.xlsx", "application/excel", "content".getBytes());
        
        when(channelProductAPIService.getChannelProducts(
            any(), any(), any(), any(), any(), any()))
            .thenReturn(testChannelProductVO);
        when(channelProductAPIService.getChannelProductsWithExtra(any(), any(), any()))
            .thenReturn(testChannelProductVO);
        when(channelProductAPIService.uploadChannelProducts(any(), any(), any()))
            .thenReturn(testChannelProductVO);

        // When
        UnifyResult<ChannelProductVO> getResult = channelProductAPIController.getChannelProducts(
            channelId, channelHotelId, supplierId, null, null, null);
        UnifyResult<ChannelProductVO> getExtraResult = channelProductAPIController.getChannelProductsWithExtra(
            channelId, channelHotelId, supplierId);
        UnifyResult<ChannelProductVO> uploadResult = channelProductAPIController.uploadChannelProducts(
            channelId, channelHotelId, file);

        // Then
        assertNotNull(getResult);
        assertNotNull(getExtraResult);
        assertNotNull(uploadResult);
        verify(channelProductAPIService).getChannelProducts(
            channelId, channelHotelId, supplierId, null, null, null);
        verify(channelProductAPIService).getChannelProductsWithExtra(channelId, channelHotelId, supplierId);
        verify(channelProductAPIService).uploadChannelProducts(channelId, channelHotelId, file);
    }
}
