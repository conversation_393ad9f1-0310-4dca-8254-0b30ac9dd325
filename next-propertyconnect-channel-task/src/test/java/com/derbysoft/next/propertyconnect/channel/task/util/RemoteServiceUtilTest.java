package com.derbysoft.next.propertyconnect.channel.task.util;

import com.derbysoft.next.propertyconnect.channel.task.service.RemoteService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.concurrent.atomic.AtomicReference;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * Comprehensive unit tests for RemoteServiceUtil
 * Tests static utility functionality for managing RemoteService instances
 */
@ExtendWith(MockitoExtension.class)
class RemoteServiceUtilTest {

    @Mock
    private RemoteService mockRemoteService;

    private RemoteServiceUtil remoteServiceUtil;

    @BeforeEach
    void setUp() {
        remoteServiceUtil = new RemoteServiceUtil();
        // Clear the static reference before each test
        clearStaticReference();
    }

    @SuppressWarnings("unchecked")
    private void clearStaticReference() {
        try {
            AtomicReference<RemoteService> reference = (AtomicReference<RemoteService>) 
                ReflectionTestUtils.getField(RemoteServiceUtil.class, "REMOTE_SERVICE");
            reference.set(null);
        } catch (Exception e) {
            throw new RuntimeException("Failed to clear static reference", e);
        }
    }

    @Test
    void testSetRemoteService_ValidService_SetsCorrectly() {
        // When
        remoteServiceUtil.setRemoteService(mockRemoteService);

        // Then
        RemoteService result = RemoteServiceUtil.getInstance();
        assertNotNull(result);
        assertSame(mockRemoteService, result);
    }

    @Test
    void testSetRemoteService_NullService_SetsNull() {
        // When
        remoteServiceUtil.setRemoteService(null);

        // Then
        RemoteService result = RemoteServiceUtil.getInstance();
        assertNull(result);
    }

    @Test
    void testGetInstance_BeforeSetRemoteService_ReturnsNull() {
        // When
        RemoteService result = RemoteServiceUtil.getInstance();

        // Then
        assertNull(result);
    }

    @Test
    void testGetInstance_AfterSetRemoteService_ReturnsSameInstance() {
        // Given
        remoteServiceUtil.setRemoteService(mockRemoteService);

        // When
        RemoteService result1 = RemoteServiceUtil.getInstance();
        RemoteService result2 = RemoteServiceUtil.getInstance();

        // Then
        assertNotNull(result1);
        assertNotNull(result2);
        assertSame(result1, result2);
        assertSame(mockRemoteService, result1);
        assertSame(mockRemoteService, result2);
    }

    @Test
    void testSetRemoteService_MultipleInstances_OverwritesPrevious() {
        // Given
        RemoteService firstService = mock(RemoteService.class);
        RemoteService secondService = mock(RemoteService.class);

        // When
        remoteServiceUtil.setRemoteService(firstService);
        RemoteService firstResult = RemoteServiceUtil.getInstance();

        remoteServiceUtil.setRemoteService(secondService);
        RemoteService secondResult = RemoteServiceUtil.getInstance();

        // Then
        assertSame(firstService, firstResult);
        assertSame(secondService, secondResult);
        assertNotSame(firstResult, secondResult);
    }

    @Test
    void testSetRemoteService_ThreadSafety_HandlesMultipleThreads() throws InterruptedException {
        // Given
        RemoteService service1 = mock(RemoteService.class);
        RemoteService service2 = mock(RemoteService.class);
        
        Thread thread1 = new Thread(() -> remoteServiceUtil.setRemoteService(service1));
        Thread thread2 = new Thread(() -> remoteServiceUtil.setRemoteService(service2));

        // When
        thread1.start();
        thread2.start();
        
        thread1.join();
        thread2.join();

        // Then
        RemoteService result = RemoteServiceUtil.getInstance();
        assertNotNull(result);
        // The result should be one of the two services (depending on thread execution order)
        assertTrue(result == service1 || result == service2);
    }

    @Test
    void testGetInstance_ThreadSafety_ConsistentReads() throws InterruptedException {
        // Given
        remoteServiceUtil.setRemoteService(mockRemoteService);
        
        RemoteService[] results = new RemoteService[10];
        Thread[] threads = new Thread[10];
        
        // Create multiple threads that read the instance
        for (int i = 0; i < 10; i++) {
            final int index = i;
            threads[i] = new Thread(() -> results[index] = RemoteServiceUtil.getInstance());
        }

        // When
        for (Thread thread : threads) {
            thread.start();
        }
        
        for (Thread thread : threads) {
            thread.join();
        }

        // Then
        for (RemoteService result : results) {
            assertNotNull(result);
            assertSame(mockRemoteService, result);
        }
    }

    @Test
    void testSetRemoteService_WithRealRemoteServiceImplementation_WorksCorrectly() {
        // Given
        RemoteService realService = createMockRemoteServiceWithMethods();

        // When
        remoteServiceUtil.setRemoteService(realService);

        // Then
        RemoteService result = RemoteServiceUtil.getInstance();
        assertNotNull(result);
        assertSame(realService, result);
        
        // Verify that the service methods can be called
        assertDoesNotThrow(() -> {
            // These would be actual method calls if RemoteService had concrete methods
            // For now, we just verify the instance is accessible
            result.toString();
        });
    }

    @Test
    void testStaticReference_AtomicReference_EnsuresThreadSafety() {
        // Given
        RemoteService service1 = mock(RemoteService.class);
        RemoteService service2 = mock(RemoteService.class);

        // When
        remoteServiceUtil.setRemoteService(service1);
        RemoteService firstRead = RemoteServiceUtil.getInstance();
        
        remoteServiceUtil.setRemoteService(service2);
        RemoteService secondRead = RemoteServiceUtil.getInstance();

        // Then
        assertSame(service1, firstRead);
        assertSame(service2, secondRead);
        assertNotSame(firstRead, secondRead);
    }

    @Test
    void testSetRemoteService_CalledMultipleTimes_UpdatesReference() {
        // Given
        RemoteService[] services = new RemoteService[5];
        for (int i = 0; i < 5; i++) {
            services[i] = mock(RemoteService.class);
        }

        // When & Then
        for (int i = 0; i < 5; i++) {
            remoteServiceUtil.setRemoteService(services[i]);
            RemoteService result = RemoteServiceUtil.getInstance();
            assertSame(services[i], result);
        }
    }

    @Test
    void testGetInstance_AfterSettingToNull_ReturnsNull() {
        // Given
        remoteServiceUtil.setRemoteService(mockRemoteService);
        assertNotNull(RemoteServiceUtil.getInstance());

        // When
        remoteServiceUtil.setRemoteService(null);

        // Then
        assertNull(RemoteServiceUtil.getInstance());
    }

    @Test
    void testRemoteServiceUtil_Constructor_CreatesInstance() {
        // When
        RemoteServiceUtil util = new RemoteServiceUtil();

        // Then
        assertNotNull(util);
    }

    @Test
    void testRemoteServiceUtil_MultipleInstances_ShareStaticReference() {
        // Given
        RemoteServiceUtil util1 = new RemoteServiceUtil();
        RemoteServiceUtil util2 = new RemoteServiceUtil();

        // When
        util1.setRemoteService(mockRemoteService);

        // Then
        RemoteService result1 = RemoteServiceUtil.getInstance();
        RemoteService result2 = RemoteServiceUtil.getInstance();
        
        assertSame(mockRemoteService, result1);
        assertSame(mockRemoteService, result2);
        assertSame(result1, result2);
    }

    @Test
    void testSetRemoteService_WithDifferentUtilInstances_UpdatesSameReference() {
        // Given
        RemoteServiceUtil util1 = new RemoteServiceUtil();
        RemoteServiceUtil util2 = new RemoteServiceUtil();
        RemoteService service1 = mock(RemoteService.class);
        RemoteService service2 = mock(RemoteService.class);

        // When
        util1.setRemoteService(service1);
        RemoteService firstResult = RemoteServiceUtil.getInstance();
        
        util2.setRemoteService(service2);
        RemoteService secondResult = RemoteServiceUtil.getInstance();

        // Then
        assertSame(service1, firstResult);
        assertSame(service2, secondResult);
        assertNotSame(firstResult, secondResult);
    }

    @Test
    void testGetInstance_StaticMethod_AccessibleWithoutInstance() {
        // Given
        remoteServiceUtil.setRemoteService(mockRemoteService);

        // When - Call static method without instance
        RemoteService result = RemoteServiceUtil.getInstance();

        // Then
        assertNotNull(result);
        assertSame(mockRemoteService, result);
    }

    @Test
    void testRemoteServiceUtil_ComponentAnnotation_SupportsSpringInjection() {
        // This test verifies that the class is properly annotated for Spring
        // Given
        Class<RemoteServiceUtil> clazz = RemoteServiceUtil.class;

        // When
        boolean hasComponentAnnotation = clazz.isAnnotationPresent(org.springframework.stereotype.Component.class);

        // Then
        assertTrue(hasComponentAnnotation, "RemoteServiceUtil should be annotated with @Component");
    }

    @Test
    void testSetRemoteService_AutowiredAnnotation_SupportsSpringInjection() throws NoSuchMethodException {
        // This test verifies that the setRemoteService method is properly annotated for Spring
        // Given
        Class<RemoteServiceUtil> clazz = RemoteServiceUtil.class;
        var method = clazz.getMethod("setRemoteService", RemoteService.class);

        // When
        boolean hasAutowiredAnnotation = method.isAnnotationPresent(org.springframework.beans.factory.annotation.Autowired.class);

        // Then
        assertTrue(hasAutowiredAnnotation, "setRemoteService method should be annotated with @Autowired");
    }

    private RemoteService createMockRemoteServiceWithMethods() {
        RemoteService service = mock(RemoteService.class);
        // Configure mock behavior if needed
        when(service.toString()).thenReturn("MockRemoteService");
        return service;
    }
}
