package com.derbysoft.next.propertyconnect.channel.task.service.impl;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AccountSettingServiceObtainProxyTest {

    @InjectMocks
    private AccountSettingServiceObtainProxy accountSettingServiceObtainProxy;

    @Mock
    private PCProfileAccountSettingService profileAccountSettingService;

    @Mock
    private LocalAccountSettingService localAccountSettingService;

    @Test
    void givenLocalSettingsExist_whenGetAccountSettings_thenReturnsLocalSettings() {
        String channelId = "channel1";
        String channelHotelId = "hotel1";
        Map<String, Object> localSettings = Collections.singletonMap("key", "local_value");

        when(localAccountSettingService.getAccountSettings(channelId, channelHotelId)).thenReturn(localSettings);

        Map<String, Object> result = accountSettingServiceObtainProxy.getAccountSettings(channelId, channelHotelId);

        assertNotNull(result);
        assertEquals(localSettings, result);
        verify(profileAccountSettingService, never()).getAccountSettings(anyString(), anyString());
    }

    @Test
    void givenNoLocalSettings_whenGetAccountSettings_thenReturnsProfileSettings() {
        String channelId = "channel1";
        String channelHotelId = "hotel1";
        Map<String, Object> profileSettings = Collections.singletonMap("key", "profile_value");

        when(localAccountSettingService.getAccountSettings(channelId, channelHotelId)).thenReturn(null);
        when(profileAccountSettingService.getAccountSettings(channelId, channelHotelId)).thenReturn(profileSettings);

        Map<String, Object> result = accountSettingServiceObtainProxy.getAccountSettings(channelId, channelHotelId);

        assertNotNull(result);
        assertEquals(profileSettings, result);
    }

    @Test
    void whenSaveOrUpdateAccountSettings_thenCallsLocalService() {
        String channelId = "channel1";
        String channelHotelId = "hotel1";
        Map<String, Object> settings = Collections.singletonMap("key", "value");

        accountSettingServiceObtainProxy.saveOrUpdateAccountSettings(channelId, channelHotelId, settings);

        verify(localAccountSettingService).saveOrUpdateAccountSettings(channelId, channelHotelId, settings);
    }

    @Test
    void whenDeleteAccountSettings_thenDoesNothing() {
        accountSettingServiceObtainProxy.deleteAccountSettings("channel1", "hotel1");
        verifyNoInteractions(localAccountSettingService, profileAccountSettingService);
    }
}