package com.derbysoft.next.propertyconnect.channel.task.service.storageservice;

import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelHotelDTO;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelHotelInfo;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelProductsDTO;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelRateInfo;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelRoomInfo;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ItemStatus;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.SyncStatus;
import com.derbysoft.next.propertyconnect.channel.task.service.impl.ctrip.SnapshotService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ChannelInfoStorageServiceProxyTest {

    @Mock
    private ChannelHotelStorageService hotelStorageService;

    @Mock
    private ChannelRoomStorageService roomStorageService;

    @Mock
    private ChannelRateStorageService rateStorageService;

    @Mock
    private SnapshotService snapshotService;

    private ChannelInfoStorageServiceProxy channelInfoStorageServiceProxy;

    @BeforeEach
    void setUp() {
        channelInfoStorageServiceProxy = new ChannelInfoStorageServiceProxy(
                hotelStorageService, roomStorageService, rateStorageService, snapshotService);
    }

    @Test
    void testGetChannelHotel() {
        // Given
        String channelId = "test-channel";
        String channelHotelId = "test-hotel";

        ChannelHotelDTO hotelInfo = createTestHotelDTO();
        ChannelHotelDTO roomInfo = createTestRoomDTO();
        ChannelHotelDTO rateInfo = createTestRateDTO();
        ChannelProductsDTO productsSnapshot = createTestProductsDTO();

        when(hotelStorageService.getChannelHotel(channelId, channelHotelId)).thenReturn(hotelInfo);
        when(roomStorageService.getChannelHotel(channelId, channelHotelId)).thenReturn(roomInfo);
        when(rateStorageService.getChannelHotel(channelId, channelHotelId)).thenReturn(rateInfo);
        when(snapshotService.get(channelId, channelHotelId)).thenReturn(productsSnapshot);

        // When
        ChannelHotelDTO result = channelInfoStorageServiceProxy.getChannelHotel(channelId, channelHotelId);

        // Then
        assertNotNull(result);
        verify(hotelStorageService).getChannelHotel(channelId, channelHotelId);
        verify(roomStorageService).getChannelHotel(channelId, channelHotelId);
        verify(rateStorageService).getChannelHotel(channelId, channelHotelId);
        verify(snapshotService).get(channelId, channelHotelId);
    }

    @Test
    void testSaveIncrementalChannelProduct_WithHotelInfo() {
        // Given
        ChannelHotelDTO channelHotel = createTestHotelDTO();
        ChannelHotelDTO savedHotelInfo = createTestHotelDTO();

        when(hotelStorageService.saveIncrementalChannelProduct(channelHotel)).thenReturn(savedHotelInfo);

        // When
        ChannelHotelDTO result = channelInfoStorageServiceProxy.saveIncrementalChannelProduct(channelHotel);

        // Then
        assertNotNull(result);
        verify(hotelStorageService).saveIncrementalChannelProduct(channelHotel);
        verifyNoInteractions(roomStorageService, rateStorageService, snapshotService);
    }

    @Test
    void testSaveIncrementalChannelProduct_WithAccountSettings() {
        // Given
        ChannelHotelDTO channelHotel = new ChannelHotelDTO();
        channelHotel.setChannelId("test-channel");
        channelHotel.setChannelHotelId("test-hotel");
        channelHotel.setAccountSettings(Map.of("key", "value"));

        ChannelHotelDTO savedHotelInfo = createTestHotelDTO();
        when(hotelStorageService.saveIncrementalChannelProduct(channelHotel)).thenReturn(savedHotelInfo);

        // When
        ChannelHotelDTO result = channelInfoStorageServiceProxy.saveIncrementalChannelProduct(channelHotel);

        // Then
        assertNotNull(result);
        verify(hotelStorageService).saveIncrementalChannelProduct(channelHotel);
    }

    @Test
    void testSaveIncrementalChannelProduct_WithRoomsInfo() {
        // Given
        ChannelHotelDTO channelHotel = createTestRoomDTO();
        ChannelHotelDTO savedRoomInfo = createTestRoomDTO();

        when(roomStorageService.saveIncrementalChannelProduct(channelHotel)).thenReturn(savedRoomInfo);

        // When
        ChannelHotelDTO result = channelInfoStorageServiceProxy.saveIncrementalChannelProduct(channelHotel);

        // Then
        assertNotNull(result);
        verify(roomStorageService).saveIncrementalChannelProduct(channelHotel);
        verifyNoInteractions(hotelStorageService, rateStorageService, snapshotService);
    }

    @Test
    void testSaveIncrementalChannelProduct_WithRatesInfo() {
        // Given
        ChannelHotelDTO channelHotel = createTestRateDTO();
        ChannelHotelDTO savedRateInfo = createTestRateDTO();

        when(rateStorageService.saveIncrementalChannelProduct(channelHotel)).thenReturn(savedRateInfo);

        // When
        ChannelHotelDTO result = channelInfoStorageServiceProxy.saveIncrementalChannelProduct(channelHotel);

        // Then
        assertNotNull(result);
        verify(rateStorageService).saveIncrementalChannelProduct(channelHotel);
        verifyNoInteractions(hotelStorageService, roomStorageService, snapshotService);
    }

    @Test
    void testSaveIncrementalChannelProduct_WithProductsInfo() {
        // Given
        ChannelHotelDTO channelHotel = createTestProductDTO();
        ChannelProductsDTO savedProductInfo = createTestProductsDTO();

        when(snapshotService.incrementalPut(any(ChannelProductsDTO.class))).thenReturn(savedProductInfo);

        // When
        ChannelHotelDTO result = channelInfoStorageServiceProxy.saveIncrementalChannelProduct(channelHotel);

        // Then
        assertNotNull(result);
        verify(snapshotService).incrementalPut(any(ChannelProductsDTO.class));
        verifyNoInteractions(hotelStorageService, roomStorageService, rateStorageService);
    }

    @Test
    void testSaveAllChannelProducts_WithAllInfo() {
        // Given
        ChannelHotelDTO channelHotel = createCompleteChannelHotelDTO();
        
        ChannelHotelDTO savedHotelInfo = createTestHotelDTO();
        ChannelHotelDTO savedRoomInfo = createTestRoomDTO();
        ChannelHotelDTO savedRateInfo = createTestRateDTO();
        ChannelProductsDTO savedProductInfo = createTestProductsDTO();

        when(hotelStorageService.saveAllChannelProducts(channelHotel)).thenReturn(savedHotelInfo);
        when(roomStorageService.saveAllChannelProducts(channelHotel)).thenReturn(savedRoomInfo);
        when(rateStorageService.saveAllChannelProducts(channelHotel)).thenReturn(savedRateInfo);
        when(snapshotService.put(any(ChannelProductsDTO.class))).thenReturn(savedProductInfo);

        // When
        ChannelHotelDTO result = channelInfoStorageServiceProxy.saveAllChannelProducts(channelHotel);

        // Then
        assertNotNull(result);
        verify(hotelStorageService).saveAllChannelProducts(channelHotel);
        verify(roomStorageService).saveAllChannelProducts(channelHotel);
        verify(rateStorageService).saveAllChannelProducts(channelHotel);
        verify(snapshotService).put(any(ChannelProductsDTO.class));
    }

    @Test
    void testDeleteChannelHotel() {
        // Given
        String channelId = "test-channel";
        String channelHotelId = "test-hotel";

        // When
        channelInfoStorageServiceProxy.deleteChannelHotel(channelId, channelHotelId);

        // Then
        verify(hotelStorageService).deleteChannelHotel(channelId, channelHotelId);
        verify(roomStorageService).deleteChannelHotel(channelId, channelHotelId);
        verify(rateStorageService).deleteChannelHotel(channelId, channelHotelId);
        verify(snapshotService).delete(any(ChannelProductsDTO.class));
    }

    @Test
    void testSaveIncrementalChannelProduct_WithNoInfo() {
        // Given
        ChannelHotelDTO channelHotel = new ChannelHotelDTO();
        channelHotel.setChannelId("test-channel");
        channelHotel.setChannelHotelId("test-hotel");

        // When
        ChannelHotelDTO result = channelInfoStorageServiceProxy.saveIncrementalChannelProduct(channelHotel);

        // Then
        assertNotNull(result);
        verifyNoInteractions(hotelStorageService, roomStorageService, rateStorageService, snapshotService);
    }

    private ChannelHotelDTO createTestHotelDTO() {
        ChannelHotelDTO dto = new ChannelHotelDTO();
        dto.setChannelId("test-channel");
        dto.setChannelHotelId("test-hotel");
        dto.setSupplierId("test-supplier");
        
        ChannelHotelInfo hotelInfo = new ChannelHotelInfo("test-hotel");
        hotelInfo.setName("Test Hotel");
        hotelInfo.setStatus(ItemStatus.Actived);
        hotelInfo.setSyncStatus(SyncStatus.SYNCED);
        dto.setHotelInfo(hotelInfo);
        
        return dto;
    }

    private ChannelHotelDTO createTestRoomDTO() {
        ChannelHotelDTO dto = new ChannelHotelDTO();
        dto.setChannelId("test-channel");
        dto.setChannelHotelId("test-hotel");
        
        ChannelRoomInfo roomInfo = new ChannelRoomInfo("room1");
        roomInfo.setName("Test Room");
        roomInfo.setStatus(ItemStatus.Actived);
        dto.setRoomsInfo(Arrays.asList(roomInfo));
        
        return dto;
    }

    private ChannelHotelDTO createTestRateDTO() {
        ChannelHotelDTO dto = new ChannelHotelDTO();
        dto.setChannelId("test-channel");
        dto.setChannelHotelId("test-hotel");
        
        ChannelRateInfo rateInfo = new ChannelRateInfo("rate1");
        rateInfo.setName("Test Rate");
        rateInfo.setStatus(ItemStatus.Actived);
        dto.setRatesInfo(Arrays.asList(rateInfo));
        
        return dto;
    }

    private ChannelHotelDTO createTestProductDTO() {
        ChannelHotelDTO dto = new ChannelHotelDTO();
        dto.setChannelId("test-channel");
        dto.setChannelHotelId("test-hotel");
        
        ChannelProductsDTO.Product product = new ChannelProductsDTO.Product();
        product.setChannelRoomId("room1");
        product.setChannelRateId("rate1");
        product.setStatus("ACTIVATED");
        dto.setProductsInfo(Arrays.asList(product));
        
        return dto;
    }

    private ChannelProductsDTO createTestProductsDTO() {
        ChannelProductsDTO dto = new ChannelProductsDTO();
        dto.setChannelId("test-channel");
        dto.setChannelHotelId("test-hotel");
        dto.setSupplierId("test-supplier");
        
        ChannelProductsDTO.Product product = new ChannelProductsDTO.Product();
        product.setChannelRoomId("room1");
        product.setChannelRateId("rate1");
        product.setStatus("ACTIVATED");
        dto.setChannelProducts(Arrays.asList(product));
        
        return dto;
    }

    private ChannelHotelDTO createCompleteChannelHotelDTO() {
        ChannelHotelDTO dto = new ChannelHotelDTO();
        dto.setChannelId("test-channel");
        dto.setChannelHotelId("test-hotel");
        dto.setSupplierId("test-supplier");
        
        // Hotel info
        ChannelHotelInfo hotelInfo = new ChannelHotelInfo("test-hotel");
        hotelInfo.setName("Test Hotel");
        dto.setHotelInfo(hotelInfo);
        
        // Room info
        ChannelRoomInfo roomInfo = new ChannelRoomInfo("room1");
        roomInfo.setName("Test Room");
        dto.setRoomsInfo(Arrays.asList(roomInfo));
        
        // Rate info
        ChannelRateInfo rateInfo = new ChannelRateInfo("rate1");
        rateInfo.setName("Test Rate");
        dto.setRatesInfo(Arrays.asList(rateInfo));
        
        // Product info
        ChannelProductsDTO.Product product = new ChannelProductsDTO.Product();
        product.setChannelRoomId("room1");
        product.setChannelRateId("rate1");
        dto.setProductsInfo(Arrays.asList(product));
        
        return dto;
    }
}
