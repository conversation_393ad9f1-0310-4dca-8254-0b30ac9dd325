package com.derbysoft.next.propertyconnect.channel.task.service;

import com.derbysoft.next.propertyconnect.channel.task.controller.vo.ChannelVO;
import com.derbysoft.next.propertyconnect.channel.task.service.impl.ChannelAPIServiceImpl;
import com.derbysoft.next.propertyconnect.channel.task.domain.entity.ChannelPO;
import com.derbysoft.next.propertyconnect.channel.task.domain.repository.ChannelRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for ChannelAPIService
 */
@ExtendWith(MockitoExtension.class)
class ChannelAPIServiceTest {

    @Mock
    private ChannelRepository channelRepository;

    @InjectMocks
    private ChannelAPIServiceImpl channelAPIService;

    private ChannelVO testChannelVO;
    private ChannelPO testChannelPO;

    @BeforeEach
    void setUp() {
        testChannelVO = new ChannelVO();
        testChannelVO.setChannelId("TEST_CHANNEL");
        testChannelVO.setChannelName("Test Channel");
        testChannelVO.setState(ChannelVO.State.ENABLED);

        testChannelPO = new ChannelPO();
        testChannelPO.setChannelId("TEST_CHANNEL");
        // ChannelPO doesn't have channelName field, it uses channelDetails map
    }

    @Test
    void testGetChannelSettings_Success() {
        // Given
        String channelId = "TEST_CHANNEL";
        when(channelRepository.findByChannelId(channelId)).thenReturn(Optional.of(testChannelPO));

        // When
        ChannelVO result = channelAPIService.getChannelSettings(channelId);

        // Then
        assertNotNull(result);
        verify(channelRepository).findByChannelId(channelId);
    }

    @Test
    void testGetChannelSettings_ChannelNotFound_ThrowsException() {
        // Given
        String channelId = "NONEXISTENT_CHANNEL";
        when(channelRepository.findByChannelId(channelId)).thenReturn(Optional.empty());

        // When & Then
        assertThrows(RuntimeException.class, () -> {
            channelAPIService.getChannelSettings(channelId);
        });
        verify(channelRepository).findByChannelId(channelId);
    }

    @Test
    void testSaveChannelSettings_NewChannel_Success() {
        // Given
        ChannelVO channelVO = new ChannelVO();
        channelVO.setChannelId("NEW_CHANNEL");
        channelVO.setChannelName("New Channel");

        when(channelRepository.findByChannelId("NEW_CHANNEL")).thenReturn(Optional.empty());
        when(channelRepository.save(any(ChannelPO.class))).thenReturn(testChannelPO);

        // When
        ChannelVO result = channelAPIService.saveChannelSettings(channelVO);

        // Then
        assertNotNull(result);
        verify(channelRepository).findByChannelId("NEW_CHANNEL");
        verify(channelRepository).save(any(ChannelPO.class));
    }

    @Test
    void testSaveChannelSettings_ExistingChannel_Success() {
        // Given
        ChannelVO channelVO = new ChannelVO();
        channelVO.setChannelId("EXISTING_CHANNEL");
        channelVO.setChannelName("Updated Channel");

        when(channelRepository.findByChannelId("EXISTING_CHANNEL")).thenReturn(Optional.of(testChannelPO));
        when(channelRepository.save(any(ChannelPO.class))).thenReturn(testChannelPO);

        // When
        ChannelVO result = channelAPIService.saveChannelSettings(channelVO);

        // Then
        assertNotNull(result);
        verify(channelRepository).findByChannelId("EXISTING_CHANNEL");
        verify(channelRepository).save(any(ChannelPO.class));
    }
}
