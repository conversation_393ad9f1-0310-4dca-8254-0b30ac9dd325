package com.derbysoft.next.propertyconnect.channel.task.service.impl.hoteltonight;

import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelHotelDTO;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelHotelInfo;
import com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.RemoteChannelService;
import com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.adapter.ChannelHotelActivationCustomizeService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Comprehensive unit tests for HotelTonightAdapterService
 * Tests channel identification, procedure customization, and property customization
 */
@ExtendWith(MockitoExtension.class)
class HotelTonightAdapterServiceTest {

    private HotelTonightAdapterService hotelTonightAdapterService;

    @BeforeEach
    void setUp() {
        hotelTonightAdapterService = new HotelTonightAdapterService();
    }

    @Test
    void testHotelTonightAdapterService_ImplementsCorrectInterface() {
        // Given & When & Then
        assertTrue(hotelTonightAdapterService instanceof ChannelHotelActivationCustomizeService,
                "HotelTonightAdapterService should implement ChannelHotelActivationCustomizeService");
    }

    @Test
    void testChannel_ReturnsCorrectChannelName() {
        // Given & When
        String channelName = hotelTonightAdapterService.channel();

        // Then
        assertNotNull(channelName, "Channel name should not be null");
        assertEquals("HOTELTONIGHT", channelName, "Channel name should be HOTELTONIGHT");
    }

    @Test
    void testChannel_ReturnsConsistentValue() {
        // Given & When
        String channelName1 = hotelTonightAdapterService.channel();
        String channelName2 = hotelTonightAdapterService.channel();

        // Then
        assertEquals(channelName1, channelName2, "Channel name should be consistent");
    }

    @Test
    void testCustomizeProperty_WithValidChannelHotel_SetsMarkUpExtension() {
        // Given
        ChannelHotelDTO channelHotelDTO = new ChannelHotelDTO();
        ChannelHotelInfo hotelInfo = new ChannelHotelInfo();
        channelHotelDTO.setHotelInfo(hotelInfo);

        // When
        hotelTonightAdapterService.customizeProperty(channelHotelDTO);

        // Then
        assertNotNull(hotelInfo.getExtensions(), "Extensions should not be null");
        assertTrue(hotelInfo.getExtensions().containsKey("mark_up"), "Should contain mark_up key");
        assertEquals("0", hotelInfo.getExtensions().get("mark_up"), "mark_up should be set to '0'");
    }

    @Test
    void testCustomizeProperty_WithNullHotelInfo_HandlesGracefully() {
        // Given
        ChannelHotelDTO channelHotelDTO = new ChannelHotelDTO();
        channelHotelDTO.setHotelInfo(null);

        // When & Then
        assertThrows(NullPointerException.class, () -> {
            hotelTonightAdapterService.customizeProperty(channelHotelDTO);
        }, "Should throw NullPointerException when hotelInfo is null");
    }

    @Test
    void testCustomizeProperty_WithNullChannelHotel_HandlesGracefully() {
        // Given
        ChannelHotelDTO nullChannelHotel = null;

        // When & Then
        assertThrows(NullPointerException.class, () -> {
            hotelTonightAdapterService.customizeProperty(nullChannelHotel);
        }, "Should throw NullPointerException when channelHotel is null");
    }

    @Test
    void testCustomizeProperty_WithExistingExtensions_ReplacesExtensions() {
        // Given
        ChannelHotelDTO channelHotelDTO = new ChannelHotelDTO();
        ChannelHotelInfo hotelInfo = new ChannelHotelInfo();
        Map<String, Object> existingExtensions = new java.util.HashMap<>();
        existingExtensions.put("existing_key", "existing_value");
        hotelInfo.setExtensions(existingExtensions);
        channelHotelDTO.setHotelInfo(hotelInfo);

        // When
        hotelTonightAdapterService.customizeProperty(channelHotelDTO);

        // Then
        Map<String, Object> extensions = hotelInfo.getExtensions();
        assertEquals(1, extensions.size(), "Should have 1 extension (replaces existing)");
        assertFalse(extensions.containsKey("existing_key"), "Should not preserve existing extension");
        assertEquals("0", extensions.get("mark_up"), "Should set mark_up extension");
    }

    @Test
    void testCustomizeProperty_OverwritesExistingMarkUp() {
        // Given
        ChannelHotelDTO channelHotelDTO = new ChannelHotelDTO();
        ChannelHotelInfo hotelInfo = new ChannelHotelInfo();
        Map<String, Object> existingExtensions = new java.util.HashMap<>();
        existingExtensions.put("mark_up", "5");
        hotelInfo.setExtensions(existingExtensions);
        channelHotelDTO.setHotelInfo(hotelInfo);

        // When
        hotelTonightAdapterService.customizeProperty(channelHotelDTO);

        // Then
        assertEquals("0", hotelInfo.getExtensions().get("mark_up"), 
                "Should overwrite existing mark_up value with '0'");
    }

    @Test
    void testCustomizeProcedure_ReturnsCorrectOperations() {
        // Given & When
        List<RemoteChannelService.Operation> operations = hotelTonightAdapterService.customizeProcedure();

        // Then
        assertNotNull(operations, "Operations list should not be null");
        assertEquals(2, operations.size(), "Should return exactly 2 operations");
        
        assertTrue(operations.contains(RemoteChannelService.Operation.SaveProperty),
                "Should contain SaveProperty operation");
        assertTrue(operations.contains(RemoteChannelService.Operation.TriggerARIRefresh),
                "Should contain TriggerARIRefresh operation");
    }

    @Test
    void testCustomizeProcedure_ReturnsOperationsInCorrectOrder() {
        // Given & When
        List<RemoteChannelService.Operation> operations = hotelTonightAdapterService.customizeProcedure();

        // Then
        assertEquals(RemoteChannelService.Operation.SaveProperty, operations.get(0),
                "First operation should be SaveProperty");
        assertEquals(RemoteChannelService.Operation.TriggerARIRefresh, operations.get(1),
                "Second operation should be TriggerARIRefresh");
    }

    @Test
    void testCustomizeProcedure_DoesNotContainUnnecessaryOperations() {
        // Given & When
        List<RemoteChannelService.Operation> operations = hotelTonightAdapterService.customizeProcedure();

        // Then
        assertFalse(operations.contains(RemoteChannelService.Operation.SaveCredential),
                "Should not contain SaveCredential operation");
        assertFalse(operations.contains(RemoteChannelService.Operation.SaveRoomTypes),
                "Should not contain SaveRoomTypes operation");
        assertFalse(operations.contains(RemoteChannelService.Operation.SaveRatePlans),
                "Should not contain SaveRatePlans operation");
        assertFalse(operations.contains(RemoteChannelService.Operation.SaveProducts),
                "Should not contain SaveProducts operation");
    }

    @Test
    void testService_IsAnnotatedWithSpringService() {
        // Given
        Class<?> serviceClass = HotelTonightAdapterService.class;

        // When
        boolean hasServiceAnnotation = serviceClass.isAnnotationPresent(org.springframework.stereotype.Service.class);

        // Then
        assertTrue(hasServiceAnnotation, "Service should be annotated with @Service");
    }

    @Test
    void testService_CanBeInstantiatedMultipleTimes() {
        // Given & When
        HotelTonightAdapterService service1 = new HotelTonightAdapterService();
        HotelTonightAdapterService service2 = new HotelTonightAdapterService();

        // Then
        assertNotNull(service1, "First instance should not be null");
        assertNotNull(service2, "Second instance should not be null");
        assertNotSame(service1, service2, "Instances should be different objects");
        
        assertEquals(service1.channel(), service2.channel(), "Both instances should return same channel");
        assertEquals(service1.customizeProcedure(), service2.customizeProcedure(),
                "Both instances should return same procedure");
    }

    @Test
    void testService_ThreadSafety() {
        // Given
        int threadCount = 10;
        Thread[] threads = new Thread[threadCount];
        String[] channelResults = new String[threadCount];
        List<RemoteChannelService.Operation>[] procedureResults = new List[threadCount];

        // When
        for (int i = 0; i < threadCount; i++) {
            final int index = i;
            threads[i] = new Thread(() -> {
                channelResults[index] = hotelTonightAdapterService.channel();
                procedureResults[index] = hotelTonightAdapterService.customizeProcedure();
            });
            threads[i].start();
        }

        // Wait for all threads to complete
        for (Thread thread : threads) {
            try {
                thread.join();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }

        // Then
        for (int i = 0; i < threadCount; i++) {
            assertEquals("HOTELTONIGHT", channelResults[i], "All threads should return same channel");
            assertNotNull(procedureResults[i], "All threads should return valid procedure");
            assertEquals(2, procedureResults[i].size(), "All threads should return same procedure size");
        }
    }

    @Test
    void testCustomizeProperty_ThreadSafety() {
        // Given
        int threadCount = 10;
        Thread[] threads = new Thread[threadCount];
        ChannelHotelDTO[] channelHotels = new ChannelHotelDTO[threadCount];
        
        // Initialize test data
        for (int i = 0; i < threadCount; i++) {
            channelHotels[i] = new ChannelHotelDTO();
            ChannelHotelInfo hotelInfo = new ChannelHotelInfo();
            channelHotels[i].setHotelInfo(hotelInfo);
        }

        // When
        for (int i = 0; i < threadCount; i++) {
            final int index = i;
            threads[i] = new Thread(() -> {
                hotelTonightAdapterService.customizeProperty(channelHotels[index]);
            });
            threads[i].start();
        }

        // Wait for all threads to complete
        for (Thread thread : threads) {
            try {
                thread.join();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }

        // Then
        for (int i = 0; i < threadCount; i++) {
            Map<String, Object> extensions = channelHotels[i].getHotelInfo().getExtensions();
            assertNotNull(extensions, "Extensions should not be null for thread " + i);
            assertEquals("0", extensions.get("mark_up"), "mark_up should be set correctly for thread " + i);
        }
    }

    @Test
    void testService_SupportsPolymorphism() {
        // Given
        ChannelHotelActivationCustomizeService service = hotelTonightAdapterService;

        // When
        String channel = service.channel();
        List<RemoteChannelService.Operation> procedure = service.customizeProcedure();

        // Then
        assertEquals("HOTELTONIGHT", channel, "Polymorphic call should work correctly");
        assertNotNull(procedure, "Polymorphic call should return valid procedure");
        assertEquals(2, procedure.size(), "Polymorphic call should return correct procedure size");
    }

    @Test
    void testService_ToString() {
        // Given & When
        String toString = hotelTonightAdapterService.toString();

        // Then
        assertNotNull(toString, "toString should not return null");
        assertTrue(toString.contains("HotelTonightAdapterService"), "toString should contain class name");
    }
}
