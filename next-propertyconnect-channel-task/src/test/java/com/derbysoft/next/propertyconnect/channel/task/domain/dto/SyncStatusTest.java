package com.derbysoft.next.propertyconnect.channel.task.domain.dto;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class SyncStatusTest {

    @Test
    void testIsSuccess() {
        // Test success statuses
        assertTrue(SyncStatus.SYNCED.isSuccess());
        assertTrue(SyncStatus.IGNORED.isSuccess());

        // Test non-success statuses
        assertFalse(SyncStatus.DRAFT.isSuccess());
        assertFalse(SyncStatus.PREPROCESS_FAILED.isSuccess());
        assertFalse(SyncStatus.SUBMITTED.isSuccess());
        assertFalse(SyncStatus.SUBMIT_FAILED.isSuccess());
        assertFalse(SyncStatus.SYNC_FAILED.isSuccess());
    }

    @Test
    void testIsFail() {
        // Test fail statuses
        assertTrue(SyncStatus.SYNC_FAILED.isFail());
        assertTrue(SyncStatus.SUBMIT_FAILED.isFail());
        assertTrue(SyncStatus.PREPROCESS_FAILED.isFail());

        // Test non-fail statuses
        assertFalse(SyncStatus.DRAFT.isFail());
        assertFalse(SyncStatus.SUBMITTED.isFail());
        assertFalse(SyncStatus.SYNCED.isFail());
        assertFalse(SyncStatus.IGNORED.isFail());
    }

    @Test
    void testIsFinalStatus() {
        // Test final statuses (success or fail)
        assertTrue(SyncStatus.SYNCED.isFinalStatus());
        assertTrue(SyncStatus.IGNORED.isFinalStatus());
        assertTrue(SyncStatus.SYNC_FAILED.isFinalStatus());
        assertTrue(SyncStatus.SUBMIT_FAILED.isFinalStatus());
        assertTrue(SyncStatus.PREPROCESS_FAILED.isFinalStatus());

        // Test non-final statuses
        assertFalse(SyncStatus.DRAFT.isFinalStatus());
        assertFalse(SyncStatus.SUBMITTED.isFinalStatus());
    }

    @Test
    void testIsProcessingStatus() {
        // Test processing statuses
        assertTrue(SyncStatus.DRAFT.isProcessingStatus());
        assertTrue(SyncStatus.SUBMITTED.isProcessingStatus());

        // Test non-processing statuses
        assertFalse(SyncStatus.PREPROCESS_FAILED.isProcessingStatus());
        assertFalse(SyncStatus.SUBMIT_FAILED.isProcessingStatus());
        assertFalse(SyncStatus.SYNCED.isProcessingStatus());
        assertFalse(SyncStatus.SYNC_FAILED.isProcessingStatus());
        assertFalse(SyncStatus.IGNORED.isProcessingStatus());
    }

    @Test
    void testAllEnumValues() {
        // Ensure all enum values are tested
        SyncStatus[] allStatuses = SyncStatus.values();
        assertEquals(7, allStatuses.length);

        // Verify all enum values exist
        assertNotNull(SyncStatus.valueOf("DRAFT"));
        assertNotNull(SyncStatus.valueOf("PREPROCESS_FAILED"));
        assertNotNull(SyncStatus.valueOf("SUBMITTED"));
        assertNotNull(SyncStatus.valueOf("SUBMIT_FAILED"));
        assertNotNull(SyncStatus.valueOf("SYNCED"));
        assertNotNull(SyncStatus.valueOf("SYNC_FAILED"));
        assertNotNull(SyncStatus.valueOf("IGNORED"));
    }

    @Test
    void testEnumToString() {
        assertEquals("DRAFT", SyncStatus.DRAFT.toString());
        assertEquals("PREPROCESS_FAILED", SyncStatus.PREPROCESS_FAILED.toString());
        assertEquals("SUBMITTED", SyncStatus.SUBMITTED.toString());
        assertEquals("SUBMIT_FAILED", SyncStatus.SUBMIT_FAILED.toString());
        assertEquals("SYNCED", SyncStatus.SYNCED.toString());
        assertEquals("SYNC_FAILED", SyncStatus.SYNC_FAILED.toString());
        assertEquals("IGNORED", SyncStatus.IGNORED.toString());
    }

    @Test
    void testEnumName() {
        assertEquals("DRAFT", SyncStatus.DRAFT.name());
        assertEquals("PREPROCESS_FAILED", SyncStatus.PREPROCESS_FAILED.name());
        assertEquals("SUBMITTED", SyncStatus.SUBMITTED.name());
        assertEquals("SUBMIT_FAILED", SyncStatus.SUBMIT_FAILED.name());
        assertEquals("SYNCED", SyncStatus.SYNCED.name());
        assertEquals("SYNC_FAILED", SyncStatus.SYNC_FAILED.name());
        assertEquals("IGNORED", SyncStatus.IGNORED.name());
    }

    @Test
    void testEnumOrdinal() {
        assertEquals(0, SyncStatus.DRAFT.ordinal());
        assertEquals(1, SyncStatus.PREPROCESS_FAILED.ordinal());
        assertEquals(2, SyncStatus.SUBMITTED.ordinal());
        assertEquals(3, SyncStatus.SUBMIT_FAILED.ordinal());
        assertEquals(4, SyncStatus.SYNCED.ordinal());
        assertEquals(5, SyncStatus.SYNC_FAILED.ordinal());
        assertEquals(6, SyncStatus.IGNORED.ordinal());
    }

    @Test
    void testStatusLogic() {
        // Test that success and fail are mutually exclusive
        for (SyncStatus status : SyncStatus.values()) {
            if (status.isSuccess()) {
                assertFalse(status.isFail(), "Status " + status + " cannot be both success and fail");
                assertFalse(status.isProcessingStatus(), "Status " + status + " cannot be both success and processing");
            }
            if (status.isFail()) {
                assertFalse(status.isSuccess(), "Status " + status + " cannot be both fail and success");
                assertFalse(status.isProcessingStatus(), "Status " + status + " cannot be both fail and processing");
            }
            if (status.isProcessingStatus()) {
                assertFalse(status.isSuccess(), "Status " + status + " cannot be both processing and success");
                assertFalse(status.isFail(), "Status " + status + " cannot be both processing and fail");
            }
        }
    }

    @Test
    void testFinalStatusLogic() {
        // Test that final status is either success or fail
        for (SyncStatus status : SyncStatus.values()) {
            if (status.isFinalStatus()) {
                assertTrue(status.isSuccess() || status.isFail(), 
                        "Final status " + status + " must be either success or fail");
            }
            if (status.isProcessingStatus()) {
                assertFalse(status.isFinalStatus(), 
                        "Processing status " + status + " cannot be final");
            }
        }
    }

    @Test
    void testSpecificStatusBehavior() {
        // Test DRAFT
        assertTrue(SyncStatus.DRAFT.isProcessingStatus());
        assertFalse(SyncStatus.DRAFT.isFinalStatus());
        assertFalse(SyncStatus.DRAFT.isSuccess());
        assertFalse(SyncStatus.DRAFT.isFail());

        // Test SUBMITTED
        assertTrue(SyncStatus.SUBMITTED.isProcessingStatus());
        assertFalse(SyncStatus.SUBMITTED.isFinalStatus());
        assertFalse(SyncStatus.SUBMITTED.isSuccess());
        assertFalse(SyncStatus.SUBMITTED.isFail());

        // Test SYNCED
        assertTrue(SyncStatus.SYNCED.isSuccess());
        assertTrue(SyncStatus.SYNCED.isFinalStatus());
        assertFalse(SyncStatus.SYNCED.isProcessingStatus());
        assertFalse(SyncStatus.SYNCED.isFail());

        // Test IGNORED
        assertTrue(SyncStatus.IGNORED.isSuccess());
        assertTrue(SyncStatus.IGNORED.isFinalStatus());
        assertFalse(SyncStatus.IGNORED.isProcessingStatus());
        assertFalse(SyncStatus.IGNORED.isFail());

        // Test SYNC_FAILED
        assertTrue(SyncStatus.SYNC_FAILED.isFail());
        assertTrue(SyncStatus.SYNC_FAILED.isFinalStatus());
        assertFalse(SyncStatus.SYNC_FAILED.isProcessingStatus());
        assertFalse(SyncStatus.SYNC_FAILED.isSuccess());

        // Test SUBMIT_FAILED
        assertTrue(SyncStatus.SUBMIT_FAILED.isFail());
        assertTrue(SyncStatus.SUBMIT_FAILED.isFinalStatus());
        assertFalse(SyncStatus.SUBMIT_FAILED.isProcessingStatus());
        assertFalse(SyncStatus.SUBMIT_FAILED.isSuccess());

        // Test PREPROCESS_FAILED
        assertTrue(SyncStatus.PREPROCESS_FAILED.isFail());
        assertTrue(SyncStatus.PREPROCESS_FAILED.isFinalStatus());
        assertFalse(SyncStatus.PREPROCESS_FAILED.isProcessingStatus());
        assertFalse(SyncStatus.PREPROCESS_FAILED.isSuccess());
    }
}
