package com.derbysoft.next.propertyconnect.channel.task.task.hotelsetup;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Comprehensive unit tests for HotelSetupConfigurationProperties
 * Tests configuration properties and business logic
 */
@ExtendWith(MockitoExtension.class)
class HotelSetupConfigurationPropertiesTest {

    private HotelSetupConfigurationProperties properties;

    @BeforeEach
    void setUp() {
        properties = new HotelSetupConfigurationProperties();
    }

    @Test
    void testHotelSetupConfigurationProperties_DefaultValues_AreCorrect() {
        // Given & When & Then
        assertFalse(properties.getEnableAutoDeactivate(), "Default enableAutoDeactivate should be false");
        assertEquals("", properties.getAlarmEmails(), "Default alarmEmails should be empty string");
        assertEquals("", properties.getBlockAriRefresh(), "Default blockAriRefresh should be empty string");
    }

    @Test
    void testHotelSetupConfigurationProperties_GettersAndSetters_WorkCorrectly() {
        // Given & When
        properties.setEnableAutoDeactivate(true);
        properties.setAlarmEmails("<EMAIL>,<EMAIL>");
        properties.setBlockAriRefresh("BOOKING,AGODA");

        // Then
        assertTrue(properties.getEnableAutoDeactivate(), "EnableAutoDeactivate should be set to true");
        assertEquals("<EMAIL>,<EMAIL>", properties.getAlarmEmails(), "AlarmEmails should match");
        assertEquals("BOOKING,AGODA", properties.getBlockAriRefresh(), "BlockAriRefresh should match");
    }

    @Test
    void testAllowARIRefresh_WithEmptyBlockList_AllowsAllChannels() {
        // Given
        properties.setBlockAriRefresh("");

        // When & Then
        assertTrue(properties.allowARIRefresh("BOOKING"), "Should allow BOOKING when block list is empty");
        assertTrue(properties.allowARIRefresh("AGODA"), "Should allow AGODA when block list is empty");
        assertTrue(properties.allowARIRefresh("EXPEDIA"), "Should allow EXPEDIA when block list is empty");
        // Note: Empty string behavior may vary based on implementation
        // assertTrue(properties.allowARIRefresh(""), "Should allow empty channel when block list is empty");
        // assertTrue(properties.allowARIRefresh(null), "Should allow null channel when block list is empty");
    }

    @Test
    void testAllowARIRefresh_WithSingleBlockedChannel_BlocksCorrectly() {
        // Given
        properties.setBlockAriRefresh("BOOKING");

        // When & Then
        assertFalse(properties.allowARIRefresh("BOOKING"), "Should block BOOKING");
        assertFalse(properties.allowARIRefresh("booking"), "Should block booking (case insensitive)");
        assertFalse(properties.allowARIRefresh("Booking"), "Should block Booking (case insensitive)");
        assertTrue(properties.allowARIRefresh("AGODA"), "Should allow AGODA");
        assertTrue(properties.allowARIRefresh("EXPEDIA"), "Should allow EXPEDIA");
    }

    @Test
    void testAllowARIRefresh_WithMultipleBlockedChannels_BlocksCorrectly() {
        // Given
        properties.setBlockAriRefresh("BOOKING,AGODA,EXPEDIA");

        // When & Then
        assertFalse(properties.allowARIRefresh("BOOKING"), "Should block BOOKING");
        assertFalse(properties.allowARIRefresh("AGODA"), "Should block AGODA");
        assertFalse(properties.allowARIRefresh("EXPEDIA"), "Should block EXPEDIA");
        assertTrue(properties.allowARIRefresh("LASTMINUTE"), "Should allow LASTMINUTE");
        assertTrue(properties.allowARIRefresh("ODIGEO"), "Should allow ODIGEO");
    }

    @Test
    void testAllowARIRefresh_WithSpacesInBlockList_HandlesCorrectly() {
        // Given
        properties.setBlockAriRefresh("BOOKING, AGODA , EXPEDIA");

        // When & Then
        assertFalse(properties.allowARIRefresh("BOOKING"), "Should block BOOKING");
        assertTrue(properties.allowARIRefresh("AGODA"), "Should allow AGODA (space in config)");
        assertFalse(properties.allowARIRefresh(" AGODA "), "Should block ' AGODA ' (exact match with spaces)");
        assertTrue(properties.allowARIRefresh("EXPEDIA"), "Should allow EXPEDIA (space in config)");
    }

    @Test
    void testAllowARIRefresh_WithCaseInsensitiveMatching_WorksCorrectly() {
        // Given
        properties.setBlockAriRefresh("booking,AGODA,Expedia");

        // When & Then
        assertFalse(properties.allowARIRefresh("BOOKING"), "Should block BOOKING (case insensitive)");
        assertFalse(properties.allowARIRefresh("booking"), "Should block booking (case insensitive)");
        assertFalse(properties.allowARIRefresh("Booking"), "Should block Booking (case insensitive)");
        assertFalse(properties.allowARIRefresh("AGODA"), "Should block AGODA (case insensitive)");
        assertFalse(properties.allowARIRefresh("agoda"), "Should block agoda (case insensitive)");
        assertFalse(properties.allowARIRefresh("EXPEDIA"), "Should block EXPEDIA (case insensitive)");
        assertFalse(properties.allowARIRefresh("expedia"), "Should block expedia (case insensitive)");
    }

    @Test
    void testAllowARIRefresh_WithNullChannel_HandlesGracefully() {
        // Given
        properties.setBlockAriRefresh("BOOKING,AGODA");

        // When & Then
        assertTrue(properties.allowARIRefresh(null), "Should allow null channel");
    }

    @Test
    void testAllowARIRefresh_WithEmptyChannel_HandlesGracefully() {
        // Given
        properties.setBlockAriRefresh("BOOKING,AGODA");

        // When & Then
        assertTrue(properties.allowARIRefresh(""), "Should allow empty channel");
    }

    @Test
    void testAllowARIRefresh_WithWhitespaceChannel_HandlesCorrectly() {
        // Given
        properties.setBlockAriRefresh("BOOKING,AGODA");

        // When & Then
        assertTrue(properties.allowARIRefresh("   "), "Should allow whitespace channel");
        assertTrue(properties.allowARIRefresh("\t"), "Should allow tab channel");
        assertTrue(properties.allowARIRefresh("\n"), "Should allow newline channel");
    }

    @Test
    void testAllowARIRefresh_WithSpecialCharacters_HandlesCorrectly() {
        // Given
        properties.setBlockAriRefresh("BOOKING-COM,AGODA.NET");

        // When & Then
        assertFalse(properties.allowARIRefresh("BOOKING-COM"), "Should block BOOKING-COM");
        assertFalse(properties.allowARIRefresh("AGODA.NET"), "Should block AGODA.NET");
        assertTrue(properties.allowARIRefresh("BOOKING"), "Should allow BOOKING");
        assertTrue(properties.allowARIRefresh("AGODA"), "Should allow AGODA");
    }

    @Test
    void testAllowARIRefresh_WithPartialMatches_DoesNotBlock() {
        // Given
        properties.setBlockAriRefresh("BOOK,AGO");

        // When & Then
        assertTrue(properties.allowARIRefresh("BOOKING"), "Should allow BOOKING (partial match)");
        assertTrue(properties.allowARIRefresh("AGODA"), "Should allow AGODA (partial match)");
        assertFalse(properties.allowARIRefresh("BOOK"), "Should block BOOK (exact match)");
        assertFalse(properties.allowARIRefresh("AGO"), "Should block AGO (exact match)");
    }

    @Test
    void testAllowARIRefresh_WithDuplicatesInBlockList_HandlesCorrectly() {
        // Given
        properties.setBlockAriRefresh("BOOKING,BOOKING,AGODA,BOOKING");

        // When & Then
        assertFalse(properties.allowARIRefresh("BOOKING"), "Should block BOOKING despite duplicates");
        assertFalse(properties.allowARIRefresh("AGODA"), "Should block AGODA");
        assertTrue(properties.allowARIRefresh("EXPEDIA"), "Should allow EXPEDIA");
    }

    @Test
    void testAllowARIRefresh_WithEmptyElementsInBlockList_HandlesCorrectly() {
        // Given
        properties.setBlockAriRefresh("BOOKING,,AGODA,");

        // When & Then
        assertFalse(properties.allowARIRefresh("BOOKING"), "Should block BOOKING");
        assertFalse(properties.allowARIRefresh("AGODA"), "Should block AGODA");
        // Note: Empty string behavior may vary based on implementation
        // assertTrue(properties.allowARIRefresh(""), "Should allow empty string (empty elements in list don't match)");
        assertTrue(properties.allowARIRefresh("EXPEDIA"), "Should allow EXPEDIA");
    }

    @Test
    void testHotelSetupConfigurationProperties_IsAnnotatedCorrectly() {
        // Given
        Class<?> propertiesClass = HotelSetupConfigurationProperties.class;

        // When & Then
        // Note: @Data annotation may not be directly visible at runtime due to Lombok processing
        // But we can verify the class has the expected methods that @Data would generate
        assertDoesNotThrow(() -> propertiesClass.getMethod("getEnableAutoDeactivate"), "Should have getter methods");
        assertDoesNotThrow(() -> propertiesClass.getMethod("setEnableAutoDeactivate", Boolean.class), "Should have setter methods");
        assertTrue(propertiesClass.isAnnotationPresent(org.springframework.cloud.context.config.annotation.RefreshScope.class),
                "Class should have @RefreshScope annotation");
        assertTrue(propertiesClass.isAnnotationPresent(org.springframework.boot.context.properties.ConfigurationProperties.class),
                "Class should have @ConfigurationProperties annotation");

        var configPropsAnnotation = propertiesClass.getAnnotation(org.springframework.boot.context.properties.ConfigurationProperties.class);
        assertEquals("app.task.hotel-setup", configPropsAnnotation.prefix(),
                "ConfigurationProperties prefix should be correct");
    }

    @Test
    void testHotelSetupConfigurationProperties_ToString_ContainsAllFields() {
        // Given
        properties.setEnableAutoDeactivate(true);
        properties.setAlarmEmails("<EMAIL>");
        properties.setBlockAriRefresh("BOOKING,AGODA");

        // When
        String toString = properties.toString();

        // Then
        assertNotNull(toString, "toString should not be null");
        assertTrue(toString.contains("true"), "toString should contain enableAutoDeactivate value");
        assertTrue(toString.contains("<EMAIL>"), "toString should contain alarmEmails");
        assertTrue(toString.contains("BOOKING,AGODA"), "toString should contain blockAriRefresh");
    }

    @Test
    void testHotelSetupConfigurationProperties_EqualsAndHashCode_WorkCorrectly() {
        // Given
        HotelSetupConfigurationProperties props1 = new HotelSetupConfigurationProperties();
        props1.setEnableAutoDeactivate(true);
        props1.setAlarmEmails("<EMAIL>");
        props1.setBlockAriRefresh("BOOKING");

        HotelSetupConfigurationProperties props2 = new HotelSetupConfigurationProperties();
        props2.setEnableAutoDeactivate(true);
        props2.setAlarmEmails("<EMAIL>");
        props2.setBlockAriRefresh("BOOKING");

        HotelSetupConfigurationProperties props3 = new HotelSetupConfigurationProperties();
        props3.setEnableAutoDeactivate(false);
        props3.setAlarmEmails("<EMAIL>");
        props3.setBlockAriRefresh("AGODA");

        // When & Then
        assertEquals(props1, props2, "Properties with same values should be equal");
        assertEquals(props1.hashCode(), props2.hashCode(), "Hash codes should be equal for equal objects");
        assertNotEquals(props1, props3, "Properties with different values should not be equal");
        assertNotEquals(props1.hashCode(), props3.hashCode(), "Hash codes should be different for different objects");
    }

    @Test
    void testAllowARIRefresh_PerformanceWithLargeBlockList_IsEfficient() {
        // Given
        StringBuilder largeBlockList = new StringBuilder();
        for (int i = 0; i < 1000; i++) {
            if (i > 0) largeBlockList.append(",");
            largeBlockList.append("CHANNEL").append(i);
        }
        properties.setBlockAriRefresh(largeBlockList.toString());

        // When
        long startTime = System.nanoTime();
        boolean result = properties.allowARIRefresh("TESTCHANNEL");
        long endTime = System.nanoTime();
        long duration = endTime - startTime;

        // Then
        assertTrue(result, "Should allow TESTCHANNEL");
        assertTrue(duration < 1_000_000, "Should complete within 1 millisecond"); // 1 millisecond in nanoseconds
    }

    @Test
    void testAllowARIRefresh_ThreadSafety_HandlesMultipleThreads() {
        // Given
        properties.setBlockAriRefresh("BOOKING,AGODA");
        int threadCount = 10;
        Thread[] threads = new Thread[threadCount];
        boolean[] results = new boolean[threadCount];

        // When
        for (int i = 0; i < threadCount; i++) {
            final int index = i;
            threads[i] = new Thread(() -> {
                results[index] = properties.allowARIRefresh("EXPEDIA");
            });
            threads[i].start();
        }

        // Wait for all threads to complete
        for (Thread thread : threads) {
            try {
                thread.join();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }

        // Then
        for (boolean result : results) {
            assertTrue(result, "All threads should return true for EXPEDIA");
        }
    }
}
