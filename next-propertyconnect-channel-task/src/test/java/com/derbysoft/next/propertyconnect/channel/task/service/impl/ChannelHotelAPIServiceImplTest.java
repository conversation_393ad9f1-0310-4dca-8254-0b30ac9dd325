package com.derbysoft.next.propertyconnect.channel.task.service.impl;

import com.derbysoft.next.commons.core.logsupport.handler.PerfLogHandler;
import com.derbysoft.next.propertyconnect.channel.task.client.WebhookAPI;
import com.derbysoft.next.propertyconnect.channel.task.config.HotelSetupConnectionWhiteListConfig;
import com.derbysoft.next.propertyconnect.channel.task.controller.vo.hotel.ChannelHotelVO;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelHotelDTO;
import com.derbysoft.next.propertyconnect.channel.task.service.ChannelHotelSetupExecutor;
import com.derbysoft.next.propertyconnect.channel.task.service.RemoteService;
import com.derbysoft.next.propertyconnect.channel.task.service.RequestSetupProcedure;
import com.derbysoft.next.propertyconnect.channel.task.service.mapping.PredictionReportService;
import com.derbysoft.next.propertyconnect.channel.task.service.storageservice.ChannelInfoStorageService;
import com.derbysoft.next.propertyconnect.channel.task.task.hotelsetup.DistributorHotelSetupTask;
import com.derbysoft.next.propertyconnect.channel.task.task.hotelsetup.HotelSetupConfigurationProperties;
import com.derbysoft.next.propertyconnect.channel.task.service.impl.ChannelHotelAPIServiceImpl.SingleHotelSetupTask;
import com.derbysoft.next.propertyconnect.channel.task.config.schedulecenter.ChannelServiceTask;
import com.derbysoft.schedulecenter.rpc.client.HttpScheduleCenterService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedConstruction;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.ObjectProvider;
import reactor.core.publisher.Mono;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.times;

@ExtendWith(MockitoExtension.class)
class ChannelHotelAPIServiceImplTest {

    @InjectMocks
    private ChannelHotelAPIServiceImpl channelHotelAPIService;

    @Mock
    private RequestSetupProcedure requestSetupProcedure;
    @Mock
    private ChannelInfoStorageService hotelStorageService;
    @Mock
    private ChannelHotelSetupExecutor channelHotelSetupExecutor;
    @Mock
    private RemoteService remoteService;
    @Mock
    private HotelSetupConfigurationProperties properties;
    @Mock
    private ObjectProvider<HttpScheduleCenterService> httpScheduleCenterServiceProvider;
    @Mock
    private DistributorHotelSetupTask distributorHotelSetupTask;
    @Mock
    private HotelSetupConnectionWhiteListConfig supplierWhiteList;
    @Mock
    private PredictionReportService predictionReportService;
    @Mock
    private WebhookAPI webhookAPI;

    @BeforeEach
    void setUp() {
        ChannelHotelDTO mockResult = new ChannelHotelDTO();
        mockResult.setChannelId("channel1");
        mockResult.setChannelHotelId("hotel1");
        mockResult.setSupplierId("supplier1");
        lenient().when(channelHotelSetupExecutor.execute(any(), any())).thenReturn(Mono.just(mockResult));
    }

    @Test
    void givenValidHotel_whenSaveChannelHotel_thenSavesAndExecutes() {
        String channelId = "channel1";
        String channelHotelId = "hotel1";
        ChannelHotelVO hotelVO = new ChannelHotelVO();
        hotelVO.setSupplierId("supplier1");

        // Mock PerfLogHandler
        PerfLogHandler mockPerfHandler = mock(PerfLogHandler.class);
        when(mockPerfHandler.getToken()).thenReturn("test-token");

        try (MockedStatic<PerfLogHandler> mockedStatic = mockStatic(PerfLogHandler.class)) {
            mockedStatic.when(PerfLogHandler::currentHandler).thenReturn(mockPerfHandler);

            when(supplierWhiteList.isProhibitedConnection(any(), any())).thenReturn(false);
            when(hotelStorageService.saveIncrementalChannelProduct(any())).thenReturn(new ChannelHotelDTO());

            ChannelHotelVO result = channelHotelAPIService.saveChannelHotel(channelId, channelHotelId, hotelVO, null, true, false, false, false);

            assertNotNull(result);
            verify(hotelStorageService, times(2)).saveIncrementalChannelProduct(any());
            verify(channelHotelSetupExecutor).execute(any(), any());
        }
    }

    @Test
    void whenDeleteChannelHotel_thenDeletesFromStorage() {
        String channelId = "channel1";
        String channelHotelId = "hotel1";

        channelHotelAPIService.deleteChannelHotel(channelId, channelHotelId);

        verify(hotelStorageService).deleteChannelHotel(channelId, channelHotelId);
    }

    @Test
    void givenScheduleCenterAvailable_whenSyncChannelHotel_thenTriggersNewTask() {
        String channelId = "channel1";
        String channelHotelId = "hotel1";
        String supplierId = "supplier1";

        // Mock PerfLogHandler
        PerfLogHandler mockPerfHandler = mock(PerfLogHandler.class);
        when(mockPerfHandler.getToken()).thenReturn("test-token");

        try (MockedStatic<PerfLogHandler> mockedStatic = mockStatic(PerfLogHandler.class)) {
            mockedStatic.when(PerfLogHandler::currentHandler).thenReturn(mockPerfHandler);

            HttpScheduleCenterService scheduleCenterService = mock(HttpScheduleCenterService.class);
            when(httpScheduleCenterServiceProvider.getIfAvailable()).thenReturn(scheduleCenterService);
            when(supplierWhiteList.isProhibitedConnection(any(), any())).thenReturn(false);

            ChannelHotelVO result = channelHotelAPIService.syncChannelHotel(channelId, channelHotelId, null, null, false, supplierId, new ChannelHotelVO());

            assertNotNull(result);
            verify(predictionReportService).saveChannelProductMappingSnapshotAndReport(supplierId, channelId, channelHotelId);
            verify(httpScheduleCenterServiceProvider).getIfAvailable();
        }
    }

    @Test
    void givenScheduleCenterNotAvailable_whenSyncChannelHotel_thenExecutesDistributorTask() {
        String channelId = "channel1";
        String channelHotelId = "hotel1";
        String supplierId = "supplier1";

        // Mock PerfLogHandler
        PerfLogHandler mockPerfHandler = mock(PerfLogHandler.class);
        when(mockPerfHandler.getToken()).thenReturn("test-token");

        try (MockedStatic<PerfLogHandler> mockedStatic = mockStatic(PerfLogHandler.class)) {
            mockedStatic.when(PerfLogHandler::currentHandler).thenReturn(mockPerfHandler);

            when(httpScheduleCenterServiceProvider.getIfAvailable()).thenReturn(null);
            when(supplierWhiteList.isProhibitedConnection(any(), any())).thenReturn(false);

            ChannelHotelVO result = channelHotelAPIService.syncChannelHotel(channelId, channelHotelId, null, null, false, supplierId, new ChannelHotelVO());

            assertNotNull(result);
            verify(predictionReportService).saveChannelProductMappingSnapshotAndReport(supplierId, channelId, channelHotelId);
            verify(distributorHotelSetupTask).doExecute(any(com.derbysoft.next.propertyconnect.channel.task.config.schedulecenter.ChannelServiceTask.class));
        }
    }

    @Test
    void givenSupplierIsProhibited_whenSaveChannelHotel_thenReturnsAsyncResponseAndSkipsExecution() {
        String channelId = "channel1";
        String channelHotelId = "hotel1";
        ChannelHotelVO hotelVO = new ChannelHotelVO();
        hotelVO.setSupplierId("prohibitedSupplier");

        PerfLogHandler mockPerfHandler = mock(PerfLogHandler.class);
        when(mockPerfHandler.getToken()).thenReturn("test-token");

        try (MockedStatic<PerfLogHandler> mockedStatic = mockStatic(PerfLogHandler.class)) {
            mockedStatic.when(PerfLogHandler::currentHandler).thenReturn(mockPerfHandler);

            when(supplierWhiteList.isProhibitedConnection("prohibitedSupplier", channelId)).thenReturn(true);

            ChannelHotelVO result = channelHotelAPIService.saveChannelHotel(channelId, channelHotelId, hotelVO, null, true, false, false, false);

            assertNotNull(result);
            verify(hotelStorageService, never()).saveIncrementalChannelProduct(any());
            verify(channelHotelSetupExecutor, never()).execute(any(), any());
        }
    }

    @Test
    void givenSaveOnlyIsTrue_whenSaveChannelHotel_thenSavesAndReturnsImmediately() {
        String channelId = "channel1";
        String channelHotelId = "hotel1";
        ChannelHotelVO hotelVO = new ChannelHotelVO();
        hotelVO.setSupplierId("supplier1");

        when(supplierWhiteList.isProhibitedConnection(any(), any())).thenReturn(false);
        when(hotelStorageService.saveIncrementalChannelProduct(any())).thenReturn(new ChannelHotelDTO());

        ChannelHotelVO result = channelHotelAPIService.saveChannelHotel(channelId, channelHotelId, hotelVO, null, true, false, false, true);

        assertNotNull(result);
        verify(hotelStorageService, times(1)).saveIncrementalChannelProduct(any());
        verify(channelHotelSetupExecutor, never()).execute(any(), any());
    }

    @Test
    void givenSyncModeIsFalse_whenSaveChannelHotel_thenExecutesAsynchronously() {
        String channelId = "channel1";
        String channelHotelId = "hotel1";
        ChannelHotelVO hotelVO = new ChannelHotelVO();
        hotelVO.setSupplierId("supplier1");

        PerfLogHandler mockPerfHandler = mock(PerfLogHandler.class);
        when(mockPerfHandler.getToken()).thenReturn("test-token");

        try (MockedStatic<PerfLogHandler> mockedStatic = mockStatic(PerfLogHandler.class)) {
            mockedStatic.when(PerfLogHandler::currentHandler).thenReturn(mockPerfHandler);

            when(supplierWhiteList.isProhibitedConnection(any(), any())).thenReturn(false);
            when(hotelStorageService.saveIncrementalChannelProduct(any())).thenReturn(new ChannelHotelDTO());

            ChannelHotelVO result = channelHotelAPIService.saveChannelHotel(channelId, channelHotelId, hotelVO, null, false, false, false, false);

            assertNotNull(result);
            verify(hotelStorageService, times(1)).saveIncrementalChannelProduct(any());
            verify(channelHotelSetupExecutor).execute(any(), any());
        }
    }

    @Test
    void givenRetryIsTrue_whenSaveChannelHotel_thenHandlesRetryLogic() {
        String channelId = "channel1";
        String channelHotelId = "hotel1";
        ChannelHotelVO hotelVO = new ChannelHotelVO();
        hotelVO.setSupplierId("supplier1");

        ChannelHotelDTO localHotel = new ChannelHotelDTO();
        localHotel.setHotelInfo(new com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelHotelInfo());

        // Mock PerfLogHandler
        PerfLogHandler mockPerfHandler = mock(PerfLogHandler.class);
        when(mockPerfHandler.getToken()).thenReturn("test-token");

        try (MockedStatic<PerfLogHandler> mockedStatic = mockStatic(PerfLogHandler.class)) {
            mockedStatic.when(PerfLogHandler::currentHandler).thenReturn(mockPerfHandler);

            when(supplierWhiteList.isProhibitedConnection(any(), any())).thenReturn(false);
            when(hotelStorageService.getChannelHotel(channelId, channelHotelId)).thenReturn(localHotel);
            when(hotelStorageService.saveIncrementalChannelProduct(any())).thenReturn(new ChannelHotelDTO());

            channelHotelAPIService.saveChannelHotel(channelId, channelHotelId, hotelVO, null, true, true, false, false);

            verify(hotelStorageService).getChannelHotel(channelId, channelHotelId);
        }
    }

    @Mock
    private com.fasterxml.jackson.databind.ObjectMapper objectMapper;

    @Test
    void givenExecutionFails_whenSaveChannelHotel_thenSendsNotifications() {
        String channelId = "channel1";
        String channelHotelId = "hotel1";
        ChannelHotelVO hotelVO = new ChannelHotelVO();
        hotelVO.setSupplierId("supplier1");

        ChannelHotelDTO failedHotelResult = new ChannelHotelDTO();
        failedHotelResult.setChannelId(channelId);
        failedHotelResult.setChannelHotelId(channelHotelId);
        com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelHotelInfo hotelInfo = new com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelHotelInfo();
        hotelInfo.setSyncStatus(com.derbysoft.next.propertyconnect.channel.task.domain.dto.SyncStatus.SYNC_FAILED);
        failedHotelResult.setHotelInfo(hotelInfo);

        PerfLogHandler mockPerfHandler = mock(PerfLogHandler.class);
        when(mockPerfHandler.getToken()).thenReturn("test-token");

        try (MockedStatic<PerfLogHandler> mockedStatic = mockStatic(PerfLogHandler.class)) {
            mockedStatic.when(PerfLogHandler::currentHandler).thenReturn(mockPerfHandler);

            lenient().when(supplierWhiteList.isProhibitedConnection(any(), any())).thenReturn(false);
            lenient().when(hotelStorageService.saveIncrementalChannelProduct(any())).thenReturn(new ChannelHotelDTO());
            lenient().when(channelHotelSetupExecutor.execute(any(), any())).thenReturn(Mono.just(failedHotelResult));
            lenient().when(properties.getAlarmEmails()).thenReturn("<EMAIL>");

            // Use syncMode = true to ensure synchronous execution and immediate notification
            channelHotelAPIService.saveChannelHotel(channelId, channelHotelId, hotelVO, "http://notify.url", true, false, false, false);

            // Verify webhook notification is called
            verify(webhookAPI, atLeastOnce()).webhookPostCall(anyString(), any());
        }
    }

    @Test
    void givenProhibitedSupplier_whenSyncChannelHotel_thenReturnsAsyncResponse() {
        String channelId = "channel1";
        String channelHotelId = "hotel1";
        String supplierId = "prohibitedSupplier";
        ChannelHotelVO channelHotelVO = new ChannelHotelVO();

        PerfLogHandler mockPerfHandler = mock(PerfLogHandler.class);
        when(mockPerfHandler.getToken()).thenReturn("test-token");

        try (MockedStatic<PerfLogHandler> mockedStatic = mockStatic(PerfLogHandler.class)) {
            mockedStatic.when(PerfLogHandler::currentHandler).thenReturn(mockPerfHandler);

            when(supplierWhiteList.isProhibitedConnection(supplierId, channelId)).thenReturn(true);

            ChannelHotelVO result = channelHotelAPIService.syncChannelHotel(channelId, channelHotelId, "SavePropertyCombo", "http://notify.url", false, supplierId, channelHotelVO);

            assertNotNull(result);
            assertEquals(channelId, result.getChannelId());
            assertEquals(channelHotelId, result.getChannelHotelId());
            assertEquals(supplierId, result.getSupplierId());
            assertEquals("test-token", result.getOperationToken());

            verify(predictionReportService).saveChannelProductMappingSnapshotAndReport(supplierId, channelId, channelHotelId);
            verify(distributorHotelSetupTask, never()).doExecute((ChannelServiceTask) any());
        }
    }

    @Test
    void givenNoHttpScheduleCenterService_whenSyncChannelHotel_thenUsesDistributorTask() {
        String channelId = "channel1";
        String channelHotelId = "hotel1";
        String supplierId = "supplier1";
        ChannelHotelVO channelHotelVO = new ChannelHotelVO();

        PerfLogHandler mockPerfHandler = mock(PerfLogHandler.class);
        when(mockPerfHandler.getToken()).thenReturn("test-token");

        try (MockedStatic<PerfLogHandler> mockedStatic = mockStatic(PerfLogHandler.class)) {
            mockedStatic.when(PerfLogHandler::currentHandler).thenReturn(mockPerfHandler);

            when(supplierWhiteList.isProhibitedConnection(supplierId, channelId)).thenReturn(false);
            when(httpScheduleCenterServiceProvider.getIfAvailable()).thenReturn(null);

            ChannelHotelVO result = channelHotelAPIService.syncChannelHotel(channelId, channelHotelId, "SavePropertyCombo,TriggerARIRefresh", "http://notify.url", true, supplierId, channelHotelVO);

            assertNotNull(result);
            assertEquals(channelId, result.getChannelId());
            assertEquals(channelHotelId, result.getChannelHotelId());
            assertEquals(supplierId, result.getSupplierId());
            assertEquals("test-token", result.getOperationToken());

            verify(predictionReportService).saveChannelProductMappingSnapshotAndReport(supplierId, channelId, channelHotelId);
            verify(distributorHotelSetupTask).doExecute((ChannelServiceTask) any());
        }
    }

    @Test
    void givenHttpScheduleCenterServiceAvailable_whenSyncChannelHotel_thenUsesScheduleCenter() {
        String channelId = "channel1";
        String channelHotelId = "hotel1";
        String supplierId = "supplier1";
        ChannelHotelVO channelHotelVO = new ChannelHotelVO();

        PerfLogHandler mockPerfHandler = mock(PerfLogHandler.class);
        when(mockPerfHandler.getToken()).thenReturn("test-token");

        HttpScheduleCenterService mockScheduleCenterService = mock(HttpScheduleCenterService.class);

        try (MockedStatic<PerfLogHandler> mockedStatic = mockStatic(PerfLogHandler.class);
             MockedConstruction<SingleHotelSetupTask> mockedConstruction = mockConstruction(SingleHotelSetupTask.class)) {

            mockedStatic.when(PerfLogHandler::currentHandler).thenReturn(mockPerfHandler);

            when(supplierWhiteList.isProhibitedConnection(supplierId, channelId)).thenReturn(false);
            when(httpScheduleCenterServiceProvider.getIfAvailable()).thenReturn(mockScheduleCenterService);

            ChannelHotelVO result = channelHotelAPIService.syncChannelHotel(channelId, channelHotelId, null, "http://notify.url", false, supplierId, channelHotelVO);

            assertNotNull(result);
            assertEquals(channelId, result.getChannelId());
            assertEquals(channelHotelId, result.getChannelHotelId());
            assertEquals(supplierId, result.getSupplierId());
            assertEquals("test-token", result.getOperationToken());

            verify(predictionReportService).saveChannelProductMappingSnapshotAndReport(supplierId, channelId, channelHotelId);
            verify(distributorHotelSetupTask, never()).doExecute((ChannelServiceTask) any());

            // Verify SingleHotelSetupTask was created and triggerNewTask was called
            assertEquals(1, mockedConstruction.constructed().size());
            SingleHotelSetupTask constructedTask = mockedConstruction.constructed().get(0);
            verify(constructedTask).triggerNewTask(mockScheduleCenterService);
        }
    }

    @Test
    void whenDeleteChannelHotel_thenCallsStorageService() {
        String channelId = "channel1";
        String channelHotelId = "hotel1";

        channelHotelAPIService.deleteChannelHotel(channelId, channelHotelId);

        verify(hotelStorageService).deleteChannelHotel(channelId, channelHotelId);
    }
}