package com.derbysoft.next.propertyconnect.channel.task.task.exceptionlistremove;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Comprehensive unit tests for ChannelHotelProduct
 * Tests builder pattern, data methods, and business logic
 */
@ExtendWith(MockitoExtension.class)
class ChannelHotelProductTest {

    private ChannelHotelProduct testProduct;

    @BeforeEach
    void setUp() {
        testProduct = ChannelHotelProduct.builder()
                .channel("TEST_CHANNEL")
                .hotel("TEST_HOTEL")
                .roomType("TEST_ROOM")
                .ratePlan("TEST_RATE")
                .errCode("ERR001")
                .errMessage("Test error message")
                .deletable(false)
                .build();
    }

    @Test
    void testChannelHotelProduct_BuilderPattern_CreatesValidObject() {
        // Given & When
        ChannelHotelProduct product = ChannelHotelProduct.builder()
                .channel("BOOKING")
                .hotel("HOTEL123")
                .roomType("DELUXE")
                .ratePlan("BAR")
                .errCode("E001")
                .errMessage("Connection timeout")
                .deletable(true)
                .build();

        // Then
        assertNotNull(product, "Product should not be null");
        assertEquals("BOOKING", product.getChannel(), "Channel should match");
        assertEquals("HOTEL123", product.getHotel(), "Hotel should match");
        assertEquals("DELUXE", product.getRoomType(), "Room type should match");
        assertEquals("BAR", product.getRatePlan(), "Rate plan should match");
        assertEquals("E001", product.getErrCode(), "Error code should match");
        assertEquals("Connection timeout", product.getErrMessage(), "Error message should match");
        assertTrue(product.isDeletable(), "Deletable should be true");
    }

    @Test
    void testChannelHotelProduct_DefaultDeletableValue_IsFalse() {
        // Given & When
        ChannelHotelProduct product = ChannelHotelProduct.builder()
                .channel("TEST")
                .hotel("TEST")
                .roomType("TEST")
                .ratePlan("TEST")
                .build();

        // Then
        assertFalse(product.isDeletable(), "Default deletable value should be false");
    }

    @Test
    void testChannelHotelProduct_ToBuilderPattern_CreatesNewInstance() {
        // Given
        ChannelHotelProduct originalProduct = ChannelHotelProduct.builder()
                .channel("ORIGINAL")
                .hotel("ORIGINAL")
                .roomType("ORIGINAL")
                .ratePlan("ORIGINAL")
                .build();

        // When
        ChannelHotelProduct modifiedProduct = originalProduct.toBuilder()
                .channel("MODIFIED")
                .deletable(true)
                .build();

        // Then
        assertNotSame(originalProduct, modifiedProduct, "Should create new instance");
        assertEquals("MODIFIED", modifiedProduct.getChannel(), "Channel should be modified");
        assertEquals("ORIGINAL", modifiedProduct.getHotel(), "Hotel should remain original");
        assertEquals("ORIGINAL", modifiedProduct.getRoomType(), "Room type should remain original");
        assertEquals("ORIGINAL", modifiedProduct.getRatePlan(), "Rate plan should remain original");
        assertTrue(modifiedProduct.isDeletable(), "Deletable should be modified");
        assertFalse(originalProduct.isDeletable(), "Original should remain unchanged");
    }

    @Test
    void testToExceptionListFormat_WithValidData_ReturnsCorrectFormat() {
        // Given
        ChannelHotelProduct product = ChannelHotelProduct.builder()
                .channel("BOOKING")
                .hotel("HOTEL123")
                .roomType("DELUXE")
                .ratePlan("BAR")
                .build();

        // When
        String result = product.toExceptionListFormat();

        // Then
        assertEquals("BOOKING|PROPERTYCONNECT|HOTEL123|BAR|DELUXE", result,
                "Exception list format should match expected pattern");
    }

    @Test
    void testToExceptionListFormat_WithSpecialCharacters_HandlesCorrectly() {
        // Given
        ChannelHotelProduct product = ChannelHotelProduct.builder()
                .channel("CHANNEL|WITH|PIPES")
                .hotel("HOTEL@123")
                .roomType("ROOM#TYPE")
                .ratePlan("RATE$PLAN")
                .build();

        // When
        String result = product.toExceptionListFormat();

        // Then
        assertEquals("CHANNEL|WITH|PIPES|PROPERTYCONNECT|HOTEL@123|RATE$PLAN|ROOM#TYPE", result,
                "Should handle special characters correctly");
    }

    @Test
    void testToExceptionListFormat_WithNullValues_HandlesGracefully() {
        // Given
        ChannelHotelProduct product = ChannelHotelProduct.builder()
                .channel(null)
                .hotel(null)
                .roomType(null)
                .ratePlan(null)
                .build();

        // When
        String result = product.toExceptionListFormat();

        // Then
        assertEquals("null|PROPERTYCONNECT|null|null|null", result,
                "Should handle null values gracefully");
    }

    @Test
    void testToExceptionListFormat_WithEmptyStrings_HandlesCorrectly() {
        // Given
        ChannelHotelProduct product = ChannelHotelProduct.builder()
                .channel("")
                .hotel("")
                .roomType("")
                .ratePlan("")
                .build();

        // When
        String result = product.toExceptionListFormat();

        // Then
        assertEquals("|PROPERTYCONNECT|||", result,
                "Should handle empty strings correctly");
    }

    @Test
    void testChannelHotelProduct_GettersAndSetters_WorkCorrectly() {
        // Given
        ChannelHotelProduct product = ChannelHotelProduct.builder()
                .channel("ORIGINAL")
                .hotel("ORIGINAL")
                .roomType("ORIGINAL")
                .ratePlan("ORIGINAL")
                .build();

        // When
        product.setChannel("NEW_CHANNEL");
        product.setHotel("NEW_HOTEL");
        product.setRoomType("NEW_ROOM");
        product.setRatePlan("NEW_RATE");
        product.setErrCode("NEW_ERR");
        product.setErrMessage("New error message");
        product.setDeletable(true);

        // Then
        assertEquals("NEW_CHANNEL", product.getChannel(), "Channel getter/setter should work");
        assertEquals("NEW_HOTEL", product.getHotel(), "Hotel getter/setter should work");
        assertEquals("NEW_ROOM", product.getRoomType(), "Room type getter/setter should work");
        assertEquals("NEW_RATE", product.getRatePlan(), "Rate plan getter/setter should work");
        assertEquals("NEW_ERR", product.getErrCode(), "Error code getter/setter should work");
        assertEquals("New error message", product.getErrMessage(), "Error message getter/setter should work");
        assertTrue(product.isDeletable(), "Deletable getter/setter should work");
    }

    @Test
    void testChannelHotelProduct_EqualsAndHashCode_WorkCorrectly() {
        // Given
        ChannelHotelProduct product1 = ChannelHotelProduct.builder()
                .channel("CHANNEL")
                .hotel("HOTEL")
                .roomType("ROOM")
                .ratePlan("RATE")
                .build();

        ChannelHotelProduct product2 = ChannelHotelProduct.builder()
                .channel("CHANNEL")
                .hotel("HOTEL")
                .roomType("ROOM")
                .ratePlan("RATE")
                .build();

        ChannelHotelProduct product3 = ChannelHotelProduct.builder()
                .channel("DIFFERENT")
                .hotel("HOTEL")
                .roomType("ROOM")
                .ratePlan("RATE")
                .build();

        // When & Then
        assertEquals(product1, product2, "Products with same data should be equal");
        assertEquals(product1.hashCode(), product2.hashCode(), "Hash codes should be equal for equal objects");
        assertNotEquals(product1, product3, "Products with different data should not be equal");
        assertNotEquals(product1.hashCode(), product3.hashCode(), "Hash codes should be different for different objects");
    }

    @Test
    void testChannelHotelProduct_ToString_ContainsAllFields() {
        // Given
        ChannelHotelProduct product = ChannelHotelProduct.builder()
                .channel("BOOKING")
                .hotel("HOTEL123")
                .roomType("DELUXE")
                .ratePlan("BAR")
                .errCode("E001")
                .errMessage("Error message")
                .deletable(true)
                .build();

        // When
        String toString = product.toString();

        // Then
        assertNotNull(toString, "toString should not be null");
        assertTrue(toString.contains("BOOKING"), "toString should contain channel");
        assertTrue(toString.contains("HOTEL123"), "toString should contain hotel");
        assertTrue(toString.contains("DELUXE"), "toString should contain room type");
        assertTrue(toString.contains("BAR"), "toString should contain rate plan");
        assertTrue(toString.contains("E001"), "toString should contain error code");
        assertTrue(toString.contains("Error message"), "toString should contain error message");
        assertTrue(toString.contains("true"), "toString should contain deletable value");
    }

    @Test
    void testChannelHotelProduct_ValidationAnnotations_ArePresent() {
        // Given
        Class<?> productClass = ChannelHotelProduct.class;

        // When & Then
        try {
            var channelField = productClass.getDeclaredField("channel");
            var hotelField = productClass.getDeclaredField("hotel");
            var roomTypeField = productClass.getDeclaredField("roomType");
            var ratePlanField = productClass.getDeclaredField("ratePlan");
            var deletableField = productClass.getDeclaredField("deletable");

            assertTrue(channelField.isAnnotationPresent(jakarta.validation.constraints.NotBlank.class),
                    "Channel field should have @NotBlank annotation");
            assertTrue(hotelField.isAnnotationPresent(jakarta.validation.constraints.NotBlank.class),
                    "Hotel field should have @NotBlank annotation");
            assertTrue(roomTypeField.isAnnotationPresent(jakarta.validation.constraints.NotBlank.class),
                    "Room type field should have @NotBlank annotation");
            assertTrue(ratePlanField.isAnnotationPresent(jakarta.validation.constraints.NotBlank.class),
                    "Rate plan field should have @NotBlank annotation");
            assertTrue(deletableField.isAnnotationPresent(com.fasterxml.jackson.annotation.JsonIgnore.class),
                    "Deletable field should have @JsonIgnore annotation");
        } catch (NoSuchFieldException e) {
            fail("Required fields should exist in ChannelHotelProduct class");
        }
    }

    @Test
    void testChannelHotelProduct_LombokAnnotations_ArePresent() {
        // Given
        Class<?> productClass = ChannelHotelProduct.class;

        // When & Then
        // Note: @Data annotation may not be directly visible at runtime due to Lombok processing
        // But we can verify the class has the expected methods that @Data would generate
        assertDoesNotThrow(() -> productClass.getMethod("getChannel"), "Should have getter methods");
        assertDoesNotThrow(() -> productClass.getMethod("setChannel", String.class), "Should have setter methods");
        // Note: @Builder annotation may not be directly visible at runtime due to Lombok processing
        // But we can verify the class has the expected builder methods
        assertDoesNotThrow(() -> productClass.getMethod("builder"), "Should have builder method");
    }

    @Test
    void testChannelHotelProduct_ImmutableAfterCreation_CanBeModified() {
        // Given
        ChannelHotelProduct product = ChannelHotelProduct.builder()
                .channel("ORIGINAL")
                .hotel("ORIGINAL")
                .roomType("ORIGINAL")
                .ratePlan("ORIGINAL")
                .deletable(false)
                .build();

        // When
        product.setChannel("MODIFIED");
        product.setDeletable(true);

        // Then
        assertEquals("MODIFIED", product.getChannel(), "Product should be mutable");
        assertTrue(product.isDeletable(), "Product should be mutable");
    }

    @Test
    void testChannelHotelProduct_BusinessLogic_DeletableFlag() {
        // Given
        ChannelHotelProduct product = ChannelHotelProduct.builder()
                .channel("BOOKING")
                .hotel("HOTEL123")
                .roomType("DELUXE")
                .ratePlan("BAR")
                .deletable(false)
                .build();

        // When
        assertFalse(product.isDeletable(), "Initially should not be deletable");

        product.setDeletable(true);

        // Then
        assertTrue(product.isDeletable(), "Should be deletable after setting");
    }

    @Test
    void testChannelHotelProduct_ErrorHandling_WithNullErrorFields() {
        // Given & When
        ChannelHotelProduct product = ChannelHotelProduct.builder()
                .channel("BOOKING")
                .hotel("HOTEL123")
                .roomType("DELUXE")
                .ratePlan("BAR")
                .errCode(null)
                .errMessage(null)
                .build();

        // Then
        assertNull(product.getErrCode(), "Error code can be null");
        assertNull(product.getErrMessage(), "Error message can be null");
        assertNotNull(product.toExceptionListFormat(), "Exception format should still work with null error fields");
    }
}
