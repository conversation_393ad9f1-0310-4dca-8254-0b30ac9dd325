package com.derbysoft.next.propertyconnect.channel.task.domain.dto;

import com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.RemoteChannelService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

class ChannelHotelInfoTest {

    private ChannelHotelInfo channelHotelInfo;

    @BeforeEach
    void setUp() {
        channelHotelInfo = new ChannelHotelInfo("hotel-123");
    }

    @Test
    void testConstructorWithId() {
        // Given
        String hotelId = "test-hotel-456";

        // When
        ChannelHotelInfo hotelInfo = new ChannelHotelInfo(hotelId);

        // Then
        assertEquals(hotelId, hotelInfo.getId());
        assertEquals(hotelId, hotelInfo.getCode());
    }

    @Test
    void testNoArgsConstructor() {
        // When
        ChannelHotelInfo hotelInfo = new ChannelHotelInfo();

        // Then
        assertNotNull(hotelInfo);
        assertNull(hotelInfo.getId());
    }

    @Test
    void testCodePattern() {
        // When
        String codePattern = channelHotelInfo.codePattern();

        // Then
        assertEquals("HotelCode", codePattern);
    }

    @Test
    void testGetCode() {
        // When
        String code = channelHotelInfo.getCode();

        // Then
        assertEquals("hotel-123", code);
    }

    @Test
    void testBasicProperties() {
        // Given
        String name = "Test Hotel";
        String description = "A beautiful test hotel";
        String currency = "USD";
        String timezone = "America/New_York";
        String ariType = "PUSH";
        String rateType = "NET";

        // When
        channelHotelInfo.setName(name);
        channelHotelInfo.setDescription(description);
        channelHotelInfo.setCurrency(currency);
        channelHotelInfo.setTimezone(timezone);
        channelHotelInfo.setAriType(ariType);
        channelHotelInfo.setRateType(rateType);

        // Then
        assertEquals(name, channelHotelInfo.getName());
        assertEquals(description, channelHotelInfo.getDescription());
        assertEquals(currency, channelHotelInfo.getCurrency());
        assertEquals(timezone, channelHotelInfo.getTimezone());
        assertEquals(ariType, channelHotelInfo.getAriType());
        assertEquals(rateType, channelHotelInfo.getRateType());
    }

    @Test
    void testStatusProperties() {
        // Given
        ItemStatus status = ItemStatus.Actived;
        SyncStatus syncStatus = SyncStatus.SYNCED;
        String errorCode = "ERR001";
        String errorMessage = "Test error";
        String lastOperationToken = "token-123";
        RemoteChannelService.Operation operation = RemoteChannelService.Operation.SaveProperty;

        // When
        channelHotelInfo.setStatus(status);
        channelHotelInfo.setSyncStatus(syncStatus);
        channelHotelInfo.setErrorCode(errorCode);
        channelHotelInfo.setErrorMessage(errorMessage);
        channelHotelInfo.setLastOperationToken(lastOperationToken);
        channelHotelInfo.setOperation(operation);

        // Then
        assertEquals(status, channelHotelInfo.getStatus());
        assertEquals(syncStatus, channelHotelInfo.getSyncStatus());
        assertEquals(errorCode, channelHotelInfo.getErrorCode());
        assertEquals(errorMessage, channelHotelInfo.getErrorMessage());
        assertEquals(lastOperationToken, channelHotelInfo.getLastOperationToken());
        assertEquals(operation, channelHotelInfo.getOperation());
    }

    @Test
    void testAddress() {
        // Given
        ChannelHotelInfo.Address address = new ChannelHotelInfo.Address();
        address.setCountryCode("US");
        address.setStateName("New York");
        address.setCityCode("NYC");
        address.setPostalCode("10001");
        address.setLine1("123 Main St");
        address.setLine2("Apt 4B");

        // When
        channelHotelInfo.setAddress(address);

        // Then
        assertNotNull(channelHotelInfo.getAddress());
        assertEquals("US", channelHotelInfo.getAddress().getCountryCode());
        assertEquals("New York", channelHotelInfo.getAddress().getStateName());
        assertEquals("NYC", channelHotelInfo.getAddress().getCityCode());
        assertEquals("10001", channelHotelInfo.getAddress().getPostalCode());
        assertEquals("123 Main St", channelHotelInfo.getAddress().getLine1());
        assertEquals("Apt 4B", channelHotelInfo.getAddress().getLine2());
    }

    @Test
    void testPosition() {
        // Given
        ChannelHotelInfo.Position position = new ChannelHotelInfo.Position();
        position.setLogitude("-74.0060");
        position.setLatitude("40.7128");

        // When
        channelHotelInfo.setPosition(position);

        // Then
        assertNotNull(channelHotelInfo.getPosition());
        assertEquals("-74.0060", channelHotelInfo.getPosition().getLogitude());
        assertEquals("40.7128", channelHotelInfo.getPosition().getLatitude());
    }

    @Test
    void testContacts() {
        // Given
        ChannelHotelInfo.ContactsItem contact = new ChannelHotelInfo.ContactsItem();
        contact.setSurName("Doe");
        contact.setGivenName("John");
        contact.setPhones(List.of("******-1234", "******-5678"));
        contact.setEmails(List.of("<EMAIL>", "<EMAIL>"));
        
        Map<String, Object> contactExtensions = new HashMap<>();
        contactExtensions.put("department", "management");
        contact.setExtensions(contactExtensions);

        List<ChannelHotelInfo.ContactsItem> contacts = List.of(contact);

        // When
        channelHotelInfo.setContacts(contacts);

        // Then
        assertNotNull(channelHotelInfo.getContacts());
        assertEquals(1, channelHotelInfo.getContacts().size());
        
        ChannelHotelInfo.ContactsItem retrievedContact = channelHotelInfo.getContacts().get(0);
        assertEquals("Doe", retrievedContact.getSurName());
        assertEquals("John", retrievedContact.getGivenName());
        assertEquals(2, retrievedContact.getPhones().size());
        assertTrue(retrievedContact.getPhones().contains("******-1234"));
        assertEquals(2, retrievedContact.getEmails().size());
        assertTrue(retrievedContact.getEmails().contains("<EMAIL>"));
        assertEquals("management", retrievedContact.getExtensions().get("department"));
    }

    @Test
    void testI18n() {
        // Given
        Map<String, Object> i18n = new HashMap<>();
        i18n.put("en", Map.of("name", "Test Hotel", "description", "A test hotel"));
        i18n.put("zh", Map.of("name", "测试酒店", "description", "一个测试酒店"));

        // When
        channelHotelInfo.setI18n(i18n);

        // Then
        assertNotNull(channelHotelInfo.getI18n());
        assertEquals(2, channelHotelInfo.getI18n().size());
        assertTrue(channelHotelInfo.getI18n().containsKey("en"));
        assertTrue(channelHotelInfo.getI18n().containsKey("zh"));
    }

    @Test
    void testSettings() {
        // Given
        Map<String, Object> settings = new HashMap<>();
        settings.put("apiKey", "test-api-key");
        settings.put("timeout", 30);
        settings.put("retryCount", 3);

        // When
        channelHotelInfo.setSettings(settings);

        // Then
        assertNotNull(channelHotelInfo.getSettings());
        assertEquals(3, channelHotelInfo.getSettings().size());
        assertEquals("test-api-key", channelHotelInfo.getSettings().get("apiKey"));
        assertEquals(30, channelHotelInfo.getSettings().get("timeout"));
        assertEquals(3, channelHotelInfo.getSettings().get("retryCount"));
    }

    @Test
    void testExtensions() {
        // Given
        Map<String, Object> extensions = new HashMap<>();
        extensions.put("customField1", "value1");
        extensions.put("customField2", 42);
        extensions.put("customField3", true);

        // When
        channelHotelInfo.setExtensions(extensions);

        // Then
        assertNotNull(channelHotelInfo.getExtensions());
        assertEquals(3, channelHotelInfo.getExtensions().size());
        assertEquals("value1", channelHotelInfo.getExtensions().get("customField1"));
        assertEquals(42, channelHotelInfo.getExtensions().get("customField2"));
        assertEquals(true, channelHotelInfo.getExtensions().get("customField3"));
    }

    @Test
    void testEqualsAndHashCode() {
        // Given
        ChannelHotelInfo hotelInfo1 = new ChannelHotelInfo("hotel-123");
        hotelInfo1.setName("Test Hotel");
        hotelInfo1.setCurrency("USD");

        ChannelHotelInfo hotelInfo2 = new ChannelHotelInfo("hotel-123");
        hotelInfo2.setName("Test Hotel");
        hotelInfo2.setCurrency("USD");

        ChannelHotelInfo hotelInfo3 = new ChannelHotelInfo("hotel-456");
        hotelInfo3.setName("Different Hotel");

        // When & Then
        assertEquals(hotelInfo1, hotelInfo2);
        assertNotEquals(hotelInfo1, hotelInfo3);
        assertEquals(hotelInfo1.hashCode(), hotelInfo2.hashCode());
    }

    @Test
    void testToString() {
        // Given
        channelHotelInfo.setName("Test Hotel");
        channelHotelInfo.setCurrency("USD");

        // When
        String toString = channelHotelInfo.toString();

        // Then
        assertNotNull(toString);
        assertTrue(toString.contains("hotel-123"));
        assertTrue(toString.contains("Test Hotel"));
        assertTrue(toString.contains("USD"));
    }

    @Test
    void testInventoryItemStatusInterface() {
        // Given
        SyncStatus syncStatus = SyncStatus.SYNCED;
        String errorCode = "ERR001";
        String errorMessage = "Test error";

        // When
        channelHotelInfo.setSyncStatus(syncStatus);
        channelHotelInfo.setErrorCode(errorCode);
        channelHotelInfo.setErrorMessage(errorMessage);

        // Then
        assertEquals(syncStatus, channelHotelInfo.getSyncStatus());
        assertEquals(errorCode, channelHotelInfo.getErrorCode());
        assertEquals(errorMessage, channelHotelInfo.getErrorMessage());
        assertEquals("HotelCode", channelHotelInfo.codePattern());
        assertEquals("hotel-123", channelHotelInfo.getCode());
    }

    @Test
    void testAddressEqualsAndHashCode() {
        // Given
        ChannelHotelInfo.Address address1 = new ChannelHotelInfo.Address();
        address1.setCountryCode("US");
        address1.setLine1("123 Main St");

        ChannelHotelInfo.Address address2 = new ChannelHotelInfo.Address();
        address2.setCountryCode("US");
        address2.setLine1("123 Main St");

        // When & Then
        assertEquals(address1, address2);
        assertEquals(address1.hashCode(), address2.hashCode());
    }

    @Test
    void testPositionEqualsAndHashCode() {
        // Given
        ChannelHotelInfo.Position position1 = new ChannelHotelInfo.Position();
        position1.setLogitude("-74.0060");
        position1.setLatitude("40.7128");

        ChannelHotelInfo.Position position2 = new ChannelHotelInfo.Position();
        position2.setLogitude("-74.0060");
        position2.setLatitude("40.7128");

        // When & Then
        assertEquals(position1, position2);
        assertEquals(position1.hashCode(), position2.hashCode());
    }

    @Test
    void testContactsItemEqualsAndHashCode() {
        // Given
        ChannelHotelInfo.ContactsItem contact1 = new ChannelHotelInfo.ContactsItem();
        contact1.setSurName("Doe");
        contact1.setGivenName("John");

        ChannelHotelInfo.ContactsItem contact2 = new ChannelHotelInfo.ContactsItem();
        contact2.setSurName("Doe");
        contact2.setGivenName("John");

        // When & Then
        assertEquals(contact1, contact2);
        assertEquals(contact1.hashCode(), contact2.hashCode());
    }
}
