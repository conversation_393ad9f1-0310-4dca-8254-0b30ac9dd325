package com.derbysoft.next.propertyconnect.channel.task.util;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Comprehensive unit tests for ConsumeException
 * Tests custom exception handling for failed item consumption scenarios
 */
@ExtendWith(MockitoExtension.class)
class ConsumeExceptionTest {

    @Test
    void testConstructor_ValidParameters_CreatesCorrectly() {
        // Given
        String message = "Processing failed";
        List<String> failedItems = Arrays.asList("item1", "item2", "item3");

        // When
        ConsumeException exception = new ConsumeException(message, failedItems);

        // Then
        assertEquals(failedItems, exception.getFailedItems());
        assertTrue(exception.getMessage().contains(message));
        assertTrue(exception.getMessage().contains(failedItems.toString()));
        assertEquals(message + failedItems.toString(), exception.getMessage());
    }

    @Test
    void testConstructor_EmptyFailedItems_HandlesCorrectly() {
        // Given
        String message = "No items to process";
        List<String> failedItems = new ArrayList<>();

        // When
        ConsumeException exception = new ConsumeException(message, failedItems);

        // Then
        assertEquals(failedItems, exception.getFailedItems());
        assertTrue(exception.getMessage().contains(message));
        assertTrue(exception.getMessage().contains("[]"));
        assertEquals(message + "[]", exception.getMessage());
    }

    @Test
    void testConstructor_NullFailedItems_ThrowsNullPointerException() {
        // Given
        String message = "Null items error";
        List<String> failedItems = null;

        // When & Then
        NullPointerException exception = assertThrows(NullPointerException.class, () ->
            new ConsumeException(message, failedItems));

        assertTrue(exception.getMessage().contains("Cannot invoke \"Object.toString()\" because \"failedItems\" is null"));
    }

    @Test
    void testConstructor_NullMessage_HandlesCorrectly() {
        // Given
        String message = null;
        List<String> failedItems = Arrays.asList("item1", "item2");

        // When
        ConsumeException exception = new ConsumeException(message, failedItems);

        // Then
        assertEquals(failedItems, exception.getFailedItems());
        assertTrue(exception.getMessage().contains(failedItems.toString()));
        assertEquals("null" + failedItems.toString(), exception.getMessage());
    }

    @Test
    void testConstructor_BothNull_ThrowsNullPointerException() {
        // Given
        String message = null;
        List<String> failedItems = null;

        // When & Then
        NullPointerException exception = assertThrows(NullPointerException.class, () ->
            new ConsumeException(message, failedItems));

        assertTrue(exception.getMessage().contains("Cannot invoke \"Object.toString()\" because \"failedItems\" is null"));
    }

    @Test
    void testGetFailedItems_StringList_ReturnsCorrectly() {
        // Given
        List<String> failedItems = Arrays.asList("error1", "error2", "error3");
        ConsumeException exception = new ConsumeException("Test", failedItems);

        // When
        List<?> result = exception.getFailedItems();

        // Then
        assertEquals(failedItems, result);
        assertEquals(3, result.size());
        assertEquals("error1", result.get(0));
        assertEquals("error2", result.get(1));
        assertEquals("error3", result.get(2));
    }

    @Test
    void testGetFailedItems_IntegerList_ReturnsCorrectly() {
        // Given
        List<Integer> failedItems = Arrays.asList(1, 2, 3, 4, 5);
        ConsumeException exception = new ConsumeException("Number processing failed", failedItems);

        // When
        List<?> result = exception.getFailedItems();

        // Then
        assertEquals(failedItems, result);
        assertEquals(5, result.size());
        assertEquals(1, result.get(0));
        assertEquals(5, result.get(4));
    }

    @Test
    void testGetFailedItems_MixedObjectList_ReturnsCorrectly() {
        // Given
        List<Object> failedItems = Arrays.asList("string", 123, true, null, new Date());
        ConsumeException exception = new ConsumeException("Mixed processing failed", failedItems);

        // When
        List<?> result = exception.getFailedItems();

        // Then
        assertEquals(failedItems, result);
        assertEquals(5, result.size());
        assertEquals("string", result.get(0));
        assertEquals(123, result.get(1));
        assertEquals(true, result.get(2));
        assertNull(result.get(3));
        assertNotNull(result.get(4));
    }

    @Test
    void testGetFailedItems_CustomObjectList_ReturnsCorrectly() {
        // Given
        List<TestObject> failedItems = Arrays.asList(
            new TestObject("obj1", 1),
            new TestObject("obj2", 2)
        );
        ConsumeException exception = new ConsumeException("Custom object processing failed", failedItems);

        // When
        List<?> result = exception.getFailedItems();

        // Then
        assertEquals(failedItems, result);
        assertEquals(2, result.size());
        assertTrue(result.get(0) instanceof TestObject);
        assertTrue(result.get(1) instanceof TestObject);
    }

    @Test
    void testInheritance_ExtendsRuntimeException_CorrectHierarchy() {
        // Given
        ConsumeException exception = new ConsumeException("Test", Arrays.asList("item"));

        // When & Then
        assertTrue(exception instanceof RuntimeException);
        assertTrue(exception instanceof Exception);
        assertTrue(exception instanceof Throwable);
    }

    @Test
    void testThrowAndCatch_NormalFlow_WorksCorrectly() {
        // Given
        String message = "Processing error";
        List<String> failedItems = Arrays.asList("failed1", "failed2");

        // When & Then
        ConsumeException thrownException = assertThrows(ConsumeException.class, () -> {
            throw new ConsumeException(message, failedItems);
        });

        assertEquals(failedItems, thrownException.getFailedItems());
        assertTrue(thrownException.getMessage().contains(message));
    }

    @Test
    void testMessage_LongFailedItemsList_IncludesAllItems() {
        // Given
        String message = "Bulk processing failed";
        List<String> failedItems = new ArrayList<>();
        for (int i = 1; i <= 100; i++) {
            failedItems.add("item" + i);
        }

        // When
        ConsumeException exception = new ConsumeException(message, failedItems);

        // Then
        assertEquals(failedItems, exception.getFailedItems());
        assertTrue(exception.getMessage().contains(message));
        assertTrue(exception.getMessage().contains("item1"));
        assertTrue(exception.getMessage().contains("item100"));
        assertTrue(exception.getMessage().contains(failedItems.toString()));
    }

    @Test
    void testMessage_SpecialCharactersInMessage_HandlesCorrectly() {
        // Given
        String message = "Error with special chars: @#$%^&*(){}[]|\\:;\"'<>?/.,~`";
        List<String> failedItems = Arrays.asList("item1");

        // When
        ConsumeException exception = new ConsumeException(message, failedItems);

        // Then
        assertTrue(exception.getMessage().contains(message));
        assertEquals(message + failedItems.toString(), exception.getMessage());
    }

    @Test
    void testMessage_UnicodeCharacters_HandlesCorrectly() {
        // Given
        String message = "处理失败: 错误信息";
        List<String> failedItems = Arrays.asList("项目1", "项目2");

        // When
        ConsumeException exception = new ConsumeException(message, failedItems);

        // Then
        assertTrue(exception.getMessage().contains(message));
        assertTrue(exception.getMessage().contains("项目1"));
        assertTrue(exception.getMessage().contains("项目2"));
    }

    @Test
    void testFailedItems_ImmutableList_ReturnsCorrectly() {
        // Given
        List<String> failedItems = Collections.unmodifiableList(Arrays.asList("item1", "item2"));
        ConsumeException exception = new ConsumeException("Test", failedItems);

        // When
        List<?> result = exception.getFailedItems();

        // Then
        assertEquals(failedItems, result);
        // Verify it's the same reference (not a copy)
        assertSame(failedItems, result);
    }

    @Test
    void testFailedItems_SingletonList_ReturnsCorrectly() {
        // Given
        List<String> failedItems = Collections.singletonList("single_item");
        ConsumeException exception = new ConsumeException("Single item failed", failedItems);

        // When
        List<?> result = exception.getFailedItems();

        // Then
        assertEquals(failedItems, result);
        assertEquals(1, result.size());
        assertEquals("single_item", result.get(0));
    }

    @Test
    void testRealWorldScenario_DatabaseProcessing_HandlesCorrectly() {
        // Given
        String message = "Database batch insert failed";
        List<String> failedItems = Arrays.asList(
            "user_id_123", "user_id_456", "user_id_789"
        );

        // When
        ConsumeException exception = new ConsumeException(message, failedItems);

        // Then
        assertEquals(failedItems, exception.getFailedItems());
        assertTrue(exception.getMessage().contains("Database batch insert failed"));
        assertTrue(exception.getMessage().contains("user_id_123"));
        assertTrue(exception.getMessage().contains("user_id_456"));
        assertTrue(exception.getMessage().contains("user_id_789"));
    }

    @Test
    void testRealWorldScenario_MessageQueueProcessing_HandlesCorrectly() {
        // Given
        String message = "Message queue processing failed";
        List<Map<String, Object>> failedItems = Arrays.asList(
            Map.of("messageId", "msg_001", "topic", "user_events"),
            Map.of("messageId", "msg_002", "topic", "order_events")
        );

        // When
        ConsumeException exception = new ConsumeException(message, failedItems);

        // Then
        assertEquals(failedItems, exception.getFailedItems());
        assertEquals(2, exception.getFailedItems().size());
        assertTrue(exception.getMessage().contains("Message queue processing failed"));
    }

    @Test
    void testRealWorldScenario_FileProcessing_HandlesCorrectly() {
        // Given
        String message = "File processing batch failed";
        List<String> failedItems = Arrays.asList(
            "/path/to/file1.txt", "/path/to/file2.csv", "/path/to/file3.json"
        );

        // When
        ConsumeException exception = new ConsumeException(message, failedItems);

        // Then
        assertEquals(failedItems, exception.getFailedItems());
        assertTrue(exception.getMessage().contains("File processing batch failed"));
        assertTrue(exception.getMessage().contains("/path/to/file1.txt"));
        assertTrue(exception.getMessage().contains("/path/to/file2.csv"));
        assertTrue(exception.getMessage().contains("/path/to/file3.json"));
    }

    @Test
    void testLombokGetter_Annotation_WorksCorrectly() {
        // This test verifies that the @Getter annotation is working
        // Given
        List<String> failedItems = Arrays.asList("item1", "item2");
        ConsumeException exception = new ConsumeException("Test", failedItems);

        // When
        List<?> result = exception.getFailedItems();

        // Then
        assertNotNull(result);
        assertEquals(failedItems, result);
        // Verify the getter method exists and is accessible
        assertDoesNotThrow(() -> exception.getFailedItems());
    }

    // Helper class for testing custom objects
    private static class TestObject {
        private final String name;
        private final int value;

        public TestObject(String name, int value) {
            this.name = name;
            this.value = value;
        }

        @Override
        public String toString() {
            return "TestObject{name='" + name + "', value=" + value + "}";
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            TestObject that = (TestObject) o;
            return value == that.value && Objects.equals(name, that.name);
        }

        @Override
        public int hashCode() {
            return Objects.hash(name, value);
        }
    }
}
