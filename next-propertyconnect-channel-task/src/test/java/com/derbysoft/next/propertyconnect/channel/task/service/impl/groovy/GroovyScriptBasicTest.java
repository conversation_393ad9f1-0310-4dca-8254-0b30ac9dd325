package com.derbysoft.next.propertyconnect.channel.task.service.impl.groovy;

import com.derbysoft.next.propertyconnect.channel.task.service.AccountSettingService;
import com.derbysoft.next.propertyconnect.channel.task.service.impl.agoda.AgodaClient;
import com.derbysoft.next.propertyconnect.channel.task.service.impl.agoda.AgodaServiceImpl;
import com.derbysoft.next.propertyconnect.channel.task.service.impl.bookingcom.BookingcomClient;
import com.derbysoft.next.propertyconnect.channel.task.service.impl.bookingcom.BookingcomServiceImpl;
import com.derbysoft.next.propertyconnect.channel.task.service.impl.ctrip.CtripServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Basic unit tests for Groovy-based service implementations
 * Tests fundamental functionality without complex dependencies
 *
 * NOTE: This file contains basic tests for Agoda, Booking.com, and Ctrip services.
 * For comprehensive tests of individual Groovy scripts, see the dedicated test files:
 * - ExpediaProductsServiceImplTest.java
 * - FliggyAdapterCreationServiceTest.java
 * - SynxisServiceImplTest.java
 * - AirbnbAdapterActivationServiceTest.java
 * - And other individual service test files
 */

/**
 * Basic unit tests for Groovy-based service implementations
 * Tests fundamental functionality without complex dependencies
 */
@ExtendWith(MockitoExtension.class)
class GroovyScriptBasicTest {

    @Mock
    private AccountSettingService accountSettingService;

    @Mock
    private AgodaClient agodaClient;

    @Mock
    private BookingcomClient bookingcomClient;

    private AgodaServiceImpl agodaService;
    private BookingcomServiceImpl bookingcomService;
    private CtripServiceImpl ctripService;

    @BeforeEach
    void setUp() {
        // Setup Agoda Service
        agodaService = new AgodaServiceImpl(accountSettingService, agodaClient);

        // Setup Booking.com Service
        bookingcomService = new BookingcomServiceImpl();
        ReflectionTestUtils.setField(bookingcomService, "accountSettingService", accountSettingService);
        ReflectionTestUtils.setField(bookingcomService, "bookingcomClient", bookingcomClient);
        ReflectionTestUtils.setField(bookingcomService, "hotelLimit", 30000);

        // Setup Ctrip Service
        ctripService = new CtripServiceImpl();
    }

    // ========== Agoda Service Tests ==========

    @Test
    void testAgodaService_Channel_ReturnsCorrectChannelName() {
        // When
        String result = agodaService.channel();

        // Then
        assertEquals("AGODA", result);
    }

    @Test
    void testAgodaService_CreateAgodaRequest_WithValidChannelHotelId_ReturnsNotNull() {
        // Given
        String channelHotelId = "HOTEL_123";

        // When
        Object result = agodaService.createAgodaRequest(channelHotelId);

        // Then
        assertNotNull(result);
        String resultStr = String.valueOf(result);
        assertNotNull(resultStr);
        assertFalse(resultStr.isEmpty());
    }

    @Test
    void testAgodaService_CreateAgodaRequest_WithNullChannelHotelId_ReturnsNotNull() {
        // Given
        String channelHotelId = null;

        // When
        Object result = agodaService.createAgodaRequest(channelHotelId);

        // Then
        assertNotNull(result);
        String resultStr = String.valueOf(result);
        assertNotNull(resultStr);
        assertFalse(resultStr.isEmpty());
    }

    @Test
    void testAgodaService_CreateAgodaRequest_WithEmptyChannelHotelId_ReturnsNotNull() {
        // Given
        String channelHotelId = "";

        // When
        Object result = agodaService.createAgodaRequest(channelHotelId);

        // Then
        assertNotNull(result);
        String resultStr = String.valueOf(result);
        assertNotNull(resultStr);
        assertFalse(resultStr.isEmpty());
    }

    // ========== Booking.com Service Tests ==========

    @Test
    void testBookingcomService_Channel_ReturnsCorrectChannelName() {
        // When
        String result = bookingcomService.channel();

        // Then
        assertEquals("BOOKINGCOM", result);
    }

    @Test
    void testBookingcomService_RequestSpan_ReturnsConfiguredHotelLimit() {
        // When
        Integer result = bookingcomService.requestSpan();

        // Then
        assertEquals(30000, result);
    }

    @Test
    void testBookingcomService_CreateBcomReqAuth_WithValidCredentials_ReturnsNotNull() {
        // Given
        String username = "testuser";
        String password = "testpass";

        // When
        Object result = bookingcomService.createBcomReqAuth(username, password);

        // Then
        assertNotNull(result);
        String resultStr = String.valueOf(result);
        assertNotNull(resultStr);
        assertFalse(resultStr.isEmpty());
    }

    @Test
    void testBookingcomService_CreateBcomReqAuth_WithNullCredentials_ReturnsNull() {
        // When
        String result1 = (String) bookingcomService.createBcomReqAuth(null, "password");
        String result2 = (String) bookingcomService.createBcomReqAuth("username", null);
        String result3 = (String) bookingcomService.createBcomReqAuth(null, null);

        // Then
        assertNull(result1);
        assertNull(result2);
        assertNull(result3);
    }

    @Test
    void testBookingcomService_CreateBcomReqAuth_WithEmptyCredentials_ReturnsNull() {
        // When
        String result1 = (String) bookingcomService.createBcomReqAuth("", "password");
        String result2 = (String) bookingcomService.createBcomReqAuth("username", "");
        String result3 = (String) bookingcomService.createBcomReqAuth("", "");

        // Then
        assertNull(result1);
        assertNull(result2);
        assertNull(result3);
    }

    @Test
    void testBookingcomService_CreateRoomRateReq_WithValidChannelHotelId_ReturnsNotNull() {
        // Given
        String channelHotelId = "HOTEL_123";

        // When
        Object result = bookingcomService.createRoomRateReq(channelHotelId);

        // Then
        assertNotNull(result);
        String resultStr = String.valueOf(result);
        assertNotNull(resultStr);
        assertFalse(resultStr.isEmpty());
    }

    @Test
    void testBookingcomService_CreateRoomRateReq_WithNullChannelHotelId_ReturnsNotNull() {
        // Given
        String channelHotelId = null;

        // When
        Object result = bookingcomService.createRoomRateReq(channelHotelId);

        // Then
        assertNotNull(result);
        String resultStr = String.valueOf(result);
        assertNotNull(resultStr);
        assertFalse(resultStr.isEmpty());
    }

    // ========== Ctrip Service Tests ==========

    @Test
    void testCtripService_Channel_ReturnsCorrectChannelName() {
        // When
        String result = ctripService.channel();

        // Then
        assertEquals("CTRIP", result);
    }

    // ========== Cross-Service Tests ==========

    @Test
    void testAllServices_ChannelNames_AreUnique() {
        // When
        String agodaChannel = agodaService.channel();
        String bookingcomChannel = bookingcomService.channel();
        String ctripChannel = ctripService.channel();

        // Then
        assertNotEquals(agodaChannel, bookingcomChannel);
        assertNotEquals(agodaChannel, ctripChannel);
        assertNotEquals(bookingcomChannel, ctripChannel);
    }

    @Test
    void testAllServices_ChannelNames_AreNotNull() {
        // When & Then
        assertNotNull(agodaService.channel());
        assertNotNull(bookingcomService.channel());
        assertNotNull(ctripService.channel());
    }

    @Test
    void testAllServices_ChannelNames_AreNotEmpty() {
        // When & Then
        assertFalse(agodaService.channel().isEmpty());
        assertFalse(bookingcomService.channel().isEmpty());
        assertFalse(ctripService.channel().isEmpty());
    }

    @Test
    void testAllServices_ChannelNames_AreUpperCase() {
        // When & Then
        assertEquals("AGODA", agodaService.channel().toUpperCase());
        assertEquals("BOOKINGCOM", bookingcomService.channel().toUpperCase());
        assertEquals("CTRIP", ctripService.channel().toUpperCase());
    }

    @Test
    void testGroovyServices_BasicFunctionality_WorksCorrectly() {
        // Given & When & Then
        assertDoesNotThrow(() -> {
            // Test basic operations that should not throw exceptions
            agodaService.channel();
            agodaService.createAgodaRequest("TEST_HOTEL");

            bookingcomService.channel();
            bookingcomService.requestSpan();
            bookingcomService.createRoomRateReq("TEST_HOTEL");
            bookingcomService.createBcomReqAuth("user", "pass");

            ctripService.channel();
        });
    }

    // ========== Additional Comprehensive Tests ==========

    @Test
    void testAgodaService_CreateAgodaRequest_WithDifferentHotelIds_ReturnsValidXML() {
        // Given
        String[] hotelIds = {"HOTEL_001", "HOTEL_002", "HOTEL_003", ""};

        for (String hotelId : hotelIds) {
            // When
            Object result = agodaService.createAgodaRequest(hotelId);

            // Then
            assertNotNull(result);
            String resultStr = String.valueOf(result);
            assertNotNull(resultStr);
            assertFalse(resultStr.isEmpty());
            assertTrue(resultStr.contains("request"));
            assertTrue(resultStr.contains("criteria"));
            assertTrue(resultStr.contains("property"));
        }
    }

    @Test
    void testBookingcomService_CreateRoomRateReq_WithDifferentHotelIds_ReturnsValidXML() {
        // Given
        String[] hotelIds = {"BOOKING_001", "BOOKING_002", "BOOKING_003", ""};

        for (String hotelId : hotelIds) {
            // When
            Object resultObj = bookingcomService.createRoomRateReq(hotelId);
            String result = String.valueOf(resultObj);

            // Then
            assertNotNull(result);
            assertFalse(result.isEmpty());
            assertTrue(result.contains("request"));
        }
    }

    @Test
    void testBookingcomService_CreateBcomReqAuth_WithValidCredentials_ReturnsBasicAuth() {
        // Given
        String username = "test-user";
        String password = "test-password";

        // When
        Object resultObj = bookingcomService.createBcomReqAuth(username, password);
        String result = String.valueOf(resultObj);

        // Then
        assertNotNull(result);
        assertFalse(result.isEmpty());
        assertTrue(result.startsWith("Basic "));
    }



    @Test
    void testBookingcomService_RequestSpan_ReturnsConfiguredValue() {
        // When
        Integer result = bookingcomService.requestSpan();

        // Then
        assertNotNull(result);
        assertEquals(30000, result);
    }



    @Test
    void testGroovyServices_ChannelNames_AreCorrect() {
        // When & Then
        assertEquals("AGODA", agodaService.channel());
        assertEquals("BOOKINGCOM", bookingcomService.channel());
        assertEquals("CTRIP", ctripService.channel());
    }

    @Test
    void testGroovyServices_ThreadSafety_ConcurrentAccess() {
        // Given
        int threadCount = 10;
        int operationsPerThread = 5;
        Exception[] exceptions = new Exception[threadCount];
        Thread[] threads = new Thread[threadCount];

        // When
        for (int i = 0; i < threadCount; i++) {
            final int threadIndex = i;
            threads[i] = new Thread(() -> {
                try {
                    for (int j = 0; j < operationsPerThread; j++) {
                        // Test concurrent access to Groovy services
                        agodaService.channel();
                        agodaService.createAgodaRequest("CONCURRENT_HOTEL_" + threadIndex + "_" + j);

                        bookingcomService.channel();
                        bookingcomService.requestSpan();
                        bookingcomService.createRoomRateReq("CONCURRENT_HOTEL_" + threadIndex + "_" + j);

                        ctripService.channel();
                    }
                } catch (Exception e) {
                    exceptions[threadIndex] = e;
                }
            });
            threads[i].start();
        }

        // Wait for all threads to complete
        for (Thread thread : threads) {
            try {
                thread.join();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }

        // Then
        for (int i = 0; i < threadCount; i++) {
            assertNull(exceptions[i], "Thread " + i + " should not have thrown an exception");
        }
    }

    @Test
    void testGroovyServices_PerformanceBaseline_ExecutesWithinReasonableTime() {
        // Given
        long startTime = System.currentTimeMillis();
        int iterations = 100;

        // When
        for (int i = 0; i < iterations; i++) {
            agodaService.channel();
            agodaService.createAgodaRequest("PERF_HOTEL_" + i);

            bookingcomService.channel();
            bookingcomService.requestSpan();
            bookingcomService.createRoomRateReq("PERF_HOTEL_" + i);
            bookingcomService.createBcomReqAuth("user" + i, "pass" + i);

            ctripService.channel();
        }

        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;

        // Then
        // Should complete within reasonable time (less than 5 seconds for 100 iterations)
        assertTrue(duration < 5000, "Performance test took too long: " + duration + "ms");
    }

    @Test
    void testGroovyServices_WithSpecialCharacters_HandlesGracefully() {
        // Given
        String specialHotelId = "HOTEL_123&<>\"'";

        // When & Then
        assertDoesNotThrow(() -> {
            Object agodaResult = agodaService.createAgodaRequest(specialHotelId);
            Object bookingcomResult = bookingcomService.createRoomRateReq(specialHotelId);

            assertNotNull(agodaResult);
            assertNotNull(bookingcomResult);
        });
    }

    @Test
    void testGroovyServices_WithUnicodeCharacters_HandlesGracefully() {
        // Given
        String unicodeHotelId = "酒店_123_北京";

        // When & Then
        assertDoesNotThrow(() -> {
            Object agodaResult = agodaService.createAgodaRequest(unicodeHotelId);
            Object bookingcomResult = bookingcomService.createRoomRateReq(unicodeHotelId);

            assertNotNull(agodaResult);
            assertNotNull(bookingcomResult);
        });
    }

    @Test
    void testGroovyServices_ThreadSafety_BasicOperations() {
        // Given
        String[] channelHotelIds = {"HOTEL_1", "HOTEL_2", "HOTEL_3"};

        // When
        Thread[] threads = new Thread[9]; // 3 services × 3 operations
        Exception[] exceptions = new Exception[9];

        for (int i = 0; i < 3; i++) {
            final int index = i;
            final String hotelId = channelHotelIds[i];

            // Agoda thread
            threads[i * 3] = new Thread(() -> {
                try {
                    agodaService.createAgodaRequest(hotelId);
                } catch (Exception e) {
                    exceptions[index * 3] = e;
                }
            });

            // Booking.com thread
            threads[i * 3 + 1] = new Thread(() -> {
                try {
                    bookingcomService.createRoomRateReq(hotelId);
                } catch (Exception e) {
                    exceptions[index * 3 + 1] = e;
                }
            });

            // Ctrip thread
            threads[i * 3 + 2] = new Thread(() -> {
                try {
                    ctripService.channel();
                } catch (Exception e) {
                    exceptions[index * 3 + 2] = e;
                }
            });
        }

        // Start all threads
        for (Thread thread : threads) {
            thread.start();
        }

        // Wait for all threads to complete
        for (Thread thread : threads) {
            try {
                thread.join();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }

        // Then
        for (int i = 0; i < 9; i++) {
            assertNull(exceptions[i], "Thread " + i + " should not have thrown an exception");
        }
    }

    @Test
    void testGroovyServices_InstanceCreation_WorksCorrectly() {
        // When & Then
        assertNotNull(agodaService);
        assertNotNull(bookingcomService);
        assertNotNull(ctripService);

        // Verify they are different instances
        assertNotSame(agodaService, bookingcomService);
        assertNotSame(agodaService, ctripService);
        assertNotSame(bookingcomService, ctripService);
    }

    @Test
    void testGroovyServices_ClassTypes_AreCorrect() {
        // When & Then
        assertTrue(agodaService instanceof AgodaServiceImpl);
        assertTrue(bookingcomService instanceof BookingcomServiceImpl);
        assertTrue(ctripService instanceof CtripServiceImpl);
    }

    @Test
    void testGroovyServices_PackageStructure_IsCorrect() {
        // When & Then
        assertEquals("com.derbysoft.next.propertyconnect.channel.task.service.impl.agoda.AgodaServiceImpl",
                    agodaService.getClass().getName());
        assertEquals("com.derbysoft.next.propertyconnect.channel.task.service.impl.bookingcom.BookingcomServiceImpl",
                    bookingcomService.getClass().getName());
        assertEquals("com.derbysoft.next.propertyconnect.channel.task.service.impl.ctrip.CtripServiceImpl",
                    ctripService.getClass().getName());
    }
}
