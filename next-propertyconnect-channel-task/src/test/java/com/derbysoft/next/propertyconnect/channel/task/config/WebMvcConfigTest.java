package com.derbysoft.next.propertyconnect.channel.task.config;

import com.derbysoft.next.commons.boot.logsupport.inteceptor.StreamLogFilter;
import com.derbysoft.next.commons.boot.logsupport.inteceptor.StreamLogInterceptor;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.multipart.MultipartResolver;
import org.springframework.web.multipart.support.StandardServletMultipartResolver;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * Comprehensive unit tests for WebMvcConfig
 * Tests Web MVC configuration including multipart resolver, interceptors, and filters
 */
@ExtendWith(MockitoExtension.class)
class WebMvcConfigTest {

    @Mock
    private InterceptorRegistry mockInterceptorRegistry;

    private WebMvcConfig webMvcConfig;

    @BeforeEach
    void setUp() {
        webMvcConfig = new WebMvcConfig();
    }

    @Test
    void testMultipartResolver_ReturnsStandardServletMultipartResolver() {
        // When
        MultipartResolver result = webMvcConfig.multipartResolver();

        // Then
        assertNotNull(result);
        assertTrue(result instanceof StandardServletMultipartResolver);
    }

    @Test
    void testMultipartResolver_HasBeanAnnotation() throws NoSuchMethodException {
        // When
        var method = WebMvcConfig.class.getMethod("multipartResolver");

        // Then
        assertTrue(method.isAnnotationPresent(org.springframework.context.annotation.Bean.class));
        
        var beanAnnotation = method.getAnnotation(org.springframework.context.annotation.Bean.class);
        assertEquals(1, beanAnnotation.name().length);
        assertEquals("multipartResolver", beanAnnotation.name()[0]);
    }

    @Test
    void testStreamLogInterceptor_ReturnsStreamLogInterceptor() {
        // When
        StreamLogInterceptor result = webMvcConfig.streamLogInterceptor();

        // Then
        assertNotNull(result);
        assertTrue(result instanceof StreamLogInterceptor);
    }

    @Test
    void testStreamLogInterceptor_HasBeanAnnotation() throws NoSuchMethodException {
        // When
        var method = WebMvcConfig.class.getMethod("streamLogInterceptor");

        // Then
        assertTrue(method.isAnnotationPresent(org.springframework.context.annotation.Bean.class));
    }

    @Test
    void testStreamLogFilter_ReturnsStreamLogFilter() {
        // When
        StreamLogFilter result = webMvcConfig.streamLogFilter();

        // Then
        assertNotNull(result);
        assertTrue(result instanceof StreamLogFilter);
    }

    @Test
    void testStreamLogFilter_HasBeanAnnotation() throws NoSuchMethodException {
        // When
        var method = WebMvcConfig.class.getMethod("streamLogFilter");

        // Then
        assertTrue(method.isAnnotationPresent(org.springframework.context.annotation.Bean.class));
    }

    @Test
    void testAddInterceptors_AddsStreamLogInterceptor() {
        // Given
        var mockRegistration = mock(org.springframework.web.servlet.config.annotation.InterceptorRegistration.class);
        when(mockInterceptorRegistry.addInterceptor(any(StreamLogInterceptor.class)))
            .thenReturn(mockRegistration);
        when(mockRegistration.addPathPatterns("/**")).thenReturn(mockRegistration);

        // When
        webMvcConfig.addInterceptors(mockInterceptorRegistry);

        // Then
        verify(mockInterceptorRegistry).addInterceptor(any(StreamLogInterceptor.class));
        verify(mockRegistration).addPathPatterns("/**");
    }

    @Test
    void testWebMvcConfig_IsSpringConfiguration() {
        // When
        Class<WebMvcConfig> clazz = WebMvcConfig.class;

        // Then
        assertTrue(clazz.isAnnotationPresent(org.springframework.context.annotation.Configuration.class));
    }

    @Test
    void testWebMvcConfig_ImplementsWebMvcConfigurer() {
        // When
        Class<WebMvcConfig> clazz = WebMvcConfig.class;

        // Then
        assertTrue(org.springframework.web.servlet.config.annotation.WebMvcConfigurer.class.isAssignableFrom(clazz));
    }

    @Test
    void testMultipartResolver_ReturnsNewInstanceEachTime() {
        // When
        MultipartResolver resolver1 = webMvcConfig.multipartResolver();
        MultipartResolver resolver2 = webMvcConfig.multipartResolver();

        // Then
        assertNotNull(resolver1);
        assertNotNull(resolver2);
        assertNotSame(resolver1, resolver2);
    }

    @Test
    void testStreamLogInterceptor_ReturnsNewInstanceEachTime() {
        // When
        StreamLogInterceptor interceptor1 = webMvcConfig.streamLogInterceptor();
        StreamLogInterceptor interceptor2 = webMvcConfig.streamLogInterceptor();

        // Then
        assertNotNull(interceptor1);
        assertNotNull(interceptor2);
        assertNotSame(interceptor1, interceptor2);
    }

    @Test
    void testStreamLogFilter_ReturnsNewInstanceEachTime() {
        // When
        StreamLogFilter filter1 = webMvcConfig.streamLogFilter();
        StreamLogFilter filter2 = webMvcConfig.streamLogFilter();

        // Then
        assertNotNull(filter1);
        assertNotNull(filter2);
        assertNotSame(filter1, filter2);
    }

    @Test
    void testMultipartResolver_IsConfiguredCorrectly() {
        // When
        MultipartResolver result = webMvcConfig.multipartResolver();

        // Then
        assertNotNull(result);
        assertTrue(result instanceof StandardServletMultipartResolver);
        
        // Verify it's usable
        assertDoesNotThrow(() -> {
            result.toString();
        });
    }

    @Test
    void testStreamLogInterceptor_IsConfiguredCorrectly() {
        // When
        StreamLogInterceptor result = webMvcConfig.streamLogInterceptor();

        // Then
        assertNotNull(result);
        assertTrue(result instanceof StreamLogInterceptor);
        
        // Verify it's usable
        assertDoesNotThrow(() -> {
            result.toString();
        });
    }

    @Test
    void testStreamLogFilter_IsConfiguredCorrectly() {
        // When
        StreamLogFilter result = webMvcConfig.streamLogFilter();

        // Then
        assertNotNull(result);
        assertTrue(result instanceof StreamLogFilter);
        
        // Verify it's usable
        assertDoesNotThrow(() -> {
            result.toString();
        });
    }

    @Test
    void testAddInterceptors_WithNullRegistry_ThrowsException() {
        // When & Then
        assertThrows(NullPointerException.class, () -> {
            webMvcConfig.addInterceptors(null);
        });
    }

    @Test
    void testAddInterceptors_CallsCorrectMethods() {
        // Given
        var mockRegistration = mock(org.springframework.web.servlet.config.annotation.InterceptorRegistration.class);
        when(mockInterceptorRegistry.addInterceptor(any(StreamLogInterceptor.class)))
            .thenReturn(mockRegistration);
        when(mockRegistration.addPathPatterns("/**")).thenReturn(mockRegistration);

        // When
        webMvcConfig.addInterceptors(mockInterceptorRegistry);

        // Then
        verify(mockInterceptorRegistry, times(1)).addInterceptor(any(StreamLogInterceptor.class));
        verify(mockRegistration, times(1)).addPathPatterns("/**");
        verifyNoMoreInteractions(mockInterceptorRegistry);
        verifyNoMoreInteractions(mockRegistration);
    }

    @Test
    void testWebMvcConfig_ThreadSafety() {
        // Given
        int threadCount = 10;
        Thread[] threads = new Thread[threadCount];
        MultipartResolver[] resolvers = new MultipartResolver[threadCount];
        StreamLogInterceptor[] interceptors = new StreamLogInterceptor[threadCount];
        StreamLogFilter[] filters = new StreamLogFilter[threadCount];
        Exception[] exceptions = new Exception[threadCount];

        // When
        for (int i = 0; i < threadCount; i++) {
            final int index = i;
            threads[i] = new Thread(() -> {
                try {
                    resolvers[index] = webMvcConfig.multipartResolver();
                    interceptors[index] = webMvcConfig.streamLogInterceptor();
                    filters[index] = webMvcConfig.streamLogFilter();
                } catch (Exception e) {
                    exceptions[index] = e;
                }
            });
            threads[i].start();
        }

        // Wait for all threads to complete
        for (Thread thread : threads) {
            try {
                thread.join();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }

        // Then
        for (int i = 0; i < threadCount; i++) {
            assertNull(exceptions[i], "Thread " + i + " should not have thrown an exception");
            assertNotNull(resolvers[i], "Thread " + i + " should have created a resolver");
            assertNotNull(interceptors[i], "Thread " + i + " should have created an interceptor");
            assertNotNull(filters[i], "Thread " + i + " should have created a filter");
        }
    }

    @Test
    void testWebMvcConfig_MethodReturnTypes() throws NoSuchMethodException {
        // When
        var multipartResolverMethod = WebMvcConfig.class.getMethod("multipartResolver");
        var streamLogInterceptorMethod = WebMvcConfig.class.getMethod("streamLogInterceptor");
        var streamLogFilterMethod = WebMvcConfig.class.getMethod("streamLogFilter");
        var addInterceptorsMethod = WebMvcConfig.class.getMethod("addInterceptors", InterceptorRegistry.class);

        // Then
        assertEquals(MultipartResolver.class, multipartResolverMethod.getReturnType());
        assertEquals(StreamLogInterceptor.class, streamLogInterceptorMethod.getReturnType());
        assertEquals(StreamLogFilter.class, streamLogFilterMethod.getReturnType());
        assertEquals(void.class, addInterceptorsMethod.getReturnType());
        
        assertEquals(0, multipartResolverMethod.getParameterCount());
        assertEquals(0, streamLogInterceptorMethod.getParameterCount());
        assertEquals(0, streamLogFilterMethod.getParameterCount());
        assertEquals(1, addInterceptorsMethod.getParameterCount());
        
        assertEquals(InterceptorRegistry.class, addInterceptorsMethod.getParameterTypes()[0]);
    }

    @Test
    void testWebMvcConfig_BeanMethods() throws NoSuchMethodException {
        // When
        var multipartResolverMethod = WebMvcConfig.class.getMethod("multipartResolver");
        var streamLogInterceptorMethod = WebMvcConfig.class.getMethod("streamLogInterceptor");
        var streamLogFilterMethod = WebMvcConfig.class.getMethod("streamLogFilter");

        // Then
        assertTrue(multipartResolverMethod.isAnnotationPresent(org.springframework.context.annotation.Bean.class));
        assertTrue(streamLogInterceptorMethod.isAnnotationPresent(org.springframework.context.annotation.Bean.class));
        assertTrue(streamLogFilterMethod.isAnnotationPresent(org.springframework.context.annotation.Bean.class));
    }

    @Test
    void testAddInterceptors_MethodExists() throws NoSuchMethodException {
        // When
        var method = WebMvcConfig.class.getMethod("addInterceptors", InterceptorRegistry.class);

        // Then
        assertNotNull(method);
        assertEquals("addInterceptors", method.getName());
        assertEquals(1, method.getParameterCount());
        assertEquals(InterceptorRegistry.class, method.getParameterTypes()[0]);
    }

    @Test
    void testWebMvcConfig_AllBeansAreCreatable() {
        // When & Then
        assertDoesNotThrow(() -> {
            MultipartResolver resolver = webMvcConfig.multipartResolver();
            StreamLogInterceptor interceptor = webMvcConfig.streamLogInterceptor();
            StreamLogFilter filter = webMvcConfig.streamLogFilter();
            
            assertNotNull(resolver);
            assertNotNull(interceptor);
            assertNotNull(filter);
        });
    }

    @Test
    void testWebMvcConfig_InterceptorRegistration() {
        // Given
        var realRegistry = mock(InterceptorRegistry.class);
        var mockRegistration = mock(org.springframework.web.servlet.config.annotation.InterceptorRegistration.class);
        
        when(realRegistry.addInterceptor(any(StreamLogInterceptor.class)))
            .thenReturn(mockRegistration);
        when(mockRegistration.addPathPatterns("/**")).thenReturn(mockRegistration);

        // When
        webMvcConfig.addInterceptors(realRegistry);

        // Then
        verify(realRegistry).addInterceptor(argThat(interceptor -> 
            interceptor instanceof StreamLogInterceptor));
        verify(mockRegistration).addPathPatterns("/**");
    }

    @Test
    void testWebMvcConfig_ComponentIntegration() {
        // Given
        MultipartResolver resolver = webMvcConfig.multipartResolver();
        StreamLogInterceptor interceptor = webMvcConfig.streamLogInterceptor();
        StreamLogFilter filter = webMvcConfig.streamLogFilter();

        // When & Then
        assertNotNull(resolver);
        assertNotNull(interceptor);
        assertNotNull(filter);
        
        // All components should be different instances
        assertNotSame(resolver, interceptor);
        assertNotSame(resolver, filter);
        assertNotSame(interceptor, filter);
        
        // All should be properly typed
        assertTrue(resolver instanceof StandardServletMultipartResolver);
        assertTrue(interceptor instanceof StreamLogInterceptor);
        assertTrue(filter instanceof StreamLogFilter);
    }
}
