package com.derbysoft.next.propertyconnect.channel.task.service.storageservice;

import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelHotelDTO;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelRateInfo;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelRoomInfo;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ItemStatus;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.SyncStatus;
import com.derbysoft.next.propertyconnect.channel.task.domain.entity.ChannelHotelPO;
import com.derbysoft.next.propertyconnect.channel.task.domain.entity.ChannelHotelRatePO;
import com.derbysoft.next.propertyconnect.channel.task.domain.entity.ChannelHotelRoomPO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mapstruct.MappingTarget;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CommonStorageServiceTest {

    @Mock
    private MongoTemplate mongoTemplate;

    @Mock
    private CommonStorageService.DTOtoPOTranslator<ChannelHotelPO> translator;

    private TestCommonStorageService commonStorageService;

    @BeforeEach
    void setUp() {
        commonStorageService = new TestCommonStorageService(mongoTemplate, ChannelHotelPO.class, translator);
    }

    @Test
    void testGetChannelHotel() {
        // Given
        String channelId = "test-channel";
        String channelHotelId = "test-hotel";

        ChannelHotelPO hotelPO = createTestHotelPO();
        ChannelHotelDTO expectedDTO = createTestChannelHotelDTO();

        when(mongoTemplate.findOne(any(Query.class), eq(ChannelHotelPO.class)))
                .thenReturn(hotelPO);
        when(translator.reverseMap(hotelPO)).thenReturn(expectedDTO);

        // When
        ChannelHotelDTO result = commonStorageService.getChannelHotel(channelId, channelHotelId);

        // Then
        assertNotNull(result);
        assertEquals(expectedDTO, result);
        verify(mongoTemplate).findOne(any(Query.class), eq(ChannelHotelPO.class));
        verify(translator).reverseMap(hotelPO);
    }

    @Test
    void testGetChannelHotel_NotFound() {
        // Given
        String channelId = "test-channel";
        String channelHotelId = "test-hotel";

        when(mongoTemplate.findOne(any(Query.class), eq(ChannelHotelPO.class)))
                .thenReturn(null);

        // When
        ChannelHotelDTO result = commonStorageService.getChannelHotel(channelId, channelHotelId);

        // Then
        assertNull(result);
        verify(mongoTemplate).findOne(any(Query.class), eq(ChannelHotelPO.class));
        verify(translator).reverseMap(null);
    }

    @Test
    void testSaveIncrementalChannelProduct_ExistingHotel() {
        // Given
        ChannelHotelDTO channelHotel = createTestChannelHotelDTO();
        ChannelHotelPO existingPO = createTestHotelPO();
        ChannelHotelPO savedPO = createTestHotelPO();
        ChannelHotelDTO expectedResult = createTestChannelHotelDTO();

        when(mongoTemplate.findOne(any(Query.class), eq(ChannelHotelPO.class)))
                .thenReturn(existingPO);
        when(mongoTemplate.save(any(ChannelHotelPO.class))).thenReturn(savedPO);
        when(translator.reverseMap(savedPO)).thenReturn(expectedResult);

        // When
        ChannelHotelDTO result = commonStorageService.saveIncrementalChannelProduct(channelHotel);

        // Then
        assertNotNull(result);
        assertEquals(expectedResult, result);
        verify(mongoTemplate).findOne(any(Query.class), eq(ChannelHotelPO.class));
        verify(translator).fillIn(channelHotel, existingPO);
        verify(mongoTemplate).save(existingPO);
        verify(translator).reverseMap(savedPO);
    }

    @Test
    void testSaveIncrementalChannelProduct_NewHotel() {
        // Given
        ChannelHotelDTO channelHotel = createTestChannelHotelDTO();
        ChannelHotelPO savedPO = createTestHotelPO();
        ChannelHotelDTO expectedResult = createTestChannelHotelDTO();

        when(mongoTemplate.findOne(any(Query.class), eq(ChannelHotelPO.class)))
                .thenReturn(null);
        when(mongoTemplate.save(any(ChannelHotelPO.class))).thenReturn(savedPO);
        when(translator.reverseMap(savedPO)).thenReturn(expectedResult);

        // When
        ChannelHotelDTO result = commonStorageService.saveIncrementalChannelProduct(channelHotel);

        // Then
        assertNotNull(result);
        assertEquals(expectedResult, result);
        verify(mongoTemplate).findOne(any(Query.class), eq(ChannelHotelPO.class));
        verify(translator).fillIn(eq(channelHotel), any(ChannelHotelPO.class));
        verify(mongoTemplate).save(any(ChannelHotelPO.class));
        verify(translator).reverseMap(savedPO);
    }

    @Test
    void testSaveIncrementalChannelProduct_WithRoomInfo() {
        // Given
        @SuppressWarnings("unchecked")
        CommonStorageService.DTOtoPOTranslator<ChannelHotelRoomPO> roomTranslator = mock(CommonStorageService.DTOtoPOTranslator.class);
        TestCommonStorageServiceForRoom roomStorageService = new TestCommonStorageServiceForRoom(mongoTemplate, ChannelHotelRoomPO.class, roomTranslator);
        ChannelHotelDTO channelHotel = createTestChannelHotelDTO();
        ChannelRoomInfo roomInfo = new ChannelRoomInfo("room1");
        channelHotel.setRoomInfo(roomInfo);

        ChannelHotelRoomPO existingRoomPO = createTestRoomPO();
        ChannelHotelRoomPO savedRoomPO = createTestRoomPO();
        ChannelHotelDTO expectedResult = createTestChannelHotelDTO();

        when(mongoTemplate.findOne(any(Query.class), eq(ChannelHotelRoomPO.class)))
                .thenReturn(existingRoomPO);
        when(mongoTemplate.save(any(ChannelHotelRoomPO.class))).thenReturn(savedRoomPO);
        when(roomTranslator.reverseMap(savedRoomPO)).thenReturn(expectedResult);

        // When
        ChannelHotelDTO result = roomStorageService.saveIncrementalChannelProduct(channelHotel);

        // Then
        assertNotNull(result);
        verify(mongoTemplate).findOne(any(Query.class), eq(ChannelHotelRoomPO.class));
        verify(roomTranslator).fillIn(eq(channelHotel), any(ChannelHotelRoomPO.class));
        verify(mongoTemplate).save(any(ChannelHotelRoomPO.class));
    }

    @Test
    void testSaveIncrementalChannelProduct_WithRateInfo() {
        // Given
        @SuppressWarnings("unchecked")
        CommonStorageService.DTOtoPOTranslator<ChannelHotelRatePO> rateTranslator = mock(CommonStorageService.DTOtoPOTranslator.class);
        TestCommonStorageServiceForRate rateStorageService = new TestCommonStorageServiceForRate(mongoTemplate, ChannelHotelRatePO.class, rateTranslator);
        ChannelHotelDTO channelHotel = createTestChannelHotelDTO();
        ChannelRateInfo rateInfo = new ChannelRateInfo("rate1");
        channelHotel.setRateInfo(rateInfo);

        ChannelHotelRatePO existingRatePO = createTestRatePO();
        ChannelHotelRatePO savedRatePO = createTestRatePO();
        ChannelHotelDTO expectedResult = createTestChannelHotelDTO();

        when(mongoTemplate.findOne(any(Query.class), eq(ChannelHotelRatePO.class)))
                .thenReturn(existingRatePO);
        when(mongoTemplate.save(any(ChannelHotelRatePO.class))).thenReturn(savedRatePO);
        when(rateTranslator.reverseMap(savedRatePO)).thenReturn(expectedResult);

        // When
        ChannelHotelDTO result = rateStorageService.saveIncrementalChannelProduct(channelHotel);

        // Then
        assertNotNull(result);
        verify(mongoTemplate).findOne(any(Query.class), eq(ChannelHotelRatePO.class));
        verify(rateTranslator).fillIn(eq(channelHotel), any(ChannelHotelRatePO.class));
        verify(mongoTemplate).save(any(ChannelHotelRatePO.class));
    }

    @Test
    void testDeleteChannelHotel() {
        // Given
        String channelId = "test-channel";
        String channelHotelId = "test-hotel";

        // When
        commonStorageService.deleteChannelHotel(channelId, channelHotelId);

        // Then
        verify(mongoTemplate).remove(any(Query.class), eq(ChannelHotelPO.class));
    }

    @Test
    void testQueryChannelHotels() {
        // Given
        String channelId = "test-channel";
        String channelHotelId = "test-hotel";

        List<ChannelHotelPO> expectedPOs = Arrays.asList(createTestHotelPO(), createTestHotelPO());
        when(mongoTemplate.find(any(Query.class), eq(ChannelHotelPO.class)))
                .thenReturn(expectedPOs);

        // When
        List<ChannelHotelPO> result = commonStorageService.queryChannelHotels(channelId, channelHotelId);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        verify(mongoTemplate).find(any(Query.class), eq(ChannelHotelPO.class));
    }

    @Test
    void testQueryChannelHotelWithCode() {
        // Given
        String channelId = "test-channel";
        String channelHotelId = "test-hotel";
        String code = "test-code";

        ChannelHotelPO expectedPO = createTestHotelPO();
        when(mongoTemplate.findOne(any(Query.class), eq(ChannelHotelPO.class)))
                .thenReturn(expectedPO);

        // When
        ChannelHotelPO result = commonStorageService.queryChannelHotel(channelId, channelHotelId, code);

        // Then
        assertNotNull(result);
        assertEquals(expectedPO, result);
        verify(mongoTemplate).findOne(any(Query.class), eq(ChannelHotelPO.class));
    }

    // Test implementation of CommonStorageService for testing purposes
    private static class TestCommonStorageService extends CommonStorageService<ChannelHotelPO> {
        public TestCommonStorageService(MongoTemplate mongoTemplate, Class<ChannelHotelPO> poClass, DTOtoPOTranslator<ChannelHotelPO> translator) {
            super(mongoTemplate, poClass, translator);
        }

        // Expose protected methods for testing
        @Override
        public ChannelHotelPO queryChannelHotel(String channelId, String channelHotelId) {
            return super.queryChannelHotel(channelId, channelHotelId);
        }

        @Override
        public ChannelHotelPO queryChannelHotel(String channelId, String channelHotelId, String code) {
            return super.queryChannelHotel(channelId, channelHotelId, code);
        }

        @Override
        public List<ChannelHotelPO> queryChannelHotels(String channelId, String channelHotelId) {
            return super.queryChannelHotels(channelId, channelHotelId);
        }
    }

    private static class TestCommonStorageServiceForRoom extends CommonStorageService<ChannelHotelRoomPO> {
        public TestCommonStorageServiceForRoom(MongoTemplate mongoTemplate, Class<ChannelHotelRoomPO> poClass, DTOtoPOTranslator<ChannelHotelRoomPO> translator) {
            super(mongoTemplate, poClass, translator);
        }
    }

    private static class TestCommonStorageServiceForRate extends CommonStorageService<ChannelHotelRatePO> {
        public TestCommonStorageServiceForRate(MongoTemplate mongoTemplate, Class<ChannelHotelRatePO> poClass, DTOtoPOTranslator<ChannelHotelRatePO> translator) {
            super(mongoTemplate, poClass, translator);
        }
    }

    private ChannelHotelDTO createTestChannelHotelDTO() {
        ChannelHotelDTO dto = new ChannelHotelDTO();
        dto.setChannelId("test-channel");
        dto.setChannelHotelId("test-hotel");
        dto.setSupplierId("test-supplier");
        return dto;
    }

    private ChannelHotelPO createTestHotelPO() {
        return ChannelHotelPO.builder()
                .id("test-id")
                .channelId("test-channel")
                .channelHotelId("test-hotel")
                .supplierId("test-supplier")
                .status(ItemStatus.Actived)
                .syncStatus(SyncStatus.SYNCED)
                .build();
    }

    private ChannelHotelRoomPO createTestRoomPO() {
        return ChannelHotelRoomPO.builder()
                .id("test-id")
                .channelId("test-channel")
                .channelHotelId("test-hotel")
                .code("room1")
                .status(ItemStatus.Actived)
                .syncStatus(SyncStatus.SYNCED)
                .build();
    }

    private ChannelHotelRatePO createTestRatePO() {
        return ChannelHotelRatePO.builder()
                .id("test-id")
                .channelId("test-channel")
                .channelHotelId("test-hotel")
                .code("rate1")
                .status(ItemStatus.Actived)
                .syncStatus(SyncStatus.SYNCED)
                .build();
    }
}
