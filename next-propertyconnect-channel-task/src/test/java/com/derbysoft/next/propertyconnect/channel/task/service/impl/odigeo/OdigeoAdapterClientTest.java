package com.derbysoft.next.propertyconnect.channel.task.service.impl.odigeo;

import com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.RemoteChannelService;
import com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.adapter.ChannelHotelActivationCustomizeService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Comprehensive unit tests for OdigeoAdapterClient
 * Tests channel identification and procedure customization for Odigeo
 */
@ExtendWith(MockitoExtension.class)
class OdigeoAdapterClientTest {

    private OdigeoAdapterClient odigeoAdapterClient;

    @BeforeEach
    void setUp() {
        odigeoAdapterClient = new OdigeoAdapterClient();
    }

    @Test
    void testOdigeoAdapterClient_ImplementsCorrectInterface() {
        // Given & When & Then
        assertTrue(odigeoAdapterClient instanceof ChannelHotelActivationCustomizeService,
                "OdigeoAdapterClient should implement ChannelHotelActivationCustomizeService");
    }

    @Test
    void testChannel_ReturnsCorrectChannelName() {
        // Given & When
        String channelName = odigeoAdapterClient.channel();

        // Then
        assertNotNull(channelName, "Channel name should not be null");
        assertEquals("ODIGEO", channelName, "Channel name should be ODIGEO");
    }

    @Test
    void testChannel_ReturnsConsistentValue() {
        // Given & When
        String channelName1 = odigeoAdapterClient.channel();
        String channelName2 = odigeoAdapterClient.channel();

        // Then
        assertEquals(channelName1, channelName2, "Channel name should be consistent");
        assertSame(channelName1, channelName2, "Channel name should return same string instance");
    }

    @Test
    void testCustomizeProcedure_ReturnsCorrectOperations() {
        // Given & When
        List<RemoteChannelService.Operation> operations = odigeoAdapterClient.customizeProcedure();

        // Then
        assertNotNull(operations, "Operations list should not be null");
        assertEquals(2, operations.size(), "Should return exactly 2 operations");
        
        assertTrue(operations.contains(RemoteChannelService.Operation.SaveProperty),
                "Should contain SaveProperty operation");
        assertTrue(operations.contains(RemoteChannelService.Operation.TriggerARIRefresh),
                "Should contain TriggerARIRefresh operation");
    }

    @Test
    void testCustomizeProcedure_ReturnsOperationsInCorrectOrder() {
        // Given & When
        List<RemoteChannelService.Operation> operations = odigeoAdapterClient.customizeProcedure();

        // Then
        assertEquals(RemoteChannelService.Operation.SaveProperty, operations.get(0),
                "First operation should be SaveProperty");
        assertEquals(RemoteChannelService.Operation.TriggerARIRefresh, operations.get(1),
                "Second operation should be TriggerARIRefresh");
    }

    @Test
    void testCustomizeProcedure_ReturnsConsistentResults() {
        // Given & When
        List<RemoteChannelService.Operation> operations1 = odigeoAdapterClient.customizeProcedure();
        List<RemoteChannelService.Operation> operations2 = odigeoAdapterClient.customizeProcedure();

        // Then
        assertEquals(operations1, operations2, "Should return consistent operations");
        assertEquals(operations1.size(), operations2.size(), "Should return same number of operations");
        
        for (int i = 0; i < operations1.size(); i++) {
            assertEquals(operations1.get(i), operations2.get(i), 
                    "Operations at index " + i + " should be the same");
        }
    }

    @Test
    void testCustomizeProcedure_DoesNotContainUnnecessaryOperations() {
        // Given & When
        List<RemoteChannelService.Operation> operations = odigeoAdapterClient.customizeProcedure();

        // Then
        assertFalse(operations.contains(RemoteChannelService.Operation.SaveCredential),
                "Should not contain SaveCredential operation");
        assertFalse(operations.contains(RemoteChannelService.Operation.SaveRoomTypes),
                "Should not contain SaveRoomTypes operation");
        assertFalse(operations.contains(RemoteChannelService.Operation.SaveRatePlans),
                "Should not contain SaveRatePlans operation");
        assertFalse(operations.contains(RemoteChannelService.Operation.SaveProducts),
                "Should not contain SaveProducts operation");
        assertFalse(operations.contains(RemoteChannelService.Operation.GetProperty),
                "Should not contain GetProperty operation");
        assertFalse(operations.contains(RemoteChannelService.Operation.DeleteProperty),
                "Should not contain DeleteProperty operation");
    }

    @Test
    void testService_IsAnnotatedWithSpringService() {
        // Given
        Class<?> serviceClass = OdigeoAdapterClient.class;

        // When
        boolean hasServiceAnnotation = serviceClass.isAnnotationPresent(org.springframework.stereotype.Service.class);

        // Then
        assertTrue(hasServiceAnnotation, "Service should be annotated with @Service");
    }

    @Test
    void testService_CanBeInstantiatedMultipleTimes() {
        // Given & When
        OdigeoAdapterClient client1 = new OdigeoAdapterClient();
        OdigeoAdapterClient client2 = new OdigeoAdapterClient();

        // Then
        assertNotNull(client1, "First instance should not be null");
        assertNotNull(client2, "Second instance should not be null");
        assertNotSame(client1, client2, "Instances should be different objects");
        
        assertEquals(client1.channel(), client2.channel(), "Both instances should return same channel");
        assertEquals(client1.customizeProcedure(), client2.customizeProcedure(),
                "Both instances should return same procedure");
    }

    @Test
    void testService_ThreadSafety() {
        // Given
        int threadCount = 10;
        Thread[] threads = new Thread[threadCount];
        String[] channelResults = new String[threadCount];
        List<RemoteChannelService.Operation>[] procedureResults = new List[threadCount];

        // When
        for (int i = 0; i < threadCount; i++) {
            final int index = i;
            threads[i] = new Thread(() -> {
                channelResults[index] = odigeoAdapterClient.channel();
                procedureResults[index] = odigeoAdapterClient.customizeProcedure();
            });
            threads[i].start();
        }

        // Wait for all threads to complete
        for (Thread thread : threads) {
            try {
                thread.join();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }

        // Then
        for (int i = 0; i < threadCount; i++) {
            assertEquals("ODIGEO", channelResults[i], "All threads should return same channel");
            assertNotNull(procedureResults[i], "All threads should return valid procedure");
            assertEquals(2, procedureResults[i].size(), "All threads should return same procedure size");
        }
    }

    @Test
    void testService_SupportsPolymorphism() {
        // Given
        ChannelHotelActivationCustomizeService service = odigeoAdapterClient;

        // When
        String channel = service.channel();
        List<RemoteChannelService.Operation> procedure = service.customizeProcedure();

        // Then
        assertEquals("ODIGEO", channel, "Polymorphic call should work correctly");
        assertNotNull(procedure, "Polymorphic call should return valid procedure");
        assertEquals(2, procedure.size(), "Polymorphic call should return correct procedure size");
    }

    @Test
    void testService_CompareWithOtherChannelServices() {
        // Given
        String odigeoChannel = odigeoAdapterClient.channel();
        List<RemoteChannelService.Operation> odigeoProcedure = odigeoAdapterClient.customizeProcedure();

        // When & Then
        assertNotEquals("LASTMINUTE", odigeoChannel, "Should be different from LastMinute channel");
        assertNotEquals("BOOKINGCOM", odigeoChannel, "Should be different from Booking.com channel");
        assertNotEquals("AGODA", odigeoChannel, "Should be different from Agoda channel");
        
        // Procedure should be similar to other simple channels
        assertEquals(2, odigeoProcedure.size(), "Should have same procedure size as other simple channels");
        assertTrue(odigeoProcedure.contains(RemoteChannelService.Operation.SaveProperty),
                "Should contain SaveProperty like other channels");
        assertTrue(odigeoProcedure.contains(RemoteChannelService.Operation.TriggerARIRefresh),
                "Should contain TriggerARIRefresh like other channels");
    }

    @Test
    void testService_PerformanceCharacteristics() {
        // Given
        long startTime = System.nanoTime();

        // When
        for (int i = 0; i < 1000; i++) {
            odigeoAdapterClient.channel();
            odigeoAdapterClient.customizeProcedure();
        }
        
        long endTime = System.nanoTime();
        long duration = endTime - startTime;

        // Then
        assertTrue(duration < 1_000_000_000, "1000 calls should complete within 1 second"); // 1 second in nanoseconds
    }

    @Test
    void testService_MemoryEfficiency() {
        // Given & When
        String channel1 = odigeoAdapterClient.channel();
        String channel2 = odigeoAdapterClient.channel();
        
        List<RemoteChannelService.Operation> procedure1 = odigeoAdapterClient.customizeProcedure();
        List<RemoteChannelService.Operation> procedure2 = odigeoAdapterClient.customizeProcedure();

        // Then
        // String interning should make these the same reference
        assertSame(channel1, channel2, "Channel strings should be the same reference");
        
        // Procedures should be equal but may be different instances
        assertEquals(procedure1, procedure2, "Procedures should be equal");
    }

    @Test
    void testService_ToString() {
        // Given & When
        String toString = odigeoAdapterClient.toString();

        // Then
        assertNotNull(toString, "toString should not return null");
        assertTrue(toString.contains("OdigeoAdapterClient"), "toString should contain class name");
    }

    @Test
    void testService_ClassMetadata() {
        // Given
        Class<?> serviceClass = OdigeoAdapterClient.class;

        // When & Then
        assertEquals("OdigeoAdapterClient", serviceClass.getSimpleName(), "Class name should be correct");
        assertTrue(serviceClass.getPackage().getName().contains("odigeo"), "Package should contain odigeo");
        assertEquals(2, serviceClass.getInterfaces().length, "Should implement two interfaces (direct and inherited)");
        assertEquals(ChannelHotelActivationCustomizeService.class, serviceClass.getInterfaces()[0],
                "Should implement ChannelHotelActivationCustomizeService");
    }
}
