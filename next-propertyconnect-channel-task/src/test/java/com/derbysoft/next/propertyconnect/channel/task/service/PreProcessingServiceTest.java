package com.derbysoft.next.propertyconnect.channel.task.service;

import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelHotelDTO;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelHotelInfo;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelRoomInfo;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelRateInfo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Comprehensive unit tests for PreProcessingService
 * Tests data preprocessing functionality including pre and post processing operations
 */
@ExtendWith(MockitoExtension.class)
class PreProcessingServiceTest {

    private TestPreProcessingService preProcessingService;

    @BeforeEach
    void setUp() {
        preProcessingService = new TestPreProcessingService();
    }

    @Test
    void testChannel_ReturnsCorrectChannelId() {
        // When
        String result = preProcessingService.channel();

        // Then
        assertEquals("TEST_CHANNEL", result);
    }

    @Test
    void testPreProcess_WithValidChannelHotel_ProcessesCorrectly() {
        // Given
        ChannelHotelDTO channelHotel = createTestChannelHotelDTO();

        // When
        preProcessingService.preProcess(channelHotel);

        // Then
        assertTrue(preProcessingService.preProcessCalled);
        assertSame(channelHotel, preProcessingService.lastPreProcessedHotel);
    }

    @Test
    void testPostProcess_WithValidChannelHotel_ProcessesCorrectly() {
        // Given
        ChannelHotelDTO channelHotel = createTestChannelHotelDTO();

        // When
        preProcessingService.postProcess(channelHotel);

        // Then
        assertTrue(preProcessingService.postProcessCalled);
        assertSame(channelHotel, preProcessingService.lastPostProcessedHotel);
    }

    @Test
    void testPreProcess_WithNullChannelHotel_HandlesGracefully() {
        // Given
        ChannelHotelDTO channelHotel = null;

        // When & Then
        assertDoesNotThrow(() -> preProcessingService.preProcess(channelHotel));
        assertTrue(preProcessingService.preProcessCalled);
        assertNull(preProcessingService.lastPreProcessedHotel);
    }

    @Test
    void testPostProcess_WithNullChannelHotel_HandlesGracefully() {
        // Given
        ChannelHotelDTO channelHotel = null;

        // When & Then
        assertDoesNotThrow(() -> preProcessingService.postProcess(channelHotel));
        assertTrue(preProcessingService.postProcessCalled);
        assertNull(preProcessingService.lastPostProcessedHotel);
    }

    @Test
    void testPreProcess_WithEmptyChannelHotel_ProcessesCorrectly() {
        // Given
        ChannelHotelDTO channelHotel = new ChannelHotelDTO();

        // When
        preProcessingService.preProcess(channelHotel);

        // Then
        assertTrue(preProcessingService.preProcessCalled);
        assertSame(channelHotel, preProcessingService.lastPreProcessedHotel);
    }

    @Test
    void testPostProcess_WithEmptyChannelHotel_ProcessesCorrectly() {
        // Given
        ChannelHotelDTO channelHotel = new ChannelHotelDTO();

        // When
        preProcessingService.postProcess(channelHotel);

        // Then
        assertTrue(preProcessingService.postProcessCalled);
        assertSame(channelHotel, preProcessingService.lastPostProcessedHotel);
    }

    @Test
    void testPreProcess_WithComplexChannelHotel_ProcessesAllFields() {
        // Given
        ChannelHotelDTO channelHotel = createComplexChannelHotelDTO();

        // When
        preProcessingService.preProcess(channelHotel);

        // Then
        assertTrue(preProcessingService.preProcessCalled);
        assertSame(channelHotel, preProcessingService.lastPreProcessedHotel);
        
        // Verify all fields are accessible
        assertNotNull(channelHotel.getChannelId());
        assertNotNull(channelHotel.getChannelHotelId());
        assertNotNull(channelHotel.getHotelInfo());
        assertNotNull(channelHotel.getRoomsInfo());
        assertNotNull(channelHotel.getRatesInfo());
        assertNotNull(channelHotel.getAccountSettings());
    }

    @Test
    void testPostProcess_WithComplexChannelHotel_ProcessesAllFields() {
        // Given
        ChannelHotelDTO channelHotel = createComplexChannelHotelDTO();

        // When
        preProcessingService.postProcess(channelHotel);

        // Then
        assertTrue(preProcessingService.postProcessCalled);
        assertSame(channelHotel, preProcessingService.lastPostProcessedHotel);
        
        // Verify all fields are accessible
        assertNotNull(channelHotel.getChannelId());
        assertNotNull(channelHotel.getChannelHotelId());
        assertNotNull(channelHotel.getHotelInfo());
        assertNotNull(channelHotel.getRoomsInfo());
        assertNotNull(channelHotel.getRatesInfo());
        assertNotNull(channelHotel.getAccountSettings());
    }

    @Test
    void testPreProcess_MultipleCallsWithDifferentHotels_ProcessesEach() {
        // Given
        ChannelHotelDTO hotel1 = createTestChannelHotelDTO();
        hotel1.setChannelHotelId("HOTEL_001");
        
        ChannelHotelDTO hotel2 = createTestChannelHotelDTO();
        hotel2.setChannelHotelId("HOTEL_002");

        // When
        preProcessingService.preProcess(hotel1);
        preProcessingService.preProcess(hotel2);

        // Then
        assertTrue(preProcessingService.preProcessCalled);
        assertSame(hotel2, preProcessingService.lastPreProcessedHotel);
        assertEquals(2, preProcessingService.preProcessCallCount);
    }

    @Test
    void testPostProcess_MultipleCallsWithDifferentHotels_ProcessesEach() {
        // Given
        ChannelHotelDTO hotel1 = createTestChannelHotelDTO();
        hotel1.setChannelHotelId("HOTEL_001");
        
        ChannelHotelDTO hotel2 = createTestChannelHotelDTO();
        hotel2.setChannelHotelId("HOTEL_002");

        // When
        preProcessingService.postProcess(hotel1);
        preProcessingService.postProcess(hotel2);

        // Then
        assertTrue(preProcessingService.postProcessCalled);
        assertSame(hotel2, preProcessingService.lastPostProcessedHotel);
        assertEquals(2, preProcessingService.postProcessCallCount);
    }

    @Test
    void testPreProcess_WithChannelHotelContainingExtensions_ProcessesExtensions() {
        // Given
        ChannelHotelDTO channelHotel = createTestChannelHotelDTO();
        Map<String, Object> extensions = new HashMap<>();
        extensions.put("customField1", "value1");
        extensions.put("customField2", 123);
        extensions.put("customField3", true);
        channelHotel.setExtensions(extensions);

        // When
        preProcessingService.preProcess(channelHotel);

        // Then
        assertTrue(preProcessingService.preProcessCalled);
        assertNotNull(channelHotel.getExtensions());
        assertEquals(3, channelHotel.getExtensions().size());
        assertEquals("value1", channelHotel.getExtensions().get("customField1"));
        assertEquals(123, channelHotel.getExtensions().get("customField2"));
        assertEquals(true, channelHotel.getExtensions().get("customField3"));
    }

    @Test
    void testPostProcess_WithChannelHotelContainingExtensions_ProcessesExtensions() {
        // Given
        ChannelHotelDTO channelHotel = createTestChannelHotelDTO();
        Map<String, Object> extensions = new HashMap<>();
        extensions.put("postField1", "postValue1");
        extensions.put("postField2", 456);
        channelHotel.setExtensions(extensions);

        // When
        preProcessingService.postProcess(channelHotel);

        // Then
        assertTrue(preProcessingService.postProcessCalled);
        assertNotNull(channelHotel.getExtensions());
        assertEquals(2, channelHotel.getExtensions().size());
        assertEquals("postValue1", channelHotel.getExtensions().get("postField1"));
        assertEquals(456, channelHotel.getExtensions().get("postField2"));
    }

    @Test
    void testPreProcess_WithChannelHotelContainingAccountSettings_ProcessesSettings() {
        // Given
        ChannelHotelDTO channelHotel = createTestChannelHotelDTO();
        Map<String, Object> accountSettings = new HashMap<>();
        accountSettings.put("username", "testuser");
        accountSettings.put("password", "testpass");
        accountSettings.put("apiKey", "test-api-key");
        channelHotel.setAccountSettings(accountSettings);

        // When
        preProcessingService.preProcess(channelHotel);

        // Then
        assertTrue(preProcessingService.preProcessCalled);
        assertNotNull(channelHotel.getAccountSettings());
        assertEquals(3, channelHotel.getAccountSettings().size());
        assertEquals("testuser", channelHotel.getAccountSettings().get("username"));
        assertEquals("testpass", channelHotel.getAccountSettings().get("password"));
        assertEquals("test-api-key", channelHotel.getAccountSettings().get("apiKey"));
    }

    @Test
    void testPostProcess_WithChannelHotelContainingAccountSettings_ProcessesSettings() {
        // Given
        ChannelHotelDTO channelHotel = createTestChannelHotelDTO();
        Map<String, Object> accountSettings = new HashMap<>();
        accountSettings.put("endpoint", "https://api.test.com");
        accountSettings.put("timeout", 30000);
        channelHotel.setAccountSettings(accountSettings);

        // When
        preProcessingService.postProcess(channelHotel);

        // Then
        assertTrue(preProcessingService.postProcessCalled);
        assertNotNull(channelHotel.getAccountSettings());
        assertEquals(2, channelHotel.getAccountSettings().size());
        assertEquals("https://api.test.com", channelHotel.getAccountSettings().get("endpoint"));
        assertEquals(30000, channelHotel.getAccountSettings().get("timeout"));
    }

    @Test
    void testPreProcess_WithChannelHotelContainingRoomsAndRates_ProcessesCollections() {
        // Given
        ChannelHotelDTO channelHotel = createTestChannelHotelDTO();
        
        List<ChannelRoomInfo> rooms = Arrays.asList(
            createTestRoomInfo("ROOM_001", "Standard Room"),
            createTestRoomInfo("ROOM_002", "Deluxe Room")
        );
        channelHotel.setRoomsInfo(rooms);
        
        List<ChannelRateInfo> rates = Arrays.asList(
            createTestRateInfo("RATE_001", "Standard Rate"),
            createTestRateInfo("RATE_002", "Premium Rate")
        );
        channelHotel.setRatesInfo(rates);

        // When
        preProcessingService.preProcess(channelHotel);

        // Then
        assertTrue(preProcessingService.preProcessCalled);
        assertNotNull(channelHotel.getRoomsInfo());
        assertNotNull(channelHotel.getRatesInfo());
        assertEquals(2, channelHotel.getRoomsInfo().size());
        assertEquals(2, channelHotel.getRatesInfo().size());
        assertEquals("ROOM_001", channelHotel.getRoomsInfo().get(0).getCode());
        assertEquals("RATE_001", channelHotel.getRatesInfo().get(0).getCode());
    }

    @Test
    void testPostProcess_WithChannelHotelContainingRoomsAndRates_ProcessesCollections() {
        // Given
        ChannelHotelDTO channelHotel = createTestChannelHotelDTO();
        
        List<ChannelRoomInfo> rooms = Arrays.asList(
            createTestRoomInfo("ROOM_003", "Suite Room"),
            createTestRoomInfo("ROOM_004", "Executive Room")
        );
        channelHotel.setRoomsInfo(rooms);
        
        List<ChannelRateInfo> rates = Arrays.asList(
            createTestRateInfo("RATE_003", "Suite Rate"),
            createTestRateInfo("RATE_004", "Executive Rate")
        );
        channelHotel.setRatesInfo(rates);

        // When
        preProcessingService.postProcess(channelHotel);

        // Then
        assertTrue(preProcessingService.postProcessCalled);
        assertNotNull(channelHotel.getRoomsInfo());
        assertNotNull(channelHotel.getRatesInfo());
        assertEquals(2, channelHotel.getRoomsInfo().size());
        assertEquals(2, channelHotel.getRatesInfo().size());
        assertEquals("ROOM_003", channelHotel.getRoomsInfo().get(0).getCode());
        assertEquals("RATE_003", channelHotel.getRatesInfo().get(0).getCode());
    }

    @Test
    void testPreProcess_ResetState_ClearsCallHistory() {
        // Given
        ChannelHotelDTO channelHotel = createTestChannelHotelDTO();
        preProcessingService.preProcess(channelHotel);
        assertTrue(preProcessingService.preProcessCalled);

        // When
        preProcessingService.resetState();

        // Then
        assertFalse(preProcessingService.preProcessCalled);
        assertFalse(preProcessingService.postProcessCalled);
        assertNull(preProcessingService.lastPreProcessedHotel);
        assertNull(preProcessingService.lastPostProcessedHotel);
        assertEquals(0, preProcessingService.preProcessCallCount);
        assertEquals(0, preProcessingService.postProcessCallCount);
    }

    @Test
    void testPostProcess_ResetState_ClearsCallHistory() {
        // Given
        ChannelHotelDTO channelHotel = createTestChannelHotelDTO();
        preProcessingService.postProcess(channelHotel);
        assertTrue(preProcessingService.postProcessCalled);

        // When
        preProcessingService.resetState();

        // Then
        assertFalse(preProcessingService.preProcessCalled);
        assertFalse(preProcessingService.postProcessCalled);
        assertNull(preProcessingService.lastPreProcessedHotel);
        assertNull(preProcessingService.lastPostProcessedHotel);
        assertEquals(0, preProcessingService.preProcessCallCount);
        assertEquals(0, preProcessingService.postProcessCallCount);
    }

    private ChannelHotelDTO createTestChannelHotelDTO() {
        ChannelHotelDTO dto = new ChannelHotelDTO();
        dto.setChannelId("TEST_CHANNEL");
        dto.setChannelHotelId("HOTEL_123");
        dto.setSupplierId("SUPPLIER_001");
        dto.setDerbyHotelId("DERBY_456");
        dto.setSupplierHotelId("SUPP_789");
        dto.setOperationToken("OP_TOKEN_001");

        ChannelHotelInfo hotelInfo = new ChannelHotelInfo();
        hotelInfo.setId("HOTEL_123");
        hotelInfo.setName("Test Hotel");
        dto.setHotelInfo(hotelInfo);

        return dto;
    }

    private ChannelHotelDTO createComplexChannelHotelDTO() {
        ChannelHotelDTO dto = createTestChannelHotelDTO();
        
        // Add rooms
        List<ChannelRoomInfo> rooms = Arrays.asList(
            createTestRoomInfo("ROOM_001", "Standard Room"),
            createTestRoomInfo("ROOM_002", "Deluxe Room"),
            createTestRoomInfo("ROOM_003", "Suite Room")
        );
        dto.setRoomsInfo(rooms);
        
        // Add rates
        List<ChannelRateInfo> rates = Arrays.asList(
            createTestRateInfo("RATE_001", "Standard Rate"),
            createTestRateInfo("RATE_002", "Premium Rate")
        );
        dto.setRatesInfo(rates);
        
        // Add account settings
        Map<String, Object> accountSettings = new HashMap<>();
        accountSettings.put("username", "complexuser");
        accountSettings.put("password", "complexpass");
        accountSettings.put("endpoint", "https://complex.api.com");
        dto.setAccountSettings(accountSettings);
        
        // Add extensions
        Map<String, Object> extensions = new HashMap<>();
        extensions.put("complexField1", "complexValue1");
        extensions.put("complexField2", 999);
        dto.setExtensions(extensions);

        return dto;
    }

    private ChannelRoomInfo createTestRoomInfo(String code, String name) {
        ChannelRoomInfo roomInfo = new ChannelRoomInfo();
        roomInfo.setCode(code);
        roomInfo.setName(name);
        return roomInfo;
    }

    private ChannelRateInfo createTestRateInfo(String code, String name) {
        ChannelRateInfo rateInfo = new ChannelRateInfo();
        rateInfo.setCode(code);
        rateInfo.setName(name);
        return rateInfo;
    }

    /**
     * Test implementation of PreProcessingService for testing purposes
     */
    private static class TestPreProcessingService implements PreProcessingService {
        boolean preProcessCalled = false;
        boolean postProcessCalled = false;
        ChannelHotelDTO lastPreProcessedHotel = null;
        ChannelHotelDTO lastPostProcessedHotel = null;
        int preProcessCallCount = 0;
        int postProcessCallCount = 0;

        @Override
        public String channel() {
            return "TEST_CHANNEL";
        }

        @Override
        public void preProcess(ChannelHotelDTO channelHotel) {
            preProcessCalled = true;
            lastPreProcessedHotel = channelHotel;
            preProcessCallCount++;
        }

        @Override
        public void postProcess(ChannelHotelDTO channelHotel) {
            postProcessCalled = true;
            lastPostProcessedHotel = channelHotel;
            postProcessCallCount++;
        }

        public void resetState() {
            preProcessCalled = false;
            postProcessCalled = false;
            lastPreProcessedHotel = null;
            lastPostProcessedHotel = null;
            preProcessCallCount = 0;
            postProcessCallCount = 0;
        }
    }
}
