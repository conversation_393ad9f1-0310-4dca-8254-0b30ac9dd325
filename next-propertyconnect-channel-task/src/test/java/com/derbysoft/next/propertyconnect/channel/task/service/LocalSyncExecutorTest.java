package com.derbysoft.next.propertyconnect.channel.task.service;

import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelHotelDTO;
import com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.RemoteChannelService;
import com.derbysoft.next.propertyconnect.channel.task.service.impl.ChannelRemoteServiceFactory;
import com.derbysoft.next.propertyconnect.channel.task.service.storageservice.ChannelInfoStorageService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Mono;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * Comprehensive unit tests for LocalSyncExecutor
 * Tests execution flow, error handling, and reactive behavior
 */
@ExtendWith(MockitoExtension.class)
class LocalSyncExecutorTest {

    @Mock
    private ChannelRemoteServiceFactory channelRemoteServiceFactory;

    @Mock
    private ChannelInfoStorageService channelInfoStorageService;

    @Mock
    private ChannelHotelSetupProcedure procedure;

    @Mock
    private RemoteChannelService remoteChannelService;

    @InjectMocks
    private LocalSyncExecutor localSyncExecutor;

    private ChannelHotelDTO testChannelHotel;

    @BeforeEach
    void setUp() {
        testChannelHotel = new ChannelHotelDTO();
        testChannelHotel.setChannelId("TEST_CHANNEL");
        testChannelHotel.setChannelHotelId("TEST_HOTEL");
        testChannelHotel.setSupplierId("TEST_SUPPLIER");
    }

    @Test
    void testExecute_Success_ReturnsChannelHotel() {
        // Given
        List<RemoteChannelService.Operation> operations = Arrays.asList(
                RemoteChannelService.Operation.SaveProperty,
                RemoteChannelService.Operation.SaveRoomTypes
        );
        
        when(procedure.setupProcedure(testChannelHotel)).thenReturn(operations);
        when(procedure.destination()).thenReturn(RemoteChannelService.Destination.Adapter);
        when(channelRemoteServiceFactory.getHandler("TEST_CHANNEL", RemoteChannelService.Destination.Adapter))
                .thenReturn(remoteChannelService);
        when(remoteChannelService.execution(any(RemoteChannelService.Operation.class), eq(testChannelHotel)))
                .thenReturn(testChannelHotel);

        // When
        Mono<ChannelHotelDTO> result = localSyncExecutor.execute(testChannelHotel, procedure);

        // Then
        assertNotNull(result, "Result should not be null");
        ChannelHotelDTO actualResult = result.block();
        assertNotNull(actualResult, "Blocked result should not be null");
        assertEquals(testChannelHotel, actualResult, "Should return the same channel hotel");

        verify(procedure).setupProcedure(testChannelHotel);
        verify(procedure, times(2)).destination(); // Called once for each operation
        verify(channelRemoteServiceFactory, times(2)).getHandler("TEST_CHANNEL", RemoteChannelService.Destination.Adapter); // Called once for each operation
        verify(remoteChannelService, times(2)).execution(any(RemoteChannelService.Operation.class), eq(testChannelHotel));
    }

    @Test
    void testExecute_NullChannelHotel_ThrowsException() {
        // Given
        ChannelHotelDTO nullChannelHotel = null;

        // When & Then
        assertThrows(IllegalStateException.class, () -> {
            localSyncExecutor.execute(nullChannelHotel, procedure);
        });

        verifyNoInteractions(procedure);
        verifyNoInteractions(channelRemoteServiceFactory);
    }

    @Test
    void testExecute_NullProcedure_ThrowsException() {
        // Given
        ChannelHotelSetupProcedure nullProcedure = null;

        // When & Then
        assertThrows(IllegalStateException.class, () -> {
            localSyncExecutor.execute(testChannelHotel, nullProcedure);
        });

        verifyNoInteractions(channelRemoteServiceFactory);
    }

    @Test
    void testExecute_EmptyOperations_CompletesSuccessfully() {
        // Given
        List<RemoteChannelService.Operation> emptyOperations = Arrays.asList();
        
        when(procedure.setupProcedure(testChannelHotel)).thenReturn(emptyOperations);

        // When
        Mono<ChannelHotelDTO> result = localSyncExecutor.execute(testChannelHotel, procedure);

        // Then
        assertNotNull(result, "Result should not be null");
        ChannelHotelDTO actualResult = result.block();
        assertNotNull(actualResult, "Blocked result should not be null");
        assertEquals(testChannelHotel, actualResult, "Should return the same channel hotel");

        verify(procedure).setupProcedure(testChannelHotel);
        verifyNoInteractions(channelRemoteServiceFactory);
    }

    @Test
    void testExecute_SingleOperation_ExecutesCorrectly() {
        // Given
        List<RemoteChannelService.Operation> singleOperation = Arrays.asList(
                RemoteChannelService.Operation.SaveProperty
        );
        
        when(procedure.setupProcedure(testChannelHotel)).thenReturn(singleOperation);
        when(procedure.destination()).thenReturn(RemoteChannelService.Destination.Channel);
        when(channelRemoteServiceFactory.getHandler("TEST_CHANNEL", RemoteChannelService.Destination.Channel))
                .thenReturn(remoteChannelService);
        when(remoteChannelService.execution(RemoteChannelService.Operation.SaveProperty, testChannelHotel))
                .thenReturn(testChannelHotel);

        // When
        Mono<ChannelHotelDTO> result = localSyncExecutor.execute(testChannelHotel, procedure);

        // Then
        assertNotNull(result, "Result should not be null");
        ChannelHotelDTO actualResult = result.block();
        assertNotNull(actualResult, "Blocked result should not be null");
        assertEquals(testChannelHotel, actualResult, "Should return the same channel hotel");

        verify(remoteChannelService).execution(RemoteChannelService.Operation.SaveProperty, testChannelHotel);
    }

    @Test
    void testExecute_MultipleOperations_ExecutesInOrder() {
        // Given
        List<RemoteChannelService.Operation> operations = Arrays.asList(
                RemoteChannelService.Operation.SaveCredential,
                RemoteChannelService.Operation.SaveProperty,
                RemoteChannelService.Operation.SaveRoomTypes,
                RemoteChannelService.Operation.SaveRatePlans
        );
        
        when(procedure.setupProcedure(testChannelHotel)).thenReturn(operations);
        when(procedure.destination()).thenReturn(RemoteChannelService.Destination.Adapter);
        when(channelRemoteServiceFactory.getHandler("TEST_CHANNEL", RemoteChannelService.Destination.Adapter))
                .thenReturn(remoteChannelService);
        when(remoteChannelService.execution(any(RemoteChannelService.Operation.class), eq(testChannelHotel)))
                .thenReturn(testChannelHotel);

        // When
        Mono<ChannelHotelDTO> result = localSyncExecutor.execute(testChannelHotel, procedure);

        // Then
        assertNotNull(result, "Result should not be null");
        ChannelHotelDTO actualResult = result.block();
        assertNotNull(actualResult, "Blocked result should not be null");
        assertEquals(testChannelHotel, actualResult, "Should return the same channel hotel");

        // Verify all operations were executed
        verify(remoteChannelService).execution(RemoteChannelService.Operation.SaveCredential, testChannelHotel);
        verify(remoteChannelService).execution(RemoteChannelService.Operation.SaveProperty, testChannelHotel);
        verify(remoteChannelService).execution(RemoteChannelService.Operation.SaveRoomTypes, testChannelHotel);
        verify(remoteChannelService).execution(RemoteChannelService.Operation.SaveRatePlans, testChannelHotel);
    }

    @Test
    void testExecute_DifferentChannelIds_UsesCorrectHandler() {
        // Given
        testChannelHotel.setChannelId("DIFFERENT_CHANNEL");
        List<RemoteChannelService.Operation> operations = Arrays.asList(
                RemoteChannelService.Operation.SaveProperty
        );
        
        when(procedure.setupProcedure(testChannelHotel)).thenReturn(operations);
        when(procedure.destination()).thenReturn(RemoteChannelService.Destination.Adapter);
        when(channelRemoteServiceFactory.getHandler("DIFFERENT_CHANNEL", RemoteChannelService.Destination.Adapter))
                .thenReturn(remoteChannelService);
        when(remoteChannelService.execution(RemoteChannelService.Operation.SaveProperty, testChannelHotel))
                .thenReturn(testChannelHotel);

        // When
        Mono<ChannelHotelDTO> result = localSyncExecutor.execute(testChannelHotel, procedure);

        // Then
        assertNotNull(result, "Result should not be null");
        ChannelHotelDTO actualResult = result.block();
        assertNotNull(actualResult, "Blocked result should not be null");
        assertEquals(testChannelHotel, actualResult, "Should return the same channel hotel");

        verify(channelRemoteServiceFactory).getHandler("DIFFERENT_CHANNEL", RemoteChannelService.Destination.Adapter);
    }

    @Test
    void testExecute_ImplementsChannelHotelSetupExecutor() {
        // Given & When & Then
        assertTrue(localSyncExecutor instanceof ChannelHotelSetupExecutor,
                "LocalSyncExecutor should implement ChannelHotelSetupExecutor");
    }

    @Test
    void testExecute_HandlesComplexChannelHotelData() {
        // Given
        ChannelHotelDTO complexChannelHotel = new ChannelHotelDTO();
        complexChannelHotel.setChannelId("COMPLEX_CHANNEL");
        complexChannelHotel.setChannelHotelId("COMPLEX_HOTEL");
        complexChannelHotel.setSupplierId("COMPLEX_SUPPLIER");
        
        List<RemoteChannelService.Operation> operations = Arrays.asList(
                RemoteChannelService.Operation.SaveProperty
        );
        
        when(procedure.setupProcedure(complexChannelHotel)).thenReturn(operations);
        when(procedure.destination()).thenReturn(RemoteChannelService.Destination.Channel);
        when(channelRemoteServiceFactory.getHandler("COMPLEX_CHANNEL", RemoteChannelService.Destination.Channel))
                .thenReturn(remoteChannelService);
        when(remoteChannelService.execution(RemoteChannelService.Operation.SaveProperty, complexChannelHotel))
                .thenReturn(complexChannelHotel);

        // When
        Mono<ChannelHotelDTO> result = localSyncExecutor.execute(complexChannelHotel, procedure);

        // Then
        assertNotNull(result, "Result should not be null");
        ChannelHotelDTO actualResult = result.block();
        assertNotNull(actualResult, "Blocked result should not be null");
        assertEquals(complexChannelHotel, actualResult, "Should return the same channel hotel");
    }

    @Test
    void testExecute_ReactiveNature_IsNonBlocking() {
        // Given
        List<RemoteChannelService.Operation> operations = Arrays.asList(
                RemoteChannelService.Operation.SaveProperty
        );
        
        when(procedure.setupProcedure(testChannelHotel)).thenReturn(operations);
        when(procedure.destination()).thenReturn(RemoteChannelService.Destination.Adapter);
        when(channelRemoteServiceFactory.getHandler("TEST_CHANNEL", RemoteChannelService.Destination.Adapter))
                .thenReturn(remoteChannelService);
        when(remoteChannelService.execution(RemoteChannelService.Operation.SaveProperty, testChannelHotel))
                .thenReturn(testChannelHotel);

        // When
        Mono<ChannelHotelDTO> result = localSyncExecutor.execute(testChannelHotel, procedure);

        // Then
        assertNotNull(result, "Should return a Mono");

        // Verify the result when subscribed
        ChannelHotelDTO actualResult = result.block();
        assertNotNull(actualResult, "Blocked result should not be null");
        assertEquals(testChannelHotel, actualResult, "Should return the same channel hotel");
    }
}
