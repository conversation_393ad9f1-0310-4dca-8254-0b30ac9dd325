package com.derbysoft.next.propertyconnect.channel.task.util;

import com.derbysoft.next.propertyconnect.channel.task.domain.dto.InventoryItemStatus;
import com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.RemoteChannelService;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.SyncStatus;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.lenient;

/**
 * Comprehensive unit tests for MailContextGenerator
 * Tests email template generation for setup failures and AI mapping reports
 */
@ExtendWith(MockitoExtension.class)
class MailContextGeneratorTest {

    private MailContextGenerator mailContextGenerator;
    private Resource mockSetupTemplate;
    private Resource mockAiMappingTemplate;

    @BeforeEach
    void setUp() throws IOException {
        mailContextGenerator = new MailContextGenerator();
        
        // Create mock resources with template content
        mockSetupTemplate = mock(Resource.class);
        mockAiMappingTemplate = mock(Resource.class);
        
        String setupTemplateContent = """
            <html>
            <head><title>{{title}}</title></head>
            <body>
                <h1>{{title}}</h1>
                <p>Environment: {{env}}</p>
                <p>Channel Hotel Code: {{channelHotelCode}}</p>
                <p>Operation Token: {{operationToken}}</p>
                <p>Performance Link: {{linkPerf}}</p>
                <p>Stream Link: {{linkStream}}</p>
                <table>
                    <thead>{{failKeys}}</thead>
                    <tbody>{{failDetails}}</tbody>
                </table>
            </body>
            </html>
            """;
            
        String aiMappingTemplateContent = """
            <html>
            <head><title>{{title}}</title></head>
            <body>
                <h1>{{title}}</h1>
                <p>Environment: {{env}}</p>
                <p>Channel Hotel Code: {{channelHotelCode}}</p>
                <p>Operation Token: {{operationToken}}</p>
                <p>Performance Link: {{linkPerf}}</p>
                <p>Stream Link: {{linkStream}}</p>
                <div>{{formItems}}</div>
            </body>
            </html>
            """;

        lenient().when(mockSetupTemplate.getInputStream()).thenReturn(
            new ByteArrayInputStream(setupTemplateContent.getBytes()));
        lenient().when(mockAiMappingTemplate.getInputStream()).thenReturn(
            new ByteArrayInputStream(aiMappingTemplateContent.getBytes()));

        // Set up the static references using reflection
        setStaticReference("setupMailTemplate", mockSetupTemplate);
        setStaticReference("aiMappingMailTemplate", mockAiMappingTemplate);
        setStaticReference("kibanaUrl", "https://kibana.test.com/");
        setStaticReference("cspUrl", "https://csp.test.com/");
    }

    @SuppressWarnings("unchecked")
    private void setStaticReference(String fieldName, Object value) {
        try {
            AtomicReference<Object> reference = (AtomicReference<Object>) 
                ReflectionTestUtils.getField(MailContextGenerator.class, fieldName);
            reference.set(value);
        } catch (Exception e) {
            throw new RuntimeException("Failed to set static reference: " + fieldName, e);
        }
    }

    @Test
    void testGenerateSetupMailContent_BasicScenario_GeneratesCorrectContent() throws IOException {
        // Given
        String channel = "BOOKING";
        String hotelCode = "HOTEL_123";
        String operationToken = "OP_TOKEN_001";
        
        List<Pair<RemoteChannelService.Operation, InventoryItemStatus>> failedItems = new ArrayList<>();
        InventoryItemStatus failedItem = createMockInventoryItemStatus("ITEM_001", SyncStatus.SYNC_FAILED, "ERR_001", "Connection timeout");
        failedItems.add(Pair.of(RemoteChannelService.Operation.SaveProperty, failedItem));

        try (MockedStatic<EnvUtil> mockedEnvUtil = mockStatic(EnvUtil.class)) {
            mockedEnvUtil.when(EnvUtil::getEnv).thenReturn("test");

            // When
            String result = MailContextGenerator.generateSetupMailContent(channel, hotelCode, operationToken, failedItems);

            // Then
            assertNotNull(result);
            assertTrue(result.contains("BOOKING Hotel Setup Failure Alarm"));
            assertTrue(result.contains("Environment: TEST"));
            assertTrue(result.contains("Channel Hotel Code: HOTEL_123"));
            assertTrue(result.contains("Operation Token: OP_TOKEN_001"));
            assertTrue(result.contains("SaveProperty"));
            assertTrue(result.contains("ITEM_001"));
            assertTrue(result.contains("SYNC_FAILED"));
            assertTrue(result.contains("ERR_001"));
            assertTrue(result.contains("Connection timeout"));
            assertTrue(result.contains("kibana.test.com"));
            assertTrue(result.contains("csp.test.com"));
        }
    }

    @Test
    void testGenerateSetupMailContent_MultipleFailedItems_GeneratesAllItems() throws IOException {
        // Given
        String channel = "EXPEDIA";
        String hotelCode = "HOTEL_456";
        String operationToken = "OP_TOKEN_002";
        
        List<Pair<RemoteChannelService.Operation, InventoryItemStatus>> failedItems = new ArrayList<>();
        
        InventoryItemStatus failedItem1 = createMockInventoryItemStatus("ROOM_001", SyncStatus.SYNC_FAILED, "ERR_001", "Room creation failed");
        InventoryItemStatus failedItem2 = createMockInventoryItemStatus("RATE_001", SyncStatus.SYNC_FAILED, "ERR_002", "Rate creation failed");
        InventoryItemStatus failedItem3 = createMockInventoryItemStatus("PRODUCT_001", SyncStatus.SYNC_FAILED, "ERR_003", "Product creation failed");

        failedItems.add(Pair.of(RemoteChannelService.Operation.SaveRoomTypes, failedItem1));
        failedItems.add(Pair.of(RemoteChannelService.Operation.SaveRatePlans, failedItem2));
        failedItems.add(Pair.of(RemoteChannelService.Operation.SaveProducts, failedItem3));

        try (MockedStatic<EnvUtil> mockedEnvUtil = mockStatic(EnvUtil.class)) {
            mockedEnvUtil.when(EnvUtil::getEnv).thenReturn("prod");

            // When
            String result = MailContextGenerator.generateSetupMailContent(channel, hotelCode, operationToken, failedItems);

            // Then
            assertNotNull(result);
            assertTrue(result.contains("EXPEDIA Hotel Setup Failure Alarm"));
            assertTrue(result.contains("Environment: PROD"));
            assertTrue(result.contains("SaveRoomTypes"));
            assertTrue(result.contains("SaveRatePlans"));
            assertTrue(result.contains("SaveProducts"));
            assertTrue(result.contains("ROOM_001"));
            assertTrue(result.contains("RATE_001"));
            assertTrue(result.contains("PRODUCT_001"));
            assertTrue(result.contains("Room creation failed"));
            assertTrue(result.contains("Rate creation failed"));
            assertTrue(result.contains("Product creation failed"));
        }
    }

    @Test
    void testGenerateSetupMailContent_EmptyFailedItems_GeneratesEmptyTable() throws IOException {
        // Given
        String channel = "AGODA";
        String hotelCode = "HOTEL_789";
        String operationToken = "OP_TOKEN_003";
        List<Pair<RemoteChannelService.Operation, InventoryItemStatus>> failedItems = new ArrayList<>();

        try (MockedStatic<EnvUtil> mockedEnvUtil = mockStatic(EnvUtil.class)) {
            mockedEnvUtil.when(EnvUtil::getEnv).thenReturn("dev");

            // When
            String result = MailContextGenerator.generateSetupMailContent(channel, hotelCode, operationToken, failedItems);

            // Then
            assertNotNull(result);
            assertTrue(result.contains("AGODA Hotel Setup Failure Alarm"));
            assertTrue(result.contains("Environment: DEV"));
            assertTrue(result.contains("Channel Hotel Code: HOTEL_789"));
            assertTrue(result.contains("Operation Token: OP_TOKEN_003"));
            // Should contain table headers but no data rows
            assertTrue(result.contains("<th"));
        }
    }

    @Test
    void testGenerateAiMappingReportMailContent_BasicScenario_GeneratesCorrectContent() throws IOException {
        // Given
        String channel = "CTRIP";
        String hotelCode = "HOTEL_999";
        String operationToken = "OP_TOKEN_004";
        
        LinkedHashMap<String, String> diagnosisData = new LinkedHashMap<>();
        diagnosisData.put("Hotel Name", "Test Hotel");
        diagnosisData.put("Room Count", "25");
        diagnosisData.put("Rate Plan Count", "5");
        diagnosisData.put("Mapping Status", "Completed");

        try (MockedStatic<EnvUtil> mockedEnvUtil = mockStatic(EnvUtil.class)) {
            mockedEnvUtil.when(EnvUtil::getEnv).thenReturn("uat");

            // When
            String result = MailContextGenerator.generateAiMappingReportMailContent(channel, hotelCode, operationToken, diagnosisData);

            // Then
            assertNotNull(result);
            assertTrue(result.contains("CTRIP AIMapping Diagnosis Report"));
            assertTrue(result.contains("Environment: UAT"));
            assertTrue(result.contains("Channel Hotel Code: HOTEL_999"));
            assertTrue(result.contains("Operation Token: OP_TOKEN_004"));
            assertTrue(result.contains("Hotel Name"));
            assertTrue(result.contains("Test Hotel"));
            assertTrue(result.contains("Room Count"));
            assertTrue(result.contains("25"));
            assertTrue(result.contains("Rate Plan Count"));
            assertTrue(result.contains("5"));
            assertTrue(result.contains("Mapping Status"));
            assertTrue(result.contains("Completed"));
        }
    }

    @Test
    void testGenerateAiMappingReportMailContent_EmptyDiagnosisData_GeneratesEmptyForm() throws IOException {
        // Given
        String channel = "AIRBNB";
        String hotelCode = "HOTEL_000";
        String operationToken = "OP_TOKEN_005";
        LinkedHashMap<String, String> diagnosisData = new LinkedHashMap<>();

        try (MockedStatic<EnvUtil> mockedEnvUtil = mockStatic(EnvUtil.class)) {
            mockedEnvUtil.when(EnvUtil::getEnv).thenReturn("test");

            // When
            String result = MailContextGenerator.generateAiMappingReportMailContent(channel, hotelCode, operationToken, diagnosisData);

            // Then
            assertNotNull(result);
            assertTrue(result.contains("AIRBNB AIMapping Diagnosis Report"));
            assertTrue(result.contains("Environment: TEST"));
            assertTrue(result.contains("Channel Hotel Code: HOTEL_000"));
            assertTrue(result.contains("Operation Token: OP_TOKEN_005"));
            // Should not contain any form items
            assertFalse(result.contains("<tr"));
        }
    }

    @Test
    void testGenerateAiMappingReportMailContent_SpecialCharacters_HandlesCorrectly() throws IOException {
        // Given
        String channel = "FLIGGY";
        String hotelCode = "HOTEL_SPECIAL";
        String operationToken = "OP_TOKEN_SPECIAL";
        
        LinkedHashMap<String, String> diagnosisData = new LinkedHashMap<>();
        diagnosisData.put("Hotel Name", "Test Hotel & Resort <Premium>");
        diagnosisData.put("Description", "A hotel with \"special\" characters & symbols");
        diagnosisData.put("Address", "123 Main St. <City>, Country");

        try (MockedStatic<EnvUtil> mockedEnvUtil = mockStatic(EnvUtil.class)) {
            mockedEnvUtil.when(EnvUtil::getEnv).thenReturn("prod");

            // When
            String result = MailContextGenerator.generateAiMappingReportMailContent(channel, hotelCode, operationToken, diagnosisData);

            // Then
            assertNotNull(result);
            assertTrue(result.contains("FLIGGY AIMapping Diagnosis Report"));
            assertTrue(result.contains("Test Hotel & Resort <Premium>"));
            assertTrue(result.contains("A hotel with \"special\" characters & symbols"));
            assertTrue(result.contains("123 Main St. <City>, Country"));
        }
    }

    @Test
    void testGenerateSetupMailContent_TableStructure_GeneratesCorrectHTML() throws IOException {
        // Given
        String channel = "BOOKING";
        String hotelCode = "HOTEL_TABLE";
        String operationToken = "OP_TOKEN_TABLE";
        
        List<Pair<RemoteChannelService.Operation, InventoryItemStatus>> failedItems = new ArrayList<>();
        InventoryItemStatus failedItem = createMockInventoryItemStatus("ITEM_TABLE", SyncStatus.SYNC_FAILED, "ERR_TABLE", "Table test error");
        failedItems.add(Pair.of(RemoteChannelService.Operation.SaveProperty, failedItem));

        try (MockedStatic<EnvUtil> mockedEnvUtil = mockStatic(EnvUtil.class)) {
            mockedEnvUtil.when(EnvUtil::getEnv).thenReturn("test");

            // When
            String result = MailContextGenerator.generateSetupMailContent(channel, hotelCode, operationToken, failedItems);

            // Then
            assertNotNull(result);
            // Check table headers are generated
            assertTrue(result.contains("<th"));
            assertTrue(result.contains("Operation"));
            assertTrue(result.contains("Item"));
            assertTrue(result.contains("SyncStatus"));
            assertTrue(result.contains("ErrorCode"));
            assertTrue(result.contains("ErrorMessage"));
            
            // Check table data is generated
            assertTrue(result.contains("<tr"));
            assertTrue(result.contains("<td"));
        }
    }

    @Test
    void testGenerateAiMappingReportMailContent_FormStructure_GeneratesCorrectHTML() throws IOException {
        // Given
        String channel = "EXPEDIA";
        String hotelCode = "HOTEL_FORM";
        String operationToken = "OP_TOKEN_FORM";
        
        LinkedHashMap<String, String> diagnosisData = new LinkedHashMap<>();
        diagnosisData.put("Field1", "Value1");
        diagnosisData.put("Field2", "Value2");

        try (MockedStatic<EnvUtil> mockedEnvUtil = mockStatic(EnvUtil.class)) {
            mockedEnvUtil.when(EnvUtil::getEnv).thenReturn("test");

            // When
            String result = MailContextGenerator.generateAiMappingReportMailContent(channel, hotelCode, operationToken, diagnosisData);

            // Then
            assertNotNull(result);
            // Check form structure is generated
            assertTrue(result.contains("<tr"));
            assertTrue(result.contains("<td"));
            assertTrue(result.contains("Field1"));
            assertTrue(result.contains("Value1"));
            assertTrue(result.contains("Field2"));
            assertTrue(result.contains("Value2"));
        }
    }

    @Test
    void testSetSetupMailTemplate_InitializesCorrectly() {
        // Given
        Resource setupTemplate = mock(Resource.class);
        Resource aiTemplate = mock(Resource.class);
        String kibanaUrl = "https://test-kibana.com/";
        String cspUrl = "https://test-csp.com/";

        // When
        mailContextGenerator.setSetupMailTemplate(setupTemplate, aiTemplate, kibanaUrl, cspUrl);

        // Then
        // Verify that the static references are set (this is tested indirectly through the generation methods)
        assertNotNull(mailContextGenerator);
    }

    private InventoryItemStatus createMockInventoryItemStatus(String code, SyncStatus syncStatus, String errorCode, String errorMessage) {
        InventoryItemStatus status = mock(InventoryItemStatus.class);
        when(status.getCode()).thenReturn(code);
        when(status.getSyncStatus()).thenReturn(syncStatus);
        when(status.getErrorCode()).thenReturn(errorCode);
        when(status.getErrorMessage()).thenReturn(errorMessage);
        return status;
    }
}
