package com.derbysoft.next.propertyconnect.channel.task.domain.dto;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class ItemStatusTest {

    @Test
    void testEnumValues() {
        // Test that all expected enum values exist
        ItemStatus[] values = ItemStatus.values();
        assertEquals(2, values.length);

        // Test specific values
        assertNotNull(ItemStatus.Actived);
        assertNotNull(ItemStatus.Deactived);
    }

    @Test
    void testValueOf() {
        // Test valueOf method
        assertEquals(ItemStatus.Actived, ItemStatus.valueOf("Actived"));
        assertEquals(ItemStatus.Deactived, ItemStatus.valueOf("Deactived"));
    }

    @Test
    void testValueOfInvalidValue() {
        // Test valueOf with invalid value
        assertThrows(IllegalArgumentException.class, () -> ItemStatus.valueOf("Invalid"));
        assertThrows(IllegalArgumentException.class, () -> ItemStatus.valueOf("ACTIVE"));
        assertThrows(IllegalArgumentException.class, () -> ItemStatus.valueOf("active"));
        assertThrows(IllegalArgumentException.class, () -> ItemStatus.valueOf(""));
    }

    @Test
    void testValueOfNullValue() {
        // Test valueOf with null value
        assertThrows(NullPointerException.class, () -> ItemStatus.valueOf(null));
    }

    @Test
    void testToString() {
        // Test toString method
        assertEquals("Actived", ItemStatus.Actived.toString());
        assertEquals("Deactived", ItemStatus.Deactived.toString());
    }

    @Test
    void testName() {
        // Test name method
        assertEquals("Actived", ItemStatus.Actived.name());
        assertEquals("Deactived", ItemStatus.Deactived.name());
    }

    @Test
    void testOrdinal() {
        // Test ordinal method
        assertEquals(0, ItemStatus.Actived.ordinal());
        assertEquals(1, ItemStatus.Deactived.ordinal());
    }

    @Test
    void testEquality() {
        // Test equality
        assertEquals(ItemStatus.Actived, ItemStatus.Actived);
        assertEquals(ItemStatus.Deactived, ItemStatus.Deactived);
        assertNotEquals(ItemStatus.Actived, ItemStatus.Deactived);
        assertNotEquals(ItemStatus.Deactived, ItemStatus.Actived);
    }

    @Test
    void testHashCode() {
        // Test hashCode consistency
        assertEquals(ItemStatus.Actived.hashCode(), ItemStatus.Actived.hashCode());
        assertEquals(ItemStatus.Deactived.hashCode(), ItemStatus.Deactived.hashCode());
        
        // Different enum values should have different hash codes (though not guaranteed)
        assertNotEquals(ItemStatus.Actived.hashCode(), ItemStatus.Deactived.hashCode());
    }

    @Test
    void testCompareTo() {
        // Test compareTo method (based on ordinal)
        assertTrue(ItemStatus.Actived.compareTo(ItemStatus.Deactived) < 0);
        assertTrue(ItemStatus.Deactived.compareTo(ItemStatus.Actived) > 0);
        assertEquals(0, ItemStatus.Actived.compareTo(ItemStatus.Actived));
        assertEquals(0, ItemStatus.Deactived.compareTo(ItemStatus.Deactived));
    }

    @Test
    void testEnumConstantProperties() {
        // Test that enum constants are final and static
        assertSame(ItemStatus.Actived, ItemStatus.valueOf("Actived"));
        assertSame(ItemStatus.Deactived, ItemStatus.valueOf("Deactived"));
    }

    @Test
    void testSwitchStatement() {
        // Test that enum can be used in switch statements
        for (ItemStatus status : ItemStatus.values()) {
            String result = switch (status) {
                case Actived -> "Active";
                case Deactived -> "Inactive";
            };
            
            if (status == ItemStatus.Actived) {
                assertEquals("Active", result);
            } else if (status == ItemStatus.Deactived) {
                assertEquals("Inactive", result);
            }
        }
    }

    @Test
    void testEnumInCollections() {
        // Test enum in collections
        java.util.Set<ItemStatus> statusSet = java.util.EnumSet.allOf(ItemStatus.class);
        assertEquals(2, statusSet.size());
        assertTrue(statusSet.contains(ItemStatus.Actived));
        assertTrue(statusSet.contains(ItemStatus.Deactived));
    }

    @Test
    void testEnumSerialization() {
        // Test that enum values can be converted to string and back
        for (ItemStatus status : ItemStatus.values()) {
            String name = status.name();
            ItemStatus restored = ItemStatus.valueOf(name);
            assertSame(status, restored);
        }
    }

    @Test
    void testEnumClass() {
        // Test enum class properties
        assertTrue(ItemStatus.class.isEnum());
        assertEquals("ItemStatus", ItemStatus.class.getSimpleName());
        assertEquals("com.derbysoft.next.propertyconnect.channel.task.domain.dto.ItemStatus", 
                     ItemStatus.class.getName());
    }

    @Test
    void testEnumDeclaringClass() {
        // Test declaring class
        assertEquals(ItemStatus.class, ItemStatus.Actived.getDeclaringClass());
        assertEquals(ItemStatus.class, ItemStatus.Deactived.getDeclaringClass());
    }

    @Test
    void testEnumValuesArray() {
        // Test that values() returns a new array each time
        ItemStatus[] values1 = ItemStatus.values();
        ItemStatus[] values2 = ItemStatus.values();
        
        assertNotSame(values1, values2); // Different array instances
        assertArrayEquals(values1, values2); // Same content
        
        // Modifying one array doesn't affect the other
        values1[0] = null;
        assertNotNull(values2[0]);
    }
}
