package com.derbysoft.next.propertyconnect.channel.task.util;

import org.junit.jupiter.api.Test;

import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Comprehensive unit tests for CollectionUtil
 */
class CollectionUtilTest {

    @Test
    void testToSingleWithCustomException() {
        // Test toSingle with custom exception supplier
        List<String> singleElementList = Arrays.asList("single");
        String result = singleElementList.stream()
                .collect(CollectionUtil.toSingle(() -> new RuntimeException("Custom error")));
        assertEquals("single", result);

        // Test with empty list
        List<String> emptyList = Arrays.asList();
        assertThrows(RuntimeException.class, () -> 
            emptyList.stream().collect(CollectionUtil.toSingle(() -> new RuntimeException("Custom error"))));

        // Test with multiple elements
        List<String> multipleElementsList = Arrays.asList("first", "second");
        assertThrows(RuntimeException.class, () -> 
            multipleElementsList.stream().collect(CollectionUtil.toSingle(() -> new RuntimeException("Custom error"))));
    }

    @Test
    void testToSingleWithDefaultException() {
        // Test toSingle with default exception
        List<String> singleElementList = Arrays.asList("single");
        String result = singleElementList.stream().collect(CollectionUtil.toSingle());
        assertEquals("single", result);

        // Test with empty list
        List<String> emptyList = Arrays.asList();
        IllegalStateException exception = assertThrows(IllegalStateException.class, () -> 
            emptyList.stream().collect(CollectionUtil.toSingle()));
        assertEquals("The collection must contains only one element", exception.getMessage());

        // Test with multiple elements
        List<String> multipleElementsList = Arrays.asList("first", "second");
        assertThrows(IllegalStateException.class, () -> 
            multipleElementsList.stream().collect(CollectionUtil.toSingle()));
    }

    @Test
    void testForEachWithIndex() {
        // Test forEachWithIndex with list
        List<String> items = Arrays.asList("a", "b", "c");
        List<String> results = new ArrayList<>();
        List<Integer> indices = new ArrayList<>();

        CollectionUtil.forEachWithIndex(items, (index, item) -> {
            indices.add(index);
            results.add(item);
        });

        assertEquals(Arrays.asList(0, 1, 2), indices);
        assertEquals(Arrays.asList("a", "b", "c"), results);

        // Test with empty list
        List<String> emptyList = Arrays.asList();
        List<String> emptyResults = new ArrayList<>();
        CollectionUtil.forEachWithIndex(emptyList, (index, item) -> emptyResults.add(item));
        assertTrue(emptyResults.isEmpty());

        // Test with set
        Set<String> set = new LinkedHashSet<>(Arrays.asList("x", "y", "z"));
        List<String> setResults = new ArrayList<>();
        List<Integer> setIndices = new ArrayList<>();
        CollectionUtil.forEachWithIndex(set, (index, item) -> {
            setIndices.add(index);
            setResults.add(item);
        });
        assertEquals(Arrays.asList(0, 1, 2), setIndices);
        assertEquals(Arrays.asList("x", "y", "z"), setResults);
    }

    @Test
    void testMapDaysInRangeWithIndexAndMapper() {
        // Test mapDaysInRange with index and mapper
        LocalDate start = LocalDate.of(2023, 1, 1);
        LocalDate end = LocalDate.of(2023, 1, 3);

        Collection<String> results = CollectionUtil.mapDaysInRange(start, end, 
            (index, date) -> "Day " + index + ": " + date);

        assertEquals(3, results.size());
        List<String> resultList = new ArrayList<>(results);
        assertEquals("Day 0: 2023-01-01", resultList.get(0));
        assertEquals("Day 1: 2023-01-02", resultList.get(1));
        assertEquals("Day 2: 2023-01-03", resultList.get(2));

        // Test with null mapper result (should be filtered out)
        Collection<String> filteredResults = CollectionUtil.mapDaysInRange(start, end, 
            (index, date) -> index % 2 == 0 ? date.toString() : null);

        assertEquals(2, filteredResults.size());
        List<String> filteredList = new ArrayList<>(filteredResults);
        assertEquals("2023-01-01", filteredList.get(0));
        assertEquals("2023-01-03", filteredList.get(1));
    }

    @Test
    void testMapDaysInRangeWithMapper() {
        // Test mapDaysInRange with just mapper
        LocalDate start = LocalDate.of(2023, 2, 1);
        LocalDate end = LocalDate.of(2023, 2, 3);

        Collection<String> results = CollectionUtil.mapDaysInRange(start, end, 
            date -> "Date: " + date);

        assertEquals(3, results.size());
        List<String> resultList = new ArrayList<>(results);
        assertEquals("Date: 2023-02-01", resultList.get(0));
        assertEquals("Date: 2023-02-02", resultList.get(1));
        assertEquals("Date: 2023-02-03", resultList.get(2));

        // Test single day range
        Collection<String> singleDayResults = CollectionUtil.mapDaysInRange(start, start, 
            date -> "Single: " + date);
        assertEquals(1, singleDayResults.size());
        assertEquals("Single: 2023-02-01", singleDayResults.iterator().next());
    }

    @Test
    void testEachDaysInRangeWithIndexAndConsumer() {
        // Test eachDaysInRange with index and consumer
        LocalDate start = LocalDate.of(2023, 3, 1);
        LocalDate end = LocalDate.of(2023, 3, 3);
        List<String> results = new ArrayList<>();

        CollectionUtil.eachDaysInRange(start, end, (index, date) -> 
            results.add("Index " + index + ": " + date));

        assertEquals(3, results.size());
        assertEquals("Index 0: 2023-03-01", results.get(0));
        assertEquals("Index 1: 2023-03-02", results.get(1));
        assertEquals("Index 2: 2023-03-03", results.get(2));
    }

    @Test
    void testEachDaysInRangeWithConsumer() {
        // Test eachDaysInRange with just consumer
        LocalDate start = LocalDate.of(2023, 4, 1);
        LocalDate end = LocalDate.of(2023, 4, 2);
        List<LocalDate> results = new ArrayList<>();

        CollectionUtil.eachDaysInRange(start, end, (Consumer<LocalDate>) results::add);

        assertEquals(2, results.size());
        assertEquals(LocalDate.of(2023, 4, 1), results.get(0));
        assertEquals(LocalDate.of(2023, 4, 2), results.get(1));
    }

    @Test
    void testMapBuilderList() {
        // Test mapBuilderList
        List<String> builderList = new ArrayList<>(Arrays.asList("a", "b", "c"));
        
        CollectionUtil.mapBuilderList(builderList, s -> s.toUpperCase());

        assertEquals(Arrays.asList("A", "B", "C"), builderList);

        // Test with empty list
        List<String> emptyList = new ArrayList<>();
        CollectionUtil.mapBuilderList(emptyList, s -> s.toUpperCase());
        assertTrue(emptyList.isEmpty());

        // Test with transformation
        List<Integer> numbers = new ArrayList<>(Arrays.asList(1, 2, 3));
        CollectionUtil.mapBuilderList(numbers, n -> n * 2);
        assertEquals(Arrays.asList(2, 4, 6), numbers);
    }

    @Test
    void testEachSpanInDateRange() {
        // Test eachSpanInDateRange
        LocalDate start = LocalDate.of(2023, 5, 1);
        LocalDate end = LocalDate.of(2023, 5, 10);
        List<String> results = new ArrayList<>();

        CollectionUtil.eachSpanInDateRange(start, end, 3, (spanStart, spanEnd) -> 
            results.add(spanStart + " to " + spanEnd));

        assertEquals(4, results.size());
        assertEquals("2023-05-01 to 2023-05-03", results.get(0));
        assertEquals("2023-05-04 to 2023-05-06", results.get(1));
        assertEquals("2023-05-07 to 2023-05-09", results.get(2));
        assertEquals("2023-05-10 to 2023-05-10", results.get(3));
    }

    @Test
    void testMapSpanInDateRange() {
        // Test mapSpanInDateRange
        LocalDate start = LocalDate.of(2023, 6, 1);
        LocalDate end = LocalDate.of(2023, 6, 7);

        Collection<String> results = CollectionUtil.mapSpanInDateRange(start, end, 3, 
            (spanStart, spanEnd) -> spanStart + "-" + spanEnd);

        assertEquals(3, results.size());
        List<String> resultList = new ArrayList<>(results);
        assertEquals("2023-06-01-2023-06-03", resultList.get(0));
        assertEquals("2023-06-04-2023-06-06", resultList.get(1));
        assertEquals("2023-06-07-2023-06-07", resultList.get(2));

        // Test with span larger than range
        Collection<String> largeSpanResults = CollectionUtil.mapSpanInDateRange(start, 
            LocalDate.of(2023, 6, 2), 5, (spanStart, spanEnd) -> spanStart + "-" + spanEnd);
        assertEquals(1, largeSpanResults.size());
        assertEquals("2023-06-01-2023-06-02", largeSpanResults.iterator().next());
    }

    @Test
    void testMapSpanInDateRangeAssertionError() {
        // Test assertion error for invalid span
        LocalDate start = LocalDate.of(2023, 1, 1);
        LocalDate end = LocalDate.of(2023, 1, 5);

        assertThrows(AssertionError.class, () -> 
            CollectionUtil.mapSpanInDateRange(start, end, 1, (s, e) -> s + "-" + e));

        assertThrows(AssertionError.class, () -> 
            CollectionUtil.mapSpanInDateRange(start, end, 0, (s, e) -> s + "-" + e));

        assertThrows(AssertionError.class, () -> 
            CollectionUtil.mapSpanInDateRange(start, end, -1, (s, e) -> s + "-" + e));
    }

    @Test
    void testEachParallel() {
        // Test eachParallel
        List<Integer> numbers = Arrays.asList(1, 2, 3, 4, 5);
        List<Integer> results = Collections.synchronizedList(new ArrayList<>());

        CollectionUtil.eachParallel(numbers, 2, results::add);

        assertEquals(5, results.size());
        assertTrue(results.containsAll(numbers));

        // Test with empty collection
        List<Integer> emptyList = Arrays.asList();
        List<Integer> emptyResults = Collections.synchronizedList(new ArrayList<>());
        CollectionUtil.eachParallel(emptyList, 2, emptyResults::add);
        assertTrue(emptyResults.isEmpty());
    }

    @Test
    void testMapEachParallel() {
        // Test mapEachParallel
        List<Integer> numbers = Arrays.asList(1, 2, 3, 4, 5);

        Collection<Integer> results = CollectionUtil.mapEachParallel(numbers, 2, n -> n * 2);

        assertEquals(5, results.size());
        List<Integer> resultList = new ArrayList<>(results);
        Collections.sort(resultList); // Sort because parallel processing order is not guaranteed
        assertEquals(Arrays.asList(2, 4, 6, 8, 10), resultList);

        // Test with empty collection
        List<Integer> emptyList = Arrays.asList();
        Collection<Integer> emptyResults = CollectionUtil.mapEachParallel(emptyList, 2, n -> n * 2);
        assertTrue(emptyResults.isEmpty());

        // Test with single thread
        Collection<String> stringResults = CollectionUtil.mapEachParallel(
            Arrays.asList("a", "b", "c"), 1, String::toUpperCase);
        assertEquals(3, stringResults.size());
        assertTrue(stringResults.containsAll(Arrays.asList("A", "B", "C")));
    }

    @Test
    void testPartition() {
        // Test partition
        Stream<Integer> stream = Stream.of(1, 2, 3, 4, 5, 6, 7);
        Stream<List<Integer>> partitioned = CollectionUtil.partition(stream, 3);

        List<List<Integer>> partitionList = partitioned.toList();
        assertEquals(3, partitionList.size());
        assertEquals(Arrays.asList(1, 2, 3), partitionList.get(0));
        assertEquals(Arrays.asList(4, 5, 6), partitionList.get(1));
        assertEquals(Arrays.asList(7), partitionList.get(2));

        // Test with empty stream
        Stream<Integer> emptyStream = Stream.empty();
        Stream<List<Integer>> emptyPartitioned = CollectionUtil.partition(emptyStream, 3);
        List<List<Integer>> emptyPartitionList = emptyPartitioned.toList();
        assertTrue(emptyPartitionList.isEmpty());

        // Test with partition size larger than stream
        Stream<Integer> smallStream = Stream.of(1, 2);
        Stream<List<Integer>> smallPartitioned = CollectionUtil.partition(smallStream, 5);
        List<List<Integer>> smallPartitionList = smallPartitioned.toList();
        assertEquals(1, smallPartitionList.size());
        assertEquals(Arrays.asList(1, 2), smallPartitionList.get(0));
    }

    @Test
    void testPartitionList() {
        // Test partitionList
        List<Integer> numbers = Arrays.asList(1, 2, 3, 4, 5, 6, 7, 8, 9, 10);
        List<List<Integer>> partitions = CollectionUtil.partitionList(numbers, 3);

        assertEquals(3, partitions.size());
        assertEquals(Arrays.asList(1, 2, 3, 4), partitions.get(0));
        assertEquals(Arrays.asList(5, 6, 7, 8), partitions.get(1));
        assertEquals(Arrays.asList(9, 10), partitions.get(2));

        // Test with empty list
        List<Integer> emptyList = Arrays.asList();
        List<List<Integer>> emptyPartitions = CollectionUtil.partitionList(emptyList, 3);
        assertEquals(3, emptyPartitions.size());
        assertTrue(emptyPartitions.get(0).isEmpty());
        assertTrue(emptyPartitions.get(1).isEmpty());
        assertTrue(emptyPartitions.get(2).isEmpty());

        // Test with single partition
        List<Integer> smallList = Arrays.asList(1, 2, 3);
        List<List<Integer>> singlePartition = CollectionUtil.partitionList(smallList, 1);
        assertEquals(1, singlePartition.size());
        assertEquals(Arrays.asList(1, 2, 3), singlePartition.get(0));

        // Test with more partitions than elements - this will cause an error in the current implementation
        // The partitionList method has a bug when partitionCount > list.size()
        List<Integer> tinyList = Arrays.asList(1, 2);
        assertThrows(IllegalArgumentException.class, () -> {
            CollectionUtil.partitionList(tinyList, 5);
        });
    }

    @Test
    void testEdgeCases() {
        // Test edge cases for date ranges
        LocalDate sameDate = LocalDate.of(2023, 1, 1);
        
        // Same start and end date
        Collection<String> sameDateResults = CollectionUtil.mapDaysInRange(sameDate, sameDate, 
            date -> date.toString());
        assertEquals(1, sameDateResults.size());
        assertEquals("2023-01-01", sameDateResults.iterator().next());

        // Test span in date range with same dates
        Collection<String> spanResults = CollectionUtil.mapSpanInDateRange(sameDate, sameDate, 2, 
            (start, end) -> start + "-" + end);
        assertEquals(1, spanResults.size());
        assertEquals("2023-01-01-2023-01-01", spanResults.iterator().next());
    }
}
