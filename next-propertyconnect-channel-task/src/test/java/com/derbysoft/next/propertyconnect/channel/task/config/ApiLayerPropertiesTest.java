package com.derbysoft.next.propertyconnect.channel.task.config;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Comprehensive unit tests for ApiLayerProperties
 * Tests API layer properties configuration functionality
 */
@ExtendWith(MockitoExtension.class)
class ApiLayerPropertiesTest {

    private ApiLayerProperties apiLayerProperties;

    @BeforeEach
    void setUp() {
        apiLayerProperties = new ApiLayerProperties();
    }

    @Test
    void testApiLayerProperties_HasConfigurationPropertiesAnnotation() {
        // When
        ConfigurationProperties annotation = ApiLayerProperties.class.getAnnotation(ConfigurationProperties.class);

        // Then
        assertNotNull(annotation);
        assertEquals("app.api-layer", annotation.prefix());
    }

    @Test
    void testApiLayerProperties_HasRefreshScopeAnnotation() {
        // When
        RefreshScope annotation = ApiLayerProperties.class.getAnnotation(RefreshScope.class);

        // Then
        assertNotNull(annotation);
    }

    @Test
    void testApiLayerProperties_DefaultValues() {
        // When & Then
        assertNull(apiLayerProperties.getUrl());
        assertNull(apiLayerProperties.getToken());
        assertNotNull(apiLayerProperties.getCustom());
        assertTrue(apiLayerProperties.getCustom().isEmpty());
    }

    @Test
    void testApiLayerProperties_SetAndGetUrl() {
        // Given
        String url = "https://api.example.com/";

        // When
        apiLayerProperties.setUrl(url);

        // Then
        assertEquals(url, apiLayerProperties.getUrl());
    }

    @Test
    void testApiLayerProperties_SetAndGetToken() {
        // Given
        String token = "test-token-123";

        // When
        apiLayerProperties.setToken(token);

        // Then
        assertEquals(token, apiLayerProperties.getToken());
    }

    @Test
    void testApiLayerProperties_SetAndGetCustom() {
        // Given
        Map<String, ApiLayerProperties.CustomUrl> customMap = new HashMap<>();
        ApiLayerProperties.CustomUrl customUrl = new ApiLayerProperties.CustomUrl();
        customUrl.setUrl("https://custom.api.com/");
        customUrl.setToken("custom-token");
        customMap.put("BOOKINGCOM", customUrl);

        // When
        apiLayerProperties.setCustom(customMap);

        // Then
        assertEquals(customMap, apiLayerProperties.getCustom());
        assertEquals(1, apiLayerProperties.getCustom().size());
        assertTrue(apiLayerProperties.getCustom().containsKey("BOOKINGCOM"));
    }

    @Test
    void testApiLayerProperties_WithNullValues() {
        // When
        apiLayerProperties.setUrl(null);
        apiLayerProperties.setToken(null);
        apiLayerProperties.setCustom(null);

        // Then
        assertNull(apiLayerProperties.getUrl());
        assertNull(apiLayerProperties.getToken());
        assertNull(apiLayerProperties.getCustom());
    }

    @Test
    void testApiLayerProperties_WithEmptyValues() {
        // Given
        Map<String, ApiLayerProperties.CustomUrl> emptyMap = new HashMap<>();

        // When
        apiLayerProperties.setUrl("");
        apiLayerProperties.setToken("");
        apiLayerProperties.setCustom(emptyMap);

        // Then
        assertEquals("", apiLayerProperties.getUrl());
        assertEquals("", apiLayerProperties.getToken());
        assertEquals(emptyMap, apiLayerProperties.getCustom());
        assertTrue(apiLayerProperties.getCustom().isEmpty());
    }

    @Test
    void testCustomUrl_DefaultValues() {
        // Given
        ApiLayerProperties.CustomUrl customUrl = new ApiLayerProperties.CustomUrl();

        // When & Then
        assertNull(customUrl.getUrl());
        assertNull(customUrl.getToken());
    }

    @Test
    void testCustomUrl_SetAndGetUrl() {
        // Given
        ApiLayerProperties.CustomUrl customUrl = new ApiLayerProperties.CustomUrl();
        String url = "https://custom.booking.com/api/";

        // When
        customUrl.setUrl(url);

        // Then
        assertEquals(url, customUrl.getUrl());
    }

    @Test
    void testCustomUrl_SetAndGetToken() {
        // Given
        ApiLayerProperties.CustomUrl customUrl = new ApiLayerProperties.CustomUrl();
        String token = "custom-token-456";

        // When
        customUrl.setToken(token);

        // Then
        assertEquals(token, customUrl.getToken());
    }

    @Test
    void testCustomUrl_WithNullValues() {
        // Given
        ApiLayerProperties.CustomUrl customUrl = new ApiLayerProperties.CustomUrl();

        // When
        customUrl.setUrl(null);
        customUrl.setToken(null);

        // Then
        assertNull(customUrl.getUrl());
        assertNull(customUrl.getToken());
    }

    @Test
    void testCustomUrl_WithEmptyValues() {
        // Given
        ApiLayerProperties.CustomUrl customUrl = new ApiLayerProperties.CustomUrl();

        // When
        customUrl.setUrl("");
        customUrl.setToken("");

        // Then
        assertEquals("", customUrl.getUrl());
        assertEquals("", customUrl.getToken());
    }

    @Test
    void testApiLayerProperties_WithMultipleCustomUrls() {
        // Given
        Map<String, ApiLayerProperties.CustomUrl> customMap = new HashMap<>();
        
        ApiLayerProperties.CustomUrl bookingUrl = new ApiLayerProperties.CustomUrl();
        bookingUrl.setUrl("https://booking.api.com/");
        bookingUrl.setToken("booking-token");
        
        ApiLayerProperties.CustomUrl airbnbUrl = new ApiLayerProperties.CustomUrl();
        airbnbUrl.setUrl("https://airbnb.api.com/");
        airbnbUrl.setToken("airbnb-token");
        
        customMap.put("BOOKINGCOM", bookingUrl);
        customMap.put("AIRBNB", airbnbUrl);

        // When
        apiLayerProperties.setCustom(customMap);

        // Then
        assertEquals(2, apiLayerProperties.getCustom().size());
        assertTrue(apiLayerProperties.getCustom().containsKey("BOOKINGCOM"));
        assertTrue(apiLayerProperties.getCustom().containsKey("AIRBNB"));
        
        ApiLayerProperties.CustomUrl retrievedBooking = apiLayerProperties.getCustom().get("BOOKINGCOM");
        assertEquals("https://booking.api.com/", retrievedBooking.getUrl());
        assertEquals("booking-token", retrievedBooking.getToken());
        
        ApiLayerProperties.CustomUrl retrievedAirbnb = apiLayerProperties.getCustom().get("AIRBNB");
        assertEquals("https://airbnb.api.com/", retrievedAirbnb.getUrl());
        assertEquals("airbnb-token", retrievedAirbnb.getToken());
    }

    @Test
    void testApiLayerProperties_CustomMapInitialization() {
        // Given
        ApiLayerProperties properties = new ApiLayerProperties();

        // When & Then
        assertNotNull(properties.getCustom());
        assertTrue(properties.getCustom() instanceof HashMap);
        assertEquals(0, properties.getCustom().size());
    }

    @Test
    void testApiLayerProperties_ModifyCustomMap() {
        // Given
        ApiLayerProperties.CustomUrl customUrl = new ApiLayerProperties.CustomUrl();
        customUrl.setUrl("https://test.api.com/");
        customUrl.setToken("test-token");

        // When
        apiLayerProperties.getCustom().put("TEST", customUrl);

        // Then
        assertEquals(1, apiLayerProperties.getCustom().size());
        assertTrue(apiLayerProperties.getCustom().containsKey("TEST"));
        assertEquals(customUrl, apiLayerProperties.getCustom().get("TEST"));
    }

    @Test
    void testApiLayerProperties_WithProductionLikeValues() {
        // Given
        String prodUrl = "https://apilayer-proxy.derbysoftsec.com/";
        String prodToken = "5e201b47f80477731000008e";

        // When
        apiLayerProperties.setUrl(prodUrl);
        apiLayerProperties.setToken(prodToken);

        // Then
        assertEquals(prodUrl, apiLayerProperties.getUrl());
        assertEquals(prodToken, apiLayerProperties.getToken());
    }

    @Test
    void testApiLayerProperties_WithUATLikeValues() {
        // Given
        String uatUrl = "https://uat-api-layer-proxy.derbysoft-test.com/";
        String uatToken = "638eeb1869a757000100000c";

        // When
        apiLayerProperties.setUrl(uatUrl);
        apiLayerProperties.setToken(uatToken);

        // Then
        assertEquals(uatUrl, apiLayerProperties.getUrl());
        assertEquals(uatToken, apiLayerProperties.getToken());
    }

    @Test
    void testApiLayerProperties_WithDevLikeValues() {
        // Given
        String devUrl = "http://10.200.176.128:3030/";
        String devToken = "638eeb1869a757000100000c";

        // When
        apiLayerProperties.setUrl(devUrl);
        apiLayerProperties.setToken(devToken);

        // Then
        assertEquals(devUrl, apiLayerProperties.getUrl());
        assertEquals(devToken, apiLayerProperties.getToken());
    }

    @Test
    void testCustomUrl_IsStaticInnerClass() {
        // When & Then
        assertTrue(ApiLayerProperties.CustomUrl.class.isMemberClass());
        assertTrue(java.lang.reflect.Modifier.isStatic(ApiLayerProperties.CustomUrl.class.getModifiers()));
        assertTrue(java.lang.reflect.Modifier.isPublic(ApiLayerProperties.CustomUrl.class.getModifiers()));
    }

    @Test
    void testApiLayerProperties_EqualsAndHashCode() {
        // Given
        ApiLayerProperties props1 = new ApiLayerProperties();
        props1.setUrl("https://test.com/");
        props1.setToken("token123");
        
        ApiLayerProperties props2 = new ApiLayerProperties();
        props2.setUrl("https://test.com/");
        props2.setToken("token123");

        // When & Then
        assertEquals(props1, props2);
        assertEquals(props1.hashCode(), props2.hashCode());
    }

    @Test
    void testCustomUrl_EqualsAndHashCode() {
        // Given
        ApiLayerProperties.CustomUrl url1 = new ApiLayerProperties.CustomUrl();
        url1.setUrl("https://test.com/");
        url1.setToken("token123");
        
        ApiLayerProperties.CustomUrl url2 = new ApiLayerProperties.CustomUrl();
        url2.setUrl("https://test.com/");
        url2.setToken("token123");

        // When & Then
        assertEquals(url1, url2);
        assertEquals(url1.hashCode(), url2.hashCode());
    }

    @Test
    void testApiLayerProperties_ToString() {
        // Given
        apiLayerProperties.setUrl("https://test.com/");
        apiLayerProperties.setToken("token123");

        // When
        String result = apiLayerProperties.toString();

        // Then
        assertNotNull(result);
        assertTrue(result.contains("ApiLayerProperties"));
        assertTrue(result.contains("https://test.com/"));
        assertTrue(result.contains("token123"));
    }

    @Test
    void testCustomUrl_ToString() {
        // Given
        ApiLayerProperties.CustomUrl customUrl = new ApiLayerProperties.CustomUrl();
        customUrl.setUrl("https://custom.com/");
        customUrl.setToken("custom-token");

        // When
        String result = customUrl.toString();

        // Then
        assertNotNull(result);
        assertTrue(result.contains("CustomUrl"));
        assertTrue(result.contains("https://custom.com/"));
        assertTrue(result.contains("custom-token"));
    }

    @Test
    void testApiLayerProperties_ThreadSafety() {
        // Given
        String baseUrl = "https://thread-test.com/";
        String baseToken = "thread-token";
        
        // When
        Thread[] threads = new Thread[10];
        Exception[] exceptions = new Exception[10];

        for (int i = 0; i < 10; i++) {
            final int index = i;
            threads[i] = new Thread(() -> {
                try {
                    ApiLayerProperties props = new ApiLayerProperties();
                    props.setUrl(baseUrl + index);
                    props.setToken(baseToken + index);
                    
                    ApiLayerProperties.CustomUrl customUrl = new ApiLayerProperties.CustomUrl();
                    customUrl.setUrl("https://custom" + index + ".com/");
                    customUrl.setToken("custom-token-" + index);
                    
                    props.getCustom().put("CHANNEL" + index, customUrl);
                } catch (Exception e) {
                    exceptions[index] = e;
                }
            });
            threads[i].start();
        }

        // Wait for all threads to complete
        for (Thread thread : threads) {
            try {
                thread.join();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }

        // Then
        for (int i = 0; i < 10; i++) {
            assertNull(exceptions[i], "Thread " + i + " should not have thrown an exception");
        }
    }

    @Test
    void testApiLayerProperties_PackageStructure() {
        // When
        String packageName = ApiLayerProperties.class.getPackage().getName();

        // Then
        assertEquals("com.derbysoft.next.propertyconnect.channel.task.config", packageName);
    }

    @Test
    void testApiLayerProperties_ClassSimpleName() {
        // When
        String className = ApiLayerProperties.class.getSimpleName();

        // Then
        assertEquals("ApiLayerProperties", className);
    }

    @Test
    void testCustomUrl_ClassSimpleName() {
        // When
        String className = ApiLayerProperties.CustomUrl.class.getSimpleName();

        // Then
        assertEquals("CustomUrl", className);
    }
}
