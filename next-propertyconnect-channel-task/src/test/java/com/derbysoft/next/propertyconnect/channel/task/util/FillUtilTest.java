package com.derbysoft.next.propertyconnect.channel.task.util;

import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelHotelDTO;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelHotelInfo;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelProductsDTO;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelRoomInfo;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelRateInfo;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ItemStatus;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.SyncStatus;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Comprehensive unit tests for FillUtil
 * Tests MapStruct-based filling functionality for ChannelHotelDTO objects
 */
@ExtendWith(MockitoExtension.class)
class FillUtilTest {

    @Test
    void testFillClone_BasicProperties_FillsCorrectly() {
        // Given
        ChannelHotelDTO source = new ChannelHotelDTO();
        source.setSupplierId("SUPPLIER_001");
        source.setChannelId("BOOKING");
        source.setChannelHotelId("HOTEL_123");
        source.setDerbyHotelId("DERBY_456");
        source.setSupplierHotelId("SUPP_789");
        source.setOperationToken("OP_TOKEN_001");

        ChannelHotelDTO target = new ChannelHotelDTO();

        // When
        FillUtil.INSTANCE.fillClone(source, target);

        // Then
        assertEquals("SUPPLIER_001", target.getSupplierId());
        assertEquals("BOOKING", target.getChannelId());
        assertEquals("HOTEL_123", target.getChannelHotelId());
        assertEquals("DERBY_456", target.getDerbyHotelId());
        assertEquals("SUPP_789", target.getSupplierHotelId());
        assertEquals("OP_TOKEN_001", target.getOperationToken());
    }

    @Test
    void testFillClone_WithHotelInfo_FillsCorrectly() {
        // Given
        ChannelHotelDTO source = new ChannelHotelDTO();
        ChannelHotelInfo hotelInfo = new ChannelHotelInfo();
        hotelInfo.setId("HOTEL_INFO_001");
        hotelInfo.setName("Test Hotel");
        hotelInfo.setDescription("A test hotel");
        hotelInfo.setCurrency("USD");
        hotelInfo.setTimezone("America/New_York");
        hotelInfo.setStatus(ItemStatus.Actived);
        hotelInfo.setSyncStatus(SyncStatus.SYNCED);
        source.setHotelInfo(hotelInfo);

        ChannelHotelDTO target = new ChannelHotelDTO();

        // When
        FillUtil.INSTANCE.fillClone(source, target);

        // Then
        assertNotNull(target.getHotelInfo());
        assertEquals("HOTEL_INFO_001", target.getHotelInfo().getId());
        assertEquals("Test Hotel", target.getHotelInfo().getName());
        assertEquals("A test hotel", target.getHotelInfo().getDescription());
        assertEquals("USD", target.getHotelInfo().getCurrency());
        assertEquals("America/New_York", target.getHotelInfo().getTimezone());
        assertEquals(ItemStatus.Actived, target.getHotelInfo().getStatus());
        assertEquals(SyncStatus.SYNCED, target.getHotelInfo().getSyncStatus());
    }

    @Test
    void testFillClone_WithRoomInfo_FillsCorrectly() {
        // Given
        ChannelHotelDTO source = new ChannelHotelDTO();
        ChannelRoomInfo roomInfo = new ChannelRoomInfo();
        roomInfo.setCode("ROOM_001");
        roomInfo.setName("Standard Room");
        roomInfo.setStatus(ItemStatus.Actived);
        roomInfo.setSyncStatus(SyncStatus.SYNCED);
        source.setRoomInfo(roomInfo);

        ChannelHotelDTO target = new ChannelHotelDTO();

        // When
        FillUtil.INSTANCE.fillClone(source, target);

        // Then
        assertNotNull(target.getRoomInfo());
        assertEquals("ROOM_001", target.getRoomInfo().getCode());
        assertEquals("Standard Room", target.getRoomInfo().getName());
        assertEquals("Standard Room", target.getRoomInfo().getName());
        assertEquals(ItemStatus.Actived, target.getRoomInfo().getStatus());
        assertEquals(SyncStatus.SYNCED, target.getRoomInfo().getSyncStatus());
    }

    @Test
    void testFillClone_WithRateInfo_FillsCorrectly() {
        // Given
        ChannelHotelDTO source = new ChannelHotelDTO();
        ChannelRateInfo rateInfo = new ChannelRateInfo();
        rateInfo.setCode("RATE_001");
        rateInfo.setName("Standard Rate");
        rateInfo.setStatus(ItemStatus.Actived);
        rateInfo.setSyncStatus(SyncStatus.SYNCED);
        source.setRateInfo(rateInfo);

        ChannelHotelDTO target = new ChannelHotelDTO();

        // When
        FillUtil.INSTANCE.fillClone(source, target);

        // Then
        assertNotNull(target.getRateInfo());
        assertEquals("RATE_001", target.getRateInfo().getCode());
        assertEquals("Standard Rate", target.getRateInfo().getName());
        assertEquals(ItemStatus.Actived, target.getRateInfo().getStatus());
        assertEquals(SyncStatus.SYNCED, target.getRateInfo().getSyncStatus());
    }

    @Test
    void testFillClone_WithProductInfo_FillsCorrectly() {
        // Given
        ChannelHotelDTO source = new ChannelHotelDTO();
        ChannelProductsDTO.Product productInfo = new ChannelProductsDTO.Product();
        productInfo.setChannelRoomId("ROOM_001");
        productInfo.setChannelRoomName("Standard Room");
        productInfo.setChannelRateId("RATE_001");
        productInfo.setChannelRateName("Standard Rate");
        productInfo.setStatus("Active");
        productInfo.setAvailStatus(true);
        source.setProductInfo(productInfo);

        ChannelHotelDTO target = new ChannelHotelDTO();

        // When
        FillUtil.INSTANCE.fillClone(source, target);

        // Then
        assertNotNull(target.getProductInfo());
        assertEquals("ROOM_001", target.getProductInfo().getChannelRoomId());
        assertEquals("Standard Room", target.getProductInfo().getChannelRoomName());
        assertEquals("RATE_001", target.getProductInfo().getChannelRateId());
        assertEquals("Standard Rate", target.getProductInfo().getChannelRateName());
        assertEquals("Active", target.getProductInfo().getStatus());
        assertTrue(target.getProductInfo().getAvailStatus());
    }

    @Test
    void testFillClone_WithAccountSettings_FillsCorrectly() {
        // Given
        ChannelHotelDTO source = new ChannelHotelDTO();
        Map<String, Object> accountSettings = new HashMap<>();
        accountSettings.put("username", "testuser");
        accountSettings.put("password", "testpass");
        accountSettings.put("apiKey", "test_api_key");
        accountSettings.put("timeout", 30000);
        source.setAccountSettings(accountSettings);

        ChannelHotelDTO target = new ChannelHotelDTO();

        // When
        FillUtil.INSTANCE.fillClone(source, target);

        // Then
        assertNotNull(target.getAccountSettings());
        assertEquals("testuser", target.getAccountSettings().get("username"));
        assertEquals("testpass", target.getAccountSettings().get("password"));
        assertEquals("test_api_key", target.getAccountSettings().get("apiKey"));
        assertEquals(30000, target.getAccountSettings().get("timeout"));
    }

    @Test
    void testFillClone_WithRoomsInfo_FillsCorrectly() {
        // Given
        ChannelHotelDTO source = new ChannelHotelDTO();
        List<ChannelRoomInfo> roomsInfo = new ArrayList<>();
        
        ChannelRoomInfo room1 = new ChannelRoomInfo();
        room1.setCode("ROOM_001");
        room1.setName("Standard Room");
        roomsInfo.add(room1);

        ChannelRoomInfo room2 = new ChannelRoomInfo();
        room2.setCode("ROOM_002");
        room2.setName("Deluxe Room");
        roomsInfo.add(room2);
        
        source.setRoomsInfo(roomsInfo);

        ChannelHotelDTO target = new ChannelHotelDTO();

        // When
        FillUtil.INSTANCE.fillClone(source, target);

        // Then
        assertNotNull(target.getRoomsInfo());
        assertEquals(2, target.getRoomsInfo().size());
        assertEquals("ROOM_001", target.getRoomsInfo().get(0).getCode());
        assertEquals("Standard Room", target.getRoomsInfo().get(0).getName());
        assertEquals("ROOM_002", target.getRoomsInfo().get(1).getCode());
        assertEquals("Deluxe Room", target.getRoomsInfo().get(1).getName());
    }

    @Test
    void testFillClone_WithRatesInfo_FillsCorrectly() {
        // Given
        ChannelHotelDTO source = new ChannelHotelDTO();
        List<ChannelRateInfo> ratesInfo = new ArrayList<>();
        
        ChannelRateInfo rate1 = new ChannelRateInfo();
        rate1.setCode("RATE_001");
        rate1.setName("Standard Rate");
        ratesInfo.add(rate1);

        ChannelRateInfo rate2 = new ChannelRateInfo();
        rate2.setCode("RATE_002");
        rate2.setName("Premium Rate");
        ratesInfo.add(rate2);
        
        source.setRatesInfo(ratesInfo);

        ChannelHotelDTO target = new ChannelHotelDTO();

        // When
        FillUtil.INSTANCE.fillClone(source, target);

        // Then
        assertNotNull(target.getRatesInfo());
        assertEquals(2, target.getRatesInfo().size());
        assertEquals("RATE_001", target.getRatesInfo().get(0).getCode());
        assertEquals("Standard Rate", target.getRatesInfo().get(0).getName());
        assertEquals("RATE_002", target.getRatesInfo().get(1).getCode());
        assertEquals("Premium Rate", target.getRatesInfo().get(1).getName());
    }

    @Test
    void testFillClone_WithProductsInfo_FillsCorrectly() {
        // Given
        ChannelHotelDTO source = new ChannelHotelDTO();
        List<ChannelProductsDTO.Product> productsInfo = new ArrayList<>();
        
        ChannelProductsDTO.Product product1 = new ChannelProductsDTO.Product();
        product1.setChannelRoomId("ROOM_001");
        product1.setChannelRateId("RATE_001");
        product1.setStatus("Active");
        productsInfo.add(product1);
        
        ChannelProductsDTO.Product product2 = new ChannelProductsDTO.Product();
        product2.setChannelRoomId("ROOM_002");
        product2.setChannelRateId("RATE_002");
        product2.setStatus("Inactive");
        productsInfo.add(product2);
        
        source.setProductsInfo(productsInfo);

        ChannelHotelDTO target = new ChannelHotelDTO();

        // When
        FillUtil.INSTANCE.fillClone(source, target);

        // Then
        assertNotNull(target.getProductsInfo());
        assertEquals(2, target.getProductsInfo().size());
        assertEquals("ROOM_001", target.getProductsInfo().get(0).getChannelRoomId());
        assertEquals("RATE_001", target.getProductsInfo().get(0).getChannelRateId());
        assertEquals("Active", target.getProductsInfo().get(0).getStatus());
        assertEquals("ROOM_002", target.getProductsInfo().get(1).getChannelRoomId());
        assertEquals("RATE_002", target.getProductsInfo().get(1).getChannelRateId());
        assertEquals("Inactive", target.getProductsInfo().get(1).getStatus());
    }

    @Test
    void testFillClone_WithExtensions_FillsCorrectly() {
        // Given
        ChannelHotelDTO source = new ChannelHotelDTO();
        Map<String, Object> extensions = new HashMap<>();
        extensions.put("customField1", "value1");
        extensions.put("customField2", 123);
        extensions.put("customField3", true);
        extensions.put("customField4", Arrays.asList("item1", "item2", "item3"));
        source.setExtensions(extensions);

        ChannelHotelDTO target = new ChannelHotelDTO();

        // When
        FillUtil.INSTANCE.fillClone(source, target);

        // Then
        assertNotNull(target.getExtensions());
        assertEquals("value1", target.getExtensions().get("customField1"));
        assertEquals(123, target.getExtensions().get("customField2"));
        assertEquals(true, target.getExtensions().get("customField3"));
        assertEquals(Arrays.asList("item1", "item2", "item3"), target.getExtensions().get("customField4"));
    }

    @Test
    void testFillClone_NullValuePropertyMappingStrategy_IgnoresNullValues() {
        // Given
        ChannelHotelDTO source = new ChannelHotelDTO();
        source.setSupplierId("SUPPLIER_001");
        source.setChannelId(null); // Null value should be ignored
        source.setChannelHotelId("HOTEL_123");

        ChannelHotelDTO target = new ChannelHotelDTO();
        target.setChannelId("EXISTING_CHANNEL"); // This should not be overwritten by null

        // When
        FillUtil.INSTANCE.fillClone(source, target);

        // Then
        assertEquals("SUPPLIER_001", target.getSupplierId());
        assertEquals("EXISTING_CHANNEL", target.getChannelId()); // Should remain unchanged
        assertEquals("HOTEL_123", target.getChannelHotelId());
    }

    @Test
    void testFillClone_PartialFill_FillsOnlyNonNullValues() {
        // Given
        ChannelHotelDTO source = new ChannelHotelDTO();
        source.setSupplierId("NEW_SUPPLIER");
        source.setOperationToken("NEW_TOKEN");
        // Other fields are null

        ChannelHotelDTO target = new ChannelHotelDTO();
        target.setChannelId("EXISTING_CHANNEL");
        target.setChannelHotelId("EXISTING_HOTEL");
        target.setDerbyHotelId("EXISTING_DERBY");

        // When
        FillUtil.INSTANCE.fillClone(source, target);

        // Then
        assertEquals("NEW_SUPPLIER", target.getSupplierId());
        assertEquals("NEW_TOKEN", target.getOperationToken());
        assertEquals("EXISTING_CHANNEL", target.getChannelId());
        assertEquals("EXISTING_HOTEL", target.getChannelHotelId());
        assertEquals("EXISTING_DERBY", target.getDerbyHotelId());
    }

    @Test
    void testFillClone_EmptySource_DoesNotModifyTarget() {
        // Given
        ChannelHotelDTO source = new ChannelHotelDTO(); // All fields are null

        ChannelHotelDTO target = new ChannelHotelDTO();
        target.setSupplierId("EXISTING_SUPPLIER");
        target.setChannelId("EXISTING_CHANNEL");
        target.setChannelHotelId("EXISTING_HOTEL");

        // When
        FillUtil.INSTANCE.fillClone(source, target);

        // Then
        assertEquals("EXISTING_SUPPLIER", target.getSupplierId());
        assertEquals("EXISTING_CHANNEL", target.getChannelId());
        assertEquals("EXISTING_HOTEL", target.getChannelHotelId());
    }

    @Test
    void testFillClone_ComplexObject_FillsAllFields() {
        // Given
        ChannelHotelDTO source = createCompleteChannelHotelDTO();
        ChannelHotelDTO target = new ChannelHotelDTO();

        // When
        FillUtil.INSTANCE.fillClone(source, target);

        // Then
        assertEquals(source.getSupplierId(), target.getSupplierId());
        assertEquals(source.getChannelId(), target.getChannelId());
        assertEquals(source.getChannelHotelId(), target.getChannelHotelId());
        assertEquals(source.getDerbyHotelId(), target.getDerbyHotelId());
        assertEquals(source.getSupplierHotelId(), target.getSupplierHotelId());
        assertEquals(source.getOperationToken(), target.getOperationToken());
        
        assertNotNull(target.getHotelInfo());
        assertEquals(source.getHotelInfo().getId(), target.getHotelInfo().getId());
        assertEquals(source.getHotelInfo().getName(), target.getHotelInfo().getName());
        
        assertNotNull(target.getRoomInfo());
        assertEquals(source.getRoomInfo().getCode(), target.getRoomInfo().getCode());
        
        assertNotNull(target.getRateInfo());
        assertEquals(source.getRateInfo().getCode(), target.getRateInfo().getCode());
        
        assertNotNull(target.getProductInfo());
        assertEquals(source.getProductInfo().getChannelRoomId(), target.getProductInfo().getChannelRoomId());
        
        assertNotNull(target.getAccountSettings());
        assertEquals(source.getAccountSettings().size(), target.getAccountSettings().size());
        
        assertNotNull(target.getRoomsInfo());
        assertEquals(source.getRoomsInfo().size(), target.getRoomsInfo().size());
        
        assertNotNull(target.getRatesInfo());
        assertEquals(source.getRatesInfo().size(), target.getRatesInfo().size());
        
        assertNotNull(target.getProductsInfo());
        assertEquals(source.getProductsInfo().size(), target.getProductsInfo().size());
        
        assertNotNull(target.getExtensions());
        assertEquals(source.getExtensions().size(), target.getExtensions().size());
    }

    private ChannelHotelDTO createCompleteChannelHotelDTO() {
        ChannelHotelDTO dto = new ChannelHotelDTO();
        dto.setSupplierId("SUPPLIER_001");
        dto.setChannelId("BOOKING");
        dto.setChannelHotelId("HOTEL_123");
        dto.setDerbyHotelId("DERBY_456");
        dto.setSupplierHotelId("SUPP_789");
        dto.setOperationToken("OP_TOKEN_001");

        // Hotel Info
        ChannelHotelInfo hotelInfo = new ChannelHotelInfo();
        hotelInfo.setId("HOTEL_INFO_001");
        hotelInfo.setName("Complete Test Hotel");
        dto.setHotelInfo(hotelInfo);

        // Room Info
        ChannelRoomInfo roomInfo = new ChannelRoomInfo();
        roomInfo.setCode("ROOM_001");
        roomInfo.setName("Standard Room");
        dto.setRoomInfo(roomInfo);

        // Rate Info
        ChannelRateInfo rateInfo = new ChannelRateInfo();
        rateInfo.setCode("RATE_001");
        rateInfo.setName("Standard Rate");
        dto.setRateInfo(rateInfo);

        // Product Info
        ChannelProductsDTO.Product productInfo = new ChannelProductsDTO.Product();
        productInfo.setChannelRoomId("ROOM_001");
        productInfo.setChannelRateId("RATE_001");
        dto.setProductInfo(productInfo);

        // Account Settings
        Map<String, Object> accountSettings = new HashMap<>();
        accountSettings.put("username", "testuser");
        dto.setAccountSettings(accountSettings);

        // Rooms Info
        List<ChannelRoomInfo> roomsInfo = Arrays.asList(roomInfo);
        dto.setRoomsInfo(roomsInfo);

        // Rates Info
        List<ChannelRateInfo> ratesInfo = Arrays.asList(rateInfo);
        dto.setRatesInfo(ratesInfo);

        // Products Info
        List<ChannelProductsDTO.Product> productsInfo = Arrays.asList(productInfo);
        dto.setProductsInfo(productsInfo);

        // Extensions
        Map<String, Object> extensions = new HashMap<>();
        extensions.put("customField", "customValue");
        dto.setExtensions(extensions);

        return dto;
    }
}
