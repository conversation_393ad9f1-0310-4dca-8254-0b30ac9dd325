package com.derbysoft.next.propertyconnect.channel.task.service;

import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelHotelDTO;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelHotelInfo;
import org.apache.commons.lang.NotImplementedException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Comprehensive unit tests for ChannelHotelCRUDService
 * Tests default interface methods and exception handling for CRUD operations
 */
@ExtendWith(MockitoExtension.class)
class ChannelHotelCRUDServiceTest {

    private ChannelHotelCRUDService crudService;

    @BeforeEach
    void setUp() {
        // Create a default implementation for testing
        crudService = new ChannelHotelCRUDService() {
            // Using default interface methods
        };
    }

    @Test
    void testGetByIds_DefaultImplementation_ThrowsNotImplementedException() {
        // Given
        String channelId = "BOOKING";
        String channelHotelId = "HOTEL_123";
        List<String> ids = Arrays.asList("ID_001", "ID_002");

        // When & Then
        NotImplementedException exception = assertThrows(NotImplementedException.class, () ->
            crudService.getByIds(channelId, channelHotelId, ids));

        assertNotNull(exception.getMessage());
        assertTrue(exception.getMessage().contains(channelId));
        assertTrue(exception.getMessage().contains(channelHotelId));
        assertTrue(exception.getMessage().contains("query operation"));
    }

    @Test
    void testGetChannelHotel_DefaultImplementation_ThrowsNotImplementedException() {
        // Given
        String channelId = "EXPEDIA";
        String channelHotelId = "HOTEL_456";

        // When & Then
        NotImplementedException exception = assertThrows(NotImplementedException.class, () ->
            crudService.getChannelHotel(channelId, channelHotelId));

        assertNotNull(exception.getMessage());
        assertTrue(exception.getMessage().contains(channelId));
        assertTrue(exception.getMessage().contains(channelHotelId));
        assertTrue(exception.getMessage().contains("query operation"));
    }

    @Test
    void testSaveIncrementalChannelProduct_DefaultImplementation_ThrowsNotImplementedException() {
        // Given
        ChannelHotelDTO channelHotel = createTestChannelHotelDTO();

        // When & Then
        NotImplementedException exception = assertThrows(NotImplementedException.class, () ->
            crudService.saveIncrementalChannelProduct(channelHotel));

        assertNotNull(exception.getMessage());
        assertTrue(exception.getMessage().contains(channelHotel.getChannelId()));
        assertTrue(exception.getMessage().contains(channelHotel.getChannelHotelId()));
        assertTrue(exception.getMessage().contains("save operation"));
    }

    @Test
    void testSaveAllChannelProducts_DefaultImplementation_ThrowsNotImplementedException() {
        // Given
        ChannelHotelDTO channelHotel = createTestChannelHotelDTO();

        // When & Then
        NotImplementedException exception = assertThrows(NotImplementedException.class, () ->
            crudService.saveAllChannelProducts(channelHotel));

        assertNotNull(exception.getMessage());
        assertTrue(exception.getMessage().contains(channelHotel.getChannelId()));
        assertTrue(exception.getMessage().contains(channelHotel.getChannelHotelId()));
        assertTrue(exception.getMessage().contains("save operation"));
    }

    @Test
    void testDeleteChannelHotel_DefaultImplementation_ThrowsNotImplementedException() {
        // Given
        String channelId = "AGODA";
        String channelHotelId = "HOTEL_789";

        // When & Then
        NotImplementedException exception = assertThrows(NotImplementedException.class, () ->
            crudService.deleteChannelHotel(channelId, channelHotelId));

        assertNotNull(exception.getMessage());
        assertTrue(exception.getMessage().contains(channelId));
        assertTrue(exception.getMessage().contains(channelHotelId));
        assertTrue(exception.getMessage().contains("delete operation"));
    }

    @Test
    void testGetByIds_WithNullIds_ThrowsNotImplementedException() {
        // Given
        String channelId = "BOOKING";
        String channelHotelId = "HOTEL_123";
        List<String> ids = null;

        // When & Then
        NotImplementedException exception = assertThrows(NotImplementedException.class, () ->
            crudService.getByIds(channelId, channelHotelId, ids));

        assertNotNull(exception.getMessage());
        assertTrue(exception.getMessage().contains("query operation"));
    }

    @Test
    void testGetByIds_WithEmptyIds_ThrowsNotImplementedException() {
        // Given
        String channelId = "BOOKING";
        String channelHotelId = "HOTEL_123";
        List<String> ids = Arrays.asList();

        // When & Then
        NotImplementedException exception = assertThrows(NotImplementedException.class, () ->
            crudService.getByIds(channelId, channelHotelId, ids));

        assertNotNull(exception.getMessage());
        assertTrue(exception.getMessage().contains("query operation"));
    }

    @Test
    void testGetChannelHotel_WithNullChannelId_ThrowsNotImplementedException() {
        // Given
        String channelId = null;
        String channelHotelId = "HOTEL_123";

        // When & Then
        NotImplementedException exception = assertThrows(NotImplementedException.class, () ->
            crudService.getChannelHotel(channelId, channelHotelId));

        assertNotNull(exception.getMessage());
        assertTrue(exception.getMessage().contains("null"));
        assertTrue(exception.getMessage().contains("query operation"));
    }

    @Test
    void testGetChannelHotel_WithNullChannelHotelId_ThrowsNotImplementedException() {
        // Given
        String channelId = "BOOKING";
        String channelHotelId = null;

        // When & Then
        NotImplementedException exception = assertThrows(NotImplementedException.class, () ->
            crudService.getChannelHotel(channelId, channelHotelId));

        assertNotNull(exception.getMessage());
        assertTrue(exception.getMessage().contains("null"));
        assertTrue(exception.getMessage().contains("query operation"));
    }

    @Test
    void testSaveIncrementalChannelProduct_WithNullChannelHotel_ThrowsNullPointerException() {
        // Given
        ChannelHotelDTO channelHotel = null;

        // When & Then
        assertThrows(NullPointerException.class, () ->
            crudService.saveIncrementalChannelProduct(channelHotel));
    }

    @Test
    void testSaveAllChannelProducts_WithNullChannelHotel_ThrowsNullPointerException() {
        // Given
        ChannelHotelDTO channelHotel = null;

        // When & Then
        assertThrows(NullPointerException.class, () ->
            crudService.saveAllChannelProducts(channelHotel));
    }

    @Test
    void testDeleteChannelHotel_WithNullChannelId_ThrowsNotImplementedException() {
        // Given
        String channelId = null;
        String channelHotelId = "HOTEL_123";

        // When & Then
        NotImplementedException exception = assertThrows(NotImplementedException.class, () ->
            crudService.deleteChannelHotel(channelId, channelHotelId));

        assertNotNull(exception.getMessage());
        assertTrue(exception.getMessage().contains("null"));
        assertTrue(exception.getMessage().contains("delete operation"));
    }

    @Test
    void testDeleteChannelHotel_WithNullChannelHotelId_ThrowsNotImplementedException() {
        // Given
        String channelId = "BOOKING";
        String channelHotelId = null;

        // When & Then
        NotImplementedException exception = assertThrows(NotImplementedException.class, () ->
            crudService.deleteChannelHotel(channelId, channelHotelId));

        assertNotNull(exception.getMessage());
        assertTrue(exception.getMessage().contains("null"));
        assertTrue(exception.getMessage().contains("delete operation"));
    }

    @Test
    void testSaveIncrementalChannelProduct_WithChannelHotelMissingChannelId_ThrowsNotImplementedException() {
        // Given
        ChannelHotelDTO channelHotel = new ChannelHotelDTO();
        channelHotel.setChannelHotelId("HOTEL_123");
        // channelId is null

        // When & Then
        NotImplementedException exception = assertThrows(NotImplementedException.class, () ->
            crudService.saveIncrementalChannelProduct(channelHotel));

        assertNotNull(exception.getMessage());
        assertTrue(exception.getMessage().contains("save operation"));
    }

    @Test
    void testSaveIncrementalChannelProduct_WithChannelHotelMissingChannelHotelId_ThrowsNotImplementedException() {
        // Given
        ChannelHotelDTO channelHotel = new ChannelHotelDTO();
        channelHotel.setChannelId("BOOKING");
        // channelHotelId is null

        // When & Then
        NotImplementedException exception = assertThrows(NotImplementedException.class, () ->
            crudService.saveIncrementalChannelProduct(channelHotel));

        assertNotNull(exception.getMessage());
        assertTrue(exception.getMessage().contains("save operation"));
    }

    @Test
    void testSaveAllChannelProducts_WithChannelHotelMissingChannelId_ThrowsNotImplementedException() {
        // Given
        ChannelHotelDTO channelHotel = new ChannelHotelDTO();
        channelHotel.setChannelHotelId("HOTEL_123");
        // channelId is null

        // When & Then
        NotImplementedException exception = assertThrows(NotImplementedException.class, () ->
            crudService.saveAllChannelProducts(channelHotel));

        assertNotNull(exception.getMessage());
        assertTrue(exception.getMessage().contains("save operation"));
    }

    @Test
    void testSaveAllChannelProducts_WithChannelHotelMissingChannelHotelId_ThrowsNotImplementedException() {
        // Given
        ChannelHotelDTO channelHotel = new ChannelHotelDTO();
        channelHotel.setChannelId("BOOKING");
        // channelHotelId is null

        // When & Then
        NotImplementedException exception = assertThrows(NotImplementedException.class, () ->
            crudService.saveAllChannelProducts(channelHotel));

        assertNotNull(exception.getMessage());
        assertTrue(exception.getMessage().contains("save operation"));
    }

    @Test
    void testGetByIds_WithSpecialCharactersInIds_ThrowsNotImplementedException() {
        // Given
        String channelId = "BOOKING";
        String channelHotelId = "HOTEL_123";
        List<String> ids = Arrays.asList("ID@#$%", "ID_WITH_SPACES ", "ID-WITH-DASHES");

        // When & Then
        NotImplementedException exception = assertThrows(NotImplementedException.class, () ->
            crudService.getByIds(channelId, channelHotelId, ids));

        assertNotNull(exception.getMessage());
        assertTrue(exception.getMessage().contains("query operation"));
    }

    @Test
    void testGetChannelHotel_WithSpecialCharactersInIds_ThrowsNotImplementedException() {
        // Given
        String channelId = "BOOKING@#$%";
        String channelHotelId = "HOTEL_123 WITH SPACES";

        // When & Then
        NotImplementedException exception = assertThrows(NotImplementedException.class, () ->
            crudService.getChannelHotel(channelId, channelHotelId));

        assertNotNull(exception.getMessage());
        assertTrue(exception.getMessage().contains(channelId));
        assertTrue(exception.getMessage().contains(channelHotelId));
        assertTrue(exception.getMessage().contains("query operation"));
    }

    @Test
    void testDeleteChannelHotel_WithSpecialCharactersInIds_ThrowsNotImplementedException() {
        // Given
        String channelId = "BOOKING-TEST_123";
        String channelHotelId = "HOTEL@#$%";

        // When & Then
        NotImplementedException exception = assertThrows(NotImplementedException.class, () ->
            crudService.deleteChannelHotel(channelId, channelHotelId));

        assertNotNull(exception.getMessage());
        assertTrue(exception.getMessage().contains(channelId));
        assertTrue(exception.getMessage().contains(channelHotelId));
        assertTrue(exception.getMessage().contains("delete operation"));
    }

    @Test
    void testSaveIncrementalChannelProduct_WithComplexChannelHotelDTO_ThrowsNotImplementedException() {
        // Given
        ChannelHotelDTO channelHotel = createComplexChannelHotelDTO();

        // When & Then
        NotImplementedException exception = assertThrows(NotImplementedException.class, () ->
            crudService.saveIncrementalChannelProduct(channelHotel));

        assertNotNull(exception.getMessage());
        assertTrue(exception.getMessage().contains(channelHotel.getChannelId()));
        assertTrue(exception.getMessage().contains(channelHotel.getChannelHotelId()));
        assertTrue(exception.getMessage().contains("save operation"));
    }

    @Test
    void testSaveAllChannelProducts_WithComplexChannelHotelDTO_ThrowsNotImplementedException() {
        // Given
        ChannelHotelDTO channelHotel = createComplexChannelHotelDTO();

        // When & Then
        NotImplementedException exception = assertThrows(NotImplementedException.class, () ->
            crudService.saveAllChannelProducts(channelHotel));

        assertNotNull(exception.getMessage());
        assertTrue(exception.getMessage().contains(channelHotel.getChannelId()));
        assertTrue(exception.getMessage().contains(channelHotel.getChannelHotelId()));
        assertTrue(exception.getMessage().contains("save operation"));
    }

    private ChannelHotelDTO createTestChannelHotelDTO() {
        ChannelHotelDTO dto = new ChannelHotelDTO();
        dto.setChannelId("BOOKING");
        dto.setChannelHotelId("HOTEL_123");
        dto.setSupplierId("SUPPLIER_001");
        dto.setDerbyHotelId("DERBY_456");
        dto.setSupplierHotelId("SUPP_789");
        dto.setOperationToken("OP_TOKEN_001");

        ChannelHotelInfo hotelInfo = new ChannelHotelInfo();
        hotelInfo.setId("HOTEL_123");
        hotelInfo.setName("Test Hotel");
        dto.setHotelInfo(hotelInfo);

        return dto;
    }

    private ChannelHotelDTO createComplexChannelHotelDTO() {
        ChannelHotelDTO dto = createTestChannelHotelDTO();
        dto.setChannelId("EXPEDIA-COMPLEX");
        dto.setChannelHotelId("HOTEL_COMPLEX_456");
        
        ChannelHotelInfo hotelInfo = dto.getHotelInfo();
        hotelInfo.setId("HOTEL_COMPLEX_456");
        hotelInfo.setName("Complex Test Hotel with Special Characters @#$%");
        hotelInfo.setDescription("A complex hotel for testing purposes");
        hotelInfo.setCurrency("USD");
        hotelInfo.setTimezone("America/New_York");

        return dto;
    }
}
