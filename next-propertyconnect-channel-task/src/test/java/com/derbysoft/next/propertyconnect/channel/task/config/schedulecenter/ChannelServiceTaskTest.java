package com.derbysoft.next.propertyconnect.channel.task.config.schedulecenter;

import com.derbysoft.schedulecenter.rpc.protocol.Task;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for ChannelServiceTask
 */
class ChannelServiceTaskTest {

    private ChannelServiceTask channelServiceTask;

    @BeforeEach
    void setUp() {
        channelServiceTask = ChannelServiceTask.builder()
                .id("task-123")
                .echoToken("echo-456")
                .channel("BOOKINGCOM")
                .supplier("PROPERTYCONNECT")
                .supplierHotel("supplier-hotel-789")
                .channelHotel("channel-hotel-101")
                .startDate("2023-01-01")
                .endDate("2023-12-31")
                .hotel("hotel-202")
                .priority(1)
                .ignorePropertyStatusCheck(false)
                .procedure(Arrays.asList("SaveProperty", "SaveRoomTypes"))
                .notifyUrl("https://example.com/notify")
                .hotels(Arrays.asList("hotel1", "hotel2"))
                .channelRooms(Arrays.asList("room1", "room2"))
                .channelRates(Arrays.asList("rate1", "rate2"))
                .channelProducts(Arrays.asList("product1", "product2"))
                .createTime(System.currentTimeMillis())
                .delayExecuteTime(1000L)
                .build();
    }

    @Test
    void testChannelServiceTaskBuilder() {
        assertNotNull(channelServiceTask);
        assertEquals("task-123", channelServiceTask.getId());
        assertEquals("echo-456", channelServiceTask.getEchoToken());
        assertEquals("BOOKINGCOM", channelServiceTask.getChannel());
        assertEquals("PROPERTYCONNECT", channelServiceTask.getSupplier());
        assertEquals("supplier-hotel-789", channelServiceTask.getSupplierHotel());
        assertEquals("channel-hotel-101", channelServiceTask.getChannelHotel());
        assertEquals("2023-01-01", channelServiceTask.getStartDate());
        assertEquals("2023-12-31", channelServiceTask.getEndDate());
        assertEquals("hotel-202", channelServiceTask.getHotel());
        assertEquals(Integer.valueOf(1), channelServiceTask.getPriority());
        assertEquals(Boolean.FALSE, channelServiceTask.getIgnorePropertyStatusCheck());
        assertEquals(Arrays.asList("SaveProperty", "SaveRoomTypes"), channelServiceTask.getProcedure());
        assertEquals("https://example.com/notify", channelServiceTask.getNotifyUrl());
        assertEquals(Arrays.asList("hotel1", "hotel2"), channelServiceTask.getHotels());
        assertEquals(Arrays.asList("room1", "room2"), channelServiceTask.getChannelRooms());
        assertEquals(Arrays.asList("rate1", "rate2"), channelServiceTask.getChannelRates());
        assertEquals(Arrays.asList("product1", "product2"), channelServiceTask.getChannelProducts());
        assertNotNull(channelServiceTask.getCreateTime());
        assertEquals(Long.valueOf(1000L), channelServiceTask.getDelayExecuteTime());
    }

    @Test
    void testToBuilder() {
        ChannelServiceTask copy = channelServiceTask.toBuilder()
                .id("new-task-456")
                .channel("EXPEDIA")
                .build();

        assertEquals("new-task-456", copy.getId());
        assertEquals("EXPEDIA", copy.getChannel());
        // Other fields should remain the same
        assertEquals("echo-456", copy.getEchoToken());
        assertEquals("PROPERTYCONNECT", copy.getSupplier());
    }

    @Test
    void testToTask() {
        Task task = channelServiceTask.toTask();
        
        assertNotNull(task);
        Map<String, Object> parameters = task.getParametersMap();
        
        assertEquals("task-123", parameters.get("id"));
        assertEquals("echo-456", parameters.get("echoToken"));
        assertEquals("BOOKINGCOM", parameters.get("channel"));
        assertEquals("PROPERTYCONNECT", parameters.get("supplier"));
        assertEquals("supplier-hotel-789", parameters.get("supplierHotel"));
        assertEquals("channel-hotel-101", parameters.get("channelHotel"));
        assertEquals("2023-01-01", parameters.get("startDate"));
        assertEquals("2023-12-31", parameters.get("endDate"));
        assertEquals("hotel-202", parameters.get("hotel"));
        assertEquals(1, parameters.get("priority"));
        assertEquals(false, parameters.get("ignorePropertyStatusCheck"));
        assertEquals(Arrays.asList("SaveProperty", "SaveRoomTypes"), parameters.get("procedure"));
        assertEquals("https://example.com/notify", parameters.get("notifyUrl"));
        assertEquals(Arrays.asList("hotel1", "hotel2"), parameters.get("hotels"));
        assertEquals(Arrays.asList("room1", "room2"), parameters.get("channelRooms"));
        assertEquals(Arrays.asList("rate1", "rate2"), parameters.get("channelRates"));
        assertEquals(Arrays.asList("product1", "product2"), parameters.get("channelProducts"));
        assertNotNull(parameters.get("createTime"));
        assertEquals(1000L, parameters.get("delayExecuteTime"));
    }

    @Test
    void testFromTask() {
        Task task = channelServiceTask.toTask();
        ChannelServiceTask converted = ChannelServiceTask.fromTask(task);
        
        assertNotNull(converted);
        assertEquals(channelServiceTask.getId(), converted.getId());
        assertEquals(channelServiceTask.getEchoToken(), converted.getEchoToken());
        assertEquals(channelServiceTask.getChannel(), converted.getChannel());
        assertEquals(channelServiceTask.getSupplier(), converted.getSupplier());
        assertEquals(channelServiceTask.getSupplierHotel(), converted.getSupplierHotel());
        assertEquals(channelServiceTask.getChannelHotel(), converted.getChannelHotel());
        assertEquals(channelServiceTask.getStartDate(), converted.getStartDate());
        assertEquals(channelServiceTask.getEndDate(), converted.getEndDate());
        assertEquals(channelServiceTask.getHotel(), converted.getHotel());
        assertEquals(channelServiceTask.getPriority(), converted.getPriority());
        assertEquals(channelServiceTask.getIgnorePropertyStatusCheck(), converted.getIgnorePropertyStatusCheck());
        assertEquals(channelServiceTask.getProcedure(), converted.getProcedure());
        assertEquals(channelServiceTask.getNotifyUrl(), converted.getNotifyUrl());
        assertEquals(channelServiceTask.getHotels(), converted.getHotels());
        assertEquals(channelServiceTask.getChannelRooms(), converted.getChannelRooms());
        assertEquals(channelServiceTask.getChannelRates(), converted.getChannelRates());
        assertEquals(channelServiceTask.getChannelProducts(), converted.getChannelProducts());
        assertEquals(channelServiceTask.getCreateTime(), converted.getCreateTime());
        assertEquals(channelServiceTask.getDelayExecuteTime(), converted.getDelayExecuteTime());
    }

    @Test
    void testToTasks() {
        ChannelServiceTask task1 = channelServiceTask;
        ChannelServiceTask task2 = channelServiceTask.toBuilder()
                .id("task-456")
                .channel("EXPEDIA")
                .build();

        List<ChannelServiceTask> channelTasks = Arrays.asList(task1, task2);
        List<Task> tasks = ChannelServiceTask.toTasks(channelTasks);
        
        assertNotNull(tasks);
        assertEquals(2, tasks.size());
        
        assertEquals("task-123", tasks.get(0).getParametersMap().get("id"));
        assertEquals("BOOKINGCOM", tasks.get(0).getParametersMap().get("channel"));
        
        assertEquals("task-456", tasks.get(1).getParametersMap().get("id"));
        assertEquals("EXPEDIA", tasks.get(1).getParametersMap().get("channel"));
    }

    @Test
    void testDefaultDelayExecuteTime() {
        ChannelServiceTask task = ChannelServiceTask.builder()
                .id("test-task")
                .delayExecuteTime(0L)  // Explicitly set since @Builder ignores field initialization
                .build();

        assertEquals(Long.valueOf(0L), task.getDelayExecuteTime());
    }

    @Test
    void testNullValues() {
        ChannelServiceTask task = new ChannelServiceTask();
        
        assertNull(task.getId());
        assertNull(task.getEchoToken());
        assertNull(task.getChannel());
        assertNull(task.getSupplier());
        assertNull(task.getSupplierHotel());
        assertNull(task.getChannelHotel());
        assertNull(task.getStartDate());
        assertNull(task.getEndDate());
        assertNull(task.getHotel());
        assertNull(task.getPriority());
        assertNull(task.getIgnorePropertyStatusCheck());
        assertNull(task.getProcedure());
        assertNull(task.getNotifyUrl());
        assertNull(task.getHotels());
        assertNull(task.getChannelRooms());
        assertNull(task.getChannelRates());
        assertNull(task.getChannelProducts());
        assertNull(task.getCreateTime());
        assertEquals(Long.valueOf(0L), task.getDelayExecuteTime());
    }
}
