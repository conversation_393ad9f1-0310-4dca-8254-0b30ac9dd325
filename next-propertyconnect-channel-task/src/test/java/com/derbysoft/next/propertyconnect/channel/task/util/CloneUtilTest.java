package com.derbysoft.next.propertyconnect.channel.task.util;

import com.derbysoft.next.propertyconnect.channel.task.domain.dto.*;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Comprehensive unit tests for CloneUtil
 */
class CloneUtilTest {

    @Test
    void testCloneUtilExists() {
        // Test that the CloneUtil class exists
        assertDoesNotThrow(() -> {
            Class<?> clazz = CloneUtil.class;
            assertNotNull(clazz);
        });
    }

    @Test
    void testCloneUtilClassStructure() {
        // Test basic class structure
        Class<?> clazz = CloneUtil.class;
        assertNotNull(clazz);
        assertEquals("CloneUtil", clazz.getSimpleName());
        assertEquals("com.derbysoft.next.propertyconnect.channel.task.util", clazz.getPackageName());
    }

    @Test
    void testCloneUtilInstance() {
        // Test that INSTANCE field exists and is properly initialized
        assertNotNull(CloneUtil.INSTANCE);
        assertTrue(CloneUtil.INSTANCE instanceof CloneUtil);
    }

    @Test
    void testCloneChannelProductsDTO() {
        // Test cloning ChannelProductsDTO
        ChannelProductsDTO original = new ChannelProductsDTO();
        original.setChannelId("TEST_CHANNEL");
        original.setChannelHotelId("TEST_HOTEL");
        original.setHotelName("Test Hotel");

        ChannelProductsDTO.Product product = new ChannelProductsDTO.Product();
        product.setChannelRoomId("ROOM_001");
        product.setChannelRoomName("Standard Room");
        product.setChannelRateId("RATE_001");
        product.setChannelRateName("Standard Rate");
        product.setStatus("Active");

        Map<String, Object> extensions = new HashMap<>();
        extensions.put("key1", "value1");
        extensions.put("key2", "value2");
        product.setExtensions(extensions);

        original.setChannelProducts(Arrays.asList(product));

        ChannelProductsDTO cloned = CloneUtil.INSTANCE.clone(original);

        assertNotNull(cloned);
        assertNotSame(original, cloned);
        assertEquals(original.getChannelId(), cloned.getChannelId());
        assertEquals(original.getChannelHotelId(), cloned.getChannelHotelId());
        assertEquals(original.getHotelName(), cloned.getHotelName());

        assertNotNull(cloned.getChannelProducts());
        assertEquals(1, cloned.getChannelProducts().size());

        ChannelProductsDTO.Product clonedProduct = cloned.getChannelProducts().get(0);
        assertNotSame(product, clonedProduct);
        assertEquals(product.getChannelRoomId(), clonedProduct.getChannelRoomId());
        assertEquals(product.getChannelRoomName(), clonedProduct.getChannelRoomName());
        assertEquals(product.getChannelRateId(), clonedProduct.getChannelRateId());
        assertEquals(product.getChannelRateName(), clonedProduct.getChannelRateName());
        assertEquals(product.getStatus(), clonedProduct.getStatus());

        // Test deep cloning of extensions
        assertNotSame(product.getExtensions(), clonedProduct.getExtensions());
        assertEquals(product.getExtensions(), clonedProduct.getExtensions());
    }

    @Test
    void testCloneChannelProductsDTOProduct() {
        // Test cloning individual Product
        ChannelProductsDTO.Product original = new ChannelProductsDTO.Product();
        original.setChannelRoomId("ROOM_002");
        original.setChannelRoomName("Deluxe Room");
        original.setChannelRateId("RATE_002");
        original.setChannelRateName("Deluxe Rate");
        original.setStatus("Inactive");
        original.setAvailStatus(true);

        Map<String, Object> extensions = new HashMap<>();
        extensions.put("roomType", "deluxe");
        extensions.put("maxOccupancy", 4);
        original.setExtensions(extensions);

        ChannelProductsDTO.Product cloned = CloneUtil.INSTANCE.clone(original);

        assertNotNull(cloned);
        assertNotSame(original, cloned);
        assertEquals(original.getChannelRoomId(), cloned.getChannelRoomId());
        assertEquals(original.getChannelRoomName(), cloned.getChannelRoomName());
        assertEquals(original.getChannelRateId(), cloned.getChannelRateId());
        assertEquals(original.getChannelRateName(), cloned.getChannelRateName());
        assertEquals(original.getStatus(), cloned.getStatus());
        assertEquals(original.getAvailStatus(), cloned.getAvailStatus());

        // Test deep cloning of extensions
        assertNotSame(original.getExtensions(), cloned.getExtensions());
        assertEquals(original.getExtensions(), cloned.getExtensions());
    }

    @Test
    void testCloneChannelHotelDTO() {
        // Test cloning ChannelHotelDTO
        ChannelHotelDTO original = new ChannelHotelDTO();
        original.setDerbyHotelId("DERBY_001");
        original.setSupplierHotelId("SUPPLIER_001");
        original.setOperationToken("TOKEN_123");

        ChannelHotelInfo hotelInfo = new ChannelHotelInfo();
        hotelInfo.setId("HOTEL_001");
        hotelInfo.setName("Test Hotel");

        ChannelHotelInfo.Address address = new ChannelHotelInfo.Address();
        address.setLine1("123 Test Street");
        hotelInfo.setAddress(address);
        original.setHotelInfo(hotelInfo);

        ChannelHotelDTO cloned = CloneUtil.INSTANCE.clone(original);

        assertNotNull(cloned);
        assertNotSame(original, cloned);
        assertEquals(original.getDerbyHotelId(), cloned.getDerbyHotelId());
        assertEquals(original.getSupplierHotelId(), cloned.getSupplierHotelId());
        assertEquals(original.getOperationToken(), cloned.getOperationToken());

        assertNotNull(cloned.getHotelInfo());
        assertNotSame(original.getHotelInfo(), cloned.getHotelInfo());
        assertEquals(original.getHotelInfo().getId(), cloned.getHotelInfo().getId());
        assertEquals(original.getHotelInfo().getName(), cloned.getHotelInfo().getName());
        assertEquals(original.getHotelInfo().getAddress().getLine1(), cloned.getHotelInfo().getAddress().getLine1());
    }

    @Test
    void testCloneChannelHotelInfo() {
        // Test cloning ChannelHotelInfo
        ChannelHotelInfo original = new ChannelHotelInfo();
        original.setId("HOTEL_002");
        original.setName("Another Test Hotel");

        ChannelHotelInfo.Address address = new ChannelHotelInfo.Address();
        address.setLine1("456 Another Street");
        address.setCityCode("Test City");
        address.setCountryCode("Test Country");
        original.setAddress(address);

        Map<String, Object> extensions = new HashMap<>();
        extensions.put("starRating", 5);
        extensions.put("amenities", Arrays.asList("WiFi", "Pool", "Gym"));
        original.setExtensions(extensions);

        ChannelHotelInfo cloned = CloneUtil.INSTANCE.clone(original);

        assertNotNull(cloned);
        assertNotSame(original, cloned);
        assertEquals(original.getId(), cloned.getId());
        assertEquals(original.getName(), cloned.getName());
        assertEquals(original.getAddress().getLine1(), cloned.getAddress().getLine1());
        assertEquals(original.getAddress().getCityCode(), cloned.getAddress().getCityCode());
        assertEquals(original.getAddress().getCountryCode(), cloned.getAddress().getCountryCode());

        // Test deep cloning of extensions
        assertNotSame(original.getExtensions(), cloned.getExtensions());
        assertEquals(original.getExtensions(), cloned.getExtensions());
    }

    @Test
    void testCloneChannelRoomInfo() {
        // Test cloning ChannelRoomInfo
        ChannelRoomInfo original = new ChannelRoomInfo();
        original.setCode("ROOM_003");
        original.setName("Suite Room");
        original.setDescription("Luxury suite with ocean view");

        Map<String, Object> extensions = new HashMap<>();
        extensions.put("bedType", "King");
        extensions.put("view", "Ocean");
        original.setExtensions(extensions);

        ChannelRoomInfo cloned = CloneUtil.INSTANCE.clone(original);

        assertNotNull(cloned);
        assertNotSame(original, cloned);
        assertEquals(original.getCode(), cloned.getCode());
        assertEquals(original.getName(), cloned.getName());
        assertEquals(original.getDescription(), cloned.getDescription());

        // Test deep cloning of extensions
        assertNotSame(original.getExtensions(), cloned.getExtensions());
        assertEquals(original.getExtensions(), cloned.getExtensions());
    }

    @Test
    void testCloneChannelRateInfo() {
        // Test cloning ChannelRateInfo
        ChannelRateInfo original = new ChannelRateInfo();
        original.setCode("RATE_003");
        original.setName("Premium Rate");
        original.setDescription("Premium rate with breakfast included");

        Map<String, Object> extensions = new HashMap<>();
        extensions.put("mealPlan", "Breakfast");
        extensions.put("cancellationPolicy", "Free cancellation");
        original.setExtensions(extensions);

        ChannelRateInfo cloned = CloneUtil.INSTANCE.clone(original);

        assertNotNull(cloned);
        assertNotSame(original, cloned);
        assertEquals(original.getCode(), cloned.getCode());
        assertEquals(original.getName(), cloned.getName());
        assertEquals(original.getDescription(), cloned.getDescription());

        // Test deep cloning of extensions
        assertNotSame(original.getExtensions(), cloned.getExtensions());
        assertEquals(original.getExtensions(), cloned.getExtensions());
    }

    @Test
    void testCloneNullObjects() {
        // Test cloning null objects
        assertNull(CloneUtil.INSTANCE.clone((ChannelProductsDTO) null));
        assertNull(CloneUtil.INSTANCE.clone((ChannelProductsDTO.Product) null));
        assertNull(CloneUtil.INSTANCE.clone((ChannelHotelDTO) null));
        assertNull(CloneUtil.INSTANCE.clone((ChannelHotelInfo) null));
        assertNull(CloneUtil.INSTANCE.clone((ChannelRoomInfo) null));
        assertNull(CloneUtil.INSTANCE.clone((ChannelRateInfo) null));
    }

    @Test
    void testCloneEmptyObjects() {
        // Test cloning empty objects
        ChannelProductsDTO emptyProductsDTO = new ChannelProductsDTO();
        ChannelProductsDTO clonedProductsDTO = CloneUtil.INSTANCE.clone(emptyProductsDTO);
        assertNotNull(clonedProductsDTO);
        assertNotSame(emptyProductsDTO, clonedProductsDTO);

        ChannelProductsDTO.Product emptyProduct = new ChannelProductsDTO.Product();
        ChannelProductsDTO.Product clonedProduct = CloneUtil.INSTANCE.clone(emptyProduct);
        assertNotNull(clonedProduct);
        assertNotSame(emptyProduct, clonedProduct);

        ChannelHotelDTO emptyHotelDTO = new ChannelHotelDTO();
        ChannelHotelDTO clonedHotelDTO = CloneUtil.INSTANCE.clone(emptyHotelDTO);
        assertNotNull(clonedHotelDTO);
        assertNotSame(emptyHotelDTO, clonedHotelDTO);

        ChannelHotelInfo emptyHotelInfo = new ChannelHotelInfo();
        ChannelHotelInfo clonedHotelInfo = CloneUtil.INSTANCE.clone(emptyHotelInfo);
        assertNotNull(clonedHotelInfo);
        assertNotSame(emptyHotelInfo, clonedHotelInfo);

        ChannelRoomInfo emptyRoomInfo = new ChannelRoomInfo();
        ChannelRoomInfo clonedRoomInfo = CloneUtil.INSTANCE.clone(emptyRoomInfo);
        assertNotNull(clonedRoomInfo);
        assertNotSame(emptyRoomInfo, clonedRoomInfo);

        ChannelRateInfo emptyRateInfo = new ChannelRateInfo();
        ChannelRateInfo clonedRateInfo = CloneUtil.INSTANCE.clone(emptyRateInfo);
        assertNotNull(clonedRateInfo);
        assertNotSame(emptyRateInfo, clonedRateInfo);
    }
}
