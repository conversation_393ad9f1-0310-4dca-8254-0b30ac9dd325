package com.derbysoft.next.propertyconnect.channel.task.service.impl.agoda;

import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelHotelDTO;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelHotelInfo;
import com.derbysoft.next.propertyconnect.channel.task.service.RemoteService;
import com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.RemoteChannelService;
import com.derbysoft.next.propertyconnect.channel.task.util.RemoteServiceUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AgodaAdapterServiceTest {

    @Mock
    private RemoteService remoteService;

    private AgodaAdapterService agodaAdapterService;

    @BeforeEach
    void setUp() {
        agodaAdapterService = new AgodaAdapterService();
    }

    @Test
    void testChannel() {
        // When
        String channel = agodaAdapterService.channel();

        // Then
        assertEquals("AGODA", channel);
    }

    @Test
    void testCustomizeProcedure() {
        // When
        List<RemoteChannelService.Operation> procedure = agodaAdapterService.customizeProcedure();

        // Then
        assertNotNull(procedure);
        assertEquals(2, procedure.size());
        assertTrue(procedure.contains(RemoteChannelService.Operation.SaveProperty));
        assertTrue(procedure.contains(RemoteChannelService.Operation.TriggerARIRefresh));
    }

    @Test
    void testCustomizeProperty_WithCompleteData() {
        // Given
        ChannelHotelDTO channelHotelDTO = createTestChannelHotelDTO();
        
        // Mock hotel connection data
        Map<String, Object> channelHotelSettings = new HashMap<>();
        Map<String, Object> rateRule = new HashMap<>();
        rateRule.put("channelRateType", "NET");
        rateRule.put("channelResPriceType", "SellPrice");
        rateRule.put("channelResRateType", "RefSellToAmountAfterTax");
        channelHotelSettings.put("rateRule", rateRule);
        
        Map<String, Object> channelSettings = new HashMap<>();
        channelSettings.put("paymentType", "UPC");
        channelHotelSettings.put("channelSettings", channelSettings);

        // Mock hotel details
        Map<String, Object> hotelDetail = new HashMap<>();
        hotelDetail.put("childPolicy", "childAsAdult");

        try (MockedStatic<RemoteServiceUtil> mockedUtil = mockStatic(RemoteServiceUtil.class)) {
            mockedUtil.when(RemoteServiceUtil::getInstance).thenReturn(remoteService);
            
            when(remoteService.uniqueHotelConnections("AGODA", "hotel-123"))
                    .thenReturn(Optional.of(channelHotelSettings));
            when(remoteService.getHotelDetails("supplier-hotel-123"))
                    .thenReturn(Optional.of(hotelDetail));

            // When
            agodaAdapterService.customizeProperty(channelHotelDTO);

            // Then
            Map<String, Object> extensions = channelHotelDTO.getHotelInfo().getExtensions();
            assertNotNull(extensions);
            assertEquals("NET", extensions.get("ariRateType"));
            assertEquals("RefSellToAmountAfterTax", extensions.get("reservationRateType"));
            assertEquals("7", extensions.get("maxLos"));
            assertEquals("false", extensions.get("noNeedUpc"));
            assertEquals("false", extensions.get("ignorePreCheck"));
            assertEquals("GMT+08:00", extensions.get("timeZoneID"));
            assertEquals("Yes", extensions.get("plusChildToAdultCount"));
            assertEquals("", extensions.get("changeAdultCnt"));
            assertEquals("test-api-key", extensions.get("apiKey"));

            verify(remoteService).uniqueHotelConnections("AGODA", "hotel-123");
            verify(remoteService).getHotelDetails("supplier-hotel-123");
        }
    }

    @Test
    void testCustomizeProperty_WithNonUPCPaymentType() {
        // Given
        ChannelHotelDTO channelHotelDTO = createTestChannelHotelDTO();
        
        Map<String, Object> channelHotelSettings = new HashMap<>();
        Map<String, Object> rateRule = new HashMap<>();
        rateRule.put("channelRateType", "GROSS");
        rateRule.put("channelResPriceType", "NetPrice");
        rateRule.put("channelResRateType", "RefNetToAmountBeforeTax");
        channelHotelSettings.put("rateRule", rateRule);
        
        Map<String, Object> channelSettings = new HashMap<>();
        channelSettings.put("paymentType", "CREDIT_CARD");
        channelHotelSettings.put("channelSettings", channelSettings);

        Map<String, Object> hotelDetail = new HashMap<>();
        hotelDetail.put("childPolicy", "normal");

        try (MockedStatic<RemoteServiceUtil> mockedUtil = mockStatic(RemoteServiceUtil.class)) {
            mockedUtil.when(RemoteServiceUtil::getInstance).thenReturn(remoteService);
            
            when(remoteService.uniqueHotelConnections("AGODA", "hotel-123"))
                    .thenReturn(Optional.of(channelHotelSettings));
            when(remoteService.getHotelDetails("supplier-hotel-123"))
                    .thenReturn(Optional.of(hotelDetail));

            // When
            agodaAdapterService.customizeProperty(channelHotelDTO);

            // Then
            Map<String, Object> extensions = channelHotelDTO.getHotelInfo().getExtensions();
            assertEquals("GROSS", extensions.get("ariRateType"));
            assertEquals("RefNetToAmountBeforeTax", extensions.get("reservationRateType"));
            assertEquals("true", extensions.get("noNeedUpc"));
            assertEquals("No", extensions.get("plusChildToAdultCount"));
        }
    }

    @Test
    void testCustomizeProperty_WithConnectionNotFound() {
        // Given
        ChannelHotelDTO channelHotelDTO = createTestChannelHotelDTO();

        try (MockedStatic<RemoteServiceUtil> mockedUtil = mockStatic(RemoteServiceUtil.class)) {
            mockedUtil.when(RemoteServiceUtil::getInstance).thenReturn(remoteService);
            
            when(remoteService.uniqueHotelConnections("AGODA", "hotel-123"))
                    .thenReturn(Optional.empty());

            // When & Then
            RuntimeException exception = assertThrows(RuntimeException.class, 
                    () -> agodaAdapterService.customizeProperty(channelHotelDTO));
            
            assertTrue(exception.getMessage().contains("AGODA-hotel-123 connection not found"));
            verify(remoteService).uniqueHotelConnections("AGODA", "hotel-123");
        }
    }

    @Test
    void testCustomizeProperty_WithDifferentTimezone() {
        // Given
        ChannelHotelDTO channelHotelDTO = createTestChannelHotelDTO();
        channelHotelDTO.getHotelInfo().setTimezone("America/New_York");
        
        Map<String, Object> channelHotelSettings = createMinimalChannelHotelSettings();

        try (MockedStatic<RemoteServiceUtil> mockedUtil = mockStatic(RemoteServiceUtil.class)) {
            mockedUtil.when(RemoteServiceUtil::getInstance).thenReturn(remoteService);
            
            when(remoteService.uniqueHotelConnections("AGODA", "hotel-123"))
                    .thenReturn(Optional.of(channelHotelSettings));
            when(remoteService.getHotelDetails("supplier-hotel-123"))
                    .thenReturn(Optional.empty());

            // When
            agodaAdapterService.customizeProperty(channelHotelDTO);

            // Then
            Map<String, Object> extensions = channelHotelDTO.getHotelInfo().getExtensions();
            String timeZoneID = (String) extensions.get("timeZoneID");
            assertNotNull(timeZoneID);
            assertTrue(timeZoneID.startsWith("GMT"));
        }
    }

    @Test
    void testCustomizeProperty_WithNullHotelDetails() {
        // Given
        ChannelHotelDTO channelHotelDTO = createTestChannelHotelDTO();
        Map<String, Object> channelHotelSettings = createMinimalChannelHotelSettings();

        try (MockedStatic<RemoteServiceUtil> mockedUtil = mockStatic(RemoteServiceUtil.class)) {
            mockedUtil.when(RemoteServiceUtil::getInstance).thenReturn(remoteService);
            
            when(remoteService.uniqueHotelConnections("AGODA", "hotel-123"))
                    .thenReturn(Optional.of(channelHotelSettings));
            when(remoteService.getHotelDetails("supplier-hotel-123"))
                    .thenReturn(Optional.empty());

            // When
            agodaAdapterService.customizeProperty(channelHotelDTO);

            // Then
            Map<String, Object> extensions = channelHotelDTO.getHotelInfo().getExtensions();
            assertEquals("No", extensions.get("plusChildToAdultCount"));
        }
    }

    @Test
    void testDefaultMethods() {
        // Test default interface methods
        assertFalse(agodaAdapterService.saveChannelHotel());
        assertTrue(agodaAdapterService.fillCredentialToHotelExtension());
        
        // Test that customizeCredential, customizeRoomType, customizeRatePlan, doAfterActivation don't throw
        ChannelHotelDTO dto = new ChannelHotelDTO();
        assertDoesNotThrow(() -> agodaAdapterService.customizeCredential(dto));
        assertDoesNotThrow(() -> agodaAdapterService.customizeRoomType(dto));
        assertDoesNotThrow(() -> agodaAdapterService.customizeRatePlan(dto));
        assertDoesNotThrow(() -> agodaAdapterService.doAfterActivation(dto));
    }

    @Test
    void testZoneIdToGMTZone_WithDifferentTimezones() {
        // This tests the private method indirectly through customizeProperty
        String[] timezones = {
                "Asia/Shanghai",
                "Europe/London", 
                "America/New_York",
                "UTC"
        };

        for (String timezone : timezones) {
            // Given
            ChannelHotelDTO channelHotelDTO = createTestChannelHotelDTO();
            channelHotelDTO.getHotelInfo().setTimezone(timezone);
            Map<String, Object> channelHotelSettings = createMinimalChannelHotelSettings();

            try (MockedStatic<RemoteServiceUtil> mockedUtil = mockStatic(RemoteServiceUtil.class)) {
                mockedUtil.when(RemoteServiceUtil::getInstance).thenReturn(remoteService);
                
                when(remoteService.uniqueHotelConnections("AGODA", "hotel-123"))
                        .thenReturn(Optional.of(channelHotelSettings));
                when(remoteService.getHotelDetails("supplier-hotel-123"))
                        .thenReturn(Optional.empty());

                // When
                agodaAdapterService.customizeProperty(channelHotelDTO);

                // Then
                Map<String, Object> extensions = channelHotelDTO.getHotelInfo().getExtensions();
                String timeZoneID = (String) extensions.get("timeZoneID");
                assertNotNull(timeZoneID, "TimeZoneID should not be null for timezone: " + timezone);
                assertTrue(timeZoneID.startsWith("GMT"), "TimeZoneID should start with GMT for timezone: " + timezone);
            }
        }
    }

    private ChannelHotelDTO createTestChannelHotelDTO() {
        ChannelHotelDTO dto = new ChannelHotelDTO();
        dto.setChannelHotelId("hotel-123");
        dto.setSupplierHotelId("supplier-hotel-123");

        ChannelHotelInfo hotelInfo = new ChannelHotelInfo("hotel-123");
        hotelInfo.setTimezone("Asia/Shanghai");
        
        Map<String, Object> settings = new HashMap<>();
        settings.put("apiKey", "test-api-key");
        hotelInfo.setSettings(settings);
        
        Map<String, Object> extensions = new HashMap<>();
        hotelInfo.setExtensions(extensions);
        
        dto.setHotelInfo(hotelInfo);
        return dto;
    }

    private Map<String, Object> createMinimalChannelHotelSettings() {
        Map<String, Object> channelHotelSettings = new HashMap<>();
        Map<String, Object> rateRule = new HashMap<>();
        rateRule.put("channelRateType", "NET");
        rateRule.put("channelResPriceType", "NetPrice");
        rateRule.put("channelResRateType", "RefNetToAmountBeforeTax");
        channelHotelSettings.put("rateRule", rateRule);
        
        Map<String, Object> channelSettings = new HashMap<>();
        channelSettings.put("paymentType", "CREDIT_CARD");
        channelHotelSettings.put("channelSettings", channelSettings);
        
        return channelHotelSettings;
    }
}
