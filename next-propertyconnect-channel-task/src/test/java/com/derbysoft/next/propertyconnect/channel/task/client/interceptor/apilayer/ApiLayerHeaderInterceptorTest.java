package com.derbysoft.next.propertyconnect.channel.task.client.interceptor.apilayer;

import com.derbysoft.next.propertyconnect.channel.task.config.ApiLayerProperties;
import feign.MethodMetadata;
import feign.RequestTemplate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Method;
import java.util.Collection;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ApiLayerHeaderInterceptorTest {

    @Mock
    private ApiLayerProperties apiLayerProperties;

    @Mock
    private RequestTemplate requestTemplate;

    @Mock
    private MethodMetadata methodMetadata;

    private ApiLayerHeaderInterceptor interceptor;

    @BeforeEach
    void setUp() {
        interceptor = new ApiLayerHeaderInterceptor(apiLayerProperties);
    }

    @Test
    void testApply_WithApiLayerRequestAnnotation_ShouldAddAuthorizationHeader() throws NoSuchMethodException {
        // Given
        String token = "test-token-123";
        when(apiLayerProperties.getToken()).thenReturn(token);
        
        Method method = TestService.class.getMethod("methodWithApiLayerRequest");
        when(requestTemplate.methodMetadata()).thenReturn(methodMetadata);
        when(methodMetadata.method()).thenReturn(method);

        // When
        interceptor.apply(requestTemplate);

        // Then
        verify(requestTemplate).header(eq("Authorization"), eq(token));
    }

    @Test
    void testApply_WithoutApiLayerRequestAnnotation_ShouldNotAddAuthorizationHeader() throws NoSuchMethodException {
        // Given
        Method method = TestService.class.getMethod("methodWithoutApiLayerRequest");
        when(requestTemplate.methodMetadata()).thenReturn(methodMetadata);
        when(methodMetadata.method()).thenReturn(method);

        // When
        interceptor.apply(requestTemplate);

        // Then
        verify(requestTemplate, never()).header(eq("Authorization"), any(String.class));
    }

    @Test
    void testApply_WithNullToken_ShouldNotAddAuthorizationHeader() throws NoSuchMethodException {
        // Given
        when(apiLayerProperties.getToken()).thenReturn(null);
        
        Method method = TestService.class.getMethod("methodWithApiLayerRequest");
        when(requestTemplate.methodMetadata()).thenReturn(methodMetadata);
        when(methodMetadata.method()).thenReturn(method);

        // When
        interceptor.apply(requestTemplate);

        // Then
        verify(requestTemplate, never()).header(eq("Authorization"), any(String.class));
    }

    @Test
    void testApply_WithEmptyToken_ShouldNotAddAuthorizationHeader() throws NoSuchMethodException {
        // Given
        when(apiLayerProperties.getToken()).thenReturn("");
        
        Method method = TestService.class.getMethod("methodWithApiLayerRequest");
        when(requestTemplate.methodMetadata()).thenReturn(methodMetadata);
        when(methodMetadata.method()).thenReturn(method);

        // When
        interceptor.apply(requestTemplate);

        // Then
        verify(requestTemplate, never()).header(eq("Authorization"), any(String.class));
    }

    @Test
    void testApply_WithBlankToken_ShouldNotAddAuthorizationHeader() throws NoSuchMethodException {
        // Given
        when(apiLayerProperties.getToken()).thenReturn("   ");
        
        Method method = TestService.class.getMethod("methodWithApiLayerRequest");
        when(requestTemplate.methodMetadata()).thenReturn(methodMetadata);
        when(methodMetadata.method()).thenReturn(method);

        // When
        interceptor.apply(requestTemplate);

        // Then
        verify(requestTemplate, never()).header(eq("Authorization"), any(String.class));
    }

    @Test
    void testApply_WithNullMethodMetadata_ShouldNotAddAuthorizationHeader() {
        // Given
        when(requestTemplate.methodMetadata()).thenReturn(null);

        // When
        interceptor.apply(requestTemplate);

        // Then
        verify(requestTemplate, never()).header(eq("Authorization"), any(String.class));
    }

    @Test
    void testApply_WithNullMethod_ShouldNotAddAuthorizationHeader() {
        // Given
        when(requestTemplate.methodMetadata()).thenReturn(methodMetadata);
        when(methodMetadata.method()).thenReturn(null);

        // When
        interceptor.apply(requestTemplate);

        // Then
        verify(requestTemplate, never()).header(eq("Authorization"), any(String.class));
    }

    @Test
    void testApply_WithValidTokenAndAnnotation_ShouldAddCorrectHeader() throws NoSuchMethodException {
        // Given
        String expectedToken = "Bearer abc123xyz";
        when(apiLayerProperties.getToken()).thenReturn(expectedToken);
        
        Method method = TestService.class.getMethod("methodWithApiLayerRequest");
        when(requestTemplate.methodMetadata()).thenReturn(methodMetadata);
        when(methodMetadata.method()).thenReturn(method);

        // When
        interceptor.apply(requestTemplate);

        // Then
        verify(requestTemplate).header(eq("Authorization"), eq(expectedToken));
        verify(apiLayerProperties).getToken();
    }

    @Test
    void testApply_MultipleCallsWithSameToken_ShouldAddHeaderEachTime() throws NoSuchMethodException {
        // Given
        String token = "consistent-token";
        when(apiLayerProperties.getToken()).thenReturn(token);
        
        Method method = TestService.class.getMethod("methodWithApiLayerRequest");
        when(requestTemplate.methodMetadata()).thenReturn(methodMetadata);
        when(methodMetadata.method()).thenReturn(method);

        // When
        interceptor.apply(requestTemplate);
        interceptor.apply(requestTemplate);

        // Then
        verify(requestTemplate, times(2)).header(eq("Authorization"), eq(token));
    }

    @Test
    void testConstructor_WithApiLayerProperties() {
        // Given
        ApiLayerProperties properties = new ApiLayerProperties();
        
        // When
        ApiLayerHeaderInterceptor newInterceptor = new ApiLayerHeaderInterceptor(properties);

        // Then
        assertNotNull(newInterceptor);
    }

    // Test service interface for annotation testing
    private interface TestService {
        @ApiLayerRequest
        void methodWithApiLayerRequest();
        
        void methodWithoutApiLayerRequest();
    }
}
