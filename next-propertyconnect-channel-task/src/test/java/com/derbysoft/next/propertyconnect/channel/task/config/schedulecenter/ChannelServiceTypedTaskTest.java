package com.derbysoft.next.propertyconnect.channel.task.config.schedulecenter;

import com.derbysoft.schedulecenter.rpc.protocol.Task;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

/**
 * Unit tests for ChannelServiceTypedTask interface
 */
@ExtendWith(MockitoExtension.class)
class ChannelServiceTypedTaskTest {

    @Mock
    private Task mockTask;

    private TestChannelServiceTypedTask testTask;

    @BeforeEach
    void setUp() {
        testTask = new TestChannelServiceTypedTask();
    }

    private void setupMockTask() {
        // Setup mock task parameters
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("id", "test-task-123");
        parameters.put("channel", "BOOKINGCOM");
        parameters.put("supplier", "PROPERTYCONNECT");
        parameters.put("hotels", Arrays.asList("hotel1", "hotel2"));

        when(mockTask.getParametersMap()).thenReturn(parameters);
        when(mockTask.getId()).thenReturn("test-task-123");  // Mock the id directly
    }

    @Test
    void testTaskTypeEnum() {
        assertEquals("PROPERTYCONNECT_CHANNEL.CHANNEL_APT_ARIREFRESH_TASK.SINGLE", 
                ChannelServiceTypedTask.TaskType.ChannelAptARIRefresh.getNextHandler());
        
        assertNull(ChannelServiceTypedTask.TaskType.ChannelAptExceptionListRemove.getNextHandler());
        
        assertEquals("PROPERTYCONNECT_CHANNEL.CHANNEL_APT_HOTEL_SETUP_SCHEDULE.SINGLE", 
                ChannelServiceTypedTask.TaskType.ChannelAptHotelSetup.getNextHandler());
    }

    @Test
    void testTaskTypeValues() {
        ChannelServiceTypedTask.TaskType[] values = ChannelServiceTypedTask.TaskType.values();
        
        assertEquals(3, values.length);
        assertEquals(ChannelServiceTypedTask.TaskType.ChannelAptARIRefresh, values[0]);
        assertEquals(ChannelServiceTypedTask.TaskType.ChannelAptExceptionListRemove, values[1]);
        assertEquals(ChannelServiceTypedTask.TaskType.ChannelAptHotelSetup, values[2]);
    }

    @Test
    void testDefaultDoExecuteMethods() {
        // Test default implementations don't throw exceptions
        assertDoesNotThrow(() -> testTask.doExecute(mockTask));
        
        ChannelServiceTask channelTask = ChannelServiceTask.builder().id("test").build();
        assertDoesNotThrow(() -> testTask.doExecute(channelTask));
    }

    @Test
    void testDefaultNextTask() {
        ChannelServiceTask channelTask = ChannelServiceTask.builder().id("test").build();
        List<ChannelServiceTask> result = testTask.nextTask(channelTask);
        
        assertNull(result);
    }

    @Test
    void testTaskTypeFromTask() {
        setupMockTask();

        ChannelServiceTask channelTask = ChannelServiceTask.fromTask(mockTask);

        assertNotNull(channelTask);
        assertEquals("test-task-123", channelTask.getId());
        assertEquals("BOOKINGCOM", channelTask.getChannel());
        assertEquals("PROPERTYCONNECT", channelTask.getSupplier());
    }

    // Test implementation of ChannelServiceTypedTask for testing purposes
    private static class TestChannelServiceTypedTask implements ChannelServiceTypedTask {
        private ChannelServiceTypedTask.TaskType taskType = ChannelServiceTypedTask.TaskType.ChannelAptARIRefresh;

        @Override
        public TaskType taskType() {
            return taskType;
        }

        @Override
        public void doExecute(ChannelServiceTask task) {
            // Test implementation - do nothing
        }

        @Override
        public List<ChannelServiceTask> nextTask(ChannelServiceTask task) {
            return null;
        }
    }
}
