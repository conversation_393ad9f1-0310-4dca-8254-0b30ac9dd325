package com.derbysoft.next.propertyconnect.channel.task.service.storageservice;

import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelHotelDTO;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelRateInfo;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ItemStatus;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.SyncStatus;
import com.derbysoft.next.propertyconnect.channel.task.domain.entity.ChannelHotelRatePO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ChannelRateStorageServiceTest {

    @Mock
    private MongoTemplate mongoTemplate;



    private ChannelRateStorageService channelRateStorageService;

    @BeforeEach
    void setUp() {
        channelRateStorageService = new ChannelRateStorageService(mongoTemplate);
    }

    @Test
    void testSaveIncrementalChannelProduct_WithNullRatesInfo() {
        // Given
        ChannelHotelDTO channelHotel = new ChannelHotelDTO();
        channelHotel.setChannelId("test-channel");
        channelHotel.setChannelHotelId("test-hotel");
        channelHotel.setRatesInfo(null);

        // When
        ChannelHotelDTO result = channelRateStorageService.saveIncrementalChannelProduct(channelHotel);

        // Then
        assertNotNull(result);
        assertEquals(channelHotel, result);
        verifyNoInteractions(mongoTemplate);
    }

    @Test
    void testSaveIncrementalChannelProduct_WithEmptyRatesInfo() {
        // Given
        ChannelHotelDTO channelHotel = new ChannelHotelDTO();
        channelHotel.setChannelId("test-channel");
        channelHotel.setChannelHotelId("test-hotel");
        channelHotel.setRatesInfo(Collections.emptyList());

        // When
        ChannelHotelDTO result = channelRateStorageService.saveIncrementalChannelProduct(channelHotel);

        // Then
        assertNotNull(result);
        assertEquals(channelHotel, result);
        verifyNoInteractions(mongoTemplate);
    }

    @Test
    void testSaveIncrementalChannelProduct_WithRatesInfo() {
        // Given
        ChannelHotelDTO channelHotel = createTestChannelHotelDTO();
        ChannelRateInfo rateInfo1 = createTestRateInfo("rate1", "Rate 1");
        ChannelRateInfo rateInfo2 = createTestRateInfo("rate2", "Rate 2");
        channelHotel.setRatesInfo(Arrays.asList(rateInfo1, rateInfo2));

        // Mock MongoDB operations
        ChannelHotelRatePO existingPO1 = createTestRatePO("rate1");
        ChannelHotelRatePO existingPO2 = null; // Second rate doesn't exist

        when(mongoTemplate.findOne(any(Query.class), eq(ChannelHotelRatePO.class)))
                .thenReturn(existingPO1, existingPO2);

        ChannelHotelRatePO savedPO1 = createTestRatePO("rate1");
        ChannelHotelRatePO savedPO2 = createTestRatePO("rate2");
        when(mongoTemplate.save(any(ChannelHotelRatePO.class)))
                .thenReturn(savedPO1, savedPO2);

        // When
        ChannelHotelDTO result = channelRateStorageService.saveIncrementalChannelProduct(channelHotel);

        // Then
        assertNotNull(result);
        assertEquals(2, result.getRatesInfo().size());
        verify(mongoTemplate, times(2)).findOne(any(Query.class), eq(ChannelHotelRatePO.class));
        verify(mongoTemplate, times(2)).save(any(ChannelHotelRatePO.class));
    }

    @Test
    void testGetChannelHotel_WithExistingRates() {
        // Given
        String channelId = "test-channel";
        String channelHotelId = "test-hotel";

        ChannelHotelRatePO ratePO1 = createTestRatePO("rate1");
        ChannelHotelRatePO ratePO2 = createTestRatePO("rate2");
        List<ChannelHotelRatePO> ratePOs = Arrays.asList(ratePO1, ratePO2);

        when(mongoTemplate.find(any(Query.class), eq(ChannelHotelRatePO.class)))
                .thenReturn(ratePOs);

        // When
        ChannelHotelDTO result = channelRateStorageService.getChannelHotel(channelId, channelHotelId);

        // Then
        assertNotNull(result);
        assertNotNull(result.getRatesInfo());
        assertEquals(2, result.getRatesInfo().size());
        assertNull(result.getRateInfo()); // Should be null after processing
        verify(mongoTemplate).find(any(Query.class), eq(ChannelHotelRatePO.class));
    }

    @Test
    void testGetChannelHotel_WithNoRates() {
        // Given
        String channelId = "test-channel";
        String channelHotelId = "test-hotel";

        when(mongoTemplate.find(any(Query.class), eq(ChannelHotelRatePO.class)))
                .thenReturn(Collections.emptyList());

        // When
        ChannelHotelDTO result = channelRateStorageService.getChannelHotel(channelId, channelHotelId);

        // Then
        assertNull(result);
        verify(mongoTemplate).find(any(Query.class), eq(ChannelHotelRatePO.class));
    }

    @Test
    void testDeleteChannelHotel() {
        // Given
        String channelId = "test-channel";
        String channelHotelId = "test-hotel";

        // When
        channelRateStorageService.deleteChannelHotel(channelId, channelHotelId);

        // Then
        verify(mongoTemplate).remove(any(Query.class), eq(ChannelHotelRatePO.class));
    }

    private ChannelHotelDTO createTestChannelHotelDTO() {
        ChannelHotelDTO dto = new ChannelHotelDTO();
        dto.setChannelId("test-channel");
        dto.setChannelHotelId("test-hotel");
        dto.setSupplierId("test-supplier");
        return dto;
    }

    private ChannelRateInfo createTestRateInfo(String code, String name) {
        ChannelRateInfo rateInfo = new ChannelRateInfo(code);
        rateInfo.setName(name);
        rateInfo.setStatus(ItemStatus.Actived);
        rateInfo.setSyncStatus(SyncStatus.SYNCED);
        return rateInfo;
    }

    private ChannelHotelRatePO createTestRatePO(String code) {
        return ChannelHotelRatePO.builder()
                .id("test-id")
                .channelId("test-channel")
                .channelHotelId("test-hotel")
                .code(code)
                .status(ItemStatus.Actived)
                .syncStatus(SyncStatus.SYNCED)
                .rateInfo(new java.util.HashMap<>(Map.of("code", code, "name", "Test Rate")))
                .build();
    }
}
