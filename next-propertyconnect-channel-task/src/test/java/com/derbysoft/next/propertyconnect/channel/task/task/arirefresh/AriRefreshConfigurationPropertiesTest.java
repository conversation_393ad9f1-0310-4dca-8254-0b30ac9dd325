package com.derbysoft.next.propertyconnect.channel.task.task.arirefresh;


import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

public class AriRefreshConfigurationPropertiesTest {

    @InjectMocks
    private AriRefreshConfigurationProperties ariRefreshConfigurationProperties;

    @Mock
    private Map<String, AriRefreshConfigurationProperties> config;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.initMocks(this);

        // Set up some example data for config map
        AriRefreshConfigurationProperties defaultProperties = new AriRefreshConfigurationProperties();
        defaultProperties.setHotelBatchSize(2);
        defaultProperties.setMaxRefreshDays(180);
        defaultProperties.setMaxSplitSpan(15);
        defaultProperties.setDaysBefore("2");
        defaultProperties.setTaskBatch(2);
        defaultProperties.setTaskTimeGap(3000L);
        defaultProperties.setDelayExecutionTime(180000L);

        // Mock the behavior of config map
        when(config.getOrDefault("channelId1", ariRefreshConfigurationProperties)).thenReturn(defaultProperties);
        when(config.getOrDefault("channelId2", ariRefreshConfigurationProperties)).thenReturn(defaultProperties);
        when(config.getOrDefault("channelId3", ariRefreshConfigurationProperties)).thenReturn(ariRefreshConfigurationProperties);
    }

    @Test
    public void testGetHotelBatchSize() {
        // Test for channelId1
        Integer result1 = ariRefreshConfigurationProperties.getHotelBatchSize("channelId1");
        assertEquals(Integer.valueOf(2), result1);

        // Test for channelId3 (non-existent channelId)
        Integer result3 = ariRefreshConfigurationProperties.getHotelBatchSize("channelId3");
        assertEquals(ariRefreshConfigurationProperties.getHotelBatchSize(), result3);
    }

    @Test
    public void testGetMaxRefreshDays() {
        // Test for channelId1
        Integer result1 = ariRefreshConfigurationProperties.getMaxRefreshDays("channelId1");
        assertEquals(Integer.valueOf(180), result1);

        // Test for channelId3 (non-existent channelId)
        Integer result3 = ariRefreshConfigurationProperties.getMaxRefreshDays("channelId3");
        assertEquals(ariRefreshConfigurationProperties.getMaxRefreshDays(), result3);
    }

    @Test
    public void testGetMaxSplitSpan() {
        // Test for channelId1
        Integer result1 = ariRefreshConfigurationProperties.getMaxSplitSpan("channelId1");
        assertEquals(Integer.valueOf(15), result1);

        // Test for channelId3 (non-existent channelId)
        Integer result3 = ariRefreshConfigurationProperties.getMaxSplitSpan("channelId3");
        assertEquals(ariRefreshConfigurationProperties.getMaxSplitSpan(), result3);
    }

    @Test
    public void testGetDaysBefore() {
        // Test for channelId1
        String result1 = ariRefreshConfigurationProperties.getDaysBefore("channelId1");
        assertEquals("2", result1);

        // Test for channelId3 (non-existent channelId)
        String result3 = ariRefreshConfigurationProperties.getDaysBefore("channelId3");
        assertEquals(ariRefreshConfigurationProperties.getDaysBefore(), result3);
    }

    @Test
    public void testGetTaskBatch() {
        // Test for channelId1
        Integer result1 = ariRefreshConfigurationProperties.getTaskBatch("channelId1");
        assertEquals(Integer.valueOf(2), result1);

        // Test for channelId3 (non-existent channelId)
        Integer result3 = ariRefreshConfigurationProperties.getTaskBatch("channelId3");
        assertEquals(ariRefreshConfigurationProperties.getTaskBatch(), result3);
    }

    @Test
    public void testGetTaskTimeGap() {
        // Test for channelId1
        Long result1 = ariRefreshConfigurationProperties.getTaskTimeGap("channelId1");
        assertEquals(Long.valueOf(3000L), result1);

        // Test for channelId3 (non-existent channelId)
        Long result3 = ariRefreshConfigurationProperties.getTaskTimeGap("channelId3");
        assertEquals(ariRefreshConfigurationProperties.getTaskTimeGap(), result3);
    }

    @Test
    public void testGetDelayExecutionTime() {
        // Test for channelId1
        Long result1 = ariRefreshConfigurationProperties.getDelayExecutionTime("channelId1");
        assertEquals(Long.valueOf(180000L), result1);

        // Test for channelId3 (non-existent channelId)
        Long result3 = ariRefreshConfigurationProperties.getDelayExecutionTime("channelId3");
        assertEquals(ariRefreshConfigurationProperties.getDelayExecutionTime(), result3);
    }
}
