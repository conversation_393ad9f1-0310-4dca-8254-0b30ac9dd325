package com.derbysoft.next.propertyconnect.channel.task.service.impl;

import com.alibaba.fastjson.JSON;
import com.derbysoft.next.propertyconnect.channel.task.client.WebhookAPI;
import com.derbysoft.next.propertyconnect.channel.task.client.connectivityoperationapi.ConnectivityOperationRequestTranslator;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelHotelDTO;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelHotelInfo;
import com.derbysoft.next.propertyconnect.channel.task.service.RemoteService;
import com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.adapter.ChannelHotelActivationCustomizeService;
import com.derbysoft.next.propertyconnect.channel.task.service.impl.airbnb.AirbnbAdapterActivationService;
import com.derbysoft.next.propertyconnect.channel.task.service.impl.airbnb.AirbnbProductService;
import com.derbysoft.next.propertyconnect.channel.task.service.storageservice.ChannelInfoStorageService;
import com.derbysoft.next.propertyconnect.channel.task.task.arirefresh.ARIRefreshService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mapstruct.factory.Mappers;

import java.util.HashMap;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.lenient;

@ExtendWith(MockitoExtension.class)
class AirbnbAdapterActivationServiceTest {

    @Mock
    private RemoteService remoteService;

    private final ConnectivityOperationRequestTranslator translator = Mappers.getMapper(ConnectivityOperationRequestTranslator.class);

    @Mock
    private ARIRefreshService ariRefreshService;

    @Mock
    private ChannelInfoStorageService channelInfoStorageService;

    @Mock
    private AirbnbProductService airbnbProductService;

    @Mock
    private List<ChannelHotelActivationCustomizeService> channelHotelActivationCustomizeServices;

    @Mock
    private WebhookAPI webhookAPI;

    @Mock
    private List<ChannelHotelActivationCustomizeService> activationCustomizers;

    @Test
    void channel() {
        AirbnbAdapterActivationService activationService = new AirbnbAdapterActivationService(remoteService, ariRefreshService, channelInfoStorageService, translator, activationCustomizers);
        assertEquals("AIRBNB", activationService.channel());
    }

    @Test
    void propertyActivationValidAriModel() {
        lenient().when(remoteService.deactivateProperty(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(Optional.of(JSON.parse("{\"responseHeader\":{\"Success\":\"true\"}}")));


        AirbnbAdapterActivationService activationService = new AirbnbAdapterActivationService(remoteService, ariRefreshService, channelInfoStorageService, translator, activationCustomizers);

        var channelHotelDTO = new ChannelHotelDTO();
        channelHotelDTO.setHotelInfo(new ChannelHotelInfo());

        channelHotelDTO.getHotelInfo().setExtensions(null);
        activationService.propertyActivation(channelHotelDTO);
        assertEquals("RATE_PLAN", channelHotelDTO.getHotelInfo().getExtensions().get("ariModel"));

        channelHotelDTO.getHotelInfo().setExtensions(new HashMap<>());

        channelHotelDTO.getHotelInfo().getExtensions().put("ariModel", "RATEPLAN");
        activationService.propertyActivation(channelHotelDTO);
        assertEquals("RATE_PLAN", channelHotelDTO.getHotelInfo().getExtensions().get("ariModel"));

        channelHotelDTO.getHotelInfo().getExtensions().put("ariModel", "ratePlan");
        activationService.propertyActivation(channelHotelDTO);
        assertEquals("RATE_PLAN", channelHotelDTO.getHotelInfo().getExtensions().get("ariModel"));

        channelHotelDTO.getHotelInfo().getExtensions().put("ariModel", "LOS");
        activationService.propertyActivation(channelHotelDTO);
        assertEquals("LOS_RECORD", channelHotelDTO.getHotelInfo().getExtensions().get("ariModel"));

        channelHotelDTO.getHotelInfo().getExtensions().put("ariModel", "STANDARD");
        activationService.propertyActivation(channelHotelDTO);
        assertEquals("STANDARD", channelHotelDTO.getHotelInfo().getExtensions().get("ariModel"));

        channelHotelDTO.getHotelInfo().getExtensions().put("ariModel", "");
        activationService.propertyActivation(channelHotelDTO);
        assertEquals("RATE_PLAN", channelHotelDTO.getHotelInfo().getExtensions().get("ariModel"));

        channelHotelDTO.getHotelInfo().getExtensions().put("ariModel", null);
        activationService.propertyActivation(channelHotelDTO);
        assertEquals("RATE_PLAN", channelHotelDTO.getHotelInfo().getExtensions().get("ariModel"));

    }

}
