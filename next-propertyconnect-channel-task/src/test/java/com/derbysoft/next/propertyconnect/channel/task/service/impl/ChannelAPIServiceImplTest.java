package com.derbysoft.next.propertyconnect.channel.task.service.impl;

import com.derbysoft.next.propertyconnect.channel.task.controller.vo.ChannelVO;
import com.derbysoft.next.propertyconnect.channel.task.domain.entity.ChannelPO;
import com.derbysoft.next.propertyconnect.channel.task.domain.repository.ChannelRepository;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ChannelAPIServiceImplTest {

    @InjectMocks
    private ChannelAPIServiceImpl channelAPIService;

    @Mock
    private ChannelRepository channelRepository;

    @Spy
    private final ChannelAPIServiceImpl.ChannelTranslator channelTranslator = new ChannelAPIServiceImpl.ChannelTranslator() {
        @Override
        public ChannelVO map(ChannelPO channelPO) {
            return new ChannelVO();
        }

        @Override
        public ChannelPO reverseMap(ChannelVO channelVO) {
            return new ChannelPO();
        }

        @Override
        public java.util.List<ChannelVO> map(java.util.List<ChannelPO> list) {
            return null;
        }

        @Override
        public java.util.List<ChannelPO> reverseMap(java.util.List<ChannelVO> list) {
            return null;
        }

        @Override
        public void fillIn(ChannelPO from, ChannelVO to) {

        }

        @Override
        public void reverseFill(ChannelVO to, ChannelPO from) {

        }
    };

    @Test
    void givenExistingChannel_whenGetChannelSettings_thenReturnsVO() {
        when(channelRepository.findByChannelId(any())).thenReturn(Optional.of(new ChannelPO()));

        ChannelVO result = channelAPIService.getChannelSettings("channel1");

        assertNotNull(result);
    }

    @Test
    void givenNonExistingChannel_whenGetChannelSettings_thenThrowsException() {
        when(channelRepository.findByChannelId(any())).thenReturn(Optional.empty());

        assertThrows(RuntimeException.class, () -> channelAPIService.getChannelSettings("channel1"));
    }

    @Test
    void givenNewChannel_whenSaveChannelSettings_thenSavesAndReturnsVO() {
        ChannelVO channelVO = new ChannelVO();
        channelVO.setChannelId("newChannel");

        when(channelRepository.findByChannelId(any())).thenReturn(Optional.empty());
        when(channelRepository.save(any())).thenReturn(new ChannelPO());

        ChannelVO result = channelAPIService.saveChannelSettings(channelVO);

        assertNotNull(result);
    }

    @Test
    void givenExistingChannel_whenSaveChannelSettings_thenUpdatesAndReturnsVO() {
        ChannelVO channelVO = new ChannelVO();
        channelVO.setChannelId("existingChannel");

        when(channelRepository.findByChannelId(any())).thenReturn(Optional.of(new ChannelPO()));
        when(channelRepository.save(any())).thenReturn(new ChannelPO());

        ChannelVO result = channelAPIService.saveChannelSettings(channelVO);

        assertNotNull(result);
    }
}