package com.derbysoft.next.propertyconnect.channel.task.config;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

class HotelSetupConnectionWhiteListConfigTest {

    private HotelSetupConnectionWhiteListConfig hotelSetupConnectionWhiteListConfigUnderTest;

    @BeforeEach
    void setUp() {
        hotelSetupConnectionWhiteListConfigUnderTest = new HotelSetupConnectionWhiteListConfig();
    }

    @Test
    void testIsProhibitedConnection() {
        // Verify the results
        assertFalse(hotelSetupConnectionWhiteListConfigUnderTest.isProhibitedConnection("PROPERTYCONNECT",
                "EXPEDIA"));

        assertFalse(hotelSetupConnectionWhiteListConfigUnderTest.isProhibitedConnection("OPERACONNECTOR",
                "BOOKINGCOM"));

        assertFalse(hotelSetupConnectionWhiteListConfigUnderTest.isProhibitedConnection("CAMPSPOT",
                "AIRBNB"));

        assertTrue(hotelSetupConnectionWhiteListConfigUnderTest.isProhibitedConnection("CAMPSPOT",
                "BOOKINGCOM"));

        assertTrue(hotelSetupConnectionWhiteListConfigUnderTest.isProhibitedConnection("CAMPSOT",
                "AIRBNB"));

        assertTrue(hotelSetupConnectionWhiteListConfigUnderTest.isProhibitedConnection("CAMPSOT",
                "BOOKINGCOM"));
    }

    @Test
    void testSetAllowedSuppliers() {
        // Setup
        // Run the test
        hotelSetupConnectionWhiteListConfigUnderTest.setAllowedConnections(List.of("TESTSUPPLIER-BOOKINGCOM"));

        // Verify the results
    }
}
