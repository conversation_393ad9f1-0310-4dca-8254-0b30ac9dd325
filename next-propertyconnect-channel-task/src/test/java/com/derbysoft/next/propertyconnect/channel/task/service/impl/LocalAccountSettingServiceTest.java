package com.derbysoft.next.propertyconnect.channel.task.service.impl;

import com.derbysoft.next.propertyconnect.channel.task.domain.entity.ChannelHotelPO;
import com.derbysoft.next.propertyconnect.channel.task.domain.repository.ChannelHotelRepository;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class LocalAccountSettingServiceTest {

    @InjectMocks
    private LocalAccountSettingService localAccountSettingService;

    @Mock
    private ChannelHotelRepository channelHotelRepository;

    @Test
    void givenExistingHotel_whenGetAccountSettings_thenReturnsSettings() {
        String channelId = "channel1";
        String channelHotelId = "hotel1";
        Map<String, Object> accountSettings = Collections.singletonMap("key", "value");

        ChannelHotelPO hotelPO = new ChannelHotelPO();
        hotelPO.setAccountSettings(accountSettings);

        when(channelHotelRepository.findFirstByChannelIdAndChannelHotelId(channelId, channelHotelId))
                .thenReturn(hotelPO);

        Map<String, Object> result = localAccountSettingService.getAccountSettings(channelId, channelHotelId);

        assertNotNull(result);
        assertEquals(accountSettings, result);
    }

    @Test
    void givenNewHotel_whenSaveOrUpdateAccountSettings_thenSavesSettings() {
        String channelId = "channel1";
        String channelHotelId = "hotel1";
        Map<String, Object> accountSettings = Collections.singletonMap("key", "value");

        when(channelHotelRepository.findFirstByChannelIdAndChannelHotelId(channelId, channelHotelId))
                .thenReturn(null);

        ChannelHotelPO savedHotel = new ChannelHotelPO();
        savedHotel.setAccountSettings(accountSettings);

        when(channelHotelRepository.save(any(ChannelHotelPO.class))).thenReturn(savedHotel);

        Map<String, Object> result = localAccountSettingService.saveOrUpdateAccountSettings(channelId, channelHotelId, accountSettings);

        assertNotNull(result);
        assertEquals(accountSettings, result);
        verify(channelHotelRepository).save(any(ChannelHotelPO.class));
    }

    @Test
    void givenExistingHotel_whenSaveOrUpdateAccountSettings_thenUpdatesSettings() {
        String channelId = "channel1";
        String channelHotelId = "hotel1";
        Map<String, Object> accountSettings = Collections.singletonMap("key", "value");

        ChannelHotelPO existingHotel = new ChannelHotelPO();
        when(channelHotelRepository.findFirstByChannelIdAndChannelHotelId(channelId, channelHotelId))
                .thenReturn(existingHotel);

        ChannelHotelPO savedHotel = new ChannelHotelPO();
        savedHotel.setAccountSettings(accountSettings);

        when(channelHotelRepository.save(any(ChannelHotelPO.class))).thenReturn(savedHotel);

        Map<String, Object> result = localAccountSettingService.saveOrUpdateAccountSettings(channelId, channelHotelId, accountSettings);

        assertNotNull(result);
        assertEquals(accountSettings, result);
        verify(channelHotelRepository).save(existingHotel);
    }

    @Test
    void givenExistingHotel_whenDeleteAccountSettings_thenSetsSettingsToNull() {
        String channelId = "channel1";
        String channelHotelId = "hotel1";

        ChannelHotelPO existingHotel = new ChannelHotelPO();
        when(channelHotelRepository.findFirstByChannelIdAndChannelHotelId(channelId, channelHotelId))
                .thenReturn(existingHotel);

        ChannelHotelPO savedHotel = new ChannelHotelPO();
        savedHotel.setAccountSettings(null);

        when(channelHotelRepository.save(any(ChannelHotelPO.class))).thenReturn(savedHotel);

        localAccountSettingService.deleteAccountSettings(channelId, channelHotelId);

        verify(channelHotelRepository).save(existingHotel);
    }
}