package com.derbysoft.next.propertyconnect.channel.task.config;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Comprehensive unit tests for EnhancedObjectMapper
 * Tests custom ObjectMapper configuration with NON_EMPTY inclusion and unknown property handling
 */
@ExtendWith(MockitoExtension.class)
class EnhancedObjectMapperTest {

    private EnhancedObjectMapper enhancedObjectMapper;

    @BeforeEach
    void setUp() {
        enhancedObjectMapper = new EnhancedObjectMapper();
    }

    @Test
    void testEnhancedObjectMapper_ExtendsObjectMapper() {
        // Then
        assertTrue(enhancedObjectMapper instanceof ObjectMapper);
    }

    @Test
    void testEnhancedObjectMapper_HasNonEmptySerializationInclusion() {
        // When
        JsonInclude.Include inclusion = enhancedObjectMapper.getSerializationConfig()
            .getDefaultPropertyInclusion().getValueInclusion();

        // Then
        assertEquals(JsonInclude.Include.NON_EMPTY, inclusion);
    }

    @Test
    void testEnhancedObjectMapper_IgnoresUnknownProperties() {
        // When
        boolean failOnUnknownProperties = enhancedObjectMapper.getDeserializationConfig()
            .isEnabled(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);

        // Then
        assertFalse(failOnUnknownProperties);
    }

    @Test
    void testSerialization_ExcludesNullValues() throws JsonProcessingException {
        // Given
        TestObject testObj = new TestObject();
        testObj.name = "test";
        testObj.nullValue = null;

        // When
        String json = enhancedObjectMapper.writeValueAsString(testObj);

        // Then
        assertNotNull(json);
        assertTrue(json.contains("name"));
        assertFalse(json.contains("nullValue"));
    }

    @Test
    void testSerialization_ExcludesEmptyStrings() throws JsonProcessingException {
        // Given
        TestObject testObj = new TestObject();
        testObj.name = "test";
        testObj.emptyString = "";

        // When
        String json = enhancedObjectMapper.writeValueAsString(testObj);

        // Then
        assertNotNull(json);
        assertTrue(json.contains("name"));
        assertFalse(json.contains("emptyString"));
    }

    @Test
    void testSerialization_ExcludesEmptyCollections() throws JsonProcessingException {
        // Given
        TestObject testObj = new TestObject();
        testObj.name = "test";
        testObj.emptyList = new ArrayList<>();
        testObj.emptyMap = new HashMap<>();

        // When
        String json = enhancedObjectMapper.writeValueAsString(testObj);

        // Then
        assertNotNull(json);
        assertTrue(json.contains("name"));
        assertFalse(json.contains("emptyList"));
        assertFalse(json.contains("emptyMap"));
    }

    @Test
    void testSerialization_IncludesNonEmptyValues() throws JsonProcessingException {
        // Given
        TestObject testObj = new TestObject();
        testObj.name = "test";
        testObj.nonEmptyString = "value";
        testObj.nonEmptyList = Arrays.asList("item1", "item2");
        testObj.nonEmptyMap = Map.of("key", "value");

        // When
        String json = enhancedObjectMapper.writeValueAsString(testObj);

        // Then
        assertNotNull(json);
        assertTrue(json.contains("name"));
        assertTrue(json.contains("nonEmptyString"));
        assertTrue(json.contains("nonEmptyList"));
        assertTrue(json.contains("nonEmptyMap"));
    }

    @Test
    void testDeserialization_IgnoresUnknownProperties() throws JsonProcessingException {
        // Given
        String jsonWithUnknownProperty = "{\"name\":\"test\",\"unknownProperty\":\"value\",\"anotherUnknown\":123}";

        // When & Then
        assertDoesNotThrow(() -> {
            TestObject result = enhancedObjectMapper.readValue(jsonWithUnknownProperty, TestObject.class);
            assertNotNull(result);
            assertEquals("test", result.name);
        });
    }

    @Test
    void testDeserialization_HandlesPartialJson() throws JsonProcessingException {
        // Given
        String partialJson = "{\"name\":\"test\"}";

        // When
        TestObject result = enhancedObjectMapper.readValue(partialJson, TestObject.class);

        // Then
        assertNotNull(result);
        assertEquals("test", result.name);
        assertNull(result.nullValue);
        assertNull(result.emptyString);
        assertNull(result.emptyList);
        assertNull(result.emptyMap);
    }

    @Test
    void testSerialization_HandlesNestedObjects() throws JsonProcessingException {
        // Given
        NestedTestObject nested = new NestedTestObject();
        nested.innerName = "inner";
        nested.innerEmptyString = "";
        nested.innerNullValue = null;

        TestObjectWithNested testObj = new TestObjectWithNested();
        testObj.name = "outer";
        testObj.nested = nested;

        // When
        String json = enhancedObjectMapper.writeValueAsString(testObj);

        // Then
        assertNotNull(json);
        assertTrue(json.contains("name"));
        assertTrue(json.contains("nested"));
        assertTrue(json.contains("innerName"));
        assertFalse(json.contains("innerEmptyString"));
        assertFalse(json.contains("innerNullValue"));
    }

    @Test
    void testSerialization_HandlesComplexCollections() throws JsonProcessingException {
        // Given
        List<TestObject> objectList = new ArrayList<>();
        TestObject obj1 = new TestObject();
        obj1.name = "obj1";
        obj1.emptyString = "";
        objectList.add(obj1);

        TestObject obj2 = new TestObject();
        obj2.name = "obj2";
        obj2.nonEmptyString = "value2";
        objectList.add(obj2);

        TestObjectWithCollections testObj = new TestObjectWithCollections();
        testObj.name = "collection_test";
        testObj.objectList = objectList;

        // When
        String json = enhancedObjectMapper.writeValueAsString(testObj);

        // Then
        assertNotNull(json);
        assertTrue(json.contains("collection_test"));
        assertTrue(json.contains("objectList"));
        assertTrue(json.contains("obj1"));
        assertTrue(json.contains("obj2"));
        assertTrue(json.contains("value2"));
        assertFalse(json.contains("emptyString"));
    }

    @Test
    void testDeserialization_HandlesNestedObjects() throws JsonProcessingException {
        // Given
        String json = "{\"name\":\"outer\",\"nested\":{\"innerName\":\"inner\"}}";

        // When
        TestObjectWithNested result = enhancedObjectMapper.readValue(json, TestObjectWithNested.class);

        // Then
        assertNotNull(result);
        assertEquals("outer", result.name);
        assertNotNull(result.nested);
        assertEquals("inner", result.nested.innerName);
    }

    @Test
    void testSerialization_HandlesArraysAndMaps() throws JsonProcessingException {
        // Given
        TestObjectWithArraysAndMaps testObj = new TestObjectWithArraysAndMaps();
        testObj.name = "test";
        testObj.stringArray = new String[]{"item1", "item2"};
        testObj.emptyArray = new String[0];
        testObj.stringMap = Map.of("key1", "value1", "key2", "value2");
        testObj.emptyMap = new HashMap<>();

        // When
        String json = enhancedObjectMapper.writeValueAsString(testObj);

        // Then
        assertNotNull(json);
        assertTrue(json.contains("name"));
        assertTrue(json.contains("stringArray"));
        assertTrue(json.contains("stringMap"));
        assertFalse(json.contains("emptyArray"));
        assertFalse(json.contains("emptyMap"));
    }

    @Test
    void testSerialization_HandlesNumericValues() throws JsonProcessingException {
        // Given
        TestObjectWithNumbers testObj = new TestObjectWithNumbers();
        testObj.name = "test";
        testObj.zeroInt = 0;
        testObj.positiveInt = 42;
        testObj.zeroDouble = 0.0;
        testObj.positiveDouble = 3.14;

        // When
        String json = enhancedObjectMapper.writeValueAsString(testObj);

        // Then
        assertNotNull(json);
        assertTrue(json.contains("name"));
        assertTrue(json.contains("positiveInt"));
        assertTrue(json.contains("positiveDouble"));
        // Zero values should be included as they are not considered "empty"
        assertTrue(json.contains("zeroInt"));
        assertTrue(json.contains("zeroDouble"));
    }

    @Test
    void testSerialization_HandlesBooleanValues() throws JsonProcessingException {
        // Given
        TestObjectWithBooleans testObj = new TestObjectWithBooleans();
        testObj.name = "test";
        testObj.trueValue = true;
        testObj.falseValue = false;

        // When
        String json = enhancedObjectMapper.writeValueAsString(testObj);

        // Then
        assertNotNull(json);
        assertTrue(json.contains("name"));
        assertTrue(json.contains("trueValue"));
        assertTrue(json.contains("falseValue"));
    }

    @Test
    void testDeserialization_HandlesComplexJson() throws JsonProcessingException {
        // Given
        String complexJson = """
            {
                "name": "complex",
                "nested": {
                    "innerName": "inner",
                    "innerValue": 123
                },
                "stringList": ["item1", "item2"],
                "numberMap": {
                    "key1": 1,
                    "key2": 2
                },
                "unknownField": "should be ignored"
            }
            """;

        // When
        ComplexTestObject result = enhancedObjectMapper.readValue(complexJson, ComplexTestObject.class);

        // Then
        assertNotNull(result);
        assertEquals("complex", result.name);
        assertNotNull(result.nested);
        assertEquals("inner", result.nested.innerName);
        assertEquals(123, result.nested.innerValue);
        assertEquals(2, result.stringList.size());
        assertTrue(result.stringList.contains("item1"));
        assertEquals(2, result.numberMap.size());
        assertEquals(Integer.valueOf(1), result.numberMap.get("key1"));
    }

    @Test
    void testRoundTripSerialization() throws JsonProcessingException {
        // Given
        TestObject original = new TestObject();
        original.name = "roundtrip";
        original.nonEmptyString = "value";
        original.nonEmptyList = Arrays.asList("a", "b", "c");
        original.nonEmptyMap = Map.of("x", "y");

        // When
        String json = enhancedObjectMapper.writeValueAsString(original);
        TestObject result = enhancedObjectMapper.readValue(json, TestObject.class);

        // Then
        assertNotNull(result);
        assertEquals(original.name, result.name);
        assertEquals(original.nonEmptyString, result.nonEmptyString);
        assertEquals(original.nonEmptyList, result.nonEmptyList);
        assertEquals(original.nonEmptyMap, result.nonEmptyMap);
    }

    // Test helper classes
    public static class TestObject {
        public String name;
        public String nullValue;
        public String emptyString;
        public String nonEmptyString;
        public List<String> emptyList;
        public List<String> nonEmptyList;
        public Map<String, String> emptyMap;
        public Map<String, String> nonEmptyMap;
    }

    public static class NestedTestObject {
        public String innerName;
        public String innerEmptyString;
        public String innerNullValue;
        public int innerValue;
    }

    public static class TestObjectWithNested {
        public String name;
        public NestedTestObject nested;
    }

    public static class TestObjectWithCollections {
        public String name;
        public List<TestObject> objectList;
    }

    public static class TestObjectWithArraysAndMaps {
        public String name;
        public String[] stringArray;
        public String[] emptyArray;
        public Map<String, String> stringMap;
        public Map<String, String> emptyMap;
    }

    public static class TestObjectWithNumbers {
        public String name;
        public int zeroInt;
        public int positiveInt;
        public double zeroDouble;
        public double positiveDouble;
    }

    public static class TestObjectWithBooleans {
        public String name;
        public boolean trueValue;
        public boolean falseValue;
    }

    public static class ComplexTestObject {
        public String name;
        public NestedTestObject nested;
        public List<String> stringList;
        public Map<String, Integer> numberMap;
    }
}
