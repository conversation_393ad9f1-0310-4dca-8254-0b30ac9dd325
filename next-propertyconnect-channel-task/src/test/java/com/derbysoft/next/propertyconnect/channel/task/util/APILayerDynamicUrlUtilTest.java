package com.derbysoft.next.propertyconnect.channel.task.util;

import com.derbysoft.next.propertyconnect.channel.task.config.ApiLayerProperties;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.net.URI;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Comprehensive unit tests for APILayerDynamicUrlUtil
 * Tests URL resolution logic for different distributors with custom and default URLs
 */
@ExtendWith(MockitoExtension.class)
class APILayerDynamicUrlUtilTest {

    private ApiLayerProperties apiLayerProperties;
    private APILayerDynamicUrlUtil apiLayerDynamicUrlUtil;

    @BeforeEach
    void setUp() {
        apiLayerProperties = new ApiLayerProperties();
        apiLayerDynamicUrlUtil = new APILayerDynamicUrlUtil(apiLayerProperties);
    }

    @Test
    void testGetBaseUrlForDistributor_WithCustomUrl_ReturnsCustomUrl() {
        // Given
        String distributorId = "BOOKINGCOM";
        String customUrl = "https://custom.booking.com/api/";
        String defaultUrl = "https://default.api.com/";
        
        ApiLayerProperties.CustomUrl customUrlConfig = new ApiLayerProperties.CustomUrl();
        customUrlConfig.setUrl(customUrl);
        
        Map<String, ApiLayerProperties.CustomUrl> customMap = new HashMap<>();
        customMap.put(distributorId, customUrlConfig);
        
        apiLayerProperties.setUrl(defaultUrl);
        apiLayerProperties.setCustom(customMap);
        
        // Reinitialize to update the static reference
        new APILayerDynamicUrlUtil(apiLayerProperties);
        
        // When
        URI result = APILayerDynamicUrlUtil.getBaseUrlForDistributor(distributorId);
        
        // Then
        assertNotNull(result);
        assertEquals(customUrl, result.toString());
    }

    @Test
    void testGetBaseUrlForDistributor_WithoutCustomUrl_ReturnsDefaultUrl() {
        // Given
        String distributorId = "EXPEDIA";
        String defaultUrl = "https://default.api.com/";
        
        apiLayerProperties.setUrl(defaultUrl);
        apiLayerProperties.setCustom(new HashMap<>());
        
        // Reinitialize to update the static reference
        new APILayerDynamicUrlUtil(apiLayerProperties);
        
        // When
        URI result = APILayerDynamicUrlUtil.getBaseUrlForDistributor(distributorId);
        
        // Then
        assertNotNull(result);
        assertEquals(defaultUrl, result.toString());
    }

    @Test
    void testGetBaseUrlForDistributor_WithNullCustomMap_ReturnsDefaultUrl() {
        // Given
        String distributorId = "AGODA";
        String defaultUrl = "https://default.api.com/";
        
        apiLayerProperties.setUrl(defaultUrl);
        apiLayerProperties.setCustom(null);
        
        // Reinitialize to update the static reference
        new APILayerDynamicUrlUtil(apiLayerProperties);
        
        // When
        URI result = APILayerDynamicUrlUtil.getBaseUrlForDistributor(distributorId);
        
        // Then
        assertNotNull(result);
        assertEquals(defaultUrl, result.toString());
    }

    @Test
    void testGetBaseUrlForDistributor_WithBlankCustomUrl_ReturnsDefaultUrl() {
        // Given
        String distributorId = "CTRIP";
        String defaultUrl = "https://default.api.com/";
        
        ApiLayerProperties.CustomUrl customUrlConfig = new ApiLayerProperties.CustomUrl();
        customUrlConfig.setUrl("   "); // Blank URL
        
        Map<String, ApiLayerProperties.CustomUrl> customMap = new HashMap<>();
        customMap.put(distributorId, customUrlConfig);
        
        apiLayerProperties.setUrl(defaultUrl);
        apiLayerProperties.setCustom(customMap);
        
        // Reinitialize to update the static reference
        new APILayerDynamicUrlUtil(apiLayerProperties);
        
        // When
        URI result = APILayerDynamicUrlUtil.getBaseUrlForDistributor(distributorId);
        
        // Then
        assertNotNull(result);
        assertEquals(defaultUrl, result.toString());
    }

    @Test
    void testGetBaseUrlForDistributor_WithNullCustomUrl_ReturnsDefaultUrl() {
        // Given
        String distributorId = "AIRBNB";
        String defaultUrl = "https://default.api.com/";
        
        ApiLayerProperties.CustomUrl customUrlConfig = new ApiLayerProperties.CustomUrl();
        customUrlConfig.setUrl(null); // Null URL
        
        Map<String, ApiLayerProperties.CustomUrl> customMap = new HashMap<>();
        customMap.put(distributorId, customUrlConfig);
        
        apiLayerProperties.setUrl(defaultUrl);
        apiLayerProperties.setCustom(customMap);
        
        // Reinitialize to update the static reference
        new APILayerDynamicUrlUtil(apiLayerProperties);
        
        // When
        URI result = APILayerDynamicUrlUtil.getBaseUrlForDistributor(distributorId);
        
        // Then
        assertNotNull(result);
        assertEquals(defaultUrl, result.toString());
    }

    @Test
    void testGetBaseUrlForDistributor_WithCustomUrlTrimming_ReturnsTrimedUrl() {
        // Given
        String distributorId = "FLIGGY";
        String customUrl = "  https://custom.fliggy.com/api/  ";
        String expectedUrl = "https://custom.fliggy.com/api/";
        
        ApiLayerProperties.CustomUrl customUrlConfig = new ApiLayerProperties.CustomUrl();
        customUrlConfig.setUrl(customUrl);
        
        Map<String, ApiLayerProperties.CustomUrl> customMap = new HashMap<>();
        customMap.put(distributorId, customUrlConfig);
        
        apiLayerProperties.setCustom(customMap);
        
        // Reinitialize to update the static reference
        new APILayerDynamicUrlUtil(apiLayerProperties);
        
        // When
        URI result = APILayerDynamicUrlUtil.getBaseUrlForDistributor(distributorId);
        
        // Then
        assertNotNull(result);
        assertEquals(expectedUrl, result.toString());
    }

    @Test
    void testGetBaseUrlForDistributor_WithNoDefaultUrl_ThrowsException() {
        // Given
        String distributorId = "UNKNOWN";
        
        apiLayerProperties.setUrl(null);
        apiLayerProperties.setCustom(new HashMap<>());
        
        // Reinitialize to update the static reference
        new APILayerDynamicUrlUtil(apiLayerProperties);
        
        // When & Then
        IllegalStateException exception = assertThrows(IllegalStateException.class, () -> 
            APILayerDynamicUrlUtil.getBaseUrlForDistributor(distributorId));
        
        assertEquals("No valid URL found for distributor: " + distributorId, exception.getMessage());
    }

    @Test
    void testGetBaseUrlForDistributor_WithBlankDefaultUrl_ThrowsException() {
        // Given
        String distributorId = "UNKNOWN";
        
        apiLayerProperties.setUrl("   "); // Blank default URL
        apiLayerProperties.setCustom(new HashMap<>());
        
        // Reinitialize to update the static reference
        new APILayerDynamicUrlUtil(apiLayerProperties);
        
        // When & Then
        IllegalStateException exception = assertThrows(IllegalStateException.class, () -> 
            APILayerDynamicUrlUtil.getBaseUrlForDistributor(distributorId));
        
        assertEquals("No valid URL found for distributor: " + distributorId, exception.getMessage());
    }

    @Test
    void testGetBaseUrlForDistributor_WithValidHttpsUrl_ReturnsCorrectURI() {
        // Given
        String distributorId = "HTTPS_TEST";
        String httpsUrl = "https://secure.api.com:8443/v1/";
        
        apiLayerProperties.setUrl(httpsUrl);
        apiLayerProperties.setCustom(new HashMap<>());
        
        // Reinitialize to update the static reference
        new APILayerDynamicUrlUtil(apiLayerProperties);
        
        // When
        URI result = APILayerDynamicUrlUtil.getBaseUrlForDistributor(distributorId);
        
        // Then
        assertNotNull(result);
        assertEquals(httpsUrl, result.toString());
        assertEquals("https", result.getScheme());
        assertEquals("secure.api.com", result.getHost());
        assertEquals(8443, result.getPort());
        assertEquals("/v1/", result.getPath());
    }

    @Test
    void testGetBaseUrlForDistributor_WithValidHttpUrl_ReturnsCorrectURI() {
        // Given
        String distributorId = "HTTP_TEST";
        String httpUrl = "http://localhost:8080/api/";
        
        apiLayerProperties.setUrl(httpUrl);
        apiLayerProperties.setCustom(new HashMap<>());
        
        // Reinitialize to update the static reference
        new APILayerDynamicUrlUtil(apiLayerProperties);
        
        // When
        URI result = APILayerDynamicUrlUtil.getBaseUrlForDistributor(distributorId);
        
        // Then
        assertNotNull(result);
        assertEquals(httpUrl, result.toString());
        assertEquals("http", result.getScheme());
        assertEquals("localhost", result.getHost());
        assertEquals(8080, result.getPort());
        assertEquals("/api/", result.getPath());
    }

    @Test
    void testGetBaseUrlForDistributor_WithMultipleCustomUrls_ReturnsCorrectUrl() {
        // Given
        String distributorId1 = "BOOKING";
        String distributorId2 = "EXPEDIA";
        String customUrl1 = "https://booking.custom.com/";
        String customUrl2 = "https://expedia.custom.com/";
        
        ApiLayerProperties.CustomUrl customUrlConfig1 = new ApiLayerProperties.CustomUrl();
        customUrlConfig1.setUrl(customUrl1);
        
        ApiLayerProperties.CustomUrl customUrlConfig2 = new ApiLayerProperties.CustomUrl();
        customUrlConfig2.setUrl(customUrl2);
        
        Map<String, ApiLayerProperties.CustomUrl> customMap = new HashMap<>();
        customMap.put(distributorId1, customUrlConfig1);
        customMap.put(distributorId2, customUrlConfig2);
        
        apiLayerProperties.setCustom(customMap);
        
        // Reinitialize to update the static reference
        new APILayerDynamicUrlUtil(apiLayerProperties);
        
        // When
        URI result1 = APILayerDynamicUrlUtil.getBaseUrlForDistributor(distributorId1);
        URI result2 = APILayerDynamicUrlUtil.getBaseUrlForDistributor(distributorId2);
        
        // Then
        assertNotNull(result1);
        assertNotNull(result2);
        assertEquals(customUrl1, result1.toString());
        assertEquals(customUrl2, result2.toString());
    }

    @Test
    void testGetBaseUrlForDistributor_WithEmptyCustomUrl_ReturnsDefaultUrl() {
        // Given
        String distributorId = "EMPTY_TEST";
        String defaultUrl = "https://default.api.com/";
        
        ApiLayerProperties.CustomUrl customUrlConfig = new ApiLayerProperties.CustomUrl();
        customUrlConfig.setUrl(""); // Empty URL
        
        Map<String, ApiLayerProperties.CustomUrl> customMap = new HashMap<>();
        customMap.put(distributorId, customUrlConfig);
        
        apiLayerProperties.setUrl(defaultUrl);
        apiLayerProperties.setCustom(customMap);
        
        // Reinitialize to update the static reference
        new APILayerDynamicUrlUtil(apiLayerProperties);
        
        // When
        URI result = APILayerDynamicUrlUtil.getBaseUrlForDistributor(distributorId);
        
        // Then
        assertNotNull(result);
        assertEquals(defaultUrl, result.toString());
    }

    @Test
    void testConstructor_InitializesPropertiesCorrectly() {
        // Given
        String defaultUrl = "https://test.api.com/";
        ApiLayerProperties properties = new ApiLayerProperties();
        properties.setUrl(defaultUrl);
        
        // When
        new APILayerDynamicUrlUtil(properties);
        URI result = APILayerDynamicUrlUtil.getBaseUrlForDistributor("TEST");
        
        // Then
        assertNotNull(result);
        assertEquals(defaultUrl, result.toString());
    }
}
