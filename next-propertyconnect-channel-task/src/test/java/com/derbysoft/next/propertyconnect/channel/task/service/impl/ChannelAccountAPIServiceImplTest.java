package com.derbysoft.next.propertyconnect.channel.task.service.impl;

import com.derbysoft.next.propertyconenct.channel.common.exception.response.RequestDataNotFoundException;
import com.derbysoft.next.propertyconnect.channel.task.controller.vo.ChannelAccountVO;
import com.derbysoft.next.propertyconnect.channel.task.service.AccountSettingService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * Comprehensive unit tests for ChannelAccountAPIServiceImpl
 * Enhanced with additional test scenarios and edge cases
 */

@ExtendWith(MockitoExtension.class)
class ChannelAccountAPIServiceImplTest {

    @InjectMocks
    private ChannelAccountAPIServiceImpl channelAccountAPIService;

    @Mock
    private AccountSettingService accountSettingService;

    @Mock
    private ChannelAccountAPIServiceImpl.AccountSettingsTranslator accountSettingsTranslator;

    private Map<String, Object> testAccountSettings;
    private ChannelAccountVO.AccountSetting testAccountSetting;

    @BeforeEach
    void setUp() {
        testAccountSettings = new HashMap<>();
        testAccountSettings.put("username", "testuser");
        testAccountSettings.put("password", "testpass");
        testAccountSettings.put("apiKey", "test-api-key");
        testAccountSettings.put("endpoint", "https://api.test.com");

        testAccountSetting = new ChannelAccountVO.AccountSetting();
        testAccountSetting.setUsername("testuser");
        testAccountSetting.setPassword("testpass");
        testAccountSetting.setApiKey("test-api-key");
    }

    @Test
    void givenExistingAccount_whenGetSpecificChannel_thenReturnsAccountSetting() {
        when(accountSettingService.getAccountSettings(any(), any())).thenReturn(Collections.emptyMap());
        when(accountSettingsTranslator.map(any(Map.class))).thenReturn(new ChannelAccountVO.AccountSetting());

        ChannelAccountVO.AccountSetting result = channelAccountAPIService.getSpecificChannel("channel1", "hotel1");

        assertNotNull(result);
    }

    @Test
    void testGetSpecificChannel_WithValidData_ReturnsCorrectAccountSetting() {
        // Given
        String channelId = "BOOKING";
        String channelHotelId = "HOTEL_123";

        when(accountSettingService.getAccountSettings(channelId, channelHotelId))
            .thenReturn(testAccountSettings);
        when(accountSettingsTranslator.map(testAccountSettings))
            .thenReturn(testAccountSetting);

        // When
        ChannelAccountVO.AccountSetting result = channelAccountAPIService.getSpecificChannel(channelId, channelHotelId);

        // Then
        assertNotNull(result);
        assertEquals(testAccountSetting, result);
        assertEquals("testuser", result.getUsername());
        assertEquals("testpass", result.getPassword());
        assertEquals("test-api-key", result.getApiKey());

        verify(accountSettingService).getAccountSettings(channelId, channelHotelId);
        verify(accountSettingsTranslator).map(testAccountSettings);
    }

    @Test
    void givenNonExistingAccount_whenGetSpecificChannel_thenThrowsException() {
        when(accountSettingService.getAccountSettings(any(), any())).thenReturn(null);

        assertThrows(RequestDataNotFoundException.class, () -> channelAccountAPIService.getSpecificChannel("channel1", "hotel1"));
    }

    @Test
    void testGetSpecificChannel_WithNullChannelHotelId_ThrowsExceptionWithCorrectMessage() {
        // Given
        String channelId = "BOOKING";
        String channelHotelId = null;

        when(accountSettingService.getAccountSettings(channelId, channelHotelId))
            .thenReturn(null);

        // When & Then
        RequestDataNotFoundException exception = assertThrows(RequestDataNotFoundException.class, () -> {
            channelAccountAPIService.getSpecificChannel(channelId, channelHotelId);
        });

        assertTrue(exception.getMessage().contains(channelId));
        verify(accountSettingService).getAccountSettings(channelId, channelHotelId);
    }

    @Test
    void testGetSpecificChannel_WithEmptyChannelHotelId_ThrowsExceptionWithCorrectMessage() {
        // Given
        String channelId = "AGODA";
        String channelHotelId = "";

        when(accountSettingService.getAccountSettings(channelId, channelHotelId))
            .thenReturn(null);

        // When & Then
        RequestDataNotFoundException exception = assertThrows(RequestDataNotFoundException.class, () -> {
            channelAccountAPIService.getSpecificChannel(channelId, channelHotelId);
        });

        assertTrue(exception.getMessage().contains(channelId));
        verify(accountSettingService).getAccountSettings(channelId, channelHotelId);
    }

    @Test
    void whenUpdateSpecificChannel_thenReturnsUpdatedAccountSetting() {
        when(accountSettingService.saveOrUpdateAccountSettings(any(), any(), any())).thenReturn(Collections.emptyMap());
        when(accountSettingsTranslator.map(any(Map.class))).thenReturn(new ChannelAccountVO.AccountSetting());

        ChannelAccountVO.AccountSetting result = channelAccountAPIService.updateSpecificChannel("channel1", "hotel1", new ChannelAccountVO.AccountSetting());

        assertNotNull(result);
    }

    @Test
    void testUpdateSpecificChannel_Success() {
        // Given
        String channelId = "EXPEDIA";
        String channelHotelId = "HOTEL_456";
        ChannelAccountVO.AccountSetting inputSetting = new ChannelAccountVO.AccountSetting();
        inputSetting.setUsername("newuser");
        inputSetting.setPassword("newpass");

        Map<String, Object> inputMap = new HashMap<>();
        inputMap.put("username", "newuser");
        inputMap.put("password", "newpass");

        Map<String, Object> savedMap = new HashMap<>();
        savedMap.put("username", "newuser");
        savedMap.put("password", "newpass");
        savedMap.put("id", "saved-id");

        ChannelAccountVO.AccountSetting savedSetting = new ChannelAccountVO.AccountSetting();
        savedSetting.setUsername("newuser");
        savedSetting.setPassword("newpass");

        when(accountSettingsTranslator.reverseMap(inputSetting)).thenReturn(inputMap);
        when(accountSettingService.saveOrUpdateAccountSettings(channelId, channelHotelId, inputMap))
            .thenReturn(savedMap);
        when(accountSettingsTranslator.map(savedMap)).thenReturn(savedSetting);

        // When
        ChannelAccountVO.AccountSetting result = channelAccountAPIService.updateSpecificChannel(
            channelId, channelHotelId, inputSetting);

        // Then
        assertNotNull(result);
        assertEquals(savedSetting, result);
        assertEquals("newuser", result.getUsername());
        assertEquals("newpass", result.getPassword());

        verify(accountSettingsTranslator).reverseMap(inputSetting);
        verify(accountSettingService).saveOrUpdateAccountSettings(channelId, channelHotelId, inputMap);
        verify(accountSettingsTranslator).map(savedMap);
    }

    @Test
    void testUpdateSpecificChannel_ServiceReturnsNull_ThrowsException() {
        // Given
        String channelId = "CTRIP";
        String channelHotelId = "HOTEL_789";
        ChannelAccountVO.AccountSetting inputSetting = new ChannelAccountVO.AccountSetting();
        Map<String, Object> inputMap = new HashMap<>();

        when(accountSettingsTranslator.reverseMap(inputSetting)).thenReturn(inputMap);
        when(accountSettingService.saveOrUpdateAccountSettings(channelId, channelHotelId, inputMap))
            .thenReturn(null);

        // When & Then
        RequestDataNotFoundException exception = assertThrows(RequestDataNotFoundException.class, () -> {
            channelAccountAPIService.updateSpecificChannel(channelId, channelHotelId, inputSetting);
        });

        assertTrue(exception.getMessage().contains(channelId));
        assertTrue(exception.getMessage().contains(channelHotelId));
        verify(accountSettingService).saveOrUpdateAccountSettings(channelId, channelHotelId, inputMap);
        verify(accountSettingsTranslator, never()).map(any(Map.class));
    }

    @Test
    void whenDeleteSpecificChannel_thenCallsDeleteOnService() {
        channelAccountAPIService.deleteSpecificChannel("channel1", "hotel1");

        verify(accountSettingService).deleteAccountSettings("channel1", "hotel1");
    }

    @Test
    void testDeleteSpecificChannel_Success() {
        // Given
        String channelId = "HOTELTONIGHT";
        String channelHotelId = "HOTEL_101";

        // When
        assertDoesNotThrow(() -> {
            channelAccountAPIService.deleteSpecificChannel(channelId, channelHotelId);
        });

        // Then
        verify(accountSettingService).deleteAccountSettings(channelId, channelHotelId);
    }

    @Test
    void testDeleteSpecificChannel_ServiceThrowsException_PropagatesException() {
        // Given
        String channelId = "ERROR_CHANNEL";
        String channelHotelId = "ERROR_HOTEL";

        doThrow(new RuntimeException("Delete error"))
            .when(accountSettingService).deleteAccountSettings(channelId, channelHotelId);

        // When & Then
        assertThrows(RuntimeException.class, () -> {
            channelAccountAPIService.deleteSpecificChannel(channelId, channelHotelId);
        });

        verify(accountSettingService).deleteAccountSettings(channelId, channelHotelId);
    }

    @Test
    void testGetSpecificChannel_WithDifferentChannels_CallsServiceCorrectly() {
        // Given
        String[] channelIds = {"BOOKING", "AGODA", "EXPEDIA", "CTRIP"};
        String channelHotelId = "COMMON_HOTEL";

        for (String channelId : channelIds) {
            when(accountSettingService.getAccountSettings(channelId, channelHotelId))
                .thenReturn(testAccountSettings);
            when(accountSettingsTranslator.map(testAccountSettings))
                .thenReturn(testAccountSetting);

            // When
            ChannelAccountVO.AccountSetting result = channelAccountAPIService.getSpecificChannel(
                channelId, channelHotelId);

            // Then
            assertNotNull(result);
            assertEquals(testAccountSetting, result);
            verify(accountSettingService).getAccountSettings(channelId, channelHotelId);
        }
    }
}