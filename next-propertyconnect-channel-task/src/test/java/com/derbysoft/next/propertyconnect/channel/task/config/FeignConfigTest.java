package com.derbysoft.next.propertyconnect.channel.task.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import feign.codec.Decoder;
import feign.codec.Encoder;
import feign.okhttp.OkHttpClient;
import okhttp3.OkHttpClient.Builder;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.cloud.openfeign.support.SpringDecoder;
import org.springframework.cloud.openfeign.support.SpringEncoder;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * Comprehensive unit tests for FeignConfig
 * Tests Feign client configuration including encoder, decoder, and HTTP client setup
 */
@ExtendWith(MockitoExtension.class)
class FeignConfigTest {

    @Mock
    private ObjectMapper mockObjectMapper;

    @Mock
    private okhttp3.OkHttpClient mockOkHttpClient;

    private FeignConfig feignConfig;

    @BeforeEach
    void setUp() {
        feignConfig = new FeignConfig();
    }

    @Test
    void testFeignEncoder_WithObjectMapper_ReturnsSpringEncoder() {
        // When
        Encoder result = feignConfig.feignEncoder(mockObjectMapper);

        // Then
        assertNotNull(result);
        assertTrue(result instanceof SpringEncoder);
    }

    @Test
    void testFeignEncoder_UsesProvidedObjectMapper() {
        // Given
        ObjectMapper realObjectMapper = new ObjectMapper();

        // When
        Encoder result = feignConfig.feignEncoder(realObjectMapper);

        // Then
        assertNotNull(result);
        assertTrue(result instanceof SpringEncoder);
        // Verify that the encoder is properly configured
        assertDoesNotThrow(() -> {
            // The encoder should be usable without throwing exceptions
            result.toString();
        });
    }

    @Test
    void testFeignDecoder_WithObjectMapper_ReturnsSpringDecoder() {
        // When
        Decoder result = feignConfig.feignDecoder(mockObjectMapper);

        // Then
        assertNotNull(result);
        assertTrue(result instanceof SpringDecoder);
    }

    @Test
    void testFeignDecoder_UsesProvidedObjectMapper() {
        // Given
        ObjectMapper realObjectMapper = new ObjectMapper();

        // When
        Decoder result = feignConfig.feignDecoder(realObjectMapper);

        // Then
        assertNotNull(result);
        assertTrue(result instanceof SpringDecoder);
        // Verify that the decoder is properly configured
        assertDoesNotThrow(() -> {
            // The decoder should be usable without throwing exceptions
            result.toString();
        });
    }

    @Test
    void testFeignClient_WithOkHttpClient_ReturnsOkHttpClient() {
        // When
        feign.Client result = feignConfig.feignClient(mockOkHttpClient);

        // Then
        assertNotNull(result);
        assertTrue(result instanceof OkHttpClient);
    }

    @Test
    void testFeignClient_UsesProvidedOkHttpClient() {
        // Given
        okhttp3.OkHttpClient realOkHttpClient = new Builder().build();

        // When
        feign.Client result = feignConfig.feignClient(realOkHttpClient);

        // Then
        assertNotNull(result);
        assertTrue(result instanceof OkHttpClient);
        // Verify that the client is properly configured
        assertDoesNotThrow(() -> {
            // The client should be usable without throwing exceptions
            result.toString();
        });
    }

    @Test
    void testFeignConfig_IsSpringConfiguration() {
        // When
        Class<FeignConfig> clazz = FeignConfig.class;

        // Then
        assertTrue(clazz.isAnnotationPresent(org.springframework.context.annotation.Configuration.class));
    }

    @Test
    void testFeignConfig_HasAutoConfigureAfterAnnotation() {
        // When
        Class<FeignConfig> clazz = FeignConfig.class;

        // Then
        assertTrue(clazz.isAnnotationPresent(org.springframework.boot.autoconfigure.AutoConfigureAfter.class));
        
        var annotation = clazz.getAnnotation(org.springframework.boot.autoconfigure.AutoConfigureAfter.class);
        assertEquals(1, annotation.value().length);
        assertEquals(JacksonConfig.class, annotation.value()[0]);
    }

    @Test
    void testFeignEncoder_HasBeanAnnotation() throws NoSuchMethodException {
        // When
        var method = FeignConfig.class.getMethod("feignEncoder", ObjectMapper.class);

        // Then
        assertTrue(method.isAnnotationPresent(org.springframework.context.annotation.Bean.class));
    }

    @Test
    void testFeignDecoder_HasBeanAnnotation() throws NoSuchMethodException {
        // When
        var method = FeignConfig.class.getMethod("feignDecoder", ObjectMapper.class);

        // Then
        assertTrue(method.isAnnotationPresent(org.springframework.context.annotation.Bean.class));
    }

    @Test
    void testFeignClient_HasBeanAnnotation() throws NoSuchMethodException {
        // When
        var method = FeignConfig.class.getMethod("feignClient", okhttp3.OkHttpClient.class);

        // Then
        assertTrue(method.isAnnotationPresent(org.springframework.context.annotation.Bean.class));
    }

    @Test
    void testFeignEncoder_WithEnhancedObjectMapper_WorksCorrectly() {
        // Given
        EnhancedObjectMapper enhancedObjectMapper = new EnhancedObjectMapper();

        // When
        Encoder result = feignConfig.feignEncoder(enhancedObjectMapper);

        // Then
        assertNotNull(result);
        assertTrue(result instanceof SpringEncoder);
    }

    @Test
    void testFeignDecoder_WithEnhancedObjectMapper_WorksCorrectly() {
        // Given
        EnhancedObjectMapper enhancedObjectMapper = new EnhancedObjectMapper();

        // When
        Decoder result = feignConfig.feignDecoder(enhancedObjectMapper);

        // Then
        assertNotNull(result);
        assertTrue(result instanceof SpringDecoder);
    }

    @Test
    void testFeignConfig_IntegrationWithJacksonConfig() {
        // Given
        JacksonConfig jacksonConfig = new JacksonConfig();
        ObjectMapper objectMapper = jacksonConfig.customObjectMapper();

        // When
        Encoder encoder = feignConfig.feignEncoder(objectMapper);
        Decoder decoder = feignConfig.feignDecoder(objectMapper);

        // Then
        assertNotNull(encoder);
        assertNotNull(decoder);
        assertTrue(encoder instanceof SpringEncoder);
        assertTrue(decoder instanceof SpringDecoder);
    }

    @Test
    void testFeignClient_WithCustomOkHttpClient_PreservesConfiguration() {
        // Given
        okhttp3.OkHttpClient customClient = new Builder()
            .connectTimeout(30, java.util.concurrent.TimeUnit.SECONDS)
            .readTimeout(60, java.util.concurrent.TimeUnit.SECONDS)
            .build();

        // When
        feign.Client result = feignConfig.feignClient(customClient);

        // Then
        assertNotNull(result);
        assertTrue(result instanceof OkHttpClient);
    }

    @Test
    void testFeignEncoder_ThreadSafety() {
        // Given
        ObjectMapper objectMapper = new ObjectMapper();
        
        // When & Then
        assertDoesNotThrow(() -> {
            // Create multiple encoders concurrently
            Thread[] threads = new Thread[10];
            Encoder[] encoders = new Encoder[10];
            
            for (int i = 0; i < 10; i++) {
                final int index = i;
                threads[i] = new Thread(() -> {
                    encoders[index] = feignConfig.feignEncoder(objectMapper);
                });
                threads[i].start();
            }
            
            for (Thread thread : threads) {
                thread.join();
            }
            
            // All encoders should be created successfully
            for (Encoder encoder : encoders) {
                assertNotNull(encoder);
                assertTrue(encoder instanceof SpringEncoder);
            }
        });
    }

    @Test
    void testFeignDecoder_ThreadSafety() {
        // Given
        ObjectMapper objectMapper = new ObjectMapper();
        
        // When & Then
        assertDoesNotThrow(() -> {
            // Create multiple decoders concurrently
            Thread[] threads = new Thread[10];
            Decoder[] decoders = new Decoder[10];
            
            for (int i = 0; i < 10; i++) {
                final int index = i;
                threads[i] = new Thread(() -> {
                    decoders[index] = feignConfig.feignDecoder(objectMapper);
                });
                threads[i].start();
            }
            
            for (Thread thread : threads) {
                thread.join();
            }
            
            // All decoders should be created successfully
            for (Decoder decoder : decoders) {
                assertNotNull(decoder);
                assertTrue(decoder instanceof SpringDecoder);
            }
        });
    }

    @Test
    void testFeignClient_ThreadSafety() {
        // Given
        okhttp3.OkHttpClient okHttpClient = new Builder().build();
        
        // When & Then
        assertDoesNotThrow(() -> {
            // Create multiple clients concurrently
            Thread[] threads = new Thread[10];
            feign.Client[] clients = new feign.Client[10];
            
            for (int i = 0; i < 10; i++) {
                final int index = i;
                threads[i] = new Thread(() -> {
                    clients[index] = feignConfig.feignClient(okHttpClient);
                });
                threads[i].start();
            }
            
            for (Thread thread : threads) {
                thread.join();
            }
            
            // All clients should be created successfully
            for (feign.Client client : clients) {
                assertNotNull(client);
                assertTrue(client instanceof OkHttpClient);
            }
        });
    }

    @Test
    void testFeignConfig_BeanMethodsReturnDifferentInstances() {
        // Given
        ObjectMapper objectMapper = new ObjectMapper();
        okhttp3.OkHttpClient okHttpClient = new Builder().build();

        // When
        Encoder encoder1 = feignConfig.feignEncoder(objectMapper);
        Encoder encoder2 = feignConfig.feignEncoder(objectMapper);
        
        Decoder decoder1 = feignConfig.feignDecoder(objectMapper);
        Decoder decoder2 = feignConfig.feignDecoder(objectMapper);
        
        feign.Client client1 = feignConfig.feignClient(okHttpClient);
        feign.Client client2 = feignConfig.feignClient(okHttpClient);

        // Then
        // Each call should return a new instance (prototype scope)
        assertNotSame(encoder1, encoder2);
        assertNotSame(decoder1, decoder2);
        assertNotSame(client1, client2);
    }

    @Test
    void testFeignConfig_HandlesNullObjectMapper() {
        // When & Then
        // Note: The actual implementation may not throw NPE immediately,
        // but will fail when the encoder/decoder is used
        assertDoesNotThrow(() -> {
            Encoder encoder = feignConfig.feignEncoder(null);
            assertNotNull(encoder);
        });

        assertDoesNotThrow(() -> {
            Decoder decoder = feignConfig.feignDecoder(null);
            assertNotNull(decoder);
        });
    }

    @Test
    void testFeignConfig_HandlesNullOkHttpClient() {
        // When & Then
        // Note: The actual implementation may not throw NPE immediately,
        // but will fail when the client is used
        assertDoesNotThrow(() -> {
            feign.Client client = feignConfig.feignClient(null);
            assertNotNull(client);
        });
    }

    @Test
    void testFeignConfig_MethodParameterTypes() throws NoSuchMethodException {
        // When
        var encoderMethod = FeignConfig.class.getMethod("feignEncoder", ObjectMapper.class);
        var decoderMethod = FeignConfig.class.getMethod("feignDecoder", ObjectMapper.class);
        var clientMethod = FeignConfig.class.getMethod("feignClient", okhttp3.OkHttpClient.class);

        // Then
        assertEquals(Encoder.class, encoderMethod.getReturnType());
        assertEquals(Decoder.class, decoderMethod.getReturnType());
        assertEquals(feign.Client.class, clientMethod.getReturnType());
        
        assertEquals(1, encoderMethod.getParameterCount());
        assertEquals(1, decoderMethod.getParameterCount());
        assertEquals(1, clientMethod.getParameterCount());
        
        assertEquals(ObjectMapper.class, encoderMethod.getParameterTypes()[0]);
        assertEquals(ObjectMapper.class, decoderMethod.getParameterTypes()[0]);
        assertEquals(okhttp3.OkHttpClient.class, clientMethod.getParameterTypes()[0]);
    }
}
