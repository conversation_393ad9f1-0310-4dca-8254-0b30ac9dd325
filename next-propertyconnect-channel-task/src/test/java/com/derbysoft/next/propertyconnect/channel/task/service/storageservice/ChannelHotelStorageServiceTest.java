package com.derbysoft.next.propertyconnect.channel.task.service.storageservice;

import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelHotelDTO;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelHotelInfo;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ItemStatus;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.SyncStatus;
import com.derbysoft.next.propertyconnect.channel.task.domain.entity.ChannelHotelPO;
import com.derbysoft.next.propertyconnect.channel.task.service.PreProcessingService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;

import java.util.Map;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ChannelHotelStorageServiceTest {

    @Mock
    private MongoTemplate mongoTemplate;

    @Mock
    private ObjectProvider<PreProcessingService> preProcessingProvider;

    @Mock
    private PreProcessingService preProcessingService;

    private ChannelHotelStorageService channelHotelStorageService;

    @BeforeEach
    void setUp() {
        channelHotelStorageService = new ChannelHotelStorageService(mongoTemplate, preProcessingProvider);
    }

    @Test
    void testGetChannelHotel_WithPreProcessing() {
        // Given
        String channelId = "test-channel";
        String channelHotelId = "test-hotel";

        ChannelHotelPO hotelPO = createTestHotelPO();
        when(mongoTemplate.findOne(any(Query.class), eq(ChannelHotelPO.class)))
                .thenReturn(hotelPO);

        when(preProcessingService.channel()).thenReturn(channelId);
        when(preProcessingProvider.stream()).thenReturn(Stream.of(preProcessingService));

        // When
        ChannelHotelDTO result = channelHotelStorageService.getChannelHotel(channelId, channelHotelId);

        // Then
        assertNotNull(result);
        verify(mongoTemplate).findOne(any(Query.class), eq(ChannelHotelPO.class));
        verify(preProcessingService).postProcess(any(ChannelHotelDTO.class));
    }

    @Test
    void testGetChannelHotel_WithoutMatchingPreProcessing() {
        // Given
        String channelId = "test-channel";
        String channelHotelId = "test-hotel";

        ChannelHotelPO hotelPO = createTestHotelPO();
        when(mongoTemplate.findOne(any(Query.class), eq(ChannelHotelPO.class)))
                .thenReturn(hotelPO);

        when(preProcessingService.channel()).thenReturn("different-channel");
        when(preProcessingProvider.stream()).thenReturn(Stream.of(preProcessingService));

        // When
        ChannelHotelDTO result = channelHotelStorageService.getChannelHotel(channelId, channelHotelId);

        // Then
        assertNotNull(result);
        verify(mongoTemplate).findOne(any(Query.class), eq(ChannelHotelPO.class));
        verify(preProcessingService, never()).postProcess(any(ChannelHotelDTO.class));
    }

    @Test
    void testGetChannelHotel_WithNullResult() {
        // Given
        String channelId = "test-channel";
        String channelHotelId = "test-hotel";

        when(mongoTemplate.findOne(any(Query.class), eq(ChannelHotelPO.class)))
                .thenReturn(null);

        when(preProcessingProvider.stream()).thenReturn(Stream.empty());

        // When
        ChannelHotelDTO result = channelHotelStorageService.getChannelHotel(channelId, channelHotelId);

        // Then
        assertNull(result);
        verify(mongoTemplate).findOne(any(Query.class), eq(ChannelHotelPO.class));
        verifyNoInteractions(preProcessingService);
    }

    @Test
    void testSaveIncrementalChannelProduct_WithPreProcessing() {
        // Given
        ChannelHotelDTO channelHotel = createTestChannelHotelDTO();

        when(preProcessingService.channel()).thenReturn(channelHotel.getChannelId());
        when(preProcessingProvider.stream()).thenReturn(Stream.of(preProcessingService));

        ChannelHotelPO existingPO = createTestHotelPO();
        when(mongoTemplate.findOne(any(Query.class), eq(ChannelHotelPO.class)))
                .thenReturn(existingPO);

        ChannelHotelPO savedPO = createTestHotelPO();
        when(mongoTemplate.save(any(ChannelHotelPO.class))).thenReturn(savedPO);

        // When
        ChannelHotelDTO result = channelHotelStorageService.saveIncrementalChannelProduct(channelHotel);

        // Then
        assertNotNull(result);
        verify(preProcessingService).preProcess(channelHotel);
        verify(mongoTemplate).findOne(any(Query.class), eq(ChannelHotelPO.class));
        verify(mongoTemplate).save(any(ChannelHotelPO.class));
    }

    @Test
    void testSaveIncrementalChannelProduct_WithoutMatchingPreProcessing() {
        // Given
        ChannelHotelDTO channelHotel = createTestChannelHotelDTO();

        when(preProcessingService.channel()).thenReturn("different-channel");
        when(preProcessingProvider.stream()).thenReturn(Stream.of(preProcessingService));

        ChannelHotelPO existingPO = createTestHotelPO();
        when(mongoTemplate.findOne(any(Query.class), eq(ChannelHotelPO.class)))
                .thenReturn(existingPO);

        ChannelHotelPO savedPO = createTestHotelPO();
        when(mongoTemplate.save(any(ChannelHotelPO.class))).thenReturn(savedPO);

        // When
        ChannelHotelDTO result = channelHotelStorageService.saveIncrementalChannelProduct(channelHotel);

        // Then
        assertNotNull(result);
        verify(preProcessingService, never()).preProcess(any(ChannelHotelDTO.class));
        verify(mongoTemplate).findOne(any(Query.class), eq(ChannelHotelPO.class));
        verify(mongoTemplate).save(any(ChannelHotelPO.class));
    }

    @Test
    void testSaveIncrementalChannelProduct_NewHotel() {
        // Given
        ChannelHotelDTO channelHotel = createTestChannelHotelDTO();

        when(preProcessingProvider.stream()).thenReturn(Stream.empty());

        when(mongoTemplate.findOne(any(Query.class), eq(ChannelHotelPO.class)))
                .thenReturn(null); // No existing hotel

        ChannelHotelPO savedPO = createTestHotelPO();
        when(mongoTemplate.save(any(ChannelHotelPO.class))).thenReturn(savedPO);

        // When
        ChannelHotelDTO result = channelHotelStorageService.saveIncrementalChannelProduct(channelHotel);

        // Then
        assertNotNull(result);
        verify(mongoTemplate).findOne(any(Query.class), eq(ChannelHotelPO.class));
        verify(mongoTemplate).save(any(ChannelHotelPO.class));
    }

    @Test
    void testDeleteChannelHotel() {
        // Given
        String channelId = "test-channel";
        String channelHotelId = "test-hotel";

        // When
        channelHotelStorageService.deleteChannelHotel(channelId, channelHotelId);

        // Then
        verify(mongoTemplate).remove(any(Query.class), eq(ChannelHotelPO.class));
    }

    @Test
    void testSaveIncrementalChannelProduct_MultiplePreProcessingServices() {
        // Given
        ChannelHotelDTO channelHotel = createTestChannelHotelDTO();

        PreProcessingService preProcessingService2 = mock(PreProcessingService.class);
        when(preProcessingService.channel()).thenReturn(channelHotel.getChannelId());
        when(preProcessingService2.channel()).thenReturn("different-channel");
        when(preProcessingProvider.stream()).thenReturn(Stream.of(preProcessingService, preProcessingService2));

        ChannelHotelPO existingPO = createTestHotelPO();
        when(mongoTemplate.findOne(any(Query.class), eq(ChannelHotelPO.class)))
                .thenReturn(existingPO);

        ChannelHotelPO savedPO = createTestHotelPO();
        when(mongoTemplate.save(any(ChannelHotelPO.class))).thenReturn(savedPO);

        // When
        ChannelHotelDTO result = channelHotelStorageService.saveIncrementalChannelProduct(channelHotel);

        // Then
        assertNotNull(result);
        verify(preProcessingService).preProcess(channelHotel);
        verify(preProcessingService2, never()).preProcess(any(ChannelHotelDTO.class));
    }

    private ChannelHotelDTO createTestChannelHotelDTO() {
        ChannelHotelDTO dto = new ChannelHotelDTO();
        dto.setChannelId("test-channel");
        dto.setChannelHotelId("test-hotel");
        dto.setSupplierId("test-supplier");
        
        ChannelHotelInfo hotelInfo = new ChannelHotelInfo("test-hotel");
        hotelInfo.setName("Test Hotel");
        hotelInfo.setStatus(ItemStatus.Actived);
        hotelInfo.setSyncStatus(SyncStatus.SYNCED);
        dto.setHotelInfo(hotelInfo);
        
        return dto;
    }

    private ChannelHotelPO createTestHotelPO() {
        return ChannelHotelPO.builder()
                .id("test-id")
                .channelId("test-channel")
                .channelHotelId("test-hotel")
                .supplierId("test-supplier")
                .status(ItemStatus.Actived)
                .syncStatus(SyncStatus.SYNCED)
                .hotelInfo(new java.util.HashMap<>(Map.of("id", "test-hotel", "name", "Test Hotel")))
                .accountSettings(new java.util.HashMap<>(Map.of("setting1", "value1")))
                .build();
    }
}
