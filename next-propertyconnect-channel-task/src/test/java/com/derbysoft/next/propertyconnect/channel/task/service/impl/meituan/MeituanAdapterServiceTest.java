package com.derbysoft.next.propertyconnect.channel.task.service.impl.meituan;

import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelHotelDTO;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelHotelInfo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

class MeituanAdapterServiceTest {

    private MeituanAdapterService meituanAdapterService;

    @BeforeEach
    void setUp() {
        meituanAdapterService = new MeituanAdapterService();
    }

    @Test
    void testChannel() {
        // When
        String channel = meituanAdapterService.channel();

        // Then
        assertEquals("MEITUAN", channel);
    }

    @Test
    void testCustomizeCredential_WithAccountSettings() {
        // Given
        ChannelHotelDTO channelHotelDTO = new ChannelHotelDTO();
        
        Map<String, Object> accountSettings = new HashMap<>();
        accountSettings.put("username", "testuser");
        accountSettings.put("password", "testpass");
        accountSettings.put("apiKey", "test-api-key");
        channelHotelDTO.setAccountSettings(accountSettings);

        ChannelHotelInfo hotelInfo = new ChannelHotelInfo("hotel-123");
        Map<String, Object> extensions = new HashMap<>();
        extensions.put("existingKey", "existingValue");
        hotelInfo.setExtensions(extensions);
        channelHotelDTO.setHotelInfo(hotelInfo);

        // When
        meituanAdapterService.customizeCredential(channelHotelDTO);

        // Then
        assertNull(channelHotelDTO.getAccountSettings());
        assertNotNull(channelHotelDTO.getHotelInfo().getExtensions());
        assertEquals("testuser", channelHotelDTO.getHotelInfo().getExtensions().get("username"));
        assertEquals("testpass", channelHotelDTO.getHotelInfo().getExtensions().get("password"));
        assertEquals("test-api-key", channelHotelDTO.getHotelInfo().getExtensions().get("apiKey"));
        assertEquals("existingValue", channelHotelDTO.getHotelInfo().getExtensions().get("existingKey"));
    }

    @Test
    void testCustomizeCredential_WithNullAccountSettings() {
        // Given
        ChannelHotelDTO channelHotelDTO = new ChannelHotelDTO();
        channelHotelDTO.setAccountSettings(null);

        ChannelHotelInfo hotelInfo = new ChannelHotelInfo("hotel-123");
        Map<String, Object> extensions = new HashMap<>();
        extensions.put("existingKey", "existingValue");
        hotelInfo.setExtensions(extensions);
        channelHotelDTO.setHotelInfo(hotelInfo);

        // When & Then
        // The current implementation will throw NullPointerException for null accountSettings
        assertThrows(NullPointerException.class, () -> meituanAdapterService.customizeCredential(channelHotelDTO));
    }

    @Test
    void testCustomizeCredential_WithEmptyAccountSettings() {
        // Given
        ChannelHotelDTO channelHotelDTO = new ChannelHotelDTO();
        channelHotelDTO.setAccountSettings(new HashMap<>());

        ChannelHotelInfo hotelInfo = new ChannelHotelInfo("hotel-123");
        Map<String, Object> extensions = new HashMap<>();
        extensions.put("existingKey", "existingValue");
        hotelInfo.setExtensions(extensions);
        channelHotelDTO.setHotelInfo(hotelInfo);

        // When
        meituanAdapterService.customizeCredential(channelHotelDTO);

        // Then
        assertNull(channelHotelDTO.getAccountSettings());
        assertNotNull(channelHotelDTO.getHotelInfo().getExtensions());
        assertEquals("existingValue", channelHotelDTO.getHotelInfo().getExtensions().get("existingKey"));
        assertEquals(1, channelHotelDTO.getHotelInfo().getExtensions().size());
    }

    @Test
    void testCustomizeCredential_WithNullHotelInfo() {
        // Given
        ChannelHotelDTO channelHotelDTO = new ChannelHotelDTO();

        Map<String, Object> accountSettings = new HashMap<>();
        accountSettings.put("username", "testuser");
        channelHotelDTO.setAccountSettings(accountSettings);
        channelHotelDTO.setHotelInfo(null);

        // When & Then
        // The ObjectUtil.getReference requires non-null superTarget, so this will throw NullPointerException
        assertThrows(NullPointerException.class, () -> meituanAdapterService.customizeCredential(channelHotelDTO));
    }

    @Test
    void testCustomizeCredential_WithNullHotelInfoExtensions() {
        // Given
        ChannelHotelDTO channelHotelDTO = new ChannelHotelDTO();
        
        Map<String, Object> accountSettings = new HashMap<>();
        accountSettings.put("username", "testuser");
        accountSettings.put("password", "testpass");
        channelHotelDTO.setAccountSettings(accountSettings);

        ChannelHotelInfo hotelInfo = new ChannelHotelInfo("hotel-123");
        hotelInfo.setExtensions(null);
        channelHotelDTO.setHotelInfo(hotelInfo);

        // When
        meituanAdapterService.customizeCredential(channelHotelDTO);

        // Then
        assertNull(channelHotelDTO.getAccountSettings());
        assertNotNull(channelHotelDTO.getHotelInfo().getExtensions());
        assertEquals("testuser", channelHotelDTO.getHotelInfo().getExtensions().get("username"));
        assertEquals("testpass", channelHotelDTO.getHotelInfo().getExtensions().get("password"));
        assertEquals(2, channelHotelDTO.getHotelInfo().getExtensions().size());
    }

    @Test
    void testCustomizeCredential_OverwriteExistingExtensions() {
        // Given
        ChannelHotelDTO channelHotelDTO = new ChannelHotelDTO();
        
        Map<String, Object> accountSettings = new HashMap<>();
        accountSettings.put("username", "newuser");
        accountSettings.put("newKey", "newValue");
        channelHotelDTO.setAccountSettings(accountSettings);

        ChannelHotelInfo hotelInfo = new ChannelHotelInfo("hotel-123");
        Map<String, Object> extensions = new HashMap<>();
        extensions.put("username", "olduser");
        extensions.put("existingKey", "existingValue");
        hotelInfo.setExtensions(extensions);
        channelHotelDTO.setHotelInfo(hotelInfo);

        // When
        meituanAdapterService.customizeCredential(channelHotelDTO);

        // Then
        assertNull(channelHotelDTO.getAccountSettings());
        assertNotNull(channelHotelDTO.getHotelInfo().getExtensions());
        assertEquals("newuser", channelHotelDTO.getHotelInfo().getExtensions().get("username")); // Overwritten
        assertEquals("newValue", channelHotelDTO.getHotelInfo().getExtensions().get("newKey"));
        assertEquals("existingValue", channelHotelDTO.getHotelInfo().getExtensions().get("existingKey"));
        assertEquals(3, channelHotelDTO.getHotelInfo().getExtensions().size());
    }

    @Test
    void testCustomizeCredential_WithComplexAccountSettings() {
        // Given
        ChannelHotelDTO channelHotelDTO = new ChannelHotelDTO();
        
        Map<String, Object> accountSettings = new HashMap<>();
        accountSettings.put("username", "testuser");
        accountSettings.put("password", "testpass");
        accountSettings.put("timeout", 30);
        accountSettings.put("enabled", true);
        accountSettings.put("config", Map.of("key1", "value1", "key2", "value2"));
        channelHotelDTO.setAccountSettings(accountSettings);

        ChannelHotelInfo hotelInfo = new ChannelHotelInfo("hotel-123");
        channelHotelDTO.setHotelInfo(hotelInfo);

        // When
        meituanAdapterService.customizeCredential(channelHotelDTO);

        // Then
        assertNull(channelHotelDTO.getAccountSettings());
        assertNotNull(channelHotelDTO.getHotelInfo().getExtensions());
        assertEquals("testuser", channelHotelDTO.getHotelInfo().getExtensions().get("username"));
        assertEquals("testpass", channelHotelDTO.getHotelInfo().getExtensions().get("password"));
        assertEquals(30, channelHotelDTO.getHotelInfo().getExtensions().get("timeout"));
        assertEquals(true, channelHotelDTO.getHotelInfo().getExtensions().get("enabled"));
        assertNotNull(channelHotelDTO.getHotelInfo().getExtensions().get("config"));
        assertEquals(5, channelHotelDTO.getHotelInfo().getExtensions().size());
    }

    @Test
    void testDefaultMethods() {
        // Test default interface methods
        assertFalse(meituanAdapterService.saveChannelHotel());
        assertTrue(meituanAdapterService.fillCredentialToHotelExtension());
        assertTrue(meituanAdapterService.customizeProcedure().isEmpty());
    }
}
