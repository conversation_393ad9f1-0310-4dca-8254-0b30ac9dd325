package com.derbysoft.next.propertyconnect.channel.task.config;

import com.derbysoft.next.propertyconnect.channel.task.service.mapping.impl.PredictionServiceConfigProperties;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;

import java.lang.annotation.Annotation;
import java.lang.reflect.Method;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * Comprehensive unit tests for S3ClientConfig
 * Tests AWS S3 configuration functionality
 */
@ExtendWith(MockitoExtension.class)
class S3ClientConfigTest {

    @Mock
    private PredictionServiceConfigProperties configProperties;

    private S3ClientConfig s3ClientConfig;

    @BeforeEach
    void setUp() {
        s3ClientConfig = new S3ClientConfig();
    }

    @Test
    void testS3ClientConfig_IsConfigurationClass() {
        // When & Then
        assertTrue(S3ClientConfig.class.isAnnotationPresent(Configuration.class));
    }

    @Test
    void testS3ClientConfig_EnablesConfigurationProperties() {
        // When
        EnableConfigurationProperties annotation = S3ClientConfig.class.getAnnotation(EnableConfigurationProperties.class);

        // Then
        assertNotNull(annotation);
        assertEquals(1, annotation.value().length);
        assertEquals(PredictionServiceConfigProperties.class, annotation.value()[0]);
    }

    @Test
    void testDefualtS3Client_WithValidRegion_ReturnsS3Client() {
        // Given
        when(configProperties.getS3Region()).thenReturn("us-east-1");

        // When
        S3Client result = s3ClientConfig.defualtS3Client(configProperties);

        // Then
        assertNotNull(result);
        verify(configProperties).getS3Region();
    }

    @Test
    void testDefualtS3Client_WithDifferentRegions_ReturnsS3Client() {
        // Given
        String[] regions = {
            "us-east-1",
            "us-west-2", 
            "eu-west-1",
            "ap-southeast-1",
            "ap-northeast-1",
            "cn-northwest-1"
        };

        for (String region : regions) {
            // Given
            when(configProperties.getS3Region()).thenReturn(region);

            // When
            S3Client result = s3ClientConfig.defualtS3Client(configProperties);

            // Then
            assertNotNull(result, "S3Client should be created for region: " + region);
        }
    }

    @Test
    void testDefualtS3Client_WithNullConfigProperties_ThrowsException() {
        // When & Then
        assertThrows(NullPointerException.class, () -> 
            s3ClientConfig.defualtS3Client(null));
    }

    @Test
    void testDefualtS3Client_WithNullRegion_ThrowsException() {
        // Given
        when(configProperties.getS3Region()).thenReturn(null);

        // When & Then
        assertThrows(Exception.class, () -> 
            s3ClientConfig.defualtS3Client(configProperties));
    }

    @Test
    void testDefualtS3Client_WithEmptyRegion_ThrowsException() {
        // Given
        when(configProperties.getS3Region()).thenReturn("");

        // When & Then
        assertThrows(Exception.class, () -> 
            s3ClientConfig.defualtS3Client(configProperties));
    }

    @Test
    void testDefualtS3Client_WithInvalidRegion_CreatesClient() {
        // Given
        when(configProperties.getS3Region()).thenReturn("invalid-region");

        // When
        S3Client result = s3ClientConfig.defualtS3Client(configProperties);

        // Then
        // AWS SDK may accept invalid region strings and create client
        // The validation happens at runtime when making actual calls
        assertNotNull(result);
    }

    @Test
    void testDefualtS3Client_MethodHasBeanAnnotation() throws Exception {
        // Given
        Method method = S3ClientConfig.class.getMethod("defualtS3Client", PredictionServiceConfigProperties.class);

        // When
        Bean beanAnnotation = method.getAnnotation(Bean.class);

        // Then
        assertNotNull(beanAnnotation);
    }

    @Test
    void testDefualtS3Client_MethodReturnType() throws Exception {
        // Given
        Method method = S3ClientConfig.class.getMethod("defualtS3Client", PredictionServiceConfigProperties.class);

        // When
        Class<?> returnType = method.getReturnType();

        // Then
        assertEquals(S3Client.class, returnType);
    }

    @Test
    void testDefualtS3Client_MethodParameterType() throws Exception {
        // Given
        Method method = S3ClientConfig.class.getMethod("defualtS3Client", PredictionServiceConfigProperties.class);

        // When
        Class<?>[] parameterTypes = method.getParameterTypes();

        // Then
        assertEquals(1, parameterTypes.length);
        assertEquals(PredictionServiceConfigProperties.class, parameterTypes[0]);
    }

    @Test
    void testDefualtS3Client_WithProductionRegions_ReturnsValidClient() {
        // Given
        String[] productionRegions = {
            "ap-southeast-1", // Production region from properties
            "cn-northwest-1", // UAT region from properties
            "us-west-2" // Dev region from properties
        };

        for (String region : productionRegions) {
            // Given
            when(configProperties.getS3Region()).thenReturn(region);

            // When
            S3Client result = s3ClientConfig.defualtS3Client(configProperties);

            // Then
            assertNotNull(result, "S3Client should be created for production region: " + region);
        }
    }

    @Test
    void testDefualtS3Client_MultipleCallsWithSameRegion_ReturnsNewInstances() {
        // Given
        when(configProperties.getS3Region()).thenReturn("us-east-1");

        // When
        S3Client client1 = s3ClientConfig.defualtS3Client(configProperties);
        S3Client client2 = s3ClientConfig.defualtS3Client(configProperties);

        // Then
        assertNotNull(client1);
        assertNotNull(client2);
        assertNotSame(client1, client2); // Each call should return a new instance
    }

    @Test
    void testDefualtS3Client_WithWhitespaceRegion_ThrowsException() {
        // Given
        when(configProperties.getS3Region()).thenReturn("   ");

        // When & Then
        assertThrows(Exception.class, () -> 
            s3ClientConfig.defualtS3Client(configProperties));
    }

    @Test
    void testDefualtS3Client_WithCaseSensitiveRegion_CreatesClient() {
        // Given
        when(configProperties.getS3Region()).thenReturn("US-EAST-1");

        // When
        S3Client result = s3ClientConfig.defualtS3Client(configProperties);

        // Then
        // AWS SDK may accept case-insensitive region strings
        assertNotNull(result);
    }

    @Test
    void testDefualtS3Client_ConfigurationPropertiesInteraction() {
        // Given
        when(configProperties.getS3Region()).thenReturn("eu-west-1");

        // When
        s3ClientConfig.defualtS3Client(configProperties);

        // Then
        verify(configProperties, times(1)).getS3Region();
        verifyNoMoreInteractions(configProperties);
    }

    @Test
    void testS3ClientConfig_ClassAnnotations() {
        // When
        Annotation[] annotations = S3ClientConfig.class.getAnnotations();

        // Then
        assertEquals(2, annotations.length);
        
        boolean hasConfiguration = false;
        boolean hasEnableConfigurationProperties = false;
        
        for (Annotation annotation : annotations) {
            if (annotation instanceof Configuration) {
                hasConfiguration = true;
            } else if (annotation instanceof EnableConfigurationProperties) {
                hasEnableConfigurationProperties = true;
            }
        }
        
        assertTrue(hasConfiguration);
        assertTrue(hasEnableConfigurationProperties);
    }

    @Test
    void testS3ClientConfig_MethodCount() {
        // When
        Method[] methods = S3ClientConfig.class.getDeclaredMethods();

        // Then - Check that we have at least the expected method
        assertTrue(methods.length >= 1);

        // Find the defualtS3Client method
        boolean foundMethod = false;
        for (Method method : methods) {
            if ("defualtS3Client".equals(method.getName())) {
                foundMethod = true;
                break;
            }
        }
        assertTrue(foundMethod, "defualtS3Client method should be present");
    }

    @Test
    void testS3ClientConfig_IsPublicClass() {
        // When
        int modifiers = S3ClientConfig.class.getModifiers();

        // Then
        assertTrue(java.lang.reflect.Modifier.isPublic(modifiers));
    }

    @Test
    void testDefualtS3Client_IsPublicMethod() throws Exception {
        // Given
        Method method = S3ClientConfig.class.getMethod("defualtS3Client", PredictionServiceConfigProperties.class);

        // When
        int modifiers = method.getModifiers();

        // Then
        assertTrue(java.lang.reflect.Modifier.isPublic(modifiers));
    }

    @Test
    void testDefualtS3Client_ThreadSafety() {
        // Given
        when(configProperties.getS3Region()).thenReturn("us-east-1");
        
        // When
        Thread[] threads = new Thread[10];
        S3Client[] results = new S3Client[10];
        Exception[] exceptions = new Exception[10];

        for (int i = 0; i < 10; i++) {
            final int index = i;
            threads[i] = new Thread(() -> {
                try {
                    results[index] = s3ClientConfig.defualtS3Client(configProperties);
                } catch (Exception e) {
                    exceptions[index] = e;
                }
            });
            threads[i].start();
        }

        // Wait for all threads to complete
        for (Thread thread : threads) {
            try {
                thread.join();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }

        // Then
        for (int i = 0; i < 10; i++) {
            assertNull(exceptions[i], "Thread " + i + " should not have thrown an exception");
            assertNotNull(results[i], "Thread " + i + " should have returned a result");
        }
    }

    @Test
    void testDefualtS3Client_WithMockConfigProperties_CallsGetS3Region() {
        // Given
        PredictionServiceConfigProperties mockConfig = mock(PredictionServiceConfigProperties.class);
        when(mockConfig.getS3Region()).thenReturn("ap-southeast-1");

        // When
        S3Client result = s3ClientConfig.defualtS3Client(mockConfig);

        // Then
        assertNotNull(result);
        verify(mockConfig).getS3Region();
    }

    @Test
    void testDefualtS3Client_WithRealConfigProperties_WorksCorrectly() {
        // Given
        PredictionServiceConfigProperties realConfig = new PredictionServiceConfigProperties();
        realConfig.setS3Region("us-east-1");

        // When
        S3Client result = s3ClientConfig.defualtS3Client(realConfig);

        // Then
        assertNotNull(result);
    }

    @Test
    void testS3ClientConfig_PackageStructure() {
        // When
        String packageName = S3ClientConfig.class.getPackage().getName();

        // Then
        assertEquals("com.derbysoft.next.propertyconnect.channel.task.config", packageName);
    }

    @Test
    void testS3ClientConfig_ClassSimpleName() {
        // When
        String className = S3ClientConfig.class.getSimpleName();

        // Then
        assertEquals("S3ClientConfig", className);
    }

    @Test
    void testDefualtS3Client_MethodNameTypo() throws Exception {
        // Given - Note: The method name has a typo "defualt" instead of "default"
        Method method = S3ClientConfig.class.getMethod("defualtS3Client", PredictionServiceConfigProperties.class);

        // When & Then
        assertEquals("defualtS3Client", method.getName());
        // This test documents the typo in the method name
    }

    @Test
    void testS3ClientConfig_ConstructorExists() {
        // When & Then
        assertDoesNotThrow(() -> new S3ClientConfig());
    }

    @Test
    void testS3ClientConfig_NoFieldsDeclared() {
        // When
        java.lang.reflect.Field[] fields = S3ClientConfig.class.getDeclaredFields();

        // Then
        assertEquals(0, fields.length);
    }
}
