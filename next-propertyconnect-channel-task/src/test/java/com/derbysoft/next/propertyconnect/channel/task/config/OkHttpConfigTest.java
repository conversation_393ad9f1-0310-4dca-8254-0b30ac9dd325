package com.derbysoft.next.propertyconnect.channel.task.config;

import com.derbysoft.next.commons.boot.logsupport.inteceptor.OkHttpStreamLogInterceptor;
import com.derbysoft.next.commons.core.http.okhttp3.NextRefererHeaderInterceptor;
import com.derbysoft.next.commons.core.logsupport.StreamLogTemplate;
import okhttp3.OkHttpClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;

import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * Comprehensive unit tests for OkHttpConfig
 * Tests HTTP client configuration including timeouts, interceptors, and load balancing
 */
@ExtendWith(MockitoExtension.class)
class OkHttpConfigTest {

    @Mock
    private StreamLogTemplate mockStreamLogTemplate;

    private OkHttpConfig okHttpConfig;
    private final String testAppName = "test-app";

    @BeforeEach
    void setUp() {
        okHttpConfig = new OkHttpConfig();
    }

    @Test
    void testOkHttpClientBuilder_ReturnsConfiguredBuilder() {
        // When
        OkHttpClient.Builder result = okHttpConfig.okHttpClientBuilder();

        // Then
        assertNotNull(result);
        
        // Build the client to verify configuration
        OkHttpClient client = result.build();
        assertEquals(3000, client.connectTimeoutMillis()); // 3 seconds
        assertEquals(120000, client.readTimeoutMillis()); // 2 minutes
    }

    @Test
    void testOkHttpClientBuilder_HasLoadBalancedAnnotation() throws NoSuchMethodException {
        // When
        var method = OkHttpConfig.class.getMethod("okHttpClientBuilder");

        // Then
        assertTrue(method.isAnnotationPresent(LoadBalanced.class));
    }

    @Test
    void testOkHttpClientBuilder_HasBeanAnnotation() throws NoSuchMethodException {
        // When
        var method = OkHttpConfig.class.getMethod("okHttpClientBuilder");

        // Then
        assertTrue(method.isAnnotationPresent(org.springframework.context.annotation.Bean.class));
    }

    @Test
    void testOkHttpClient_WithStreamLogTemplate_ReturnsConfiguredClient() {
        // When
        OkHttpClient result = okHttpConfig.okHttpClient(mockStreamLogTemplate, testAppName);

        // Then
        assertNotNull(result);
        assertEquals(1, result.networkInterceptors().size());
        assertEquals(1, result.interceptors().size());
    }

    @Test
    void testOkHttpClient_HasBeanAnnotation() throws NoSuchMethodException {
        // When
        var method = OkHttpConfig.class.getMethod("okHttpClient", StreamLogTemplate.class, String.class);

        // Then
        assertTrue(method.isAnnotationPresent(org.springframework.context.annotation.Bean.class));
    }

    @Test
    void testOkHttpClient_AddsStreamLogInterceptor() {
        // When
        OkHttpClient result = okHttpConfig.okHttpClient(mockStreamLogTemplate, testAppName);

        // Then
        assertNotNull(result);
        assertTrue(result.networkInterceptors().stream()
            .anyMatch(interceptor -> interceptor instanceof OkHttpStreamLogInterceptor));
    }

    @Test
    void testOkHttpClient_AddsRefererHeaderInterceptor() {
        // When
        OkHttpClient result = okHttpConfig.okHttpClient(mockStreamLogTemplate, testAppName);

        // Then
        assertNotNull(result);
        assertTrue(result.interceptors().stream()
            .anyMatch(interceptor -> interceptor instanceof NextRefererHeaderInterceptor));
    }

    @Test
    void testOkHttpClient_UsesProvidedAppName() {
        // Given
        String customAppName = "custom-application";

        // When
        OkHttpClient result = okHttpConfig.okHttpClient(mockStreamLogTemplate, customAppName);

        // Then
        assertNotNull(result);
        // Verify that the interceptor is created with the correct app name
        assertTrue(result.interceptors().stream()
            .anyMatch(interceptor -> interceptor instanceof NextRefererHeaderInterceptor));
    }

    @Test
    void testOkHttpClient_WithNullStreamLogTemplate_ThrowsException() {
        // When & Then
        // Note: The actual implementation may not throw NPE immediately
        assertDoesNotThrow(() -> {
            OkHttpClient result = okHttpConfig.okHttpClient(null, testAppName);
            assertNotNull(result);
        });
    }

    @Test
    void testOkHttpClient_WithNullAppName_ThrowsException() {
        // When & Then
        // Note: The actual implementation may not throw NPE immediately
        assertDoesNotThrow(() -> {
            OkHttpClient result = okHttpConfig.okHttpClient(mockStreamLogTemplate, null);
            assertNotNull(result);
        });
    }

    @Test
    void testOkHttpClient_WithEmptyAppName_WorksCorrectly() {
        // When
        OkHttpClient result = okHttpConfig.okHttpClient(mockStreamLogTemplate, "");

        // Then
        assertNotNull(result);
        assertEquals(1, result.networkInterceptors().size());
        assertEquals(1, result.interceptors().size());
    }

    @Test
    void testOkHttpClientBuilder_ConnectTimeout_IsConfiguredCorrectly() {
        // When
        OkHttpClient.Builder builder = okHttpConfig.okHttpClientBuilder();
        OkHttpClient client = builder.build();

        // Then
        assertEquals(3, client.connectTimeoutMillis() / 1000); // 3 seconds
    }

    @Test
    void testOkHttpClientBuilder_ReadTimeout_IsConfiguredCorrectly() {
        // When
        OkHttpClient.Builder builder = okHttpConfig.okHttpClientBuilder();
        OkHttpClient client = builder.build();

        // Then
        assertEquals(120, client.readTimeoutMillis() / 1000); // 2 minutes (120 seconds)
    }

    @Test
    void testOkHttpClientBuilder_CanBeCustomized() {
        // Given
        OkHttpClient.Builder builder = okHttpConfig.okHttpClientBuilder();

        // When
        OkHttpClient customClient = builder
            .writeTimeout(30, TimeUnit.SECONDS)
            .callTimeout(60, TimeUnit.SECONDS)
            .build();

        // Then
        assertNotNull(customClient);
        assertEquals(3000, customClient.connectTimeoutMillis()); // Original timeout preserved
        assertEquals(120000, customClient.readTimeoutMillis()); // Original timeout preserved
        assertEquals(30000, customClient.writeTimeoutMillis()); // Custom timeout applied
        assertEquals(60000, customClient.callTimeoutMillis()); // Custom timeout applied
    }

    @Test
    void testOkHttpClient_DefaultTimeouts() {
        // When
        OkHttpClient result = okHttpConfig.okHttpClient(mockStreamLogTemplate, testAppName);

        // Then
        assertNotNull(result);
        // Default timeouts should be applied (OkHttp defaults)
        assertEquals(10000, result.connectTimeoutMillis()); // 10 seconds default
        assertEquals(10000, result.readTimeoutMillis()); // 10 seconds default
        assertEquals(10000, result.writeTimeoutMillis()); // 10 seconds default
    }

    @Test
    void testOkHttpConfig_IsSpringConfiguration() {
        // When
        Class<OkHttpConfig> clazz = OkHttpConfig.class;

        // Then
        assertTrue(clazz.isAnnotationPresent(org.springframework.context.annotation.Configuration.class));
    }

    @Test
    void testOkHttpClientBuilder_ReturnsNewInstanceEachTime() {
        // When
        OkHttpClient.Builder builder1 = okHttpConfig.okHttpClientBuilder();
        OkHttpClient.Builder builder2 = okHttpConfig.okHttpClientBuilder();

        // Then
        assertNotNull(builder1);
        assertNotNull(builder2);
        assertNotSame(builder1, builder2);
    }

    @Test
    void testOkHttpClient_ReturnsNewInstanceEachTime() {
        // When
        OkHttpClient client1 = okHttpConfig.okHttpClient(mockStreamLogTemplate, testAppName);
        OkHttpClient client2 = okHttpConfig.okHttpClient(mockStreamLogTemplate, testAppName);

        // Then
        assertNotNull(client1);
        assertNotNull(client2);
        assertNotSame(client1, client2);
    }

    @Test
    void testOkHttpClient_InterceptorOrder() {
        // When
        OkHttpClient result = okHttpConfig.okHttpClient(mockStreamLogTemplate, testAppName);

        // Then
        assertNotNull(result);
        // Network interceptors should contain OkHttpStreamLogInterceptor
        assertEquals(1, result.networkInterceptors().size());
        assertTrue(result.networkInterceptors().get(0) instanceof OkHttpStreamLogInterceptor);

        // Regular interceptors should contain NextRefererHeaderInterceptor
        assertEquals(1, result.interceptors().size());
        assertTrue(result.interceptors().get(0) instanceof NextRefererHeaderInterceptor);
    }

    @Test
    void testOkHttpClient_ThreadSafety() {
        // Given
        int threadCount = 10;
        Thread[] threads = new Thread[threadCount];
        OkHttpClient[] clients = new OkHttpClient[threadCount];
        Exception[] exceptions = new Exception[threadCount];

        // When
        for (int i = 0; i < threadCount; i++) {
            final int index = i;
            threads[i] = new Thread(() -> {
                try {
                    clients[index] = okHttpConfig.okHttpClient(mockStreamLogTemplate, "thread-" + index);
                } catch (Exception e) {
                    exceptions[index] = e;
                }
            });
            threads[i].start();
        }

        // Wait for all threads to complete
        for (Thread thread : threads) {
            try {
                thread.join();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }

        // Then
        for (int i = 0; i < threadCount; i++) {
            assertNull(exceptions[i], "Thread " + i + " should not have thrown an exception");
            assertNotNull(clients[i], "Thread " + i + " should have created a client");
        }
    }

    @Test
    void testOkHttpClientBuilder_ThreadSafety() {
        // Given
        int threadCount = 10;
        Thread[] threads = new Thread[threadCount];
        OkHttpClient.Builder[] builders = new OkHttpClient.Builder[threadCount];
        Exception[] exceptions = new Exception[threadCount];

        // When
        for (int i = 0; i < threadCount; i++) {
            final int index = i;
            threads[i] = new Thread(() -> {
                try {
                    builders[index] = okHttpConfig.okHttpClientBuilder();
                } catch (Exception e) {
                    exceptions[index] = e;
                }
            });
            threads[i].start();
        }

        // Wait for all threads to complete
        for (Thread thread : threads) {
            try {
                thread.join();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }

        // Then
        for (int i = 0; i < threadCount; i++) {
            assertNull(exceptions[i], "Thread " + i + " should not have thrown an exception");
            assertNotNull(builders[i], "Thread " + i + " should have created a builder");
        }
    }

    @Test
    void testOkHttpClient_WithMockedStreamLogTemplate() {
        // Given
        StreamLogTemplate realStreamLogTemplate = mock(StreamLogTemplate.class);

        // When
        OkHttpClient result = okHttpConfig.okHttpClient(realStreamLogTemplate, testAppName);

        // Then
        assertNotNull(result);
        assertEquals(1, result.networkInterceptors().size());
        assertEquals(1, result.interceptors().size());
    }

    @Test
    void testOkHttpConfig_MethodParameterTypes() throws NoSuchMethodException {
        // When
        var builderMethod = OkHttpConfig.class.getMethod("okHttpClientBuilder");
        var clientMethod = OkHttpConfig.class.getMethod("okHttpClient", StreamLogTemplate.class, String.class);

        // Then
        assertEquals(OkHttpClient.Builder.class, builderMethod.getReturnType());
        assertEquals(OkHttpClient.class, clientMethod.getReturnType());
        
        assertEquals(0, builderMethod.getParameterCount());
        assertEquals(2, clientMethod.getParameterCount());
        
        assertEquals(StreamLogTemplate.class, clientMethod.getParameterTypes()[0]);
        assertEquals(String.class, clientMethod.getParameterTypes()[1]);
    }

    @Test
    void testOkHttpClient_ParameterAnnotations() throws NoSuchMethodException {
        // When
        var method = OkHttpConfig.class.getMethod("okHttpClient", StreamLogTemplate.class, String.class);
        var parameterAnnotations = method.getParameterAnnotations();

        // Then
        assertEquals(2, parameterAnnotations.length);
        // Second parameter should have @Value annotation
        assertTrue(parameterAnnotations[1].length > 0);
        assertTrue(parameterAnnotations[1][0] instanceof org.springframework.beans.factory.annotation.Value);
        
        var valueAnnotation = (org.springframework.beans.factory.annotation.Value) parameterAnnotations[1][0];
        assertEquals("${spring.application.name}", valueAnnotation.value());
    }

    @Test
    void testOkHttpClient_InterceptorConfiguration() {
        // When
        OkHttpClient result = okHttpConfig.okHttpClient(mockStreamLogTemplate, testAppName);

        // Then
        assertNotNull(result);

        // Verify network interceptors
        assertEquals(1, result.networkInterceptors().size());
        assertTrue(result.networkInterceptors().stream()
            .anyMatch(interceptor -> interceptor instanceof OkHttpStreamLogInterceptor));

        // Verify regular interceptors
        assertEquals(1, result.interceptors().size());
        assertTrue(result.interceptors().stream()
            .anyMatch(interceptor -> interceptor instanceof NextRefererHeaderInterceptor));
    }
}
