package com.derbysoft.next.propertyconnect.channel.task.util;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Comprehensive unit tests for SubServiceError
 * Tests error handling data structure for sub-service operations
 */
@ExtendWith(MockitoExtension.class)
class SubServiceErrorTest {

    @Test
    void testConstructor_ValidParameters_CreatesCorrectly() {
        // Given
        String target = "UserService";
        String errorCode = "USER_001";
        String errorMessage = "User not found";

        // When
        SubServiceError error = new SubServiceError(target, errorCode, errorMessage);

        // Then
        assertEquals(target, error.getTarget());
        assertEquals(errorCode, error.getErrorCode());
        assertEquals(errorMessage, error.getErrorMessage());
    }

    @Test
    void testConstructor_NullParameters_AcceptsNullValues() {
        // When
        SubServiceError error = new SubServiceError(null, null, null);

        // Then
        assertNull(error.getTarget());
        assertNull(error.getErrorCode());
        assertNull(error.getErrorMessage());
    }

    @Test
    void testSetTarget_ValidValue_SetsCorrectly() {
        // Given
        SubServiceError error = new SubServiceError("Initial", "CODE", "Message");
        String newTarget = "UpdatedService";

        // When
        error.setTarget(newTarget);

        // Then
        assertEquals(newTarget, error.getTarget());
    }

    @Test
    void testSetTarget_NullValue_SetsNull() {
        // Given
        SubServiceError error = new SubServiceError("Initial", "CODE", "Message");

        // When
        error.setTarget(null);

        // Then
        assertNull(error.getTarget());
    }

    @Test
    void testSetErrorCode_ValidValue_SetsCorrectly() {
        // Given
        SubServiceError error = new SubServiceError("Service", "INITIAL", "Message");
        String newErrorCode = "UPDATED_001";

        // When
        error.setErrorCode(newErrorCode);

        // Then
        assertEquals(newErrorCode, error.getErrorCode());
    }

    @Test
    void testSetErrorCode_NullValue_SetsNull() {
        // Given
        SubServiceError error = new SubServiceError("Service", "INITIAL", "Message");

        // When
        error.setErrorCode(null);

        // Then
        assertNull(error.getErrorCode());
    }

    @Test
    void testSetErrorMessage_ValidValue_SetsCorrectly() {
        // Given
        SubServiceError error = new SubServiceError("Service", "CODE", "Initial message");
        String newErrorMessage = "Updated error message";

        // When
        error.setErrorMessage(newErrorMessage);

        // Then
        assertEquals(newErrorMessage, error.getErrorMessage());
    }

    @Test
    void testSetErrorMessage_NullValue_SetsNull() {
        // Given
        SubServiceError error = new SubServiceError("Service", "CODE", "Initial message");

        // When
        error.setErrorMessage(null);

        // Then
        assertNull(error.getErrorMessage());
    }

    @Test
    void testEquals_SameObject_ReturnsTrue() {
        // Given
        SubServiceError error = new SubServiceError("Service", "CODE", "Message");

        // When & Then
        assertEquals(error, error);
        assertTrue(error.equals(error));
    }

    @Test
    void testEquals_EqualObjects_ReturnsTrue() {
        // Given
        SubServiceError error1 = new SubServiceError("Service", "CODE", "Message");
        SubServiceError error2 = new SubServiceError("Service", "CODE", "Message");

        // When & Then
        assertEquals(error1, error2);
        assertTrue(error1.equals(error2));
        assertTrue(error2.equals(error1));
    }

    @Test
    void testEquals_DifferentTarget_ReturnsFalse() {
        // Given
        SubServiceError error1 = new SubServiceError("Service1", "CODE", "Message");
        SubServiceError error2 = new SubServiceError("Service2", "CODE", "Message");

        // When & Then
        assertNotEquals(error1, error2);
        assertFalse(error1.equals(error2));
    }

    @Test
    void testEquals_DifferentErrorCode_ReturnsFalse() {
        // Given
        SubServiceError error1 = new SubServiceError("Service", "CODE1", "Message");
        SubServiceError error2 = new SubServiceError("Service", "CODE2", "Message");

        // When & Then
        assertNotEquals(error1, error2);
        assertFalse(error1.equals(error2));
    }

    @Test
    void testEquals_DifferentErrorMessage_ReturnsFalse() {
        // Given
        SubServiceError error1 = new SubServiceError("Service", "CODE", "Message1");
        SubServiceError error2 = new SubServiceError("Service", "CODE", "Message2");

        // When & Then
        assertNotEquals(error1, error2);
        assertFalse(error1.equals(error2));
    }

    @Test
    void testEquals_NullObject_ReturnsFalse() {
        // Given
        SubServiceError error = new SubServiceError("Service", "CODE", "Message");

        // When & Then
        assertNotEquals(error, null);
        assertFalse(error.equals(null));
    }

    @Test
    void testEquals_DifferentClass_ReturnsFalse() {
        // Given
        SubServiceError error = new SubServiceError("Service", "CODE", "Message");
        String otherObject = "Not a SubServiceError";

        // When & Then
        assertNotEquals(error, otherObject);
        assertFalse(error.equals(otherObject));
    }

    @Test
    void testEquals_AllNullFields_ReturnsTrue() {
        // Given
        SubServiceError error1 = new SubServiceError(null, null, null);
        SubServiceError error2 = new SubServiceError(null, null, null);

        // When & Then
        assertEquals(error1, error2);
        assertTrue(error1.equals(error2));
    }

    @Test
    void testEquals_MixedNullFields_WorksCorrectly() {
        // Given
        SubServiceError error1 = new SubServiceError(null, "CODE", "Message");
        SubServiceError error2 = new SubServiceError(null, "CODE", "Message");
        SubServiceError error3 = new SubServiceError("Service", "CODE", "Message");

        // When & Then
        assertEquals(error1, error2);
        assertNotEquals(error1, error3);
        assertNotEquals(error2, error3);
    }

    @Test
    void testHashCode_EqualObjects_SameHashCode() {
        // Given
        SubServiceError error1 = new SubServiceError("Service", "CODE", "Message");
        SubServiceError error2 = new SubServiceError("Service", "CODE", "Message");

        // When & Then
        assertEquals(error1.hashCode(), error2.hashCode());
    }

    @Test
    void testHashCode_DifferentObjects_DifferentHashCode() {
        // Given
        SubServiceError error1 = new SubServiceError("Service1", "CODE", "Message");
        SubServiceError error2 = new SubServiceError("Service2", "CODE", "Message");

        // When & Then
        assertNotEquals(error1.hashCode(), error2.hashCode());
    }

    @Test
    void testHashCode_NullFields_HandlesCorrectly() {
        // Given
        SubServiceError error1 = new SubServiceError(null, null, null);
        SubServiceError error2 = new SubServiceError(null, null, null);

        // When & Then
        assertEquals(error1.hashCode(), error2.hashCode());
    }

    @Test
    void testHashCode_Consistency_SameObjectSameHashCode() {
        // Given
        SubServiceError error = new SubServiceError("Service", "CODE", "Message");

        // When
        int hashCode1 = error.hashCode();
        int hashCode2 = error.hashCode();

        // Then
        assertEquals(hashCode1, hashCode2);
    }

    @Test
    void testGettersAndSetters_ChainedOperations_WorkCorrectly() {
        // Given
        SubServiceError error = new SubServiceError("Initial", "INIT", "Initial message");

        // When
        error.setTarget("Updated");
        error.setErrorCode("UPD_001");
        error.setErrorMessage("Updated message");

        // Then
        assertEquals("Updated", error.getTarget());
        assertEquals("UPD_001", error.getErrorCode());
        assertEquals("Updated message", error.getErrorMessage());
    }

    @Test
    void testSubServiceError_RealWorldScenario_DatabaseError() {
        // Given
        String target = "DatabaseService";
        String errorCode = "DB_CONNECTION_FAILED";
        String errorMessage = "Unable to establish connection to database server";

        // When
        SubServiceError error = new SubServiceError(target, errorCode, errorMessage);

        // Then
        assertEquals("DatabaseService", error.getTarget());
        assertEquals("DB_CONNECTION_FAILED", error.getErrorCode());
        assertEquals("Unable to establish connection to database server", error.getErrorMessage());
    }

    @Test
    void testSubServiceError_RealWorldScenario_APIError() {
        // Given
        String target = "ExternalAPIService";
        String errorCode = "API_RATE_LIMIT_EXCEEDED";
        String errorMessage = "Rate limit exceeded: 1000 requests per hour";

        // When
        SubServiceError error = new SubServiceError(target, errorCode, errorMessage);

        // Then
        assertEquals("ExternalAPIService", error.getTarget());
        assertEquals("API_RATE_LIMIT_EXCEEDED", error.getErrorCode());
        assertEquals("Rate limit exceeded: 1000 requests per hour", error.getErrorMessage());
    }

    @Test
    void testSubServiceError_RealWorldScenario_ValidationError() {
        // Given
        String target = "ValidationService";
        String errorCode = "INVALID_INPUT";
        String errorMessage = "Email format is invalid";

        // When
        SubServiceError error = new SubServiceError(target, errorCode, errorMessage);

        // Then
        assertEquals("ValidationService", error.getTarget());
        assertEquals("INVALID_INPUT", error.getErrorCode());
        assertEquals("Email format is invalid", error.getErrorMessage());
    }

    @Test
    void testSubServiceError_EmptyStrings_HandlesCorrectly() {
        // Given
        String target = "";
        String errorCode = "";
        String errorMessage = "";

        // When
        SubServiceError error = new SubServiceError(target, errorCode, errorMessage);

        // Then
        assertEquals("", error.getTarget());
        assertEquals("", error.getErrorCode());
        assertEquals("", error.getErrorMessage());
    }

    @Test
    void testSubServiceError_LongStrings_HandlesCorrectly() {
        // Given
        String target = "VeryLongServiceNameThatExceedsNormalLengthExpectations";
        String errorCode = "VERY_LONG_ERROR_CODE_THAT_MIGHT_BE_USED_IN_SOME_SYSTEMS";
        String errorMessage = "This is a very long error message that might contain detailed information about what went wrong in the system and how to potentially resolve the issue";

        // When
        SubServiceError error = new SubServiceError(target, errorCode, errorMessage);

        // Then
        assertEquals(target, error.getTarget());
        assertEquals(errorCode, error.getErrorCode());
        assertEquals(errorMessage, error.getErrorMessage());
    }

    @Test
    void testSubServiceError_SpecialCharacters_HandlesCorrectly() {
        // Given
        String target = "Service@#$%";
        String errorCode = "ERR_001!@#";
        String errorMessage = "Error with special chars: <>?\"{}[]|\\";

        // When
        SubServiceError error = new SubServiceError(target, errorCode, errorMessage);

        // Then
        assertEquals(target, error.getTarget());
        assertEquals(errorCode, error.getErrorCode());
        assertEquals(errorMessage, error.getErrorMessage());
    }

    @Test
    void testSubServiceError_UnicodeCharacters_HandlesCorrectly() {
        // Given
        String target = "服务";
        String errorCode = "错误_001";
        String errorMessage = "Unicode error message: 测试错误信息";

        // When
        SubServiceError error = new SubServiceError(target, errorCode, errorMessage);

        // Then
        assertEquals(target, error.getTarget());
        assertEquals(errorCode, error.getErrorCode());
        assertEquals(errorMessage, error.getErrorMessage());
    }
}
