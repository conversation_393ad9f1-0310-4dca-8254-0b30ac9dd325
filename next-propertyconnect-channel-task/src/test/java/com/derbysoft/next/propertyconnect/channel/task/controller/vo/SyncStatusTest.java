package com.derbysoft.next.propertyconnect.channel.task.controller.vo;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for SyncStatus enum
 */
class SyncStatusTest {

    @Test
    void testSyncStatusValues() {
        SyncStatus[] values = SyncStatus.values();

        assertEquals(7, values.length);
        assertEquals(SyncStatus.DRAFT, values[0]);
        assertEquals(SyncStatus.PREPROCESS_FAILED, values[1]);
        assertEquals(SyncStatus.SUBMITTED, values[2]);
        assertEquals(SyncStatus.SUBMIT_FAILED, values[3]);
        assertEquals(SyncStatus.SYNCED, values[4]);
        assertEquals(SyncStatus.SYNC_FAILED, values[5]);
        assertEquals(SyncStatus.IGNORED, values[6]);
    }

    @Test
    void testIsSuccess_SyncedStatus() {
        assertTrue(SyncStatus.SYNCED.isSuccess());
    }

    @Test
    void testIsSuccess_IgnoredStatus() {
        assertTrue(SyncStatus.IGNORED.isSuccess());
    }

    @Test
    void testIsSuccess_DraftStatus() {
        assertFalse(SyncStatus.DRAFT.isSuccess());
    }

    @Test
    void testIsSuccess_PreprocessFailedStatus() {
        assertFalse(SyncStatus.PREPROCESS_FAILED.isSuccess());
    }

    @Test
    void testIsSuccess_SubmittedStatus() {
        assertFalse(SyncStatus.SUBMITTED.isSuccess());
    }

    @Test
    void testIsSuccess_SubmitFailedStatus() {
        assertFalse(SyncStatus.SUBMIT_FAILED.isSuccess());
    }

    @Test
    void testIsSuccess_SyncFailedStatus() {
        assertFalse(SyncStatus.SYNC_FAILED.isSuccess());
    }

    @Test
    void testValueOf() {
        assertEquals(SyncStatus.DRAFT, SyncStatus.valueOf("DRAFT"));
        assertEquals(SyncStatus.SYNCED, SyncStatus.valueOf("SYNCED"));
        assertEquals(SyncStatus.IGNORED, SyncStatus.valueOf("IGNORED"));
        assertEquals(SyncStatus.PREPROCESS_FAILED, SyncStatus.valueOf("PREPROCESS_FAILED"));
        assertEquals(SyncStatus.SUBMITTED, SyncStatus.valueOf("SUBMITTED"));
        assertEquals(SyncStatus.SUBMIT_FAILED, SyncStatus.valueOf("SUBMIT_FAILED"));
        assertEquals(SyncStatus.SYNC_FAILED, SyncStatus.valueOf("SYNC_FAILED"));
    }

    @Test
    void testValueOf_InvalidValue() {
        assertThrows(IllegalArgumentException.class, () -> {
            SyncStatus.valueOf("INVALID_STATUS");
        });
    }

    @Test
    void testToString() {
        assertEquals("DRAFT", SyncStatus.DRAFT.toString());
        assertEquals("SYNCED", SyncStatus.SYNCED.toString());
        assertEquals("IGNORED", SyncStatus.IGNORED.toString());
        assertEquals("PREPROCESS_FAILED", SyncStatus.PREPROCESS_FAILED.toString());
        assertEquals("SUBMITTED", SyncStatus.SUBMITTED.toString());
        assertEquals("SUBMIT_FAILED", SyncStatus.SUBMIT_FAILED.toString());
        assertEquals("SYNC_FAILED", SyncStatus.SYNC_FAILED.toString());
    }

    @Test
    void testSuccessStatuses() {
        // Test all success statuses
        SyncStatus[] successStatuses = {SyncStatus.SYNCED, SyncStatus.IGNORED};
        
        for (SyncStatus status : successStatuses) {
            assertTrue(status.isSuccess(), 
                String.format("Status %s should be considered successful", status));
        }
    }

    @Test
    void testFailureStatuses() {
        // Test all failure statuses
        SyncStatus[] failureStatuses = {
            SyncStatus.DRAFT, 
            SyncStatus.PREPROCESS_FAILED, 
            SyncStatus.SUBMITTED, 
            SyncStatus.SUBMIT_FAILED, 
            SyncStatus.SYNC_FAILED
        };
        
        for (SyncStatus status : failureStatuses) {
            assertFalse(status.isSuccess(), 
                String.format("Status %s should not be considered successful", status));
        }
    }
}
