package com.derbysoft.next.propertyconnect.channel.task.task.arirefresh;

import com.derbysoft.next.propertyconnect.channel.task.task.hotelsetup.HotelSetupConfigurationProperties;
import com.derbysoft.schedulecenter.rpc.client.HttpScheduleCenterService;
import com.derbysoft.schedulecenter.rpc.protocol.Task;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ARIRefreshServiceTest {

    @Mock
    private AriRefreshTask.AriRefreshTaskScheduler mockScheduler;
    @Mock
    private AriRefreshTask.AriRefreshTaskExecutor mockExecutor;
    @Mock
    private HttpScheduleCenterService mockScheduleCenterService;
    @Mock
    private AriRefreshConfigurationProperties ariRefreshConfigurationProperties;
    @Mock
    private HotelSetupConfigurationProperties hotelSetupConfigurationProperties;

    private ARIRefreshService ariRefreshServiceUnderTest;

    @Captor
    private ArgumentCaptor<Task> tasksArgumentCaptor;

    @BeforeEach
    void setUp() {
        ariRefreshServiceUnderTest = new ARIRefreshService(mockScheduler, mockExecutor, mockScheduleCenterService, ariRefreshConfigurationProperties, hotelSetupConfigurationProperties);
    }

    @Test
    void testSubmitARIRefresh() {
        String supplier = "Supplier";
        String channel = "Channel";
        String hotelId = "Hotel123";

        when(hotelSetupConfigurationProperties.allowARIRefresh(channel)).thenReturn(true);
        ariRefreshServiceUnderTest.submitARIRefresh(supplier, channel, Collections.singletonList(hotelId));

        verify(mockScheduler).afterExecute(any(HttpScheduleCenterService.class), tasksArgumentCaptor.capture());

        var capturedTask = tasksArgumentCaptor.getValue().getParametersMap();

        assertEquals(supplier, capturedTask.get("supplier"));
        assertEquals(channel, capturedTask.get("channel"));
        assertEquals(Collections.singletonList(hotelId), capturedTask.get("hotels"));
    }

    @Test
    void testTriggerARIRefresh() {
        // Setup
        // Run the test
        ariRefreshServiceUnderTest.triggerARIRefresh("supplier", "channel", List.of("value"), "startDate", "endDate");

        // Verify the results
        verify(mockExecutor).doExecute(any(Task.class));
    }
}
