package com.derbysoft.next.propertyconnect.channel.task.service.impl.groovy;

import com.alibaba.fastjson2.JSONObject;
import com.derbysoft.next.propertyconenct.channel.common.exception.BusinessAuthorizeException;
import com.derbysoft.next.propertyconenct.channel.common.exception.BusinessException;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelProductsDTO;
import com.derbysoft.next.propertyconnect.channel.task.service.AccountSettingService;
import com.derbysoft.next.propertyconnect.channel.task.service.impl.expedia.ExpediaClient;
import com.derbysoft.next.propertyconnect.channel.task.service.impl.expedia.ExpediaProductsServiceImpl;
import feign.FeignException;
import feign.Response;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Comprehensive unit tests for ExpediaProductsServiceImpl Groovy script
 * Tests product retrieval, parallel processing, error handling, and field mappings
 */
@ExtendWith(MockitoExtension.class)
class ExpediaProductsServiceImplTest {

    @Mock
    private AccountSettingService accountSettingService;

    @Mock
    private ExpediaClient expediaClient;

    @Mock
    private FeignException feignException;

    private ExpediaProductsServiceImpl expediaService;

    @BeforeEach
    void setUp() {
        expediaService = new ExpediaProductsServiceImpl(accountSettingService, expediaClient);
    }

    @Test
    void testChannel_ReturnsCorrectChannelName() {
        // When
        String result = expediaService.channel();

        // Then
        assertEquals("EXPEDIA", result);
    }

    @Test
    void testGetChannelProducts_WithValidData_ReturnsChannelProductsDTO() {
        // Given
        String channelId = "EXPEDIA";
        String channelHotelId = "HOTEL_123";
        
        Map<String, Object> accountSettings = new HashMap<>();
        accountSettings.put("username", "testuser");
        accountSettings.put("password", "testpass");
        
        when(accountSettingService.getAccountSettings(channelId, channelHotelId))
                .thenReturn(accountSettings);

        // Mock hotel response
        Map<String, Object> hotelResponse = new HashMap<>();
        Map<String, Object> hotelEntity = new HashMap<>();
        hotelEntity.put("resourceId", "EXP_HOTEL_123");
        hotelEntity.put("name", "Test Hotel");
        hotelResponse.put("entity", hotelEntity);
        
        when(expediaClient.getHotel(anyString(), eq(channelHotelId)))
                .thenReturn(hotelResponse);

        // Mock room types response
        List<Map<String, Object>> roomTypes = new ArrayList<>();
        Map<String, Object> room = new HashMap<>();
        room.put("resourceId", "ROOM_001");
        Map<String, Object> roomName = new HashMap<>();
        roomName.put("value", "Standard Room");
        room.put("name", roomName);
        
        Map<String, Object> maxOccupancy = new HashMap<>();
        maxOccupancy.put("total", 2);
        room.put("maxOccupancy", maxOccupancy);
        
        List<Map<String, Object>> standardBedding = new ArrayList<>();
        Map<String, Object> bedding = new HashMap<>();
        List<Map<String, Object>> options = new ArrayList<>();
        Map<String, Object> option = new HashMap<>();
        option.put("type", "KING");
        options.add(option);
        bedding.put("option", options);
        standardBedding.add(bedding);
        room.put("standardBedding", standardBedding);
        
        roomTypes.add(room);
        
        Map<String, Object> roomTypesResponse = new HashMap<>();
        roomTypesResponse.put("entity", roomTypes);
        
        when(expediaClient.getRoomType(anyString(), eq("EXP_HOTEL_123")))
                .thenReturn(roomTypesResponse);

        // Mock rate plans response
        List<Map<String, Object>> ratePlans = new ArrayList<>();
        Map<String, Object> ratePlan = new HashMap<>();
        ratePlan.put("resourceId", "RATE_001");
        ratePlan.put("name", "Best Available Rate");
        
        List<Map<String, Object>> distributionRules = new ArrayList<>();
        Map<String, Object> distributionRule = new HashMap<>();
        distributionRule.put("expediaId", "EXP_RATE_001");
        distributionRule.put("manageable", true);
        distributionRule.put("distributionModel", "MERCHANT");
        distributionRules.add(distributionRule);
        ratePlan.put("distributionRules", distributionRules);
        
        List<String> valueAddInclusions = Arrays.asList("BREAKFAST", "WIFI");
        ratePlan.put("valueAddInclusions", valueAddInclusions);
        
        ratePlans.add(ratePlan);
        
        Map<String, Object> ratePlansResponse = new HashMap<>();
        ratePlansResponse.put("entity", ratePlans);
        
        when(expediaClient.getRatePlan(anyString(), eq("EXP_HOTEL_123"), eq("ROOM_001")))
                .thenReturn(ratePlansResponse);

        // When
        ChannelProductsDTO result = expediaService.getChannelProducts(channelId, channelHotelId);

        // Then
        assertNotNull(result);
        assertEquals("EXP_HOTEL_123", result.getChannelHotelId());
        assertEquals("Test Hotel", result.getHotelName());
        assertEquals("EXPEDIA", result.getChannelId());
        assertNotNull(result.getChannelProducts());
        assertEquals(1, result.getChannelProducts().size());
        
        ChannelProductsDTO.Product product = result.getChannelProducts().get(0);
        assertEquals("ROOM_001", product.getChannelRoomId());
        assertEquals("Standard Room", product.getChannelRoomName());
        assertEquals("EXP_RATE_001", product.getChannelRateId());
        assertEquals("Best Available Rate", product.getChannelRateName());
        assertEquals("Actived", product.getStatus());
        assertTrue(product.getAvailStatus());
        assertEquals(2, product.getMaxOccupancy());
        assertEquals("KING", product.getBedType());
        
        // Verify extensions are null for getChannelProducts (not getChannelProductsWithExtra)
        assertNotNull(product.getExtensions());
        assertNull(product.getExtensions().get("roomExtension"));
        assertNull(product.getExtensions().get("rateExtension"));
    }

    @Test
    void testGetChannelProductsWithExtra_WithValidData_ReturnsChannelProductsDTOWithExtensions() {
        // Given
        String supplierId = "SUPPLIER_001";
        String channelId = "EXPEDIA";
        String channelHotelId = "HOTEL_123";
        
        Map<String, Object> accountSettings = new HashMap<>();
        accountSettings.put("username", "testuser");
        accountSettings.put("password", "testpass");
        
        when(accountSettingService.getAccountSettings(channelId, channelHotelId))
                .thenReturn(accountSettings);

        // Mock responses (similar to above but checking extensions are preserved)
        setupMockResponses(channelHotelId);

        // When
        ChannelProductsDTO result = expediaService.getChannelProductsWithExtra(supplierId, channelId, channelHotelId);

        // Then
        assertNotNull(result);
        assertEquals(1, result.getChannelProducts().size());
        
        ChannelProductsDTO.Product product = result.getChannelProducts().get(0);
        
        // Verify extensions are preserved for getChannelProductsWithExtra
        assertNotNull(product.getExtensions());
        assertNotNull(product.getExtensions().get("roomExtension"));
        assertNotNull(product.getExtensions().get("rateExtension"));
        
        // Verify room extension details
        @SuppressWarnings("unchecked")
        Map<String, Object> roomExtension = (Map<String, Object>) product.getExtensions().get("roomExtension");
        assertEquals("EXP_HOTEL_123", roomExtension.get("distributor_hotel_id"));
        assertEquals("ROOM_001", roomExtension.get("distributor_room_id"));
        assertEquals("Standard Room", roomExtension.get("distributor_room_name"));
        assertEquals("KING", roomExtension.get("distributor_bedtype"));
        assertEquals("2", roomExtension.get("distributor_occupancy"));
        
        // Verify rate extension details
        @SuppressWarnings("unchecked")
        Map<String, Object> rateExtension = (Map<String, Object>) product.getExtensions().get("rateExtension");
        assertEquals("EXP_HOTEL_123", rateExtension.get("distributor_hotel_id"));
        assertEquals("EXP_RATE_001", rateExtension.get("distributor_rateplan_id"));
        assertEquals("Best Available Rate", rateExtension.get("distributor_rateplan_name"));
        assertEquals("BREAKFAST,WIFI", rateExtension.get("distributor_meal"));
        assertEquals("", rateExtension.get("distributor_paytype"));
        assertEquals(channelId, rateExtension.get("distributor_channel_name"));
        assertEquals("MERCHANT", rateExtension.get("distributor_collect_type"));
    }

    @Test
    void testGetChannelProducts_WithNullAccountSettings_ReturnsNull() {
        // Given
        String channelId = "EXPEDIA";
        String channelHotelId = "HOTEL_123";
        
        when(accountSettingService.getAccountSettings(channelId, channelHotelId))
                .thenReturn(null);

        // When
        ChannelProductsDTO result = expediaService.getChannelProducts(channelId, channelHotelId);

        // Then
        assertNull(result);
        verify(expediaClient, never()).getHotel(anyString(), anyString());
    }

    @Test
    void testGetChannelProducts_WithEmptyAccountSettings_ReturnsNull() {
        // Given
        String channelId = "EXPEDIA";
        String channelHotelId = "HOTEL_123";
        
        when(accountSettingService.getAccountSettings(channelId, channelHotelId))
                .thenReturn(new HashMap<>());

        // When
        ChannelProductsDTO result = expediaService.getChannelProducts(channelId, channelHotelId);

        // Then
        assertNull(result);
        verify(expediaClient, never()).getHotel(anyString(), anyString());
    }

    @Test
    void testGetChannelProducts_WithFeignExceptionContaining1000Error_ThrowsBusinessAuthorizeException() {
        // Given
        String channelId = "EXPEDIA";
        String channelHotelId = "HOTEL_123";
        
        Map<String, Object> accountSettings = new HashMap<>();
        accountSettings.put("username", "testuser");
        accountSettings.put("password", "testpass");
        
        when(accountSettingService.getAccountSettings(channelId, channelHotelId))
                .thenReturn(accountSettings);

        // Mock FeignException with error code 1000
        String errorResponse = "{\"errors\":[{\"code\":\"1000\",\"message\":\"Authentication failed\"}]}";
        ByteBuffer responseBody = StandardCharsets.UTF_8.encode(errorResponse);
        
        when(feignException.responseBody()).thenReturn(Optional.of(responseBody));
        when(expediaClient.getHotel(anyString(), eq(channelHotelId)))
                .thenThrow(feignException);

        // When & Then
        BusinessAuthorizeException exception = assertThrows(BusinessAuthorizeException.class, () -> {
            expediaService.getChannelProducts(channelId, channelHotelId);
        });
        
        assertTrue(exception.getMessage().contains("[1000]: Authentication failed"));
    }

    @Test
    void testGetChannelProducts_WithFeignExceptionNotContaining1000Error_ThrowsBusinessException() {
        // Given
        String channelId = "EXPEDIA";
        String channelHotelId = "HOTEL_123";
        
        Map<String, Object> accountSettings = new HashMap<>();
        accountSettings.put("username", "testuser");
        accountSettings.put("password", "testpass");
        
        when(accountSettingService.getAccountSettings(channelId, channelHotelId))
                .thenReturn(accountSettings);

        // Mock FeignException with error code other than 1000
        String errorResponse = "{\"errors\":[{\"code\":\"2000\",\"message\":\"Hotel not found\"}]}";
        ByteBuffer responseBody = StandardCharsets.UTF_8.encode(errorResponse);
        
        when(feignException.responseBody()).thenReturn(Optional.of(responseBody));
        when(expediaClient.getHotel(anyString(), eq(channelHotelId)))
                .thenThrow(feignException);

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            expediaService.getChannelProducts(channelId, channelHotelId);
        });
        
        assertTrue(exception.getMessage().contains("[2000]: Hotel not found"));
    }

    @Test
    void testGetChannelProducts_WithNullHotelId_ReturnsNull() {
        // Given
        String channelId = "EXPEDIA";
        String channelHotelId = "HOTEL_123";
        
        Map<String, Object> accountSettings = new HashMap<>();
        accountSettings.put("username", "testuser");
        accountSettings.put("password", "testpass");
        
        when(accountSettingService.getAccountSettings(channelId, channelHotelId))
                .thenReturn(accountSettings);

        // Mock hotel response with null resourceId
        Map<String, Object> hotelResponse = new HashMap<>();
        Map<String, Object> hotelEntity = new HashMap<>();
        hotelEntity.put("resourceId", null);
        hotelResponse.put("entity", hotelEntity);
        
        when(expediaClient.getHotel(anyString(), eq(channelHotelId)))
                .thenReturn(hotelResponse);

        // When
        ChannelProductsDTO result = expediaService.getChannelProducts(channelId, channelHotelId);

        // Then
        assertNull(result);
        verify(expediaClient, never()).getRoomType(anyString(), anyString());
    }

    @Test
    void testRemapping_ModifiesChannelRateNames() {
        // Given
        ChannelProductsDTO channelProducts = new ChannelProductsDTO();
        List<ChannelProductsDTO.Product> products = new ArrayList<>();
        
        ChannelProductsDTO.Product product1 = new ChannelProductsDTO.Product();
        product1.setChannelRateName("Standard Rate");
        products.add(product1);
        
        ChannelProductsDTO.Product product2 = new ChannelProductsDTO.Product();
        product2.setChannelRateName("Premium Rate");
        products.add(product2);
        
        channelProducts.setChannelProducts(products);

        // When
        expediaService.remapping(channelProducts);

        // Then
        assertEquals("EXPEDIA Standard Rate", product1.getChannelRateName());
        assertEquals("EXPEDIA Premium Rate", product2.getChannelRateName());
    }

    @Test
    void testRemapping_WithNullChannelProducts_DoesNotThrowException() {
        // Given
        ChannelProductsDTO channelProducts = null;

        // When & Then
        assertDoesNotThrow(() -> {
            expediaService.remapping(channelProducts);
        });
    }

    @Test
    void testRemapping_WithNullProductList_DoesNotThrowException() {
        // Given
        ChannelProductsDTO channelProducts = new ChannelProductsDTO();
        channelProducts.setChannelProducts(null);

        // When & Then
        assertDoesNotThrow(() -> {
            expediaService.remapping(channelProducts);
        });
    }

    private void setupMockResponses(String channelHotelId) {
        // Mock hotel response
        Map<String, Object> hotelResponse = new HashMap<>();
        Map<String, Object> hotelEntity = new HashMap<>();
        hotelEntity.put("resourceId", "EXP_HOTEL_123");
        hotelEntity.put("name", "Test Hotel");
        hotelResponse.put("entity", hotelEntity);
        
        when(expediaClient.getHotel(anyString(), eq(channelHotelId)))
                .thenReturn(hotelResponse);

        // Mock room types response
        List<Map<String, Object>> roomTypes = new ArrayList<>();
        Map<String, Object> room = new HashMap<>();
        room.put("resourceId", "ROOM_001");
        Map<String, Object> roomName = new HashMap<>();
        roomName.put("value", "Standard Room");
        room.put("name", roomName);
        
        Map<String, Object> maxOccupancy = new HashMap<>();
        maxOccupancy.put("total", 2);
        room.put("maxOccupancy", maxOccupancy);
        
        List<Map<String, Object>> standardBedding = new ArrayList<>();
        Map<String, Object> bedding = new HashMap<>();
        List<Map<String, Object>> options = new ArrayList<>();
        Map<String, Object> option = new HashMap<>();
        option.put("type", "KING");
        options.add(option);
        bedding.put("option", options);
        standardBedding.add(bedding);
        room.put("standardBedding", standardBedding);
        
        roomTypes.add(room);
        
        Map<String, Object> roomTypesResponse = new HashMap<>();
        roomTypesResponse.put("entity", roomTypes);
        
        when(expediaClient.getRoomType(anyString(), eq("EXP_HOTEL_123")))
                .thenReturn(roomTypesResponse);

        // Mock rate plans response
        List<Map<String, Object>> ratePlans = new ArrayList<>();
        Map<String, Object> ratePlan = new HashMap<>();
        ratePlan.put("resourceId", "RATE_001");
        ratePlan.put("name", "Best Available Rate");
        
        List<Map<String, Object>> distributionRules = new ArrayList<>();
        Map<String, Object> distributionRule = new HashMap<>();
        distributionRule.put("expediaId", "EXP_RATE_001");
        distributionRule.put("manageable", true);
        distributionRule.put("distributionModel", "MERCHANT");
        distributionRules.add(distributionRule);
        ratePlan.put("distributionRules", distributionRules);
        
        List<String> valueAddInclusions = Arrays.asList("BREAKFAST", "WIFI");
        ratePlan.put("valueAddInclusions", valueAddInclusions);
        
        ratePlans.add(ratePlan);
        
        Map<String, Object> ratePlansResponse = new HashMap<>();
        ratePlansResponse.put("entity", ratePlans);
        
        when(expediaClient.getRatePlan(anyString(), eq("EXP_HOTEL_123"), eq("ROOM_001")))
                .thenReturn(ratePlansResponse);
    }
}
