package com.derbysoft.next.propertyconnect.channel.task.util;

import com.derbysoft.next.propertyconenct.channel.common.exception.ErrorCode;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.*;
import com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.RemoteChannelService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Comprehensive unit tests for InventoryItemErrorUtil
 */
class InventoryItemErrorUtilTest {

    private TestInventoryItem testItem;

    @BeforeEach
    void setUp() {
        testItem = new TestInventoryItem();
        testItem.setCode("TEST_CODE");
        testItem.setName("Test Item");
        testItem.setSyncStatus(SyncStatus.DRAFT);
    }

    @Test
    void testRoomNotFound() {
        InventoryItemErrorUtil.roomNotFound(testItem);

        assertEquals(SyncStatus.PREPROCESS_FAILED, testItem.getSyncStatus());
        assertEquals(ErrorCode.BusinessFailed.name(), testItem.getErrorCode());
        assertEquals("[TEST_CODE]: Room not found", testItem.getErrorMessage());
    }

    @Test
    void testRateNotFound() {
        InventoryItemErrorUtil.rateNotFound(testItem);

        assertEquals(SyncStatus.PREPROCESS_FAILED, testItem.getSyncStatus());
        assertEquals(ErrorCode.BusinessFailed.name(), testItem.getErrorCode());
        assertEquals("[TEST_CODE]: Rate not found", testItem.getErrorMessage());
    }

    @Test
    void testItemNotFound() {
        InventoryItemErrorUtil.itemNotFound(testItem);

        assertEquals(SyncStatus.PREPROCESS_FAILED, testItem.getSyncStatus());
        assertEquals(ErrorCode.BusinessFailed.name(), testItem.getErrorCode());
        assertEquals("[TEST_CODE]: Not found", testItem.getErrorMessage());
    }

    @Test
    void testAssociatedItemStatusError() {
        String customMessage = "Associated room is inactive";
        InventoryItemErrorUtil.associatedItemStatusError(testItem, customMessage);

        assertEquals(SyncStatus.PREPROCESS_FAILED, testItem.getSyncStatus());
        assertEquals(ErrorCode.BusinessFailed.name(), testItem.getErrorCode());
        assertEquals("[TEST_CODE]: " + customMessage, testItem.getErrorMessage());
    }

    @Test
    void testRequestNameDuplicateItemError() {
        InventoryItemErrorUtil.requestNameDuplicateItemError(testItem);

        assertEquals(SyncStatus.PREPROCESS_FAILED, testItem.getSyncStatus());
        assertEquals(ErrorCode.BusinessFailed.name(), testItem.getErrorCode());
        assertEquals("[TEST_CODE]: Duplicate in request", testItem.getErrorMessage());
    }

    @Test
    void testPartnerDuplicateItemError() {
        InventoryItemErrorUtil.partnerDuplicateItemError(testItem);

        assertEquals(SyncStatus.PREPROCESS_FAILED, testItem.getSyncStatus());
        assertEquals(ErrorCode.BusinessFailed.name(), testItem.getErrorCode());
        assertEquals("[TEST_CODE]: Duplicate with partner", testItem.getErrorMessage());
    }

    @Test
    void testUnprocessed() {
        InventoryItemErrorUtil.unprocessed(testItem);

        assertEquals(SyncStatus.SYNC_FAILED, testItem.getSyncStatus());
        assertEquals(ErrorCode.BusinessFailed.name(), testItem.getErrorCode());
        assertEquals("[TEST_CODE]: Unprocessed", testItem.getErrorMessage());
    }

    @Test
    void testQueryFromPartnerFailed() {
        String responseString = "HTTP 500 Internal Server Error";
        InventoryItemErrorUtil.queryFromPartnerFailed(testItem, responseString);

        assertEquals(SyncStatus.PREPROCESS_FAILED, testItem.getSyncStatus());
        assertEquals(ErrorCode.BusinessFailed.name(), testItem.getErrorCode());
        assertEquals("[TEST_CODE]: Query with partner failed: " + responseString, testItem.getErrorMessage());
    }

    @Test
    void testPartnerResponseError() {
        String responseString = "Invalid request format";
        InventoryItemErrorUtil.partnerResponseError(testItem, responseString);

        assertEquals(SyncStatus.SYNC_FAILED, testItem.getSyncStatus());
        assertEquals(ErrorCode.Unknown.name(), testItem.getErrorCode());
        assertEquals(responseString, testItem.getErrorMessage());
    }

    @Test
    void testPartnerPrecheckResponseError() {
        String responseString = "Precheck validation failed";
        InventoryItemErrorUtil.partnerPrecheckResponseError(testItem, responseString);

        assertEquals(SyncStatus.PREPROCESS_FAILED, testItem.getSyncStatus());
        assertEquals(ErrorCode.Unknown.name(), testItem.getErrorCode());
        assertEquals(responseString, testItem.getErrorMessage());
    }

    @Test
    void testCredentialCreateError() {
        String responseString = "Authentication failed";
        InventoryItemErrorUtil.credentialCreateError(testItem, responseString);

        assertEquals(SyncStatus.SYNC_FAILED, testItem.getSyncStatus());
        assertEquals(ErrorCode.Unknown.name(), testItem.getErrorCode());
        assertEquals(responseString, testItem.getErrorMessage());
    }

    @Test
    void testHotelSyncFailed() {
        String responseString = "Hotel validation error";
        InventoryItemErrorUtil.hotelSyncFailed(testItem, responseString);

        assertEquals(SyncStatus.PREPROCESS_FAILED, testItem.getSyncStatus());
        assertEquals(ErrorCode.BusinessFailed.name(), testItem.getErrorCode());
        assertEquals("[TEST_CODE]: Fail because hotel sync failed: " + responseString, testItem.getErrorMessage());
    }

    @Test
    void testHotelSyncFailedWithNullResponse() {
        InventoryItemErrorUtil.hotelSyncFailed(testItem, null);

        assertEquals(SyncStatus.PREPROCESS_FAILED, testItem.getSyncStatus());
        assertEquals(ErrorCode.BusinessFailed.name(), testItem.getErrorCode());
        assertEquals("[TEST_CODE]: Fail because hotel sync failed", testItem.getErrorMessage());
    }

    @Test
    void testSystemErrorWithThrowable() {
        RuntimeException exception = new RuntimeException("Database connection failed");
        InventoryItemErrorUtil.systemError(testItem, exception);

        assertEquals(SyncStatus.PREPROCESS_FAILED, testItem.getSyncStatus());
        assertEquals(ErrorCode.System.name(), testItem.getErrorCode());
        // The actual implementation has a bug - SystemRaw format is "%s" but two parameters are passed
        // This results in only the first parameter (code) being used
        assertEquals("TEST_CODE", testItem.getErrorMessage());
    }

    @Test
    void testSystemErrorWithString() {
        String errorMessage = "Memory allocation failed";
        InventoryItemErrorUtil.systemError(testItem, errorMessage);

        assertEquals(SyncStatus.PREPROCESS_FAILED, testItem.getSyncStatus());
        assertEquals(ErrorCode.System.name(), testItem.getErrorCode());
        assertEquals(errorMessage, testItem.getErrorMessage());
    }

    @Test
    void testInventoryItemResult() {
        // Test the record structure
        List<InventoryItemStatus> failedItems = Arrays.asList(testItem);
        List<InventoryItemStatus> successItems = Arrays.asList();

        InventoryItemErrorUtil.InventoryItemResult result = 
            new InventoryItemErrorUtil.InventoryItemResult(failedItems, successItems);

        assertEquals(failedItems, result.failedItems());
        assertEquals(successItems, result.successItems());
    }

    @Test
    void testCountByOperationWithNullOperations() {
        ChannelHotelDTO dto = new ChannelHotelDTO();
        
        InventoryItemErrorUtil.InventoryItemResult result = 
            InventoryItemErrorUtil.countByOperation(null, dto);

        assertTrue(result.failedItems().isEmpty());
        assertTrue(result.successItems().isEmpty());
    }

    @Test
    void testCountByOperationWithEmptyOperations() {
        ChannelHotelDTO dto = new ChannelHotelDTO();
        List<RemoteChannelService.Operation> operations = Arrays.asList();
        
        InventoryItemErrorUtil.InventoryItemResult result = 
            InventoryItemErrorUtil.countByOperation(operations, dto);

        assertTrue(result.failedItems().isEmpty());
        assertTrue(result.successItems().isEmpty());
    }

    @Test
    void testCountByOperationWithHotelInfo() {
        ChannelHotelDTO dto = new ChannelHotelDTO();
        ChannelHotelInfo hotelInfo = new ChannelHotelInfo();
        hotelInfo.setId("HOTEL_001");
        hotelInfo.setSyncStatus(SyncStatus.SYNC_FAILED);
        dto.setHotelInfo(hotelInfo);

        List<RemoteChannelService.Operation> operations = 
            Arrays.asList(RemoteChannelService.Operation.SaveProperty);
        
        InventoryItemErrorUtil.InventoryItemResult result = 
            InventoryItemErrorUtil.countByOperation(operations, dto);

        assertEquals(1, result.failedItems().size());
        assertEquals(0, result.successItems().size());
        assertEquals("HOTEL_001", result.failedItems().get(0).getCode());
    }

    @Test
    void testCountByOperationWithSuccessfulHotelInfo() {
        ChannelHotelDTO dto = new ChannelHotelDTO();
        ChannelHotelInfo hotelInfo = new ChannelHotelInfo();
        hotelInfo.setId("HOTEL_002");
        hotelInfo.setSyncStatus(SyncStatus.SYNCED);
        dto.setHotelInfo(hotelInfo);

        List<RemoteChannelService.Operation> operations = 
            Arrays.asList(RemoteChannelService.Operation.SaveProperty);
        
        InventoryItemErrorUtil.InventoryItemResult result = 
            InventoryItemErrorUtil.countByOperation(operations, dto);

        assertEquals(0, result.failedItems().size());
        assertEquals(1, result.successItems().size());
        assertEquals("HOTEL_002", result.successItems().get(0).getCode());
    }

    @Test
    void testCountByOperationWithProcessingHotelInfo() {
        ChannelHotelDTO dto = new ChannelHotelDTO();
        ChannelHotelInfo hotelInfo = new ChannelHotelInfo();
        hotelInfo.setId("HOTEL_003");
        hotelInfo.setSyncStatus(SyncStatus.DRAFT);
        dto.setHotelInfo(hotelInfo);

        List<RemoteChannelService.Operation> operations = 
            Arrays.asList(RemoteChannelService.Operation.SaveProperty);
        
        InventoryItemErrorUtil.InventoryItemResult result = 
            InventoryItemErrorUtil.countByOperation(operations, dto);

        assertEquals(1, result.failedItems().size());
        assertEquals(0, result.successItems().size());
        // Should be marked as unprocessed
        assertEquals(SyncStatus.SYNC_FAILED, result.failedItems().get(0).getSyncStatus());
    }

    @Test
    void testCountByOperationWithRoomsInfo() {
        ChannelHotelDTO dto = new ChannelHotelDTO();
        
        ChannelRoomInfo room1 = new ChannelRoomInfo("ROOM_001");
        room1.setSyncStatus(SyncStatus.SYNCED);
        
        ChannelRoomInfo room2 = new ChannelRoomInfo("ROOM_002");
        room2.setSyncStatus(SyncStatus.SYNC_FAILED);
        
        dto.setRoomsInfo(Arrays.asList(room1, room2));

        List<RemoteChannelService.Operation> operations = 
            Arrays.asList(RemoteChannelService.Operation.SaveRoomTypes);
        
        InventoryItemErrorUtil.InventoryItemResult result = 
            InventoryItemErrorUtil.countByOperation(operations, dto);

        assertEquals(1, result.failedItems().size());
        assertEquals(1, result.successItems().size());
        assertEquals("ROOM_002", result.failedItems().get(0).getCode());
        assertEquals("ROOM_001", result.successItems().get(0).getCode());
    }

    @Test
    void testCountByOperationWithRatesInfo() {
        ChannelHotelDTO dto = new ChannelHotelDTO();
        
        ChannelRateInfo rate1 = new ChannelRateInfo("RATE_001");
        rate1.setSyncStatus(SyncStatus.IGNORED);
        
        ChannelRateInfo rate2 = new ChannelRateInfo("RATE_002");
        rate2.setSyncStatus(SyncStatus.PREPROCESS_FAILED);
        
        dto.setRatesInfo(Arrays.asList(rate1, rate2));

        List<RemoteChannelService.Operation> operations = 
            Arrays.asList(RemoteChannelService.Operation.SaveRatePlans);
        
        InventoryItemErrorUtil.InventoryItemResult result = 
            InventoryItemErrorUtil.countByOperation(operations, dto);

        assertEquals(1, result.failedItems().size());
        assertEquals(1, result.successItems().size());
        assertEquals("RATE_002", result.failedItems().get(0).getCode());
        assertEquals("RATE_001", result.successItems().get(0).getCode());
    }

    @Test
    void testWithRealInventoryItemImplementations() {
        // Test with actual ChannelRoomInfo
        ChannelRoomInfo roomInfo = new ChannelRoomInfo("ROOM_TEST");
        roomInfo.setName("Test Room");
        
        InventoryItemErrorUtil.roomNotFound(roomInfo);
        
        assertEquals(SyncStatus.PREPROCESS_FAILED, roomInfo.getSyncStatus());
        assertEquals(ErrorCode.BusinessFailed.name(), roomInfo.getErrorCode());
        assertEquals("[ROOM_TEST]: Room not found", roomInfo.getErrorMessage());

        // Test with actual ChannelRateInfo
        ChannelRateInfo rateInfo = new ChannelRateInfo("RATE_TEST");
        rateInfo.setName("Test Rate");
        
        InventoryItemErrorUtil.rateNotFound(rateInfo);
        
        assertEquals(SyncStatus.PREPROCESS_FAILED, rateInfo.getSyncStatus());
        assertEquals(ErrorCode.BusinessFailed.name(), rateInfo.getErrorCode());
        assertEquals("[RATE_TEST]: Rate not found", rateInfo.getErrorMessage());

        // Test with actual ChannelHotelInfo
        ChannelHotelInfo hotelInfo = new ChannelHotelInfo();
        hotelInfo.setId("HOTEL_TEST");
        hotelInfo.setName("Test Hotel");
        
        InventoryItemErrorUtil.systemError(hotelInfo, "Test system error");
        
        assertEquals(SyncStatus.PREPROCESS_FAILED, hotelInfo.getSyncStatus());
        assertEquals(ErrorCode.System.name(), hotelInfo.getErrorCode());
        assertEquals("Test system error", hotelInfo.getErrorMessage());
    }

    // Test implementation of InventoryItemStatus for testing purposes
    private static class TestInventoryItem implements InventoryItemStatus {
        private String code;
        private String name;
        private SyncStatus syncStatus;
        private String errorCode;
        private String errorMessage;
        private RemoteChannelService.Operation operation;

        @Override
        public RemoteChannelService.Operation getOperation() {
            return operation;
        }

        @Override
        public String codePattern() {
            return "TestCode";
        }

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getName() {
            return name;
        }

        @Override
        public SyncStatus getSyncStatus() {
            return syncStatus;
        }

        @Override
        public String getErrorCode() {
            return errorCode;
        }

        @Override
        public String getErrorMessage() {
            return errorMessage;
        }

        @Override
        public void setSyncStatus(SyncStatus syncStatus) {
            this.syncStatus = syncStatus;
        }

        @Override
        public void setErrorCode(String errorCode) {
            this.errorCode = errorCode;
        }

        @Override
        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }

        // Setters for test setup
        public void setCode(String code) {
            this.code = code;
        }

        public void setName(String name) {
            this.name = name;
        }

        public void setOperation(RemoteChannelService.Operation operation) {
            this.operation = operation;
        }
    }
}
