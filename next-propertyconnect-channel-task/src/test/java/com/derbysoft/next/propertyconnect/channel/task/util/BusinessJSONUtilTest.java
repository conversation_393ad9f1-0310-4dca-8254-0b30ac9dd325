package com.derbysoft.next.propertyconnect.channel.task.util;

import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Comprehensive unit tests for BusinessJSONUtil
 */
class BusinessJSONUtilTest {

    @Test
    void testBusinessJSONUtilExists() {
        // Test that the BusinessJSONUtil class exists
        assertDoesNotThrow(() -> {
            Class<?> clazz = BusinessJSONUtil.class;
            assertNotNull(clazz);
        });
    }

    @Test
    void testBusinessJSONUtilClassStructure() {
        // Test basic class structure
        Class<?> clazz = BusinessJSONUtil.class;
        assertNotNull(clazz);
        assertEquals("BusinessJSONUtil", clazz.getSimpleName());
        assertEquals("com.derbysoft.next.propertyconnect.channel.task.util", clazz.getPackageName());
    }

    @Test
    void testIsActiveWithActivedStatus() {
        // Test isActive with "Actived" status (case sensitive)
        Map<String, Object> item = new HashMap<>();
        item.put("status", "Actived");

        Boolean result = BusinessJSONUtil.isActive(item);

        assertTrue(result);
    }

    @Test
    void testIsActiveWithActivedStatusLowerCase() {
        // Test isActive with "actived" status (case insensitive)
        Map<String, Object> item = new HashMap<>();
        item.put("status", "actived");

        Boolean result = BusinessJSONUtil.isActive(item);

        assertTrue(result);
    }

    @Test
    void testIsActiveWithActivedStatusUpperCase() {
        // Test isActive with "ACTIVED" status (case insensitive)
        Map<String, Object> item = new HashMap<>();
        item.put("status", "ACTIVED");

        Boolean result = BusinessJSONUtil.isActive(item);

        assertTrue(result);
    }

    @Test
    void testIsActiveWithActivedStatusMixedCase() {
        // Test isActive with "AcTiVeD" status (case insensitive)
        Map<String, Object> item = new HashMap<>();
        item.put("status", "AcTiVeD");

        Boolean result = BusinessJSONUtil.isActive(item);

        assertTrue(result);
    }

    @Test
    void testIsActiveWithInactiveStatus() {
        // Test isActive with "Inactive" status
        Map<String, Object> item = new HashMap<>();
        item.put("status", "Inactive");

        Boolean result = BusinessJSONUtil.isActive(item);

        assertFalse(result);
    }

    @Test
    void testIsActiveWithActiveStatus() {
        // Test isActive with "Active" status (not "Actived")
        Map<String, Object> item = new HashMap<>();
        item.put("status", "Active");

        Boolean result = BusinessJSONUtil.isActive(item);

        assertFalse(result);
    }

    @Test
    void testIsActiveWithNullStatus() {
        // Test isActive with null status
        Map<String, Object> item = new HashMap<>();
        item.put("status", null);

        Boolean result = BusinessJSONUtil.isActive(item);

        assertFalse(result);
    }

    @Test
    void testIsActiveWithMissingStatus() {
        // Test isActive with missing status field
        Map<String, Object> item = new HashMap<>();
        // No status field

        Boolean result = BusinessJSONUtil.isActive(item);

        assertFalse(result);
    }

    @Test
    void testIsActiveWithEmptyStatus() {
        // Test isActive with empty status
        Map<String, Object> item = new HashMap<>();
        item.put("status", "");

        Boolean result = BusinessJSONUtil.isActive(item);

        assertFalse(result);
    }

    @Test
    void testIsActiveWithNumericStatus() {
        // Test isActive with numeric status
        Map<String, Object> item = new HashMap<>();
        item.put("status", 1);

        Boolean result = BusinessJSONUtil.isActive(item);

        assertFalse(result);
    }

    @Test
    void testIsActiveWithBooleanStatus() {
        // Test isActive with boolean status
        Map<String, Object> item = new HashMap<>();
        item.put("status", true);

        Boolean result = BusinessJSONUtil.isActive(item);

        assertFalse(result);
    }

    @Test
    void testIsActiveWithComplexObject() {
        // Test isActive with complex nested object
        Map<String, Object> nestedStatus = new HashMap<>();
        nestedStatus.put("value", "Actived");

        Map<String, Object> item = new HashMap<>();
        item.put("status", nestedStatus);

        Boolean result = BusinessJSONUtil.isActive(item);

        assertFalse(result); // Should be false because status is not a string
    }

    @Test
    void testIsActiveWithNullItem() {
        // Test isActive with null item - this will throw NPE due to JSONUtil implementation
        assertThrows(NullPointerException.class, () -> {
            BusinessJSONUtil.isActive(null);
        });
    }

    @Test
    void testIsActiveWithEmptyMap() {
        // Test isActive with empty map
        Map<String, Object> item = new HashMap<>();

        Boolean result = BusinessJSONUtil.isActive(item);

        assertFalse(result);
    }

    @Test
    void testIsActiveWithNonMapObject() {
        // Test isActive with non-map object
        String item = "not a map";

        Boolean result = BusinessJSONUtil.isActive(item);

        assertFalse(result);
    }

    @Test
    void testIsActiveWithActivedAndExtraWhitespace() {
        // Test isActive with "Actived" and extra whitespace
        Map<String, Object> item = new HashMap<>();
        item.put("status", " Actived ");

        Boolean result = BusinessJSONUtil.isActive(item);

        assertFalse(result); // Should be false due to whitespace
    }

    @Test
    void testIsActiveWithSimilarButDifferentValues() {
        // Test isActive with similar but different values
        String[] nonActivedValues = {
            "Activated", "Active", "Enabled", "On", "True", "Yes",
            "Activ", "Activd", "Activeed", "Actived1", "1Actived"
        };

        for (String value : nonActivedValues) {
            Map<String, Object> item = new HashMap<>();
            item.put("status", value);

            Boolean result = BusinessJSONUtil.isActive(item);

            assertFalse(result, "Value '" + value + "' should not be considered active");
        }
    }

    @Test
    void testIsActiveMethodSignature() {
        // Test that isActive method has correct signature
        assertDoesNotThrow(() -> {
            BusinessJSONUtil.class.getDeclaredMethod("isActive", Object.class);
        });
    }

    @Test
    void testUtilityClassStructure() {
        // Test that BusinessJSONUtil is a proper utility class
        Class<?> clazz = BusinessJSONUtil.class;

        // Should be a utility class (final or have private constructor)
        assertTrue(clazz.getAnnotations().length > 0 ||
                  java.lang.reflect.Modifier.isFinal(clazz.getModifiers()));
    }
}
