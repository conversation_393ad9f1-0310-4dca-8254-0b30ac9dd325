package com.derbysoft.next.propertyconnect.channel.task.domain.entity;

import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

class ChannelPOTest {

    @Test
    void testChannelPOCreation() {
        // Given
        ChannelPO channelPO = new ChannelPO();

        // When & Then
        assertNotNull(channelPO);
    }

    @Test
    void testChannelPOWithId() {
        // Given
        String expectedId = "test-channel-id";

        // When
        ChannelPO channelPO = new ChannelPO();
        channelPO.setId(expectedId);

        // Then
        assertEquals(expectedId, channelPO.getId());
    }

    @Test
    void testChannelPOWithChannelId() {
        // Given
        String expectedChannelId = "TEST_CHANNEL";

        // When
        ChannelPO channelPO = new ChannelPO();
        channelPO.setChannelId(expectedChannelId);

        // Then
        assertEquals(expectedChannelId, channelPO.getChannelId());
    }

    @Test
    void testChannelPOWithAccountSettings() {
        // Given
        HashMap<String, String> expectedAccountSettings = new HashMap<>();
        expectedAccountSettings.put("username", "testuser");
        expectedAccountSettings.put("password", "testpass");

        // When
        ChannelPO channelPO = new ChannelPO();
        channelPO.setAccountSettings(expectedAccountSettings);

        // Then
        assertEquals(expectedAccountSettings, channelPO.getAccountSettings());
        assertEquals("testuser", channelPO.getAccountSettings().get("username"));
        assertEquals("testpass", channelPO.getAccountSettings().get("password"));
    }

    @Test
    void testChannelPOWithChannelDetails() {
        // Given
        Map<String, String> expectedChannelDetails = new HashMap<>();
        expectedChannelDetails.put("productQueryUrl", "https://api.example.com/products");
        expectedChannelDetails.put("baseUrl", "https://api.example.com");

        // When
        ChannelPO channelPO = new ChannelPO();
        channelPO.setChannelDetails(expectedChannelDetails);

        // Then
        assertEquals(expectedChannelDetails, channelPO.getChannelDetails());
        assertEquals("https://api.example.com/products", channelPO.getChannelDetails().get("productQueryUrl"));
        assertEquals("https://api.example.com", channelPO.getChannelDetails().get("baseUrl"));
    }

    @Test
    void testChannelPOEqualsAndHashCode() {
        // Given
        ChannelPO channelPO1 = new ChannelPO();
        channelPO1.setId("test-id");
        channelPO1.setChannelId("TEST_CHANNEL");

        HashMap<String, String> accountSettings = new HashMap<>();
        accountSettings.put("username", "testuser");
        channelPO1.setAccountSettings(accountSettings);

        ChannelPO channelPO2 = new ChannelPO();
        channelPO2.setId("test-id");
        channelPO2.setChannelId("TEST_CHANNEL");

        HashMap<String, String> accountSettings2 = new HashMap<>();
        accountSettings2.put("username", "testuser");
        channelPO2.setAccountSettings(accountSettings2);

        ChannelPO channelPO3 = new ChannelPO();
        channelPO3.setId("different-id");
        channelPO3.setChannelId("DIFFERENT_CHANNEL");

        // When & Then
        assertEquals(channelPO1, channelPO2);
        assertNotEquals(channelPO1, channelPO3);
        assertEquals(channelPO1.hashCode(), channelPO2.hashCode());
    }

    @Test
    void testChannelPOToString() {
        // Given
        ChannelPO channelPO = new ChannelPO();
        channelPO.setId("test-id");
        channelPO.setChannelId("TEST_CHANNEL");

        // When
        String toString = channelPO.toString();

        // Then
        assertNotNull(toString);
        assertTrue(toString.contains("test-id"));
        assertTrue(toString.contains("TEST_CHANNEL"));
    }

    @Test
    void testChannelPONullValues() {
        // Given
        ChannelPO channelPO = new ChannelPO();

        // When & Then
        assertNull(channelPO.getId());
        assertNull(channelPO.getChannelId());
        assertNull(channelPO.getAccountSettings());
        assertNull(channelPO.getChannelDetails());
    }

    @Test
    void testGetProductQueryUrl() {
        // Given
        ChannelPO channelPO = new ChannelPO();
        Map<String, String> channelDetails = new HashMap<>();
        channelDetails.put("productQueryUrl", "https://api.example.com/products");
        channelPO.setChannelDetails(channelDetails);

        // When
        String productQueryUrl = channelPO.getProductQueryUrl();

        // Then
        assertEquals("https://api.example.com/products", productQueryUrl);
    }

    @Test
    void testGetProductQueryUrl_WithNullChannelDetails() {
        // Given
        ChannelPO channelPO = new ChannelPO();
        channelPO.setChannelDetails(null);

        // When
        String productQueryUrl = channelPO.getProductQueryUrl();

        // Then
        assertNull(productQueryUrl);
    }

    @Test
    void testGetProductQueryUrl_WithEmptyChannelDetails() {
        // Given
        ChannelPO channelPO = new ChannelPO();
        channelPO.setChannelDetails(new HashMap<>());

        // When
        String productQueryUrl = channelPO.getProductQueryUrl();

        // Then
        assertNull(productQueryUrl);
    }
}
