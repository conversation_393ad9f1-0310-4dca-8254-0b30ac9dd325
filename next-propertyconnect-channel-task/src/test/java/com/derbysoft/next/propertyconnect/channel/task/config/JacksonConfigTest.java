package com.derbysoft.next.propertyconnect.channel.task.config;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Comprehensive unit tests for JacksonConfig
 * Tests JSON configuration and ObjectMapper customization
 */
@ExtendWith(MockitoExtension.class)
class JacksonConfigTest {

    private JacksonConfig jacksonConfig;
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        jacksonConfig = new JacksonConfig();
        objectMapper = jacksonConfig.customObjectMapper();
    }

    @Test
    void testCustomObjectMapper_ReturnsEnhancedObjectMapper() {
        // When
        ObjectMapper result = jacksonConfig.customObjectMapper();

        // Then
        assertNotNull(result);
        assertTrue(result instanceof EnhancedObjectMapper);
    }

    @Test
    void testCustomObjectMapper_HasJdk8Module() {
        // When
        ObjectMapper result = jacksonConfig.customObjectMapper();

        // Then
        assertNotNull(result);
        // Verify Jdk8Module is registered by testing Optional serialization
        assertDoesNotThrow(() -> {
            Optional<String> optional = Optional.of("test");
            String json = result.writeValueAsString(optional);
            assertNotNull(json);
        });
    }

    @Test
    void testCustomObjectMapper_HasJavaTimeModule() {
        // When
        ObjectMapper result = jacksonConfig.customObjectMapper();

        // Then
        assertNotNull(result);
        // Verify JavaTimeModule is registered by testing LocalDateTime serialization
        assertDoesNotThrow(() -> {
            LocalDateTime now = LocalDateTime.now();
            String json = result.writeValueAsString(now);
            assertNotNull(json);
        });
    }

    @Test
    void testObjectMapper_SerializesOptionalPresent() throws JsonProcessingException {
        // Given
        Optional<String> optional = Optional.of("test_value");

        // When
        String json = objectMapper.writeValueAsString(optional);

        // Then
        assertNotNull(json);
        assertTrue(json.contains("test_value"));
    }

    @Test
    void testObjectMapper_SerializesOptionalEmpty() throws JsonProcessingException {
        // Given
        Optional<String> optional = Optional.empty();

        // When
        String json = objectMapper.writeValueAsString(optional);

        // Then
        assertNotNull(json);
        // Empty optional should serialize to null or empty based on configuration
        assertTrue(json.equals("null") || json.equals("{}"));
    }

    @Test
    void testObjectMapper_SerializesLocalDateTime() throws JsonProcessingException {
        // Given
        LocalDateTime dateTime = LocalDateTime.of(2023, 12, 25, 10, 30, 45);

        // When
        String json = objectMapper.writeValueAsString(dateTime);

        // Then
        assertNotNull(json);
        // Should serialize as ISO format array or string
        assertTrue(json.contains("2023") || json.contains("12") || json.contains("25"));
    }

    @Test
    void testObjectMapper_SerializesLocalDate() throws JsonProcessingException {
        // Given
        LocalDate date = LocalDate.of(2023, 12, 25);

        // When
        String json = objectMapper.writeValueAsString(date);

        // Then
        assertNotNull(json);
        assertTrue(json.contains("2023") || json.contains("12") || json.contains("25"));
    }

    @Test
    void testObjectMapper_DeserializesOptional() throws JsonProcessingException {
        // Given
        String json = "\"test_value\"";

        // When
        Optional<String> result = objectMapper.readValue(json, objectMapper.getTypeFactory()
            .constructParametricType(Optional.class, String.class));

        // Then
        assertNotNull(result);
        assertTrue(result.isPresent());
        assertEquals("test_value", result.get());
    }

    @Test
    void testObjectMapper_DeserializesLocalDateTime() throws JsonProcessingException {
        // Given
        String json = "[2023,12,25,10,30,45]";

        // When
        LocalDateTime result = objectMapper.readValue(json, LocalDateTime.class);

        // Then
        assertNotNull(result);
        assertEquals(2023, result.getYear());
        assertEquals(12, result.getMonthValue());
        assertEquals(25, result.getDayOfMonth());
        assertEquals(10, result.getHour());
        assertEquals(30, result.getMinute());
        assertEquals(45, result.getSecond());
    }

    @Test
    void testObjectMapper_DeserializesLocalDate() throws JsonProcessingException {
        // Given
        String json = "[2023,12,25]";

        // When
        LocalDate result = objectMapper.readValue(json, LocalDate.class);

        // Then
        assertNotNull(result);
        assertEquals(2023, result.getYear());
        assertEquals(12, result.getMonthValue());
        assertEquals(25, result.getDayOfMonth());
    }

    @Test
    void testObjectMapper_HandlesComplexObjectWithOptional() throws JsonProcessingException {
        // Given
        TestObjectWithOptional testObj = new TestObjectWithOptional();
        testObj.name = "test";
        testObj.optionalValue = Optional.of("optional_test");
        testObj.emptyOptional = Optional.empty();

        // When
        String json = objectMapper.writeValueAsString(testObj);
        TestObjectWithOptional result = objectMapper.readValue(json, TestObjectWithOptional.class);

        // Then
        assertNotNull(json);
        assertNotNull(result);
        assertEquals("test", result.name);
        assertTrue(result.optionalValue.isPresent());
        assertEquals("optional_test", result.optionalValue.get());
    }

    @Test
    void testObjectMapper_HandlesComplexObjectWithDateTime() throws JsonProcessingException {
        // Given
        TestObjectWithDateTime testObj = new TestObjectWithDateTime();
        testObj.name = "test";
        testObj.createdAt = LocalDateTime.of(2023, 12, 25, 10, 30, 45);
        testObj.birthDate = LocalDate.of(1990, 5, 15);

        // When
        String json = objectMapper.writeValueAsString(testObj);
        TestObjectWithDateTime result = objectMapper.readValue(json, TestObjectWithDateTime.class);

        // Then
        assertNotNull(json);
        assertNotNull(result);
        assertEquals("test", result.name);
        assertEquals(testObj.createdAt, result.createdAt);
        assertEquals(testObj.birthDate, result.birthDate);
    }

    @Test
    void testObjectMapper_InheritsEnhancedObjectMapperFeatures() throws JsonProcessingException {
        // Given
        TestObjectWithNulls testObj = new TestObjectWithNulls();
        testObj.name = "test";
        testObj.nullValue = null;
        testObj.emptyString = "";
        testObj.emptyList = new ArrayList<>();
        testObj.emptyMap = new HashMap<>();

        // When
        String json = objectMapper.writeValueAsString(testObj);

        // Then
        assertNotNull(json);
        // Should not include empty values due to EnhancedObjectMapper configuration
        assertFalse(json.contains("nullValue"));
        assertFalse(json.contains("emptyString"));
        assertFalse(json.contains("emptyList"));
        assertFalse(json.contains("emptyMap"));
        assertTrue(json.contains("name"));
    }

    @Test
    void testObjectMapper_IgnoresUnknownProperties() throws JsonProcessingException {
        // Given
        String jsonWithUnknownProperty = "{\"name\":\"test\",\"unknownProperty\":\"value\"}";

        // When & Then
        assertDoesNotThrow(() -> {
            TestSimpleObject result = objectMapper.readValue(jsonWithUnknownProperty, TestSimpleObject.class);
            assertNotNull(result);
            assertEquals("test", result.name);
        });
    }

    @Test
    void testObjectMapper_HandlesNestedObjects() throws JsonProcessingException {
        // Given
        TestNestedObject nested = new TestNestedObject();
        nested.innerName = "inner";
        nested.innerDate = LocalDate.of(2023, 1, 1);

        TestObjectWithNested testObj = new TestObjectWithNested();
        testObj.name = "outer";
        testObj.nested = nested;
        testObj.optionalNested = Optional.of(nested);

        // When
        String json = objectMapper.writeValueAsString(testObj);
        TestObjectWithNested result = objectMapper.readValue(json, TestObjectWithNested.class);

        // Then
        assertNotNull(json);
        assertNotNull(result);
        assertEquals("outer", result.name);
        assertNotNull(result.nested);
        assertEquals("inner", result.nested.innerName);
        assertEquals(LocalDate.of(2023, 1, 1), result.nested.innerDate);
        assertTrue(result.optionalNested.isPresent());
    }

    @Test
    void testObjectMapper_HandlesCollections() throws JsonProcessingException {
        // Given
        List<String> stringList = Arrays.asList("item1", "item2", "item3");
        Map<String, Integer> stringIntMap = Map.of("key1", 1, "key2", 2);

        TestObjectWithCollections testObj = new TestObjectWithCollections();
        testObj.stringList = stringList;
        testObj.stringIntMap = stringIntMap;

        // When
        String json = objectMapper.writeValueAsString(testObj);
        TestObjectWithCollections result = objectMapper.readValue(json, TestObjectWithCollections.class);

        // Then
        assertNotNull(json);
        assertNotNull(result);
        assertEquals(3, result.stringList.size());
        assertTrue(result.stringList.contains("item1"));
        assertEquals(2, result.stringIntMap.size());
        assertEquals(Integer.valueOf(1), result.stringIntMap.get("key1"));
    }

    @Test
    void testJacksonConfig_IsSpringConfiguration() {
        // When
        Class<JacksonConfig> clazz = JacksonConfig.class;

        // Then
        assertTrue(clazz.isAnnotationPresent(org.springframework.context.annotation.Configuration.class));
    }

    @Test
    void testCustomObjectMapper_HasBeanAnnotation() throws NoSuchMethodException {
        // When
        var method = JacksonConfig.class.getMethod("customObjectMapper");

        // Then
        assertTrue(method.isAnnotationPresent(org.springframework.context.annotation.Bean.class));
    }

    @Test
    void testObjectMapper_ThreadSafety() {
        // Given
        List<Thread> threads = new ArrayList<>();
        List<Exception> exceptions = Collections.synchronizedList(new ArrayList<>());

        // When
        for (int i = 0; i < 10; i++) {
            Thread thread = new Thread(() -> {
                try {
                    for (int j = 0; j < 100; j++) {
                        TestSimpleObject obj = new TestSimpleObject();
                        obj.name = "test" + j;
                        String json = objectMapper.writeValueAsString(obj);
                        TestSimpleObject result = objectMapper.readValue(json, TestSimpleObject.class);
                        assertEquals("test" + j, result.name);
                    }
                } catch (Exception e) {
                    exceptions.add(e);
                }
            });
            threads.add(thread);
            thread.start();
        }

        // Wait for all threads to complete
        threads.forEach(thread -> {
            try {
                thread.join();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        });

        // Then
        assertTrue(exceptions.isEmpty(), "ObjectMapper should be thread-safe");
    }

    // Test helper classes
    public static class TestObjectWithOptional {
        public String name;
        public Optional<String> optionalValue;
        public Optional<String> emptyOptional;
    }

    public static class TestObjectWithDateTime {
        public String name;
        public LocalDateTime createdAt;
        public LocalDate birthDate;
    }

    public static class TestObjectWithNulls {
        public String name;
        public String nullValue;
        public String emptyString;
        public List<String> emptyList;
        public Map<String, String> emptyMap;
    }

    public static class TestSimpleObject {
        public String name;
    }

    public static class TestNestedObject {
        public String innerName;
        public LocalDate innerDate;
    }

    public static class TestObjectWithNested {
        public String name;
        public TestNestedObject nested;
        public Optional<TestNestedObject> optionalNested;
    }

    public static class TestObjectWithCollections {
        public List<String> stringList;
        public Map<String, Integer> stringIntMap;
    }
}
