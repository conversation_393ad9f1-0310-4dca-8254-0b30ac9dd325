package com.derbysoft.next.propertyconnect.channel.task.domain.dto;

import com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.RemoteChannelService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

class ChannelRoomInfoTest {

    private ChannelRoomInfo channelRoomInfo;

    @BeforeEach
    void setUp() {
        channelRoomInfo = new ChannelRoomInfo("room-123");
    }

    @Test
    void testConstructorWithCode() {
        // Given
        String roomCode = "test-room-456";

        // When
        ChannelRoomInfo roomInfo = new ChannelRoomInfo(roomCode);

        // Then
        assertEquals(roomCode, roomInfo.getCode());
    }

    @Test
    void testNoArgsConstructor() {
        // When
        ChannelRoomInfo roomInfo = new ChannelRoomInfo();

        // Then
        assertNotNull(roomInfo);
        assertNull(roomInfo.getCode());
    }

    @Test
    void testCodePattern() {
        // When
        String codePattern = channelRoomInfo.codePattern();

        // Then
        assertEquals("RoomCode", codePattern);
    }

    @Test
    void testGetCode() {
        // When
        String code = channelRoomInfo.getCode();

        // Then
        assertEquals("room-123", code);
    }

    @Test
    void testBasicProperties() {
        // Given
        String name = "Deluxe Room";
        String description = "A spacious deluxe room with ocean view";
        Boolean smoking = false;

        // When
        channelRoomInfo.setName(name);
        channelRoomInfo.setDescription(description);
        channelRoomInfo.setSmoking(smoking);

        // Then
        assertEquals(name, channelRoomInfo.getName());
        assertEquals(description, channelRoomInfo.getDescription());
        assertEquals(smoking, channelRoomInfo.getSmoking());
    }

    @Test
    void testStatusProperties() {
        // Given
        ItemStatus status = ItemStatus.Actived;
        SyncStatus syncStatus = SyncStatus.SYNCED;
        String errorCode = "ERR001";
        String errorMessage = "Test error";
        String lastOperationToken = "token-123";
        RemoteChannelService.Operation operation = RemoteChannelService.Operation.SaveRoomTypes;

        // When
        channelRoomInfo.setStatus(status);
        channelRoomInfo.setSyncStatus(syncStatus);
        channelRoomInfo.setErrorCode(errorCode);
        channelRoomInfo.setErrorMessage(errorMessage);
        channelRoomInfo.setLastOperationToken(lastOperationToken);
        channelRoomInfo.setOperation(operation);

        // Then
        assertEquals(status, channelRoomInfo.getStatus());
        assertEquals(syncStatus, channelRoomInfo.getSyncStatus());
        assertEquals(errorCode, channelRoomInfo.getErrorCode());
        assertEquals(errorMessage, channelRoomInfo.getErrorMessage());
        assertEquals(lastOperationToken, channelRoomInfo.getLastOperationToken());
        assertEquals(operation, channelRoomInfo.getOperation());
    }

    @Test
    void testOccupancy() {
        // Given
        ChannelRoomInfo.Occupancy occupancy = new ChannelRoomInfo.Occupancy();
        occupancy.setMaxOccupancy(4);
        occupancy.setMaxAdult(2);
        occupancy.setMaxChild(2);

        // When
        channelRoomInfo.setOccupancy(occupancy);

        // Then
        assertNotNull(channelRoomInfo.getOccupancy());
        assertEquals(4, channelRoomInfo.getOccupancy().getMaxOccupancy());
        assertEquals(2, channelRoomInfo.getOccupancy().getMaxAdult());
        assertEquals(2, channelRoomInfo.getOccupancy().getMaxChild());
    }

    @Test
    void testBeds() {
        // Given
        ChannelRoomInfo.BedsItem bed1 = new ChannelRoomInfo.BedsItem();
        bed1.setType("King");
        bed1.setSize("Large");

        ChannelRoomInfo.BedsItem bed2 = new ChannelRoomInfo.BedsItem();
        bed2.setType("Single");
        bed2.setSize("Standard");

        List<ChannelRoomInfo.BedsItem> beds = List.of(bed1, bed2);

        // When
        channelRoomInfo.setBeds(beds);

        // Then
        assertNotNull(channelRoomInfo.getBeds());
        assertEquals(2, channelRoomInfo.getBeds().size());
        assertEquals("King", channelRoomInfo.getBeds().get(0).getType());
        assertEquals("Large", channelRoomInfo.getBeds().get(0).getSize());
        assertEquals("Single", channelRoomInfo.getBeds().get(1).getType());
        assertEquals("Standard", channelRoomInfo.getBeds().get(1).getSize());
    }

    @Test
    void testExtraBedding() {
        // Given
        ChannelRoomInfo.ExtraBeddingItem extraBed = new ChannelRoomInfo.ExtraBeddingItem();
        extraBed.setType("Sofa Bed");
        extraBed.setQuantity(1);
        extraBed.setChargeAmount(50);

        List<ChannelRoomInfo.ExtraBeddingItem> extraBedding = List.of(extraBed);

        // When
        channelRoomInfo.setExtraBedding(extraBedding);

        // Then
        assertNotNull(channelRoomInfo.getExtraBedding());
        assertEquals(1, channelRoomInfo.getExtraBedding().size());
        assertEquals("Sofa Bed", channelRoomInfo.getExtraBedding().get(0).getType());
        assertEquals(1, channelRoomInfo.getExtraBedding().get(0).getQuantity());
        assertEquals(50, channelRoomInfo.getExtraBedding().get(0).getChargeAmount());
    }

    @Test
    void testI18n() {
        // Given
        Map<String, Object> i18n = new HashMap<>();
        i18n.put("en", Map.of("name", "Deluxe Room", "description", "A deluxe room"));
        i18n.put("zh", Map.of("name", "豪华房", "description", "一间豪华房间"));

        // When
        channelRoomInfo.setI18n(i18n);

        // Then
        assertNotNull(channelRoomInfo.getI18n());
        assertEquals(2, channelRoomInfo.getI18n().size());
        assertTrue(channelRoomInfo.getI18n().containsKey("en"));
        assertTrue(channelRoomInfo.getI18n().containsKey("zh"));
    }

    @Test
    void testExtensions() {
        // Given
        Map<String, Object> extensions = new HashMap<>();
        extensions.put("customField1", "value1");
        extensions.put("customField2", 42);
        extensions.put("customField3", true);

        // When
        channelRoomInfo.setExtensions(extensions);

        // Then
        assertNotNull(channelRoomInfo.getExtensions());
        assertEquals(3, channelRoomInfo.getExtensions().size());
        assertEquals("value1", channelRoomInfo.getExtensions().get("customField1"));
        assertEquals(42, channelRoomInfo.getExtensions().get("customField2"));
        assertEquals(true, channelRoomInfo.getExtensions().get("customField3"));
    }

    @Test
    void testEqualsAndHashCode() {
        // Given
        ChannelRoomInfo roomInfo1 = new ChannelRoomInfo("room-123");
        roomInfo1.setName("Deluxe Room");
        roomInfo1.setSmoking(false);

        ChannelRoomInfo roomInfo2 = new ChannelRoomInfo("room-123");
        roomInfo2.setName("Deluxe Room");
        roomInfo2.setSmoking(false);

        ChannelRoomInfo roomInfo3 = new ChannelRoomInfo("room-456");
        roomInfo3.setName("Standard Room");

        // When & Then
        assertEquals(roomInfo1, roomInfo2);
        assertNotEquals(roomInfo1, roomInfo3);
        assertEquals(roomInfo1.hashCode(), roomInfo2.hashCode());
    }

    @Test
    void testToString() {
        // Given
        channelRoomInfo.setName("Deluxe Room");
        channelRoomInfo.setSmoking(false);

        // When
        String toString = channelRoomInfo.toString();

        // Then
        assertNotNull(toString);
        assertTrue(toString.contains("room-123"));
        assertTrue(toString.contains("Deluxe Room"));
    }

    @Test
    void testInventoryItemStatusInterface() {
        // Given
        SyncStatus syncStatus = SyncStatus.SYNCED;
        String errorCode = "ERR001";
        String errorMessage = "Test error";

        // When
        channelRoomInfo.setSyncStatus(syncStatus);
        channelRoomInfo.setErrorCode(errorCode);
        channelRoomInfo.setErrorMessage(errorMessage);

        // Then
        assertEquals(syncStatus, channelRoomInfo.getSyncStatus());
        assertEquals(errorCode, channelRoomInfo.getErrorCode());
        assertEquals(errorMessage, channelRoomInfo.getErrorMessage());
        assertEquals("RoomCode", channelRoomInfo.codePattern());
        assertEquals("room-123", channelRoomInfo.getCode());
    }

    @Test
    void testGetName() {
        // Given
        String roomName = "Executive Suite";
        channelRoomInfo.setName(roomName);

        // When
        String name = channelRoomInfo.getName();

        // Then
        assertEquals(roomName, name);
    }

    @Test
    void testNullValues() {
        // Given
        ChannelRoomInfo roomInfo = new ChannelRoomInfo();

        // When & Then
        assertNull(roomInfo.getCode());
        assertNull(roomInfo.getName());
        assertNull(roomInfo.getDescription());
        assertNull(roomInfo.getOccupancy());
        assertNull(roomInfo.getSmoking());
        assertNull(roomInfo.getBeds());
        assertNull(roomInfo.getExtraBedding());
        assertNull(roomInfo.getI18n());
        assertNull(roomInfo.getStatus());
        assertNull(roomInfo.getSyncStatus());
        assertNull(roomInfo.getErrorCode());
        assertNull(roomInfo.getErrorMessage());
        assertNull(roomInfo.getLastOperationToken());
        assertNull(roomInfo.getOperation());
        assertNull(roomInfo.getExtensions());
    }

    @Test
    void testSetCode() {
        // Given
        String newCode = "new-room-code";

        // When
        channelRoomInfo.setCode(newCode);

        // Then
        assertEquals(newCode, channelRoomInfo.getCode());
    }

    @Test
    void testComplexRoomConfiguration() {
        // Given
        channelRoomInfo.setName("Presidential Suite");
        channelRoomInfo.setDescription("Luxurious presidential suite with panoramic view");
        channelRoomInfo.setSmoking(false);

        ChannelRoomInfo.Occupancy occupancy = new ChannelRoomInfo.Occupancy();
        occupancy.setMaxOccupancy(6);
        occupancy.setMaxAdult(4);
        occupancy.setMaxChild(2);
        channelRoomInfo.setOccupancy(occupancy);

        ChannelRoomInfo.BedsItem kingBed = new ChannelRoomInfo.BedsItem();
        kingBed.setType("King");
        kingBed.setSize("Large");

        ChannelRoomInfo.BedsItem queenBed = new ChannelRoomInfo.BedsItem();
        queenBed.setType("Queen");
        queenBed.setSize("Medium");

        channelRoomInfo.setBeds(List.of(kingBed, queenBed));

        ChannelRoomInfo.ExtraBeddingItem sofaBed = new ChannelRoomInfo.ExtraBeddingItem();
        sofaBed.setType("Sofa Bed");
        sofaBed.setQuantity(1);
        sofaBed.setChargeAmount(30);
        channelRoomInfo.setExtraBedding(List.of(sofaBed));

        Map<String, Object> extensions = new HashMap<>();
        extensions.put("hasBalcony", true);
        extensions.put("floorLevel", "Top");
        extensions.put("squareMeters", 150);
        channelRoomInfo.setExtensions(extensions);

        // When & Then
        assertEquals("Presidential Suite", channelRoomInfo.getName());
        assertEquals(6, channelRoomInfo.getOccupancy().getMaxOccupancy());
        assertEquals(2, channelRoomInfo.getBeds().size());
        assertEquals(1, channelRoomInfo.getExtraBedding().size());
        assertEquals(true, channelRoomInfo.getExtensions().get("hasBalcony"));
        assertEquals("Top", channelRoomInfo.getExtensions().get("floorLevel"));
        assertEquals(150, channelRoomInfo.getExtensions().get("squareMeters"));
    }
}
