package com.derbysoft.next.propertyconnect.channel.task.service.impl;

import com.derbysoft.next.commons.core.logsupport.handler.PerfLogHandler;
import com.derbysoft.next.propertyconenct.channel.common.exception.response.UnifyResult;
import com.derbysoft.next.propertyconnect.channel.task.controller.vo.ChannelRatePlanSaveVO;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelProductsDTO;
import com.derbysoft.next.propertyconnect.channel.task.service.RatePlanService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.ObjectProvider;

import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ChannelRatePlanAPIServiceImplTest {

    @InjectMocks
    private ChannelRatePlanAPIServiceImpl channelRatePlanAPIService;

    @Mock
    private ObjectProvider<RatePlanService> ratePlanServices;

    @Mock
    private ChannelRatePlanAPIServiceImpl.ChannelRatePlanTranslator translator;

    @Test
    void givenSupportedChannel_whenSyncRatePlans_thenReturnsResult() {
        String channelId = "supportedChannel";
        String channelHotelId = "hotel1";
        String hotelId = "hotel1";

        RatePlanService ratePlanService = mock(RatePlanService.class);
        when(ratePlanService.channel()).thenReturn(channelId);
        when(ratePlanServices.stream()).thenReturn(Stream.of(ratePlanService));
        when(ratePlanService.syncRatePlans(any(), any(), any())).thenReturn(new ChannelProductsDTO());
        when(translator.reverseMap(any(ChannelProductsDTO.class))).thenReturn(new ChannelRatePlanSaveVO());

        UnifyResult<ChannelRatePlanSaveVO> result = channelRatePlanAPIService.syncRatePlans(channelId, channelHotelId, hotelId);

        assertNotNull(result);
    }

    @Test
    void givenUnsupportedChannel_whenSyncRatePlans_thenThrowsException() {
        String channelId = "unsupportedChannel";
        String channelHotelId = "hotel1";
        String hotelId = "hotel1";

        when(ratePlanServices.stream()).thenReturn(Stream.empty());

        assertThrows(IllegalArgumentException.class, () -> channelRatePlanAPIService.syncRatePlans(channelId, channelHotelId, hotelId));
    }

    @Test
    void givenSuccessCallback_whenSyncRatePlansCallback_thenReturnsNull() {
        // Mock PerfLogHandler
        PerfLogHandler mockPerfHandler = mock(PerfLogHandler.class);

        try (MockedStatic<PerfLogHandler> mockedStatic = mockStatic(PerfLogHandler.class)) {
            mockedStatic.when(PerfLogHandler::currentHandler).thenReturn(mockPerfHandler);

            String callbackData = "<Success />";
            UnifyResult<ChannelRatePlanSaveVO> result = channelRatePlanAPIService.syncRatePlansCallback("channel1", "hotel1", "hotel1", callbackData);
            assertNotNull(result);
        }
    }

    @Test
    void givenErrorCallback_whenSyncRatePlansCallback_thenThrowsException() {
        // Mock PerfLogHandler
        PerfLogHandler mockPerfHandler = mock(PerfLogHandler.class);

        try (MockedStatic<PerfLogHandler> mockedStatic = mockStatic(PerfLogHandler.class)) {
            mockedStatic.when(PerfLogHandler::currentHandler).thenReturn(mockPerfHandler);

            String callbackData = "<Error ShortText=\"Error Message\" Code=\"123\" />";
            assertThrows(IllegalStateException.class, () -> channelRatePlanAPIService.syncRatePlansCallback("channel1", "hotel1", "hotel1", callbackData));
        }
    }
}