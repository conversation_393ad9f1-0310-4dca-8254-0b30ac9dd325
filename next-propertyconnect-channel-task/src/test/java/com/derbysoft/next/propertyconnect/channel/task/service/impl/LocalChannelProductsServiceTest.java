package com.derbysoft.next.propertyconnect.channel.task.service.impl;

import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelProductsDTO;
import com.derbysoft.next.propertyconnect.channel.task.domain.entity.ChannelProductsPO;
import com.derbysoft.next.propertyconnect.channel.task.domain.repository.ChannelProductsCacheRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * Comprehensive unit tests for LocalChannelProductsService
 * Tests local channel products management including caching and retrieval
 */
@ExtendWith(MockitoExtension.class)
class LocalChannelProductsServiceTest {

    @Mock
    private ChannelProductsCacheRepository mockRepository;

    private LocalChannelProductsService localChannelProductsService;

    @BeforeEach
    void setUp() {
        localChannelProductsService = new LocalChannelProductsService(mockRepository);
    }

    @Test
    void testGetChannelProducts_WithValidParameters_ReturnsChannelProductsDTO() {
        // Given
        String channelId = "BOOKING";
        String channelHotelId = "HOTEL_123";
        ChannelProductsPO mockPO = createTestChannelProductsPO();
        
        when(mockRepository.findByChannelIdAndChannelHotelId(channelId, channelHotelId))
            .thenReturn(mockPO);

        // When
        ChannelProductsDTO result = localChannelProductsService.getChannelProducts(channelId, channelHotelId);

        // Then
        assertNotNull(result);
        assertEquals(channelId, result.getChannelId());
        assertEquals(channelHotelId, result.getChannelHotelId());
        assertEquals("Test Hotel", result.getHotelName());
        assertNotNull(result.getChannelProducts());
        assertEquals(2, result.getChannelProducts().size());
    }

    @Test
    void testGetChannelProducts_WithNullResult_ReturnsNull() {
        // Given
        String channelId = "BOOKING";
        String channelHotelId = "HOTEL_123";
        
        when(mockRepository.findByChannelIdAndChannelHotelId(channelId, channelHotelId))
            .thenReturn(null);

        // When
        ChannelProductsDTO result = localChannelProductsService.getChannelProducts(channelId, channelHotelId);

        // Then
        assertNull(result);
    }

    @Test
    void testGetLastQueryDate_WithExistingData_ReturnsLastRetrieveDate() {
        // Given
        String channelId = "BOOKING";
        LocalDateTime expectedDate = LocalDateTime.of(2023, 12, 25, 10, 30, 45);
        ChannelProductsPO mockPO = createTestChannelProductsPO();
        mockPO.setRetrieveDate(expectedDate);
        
        when(mockRepository.findFirstByChannelIdOrderByRetrieveDateDesc(channelId))
            .thenReturn(mockPO);

        // When
        LocalDateTime result = localChannelProductsService.getLastQueryDate(channelId);

        // Then
        assertNotNull(result);
        assertEquals(expectedDate, result);
    }

    @Test
    void testGetLastQueryDate_WithNoData_ReturnsLocalDateTimeMin() {
        // Given
        String channelId = "BOOKING";
        
        when(mockRepository.findFirstByChannelIdOrderByRetrieveDateDesc(channelId))
            .thenReturn(null);

        // When
        LocalDateTime result = localChannelProductsService.getLastQueryDate(channelId);

        // Then
        assertNotNull(result);
        assertEquals(LocalDateTime.MIN, result);
    }

    @Test
    void testSaveChannelProductCache_WithNewEntity_CreatesAndSavesNewEntity() {
        // Given
        ChannelProductsDTO productsCache = createTestChannelProductsDTO();
        ChannelProductsPO savedPO = createTestChannelProductsPO();
        
        when(mockRepository.findByChannelIdAndChannelHotelId(
            productsCache.getChannelId(), productsCache.getChannelHotelId()))
            .thenReturn(null);
        when(mockRepository.save(any(ChannelProductsPO.class))).thenReturn(savedPO);

        // When
        ChannelProductsPO result = localChannelProductsService.saveChannelProductCache(productsCache);

        // Then
        assertNotNull(result);
        assertSame(savedPO, result);
        verify(mockRepository).save(any(ChannelProductsPO.class));
    }

    @Test
    void testSaveChannelProductCache_WithExistingEntity_UpdatesExistingEntity() {
        // Given
        ChannelProductsDTO productsCache = createTestChannelProductsDTO();
        ChannelProductsPO existingPO = createTestChannelProductsPO();
        ChannelProductsPO savedPO = createTestChannelProductsPO();
        
        when(mockRepository.findByChannelIdAndChannelHotelId(
            productsCache.getChannelId(), productsCache.getChannelHotelId()))
            .thenReturn(existingPO);
        when(mockRepository.save(existingPO)).thenReturn(savedPO);

        // When
        ChannelProductsPO result = localChannelProductsService.saveChannelProductCache(productsCache);

        // Then
        assertNotNull(result);
        assertSame(savedPO, result);
        verify(mockRepository).save(existingPO);
    }

    @Test
    void testSaveChannelProductCache_SetsRetrieveDateCorrectly() {
        // Given
        ChannelProductsDTO productsCache = createTestChannelProductsDTO();
        String retrieveDateString = "2023-12-25T10:30:45.123";
        productsCache.setRetrieveDate(retrieveDateString);
        
        ChannelProductsPO savedPO = createTestChannelProductsPO();
        
        when(mockRepository.findByChannelIdAndChannelHotelId(
            productsCache.getChannelId(), productsCache.getChannelHotelId()))
            .thenReturn(null);
        when(mockRepository.save(any(ChannelProductsPO.class))).thenReturn(savedPO);

        // When
        ChannelProductsPO result = localChannelProductsService.saveChannelProductCache(productsCache);

        // Then
        assertNotNull(result);
        verify(mockRepository).save(argThat(po -> {
            LocalDateTime expectedDate = LocalDateTime.parse(retrieveDateString);
            return expectedDate.equals(po.getRetrieveDate());
        }));
    }

    @Test
    void testGetChannelProducts_WithEmptyChannelProducts_HandlesCorrectly() {
        // Given
        String channelId = "BOOKING";
        String channelHotelId = "HOTEL_123";
        ChannelProductsPO mockPO = createTestChannelProductsPO();
        mockPO.setChannelProducts(new ArrayList<>()); // Empty list
        
        when(mockRepository.findByChannelIdAndChannelHotelId(channelId, channelHotelId))
            .thenReturn(mockPO);

        // When
        ChannelProductsDTO result = localChannelProductsService.getChannelProducts(channelId, channelHotelId);

        // Then
        assertNotNull(result);
        assertEquals(channelId, result.getChannelId());
        assertEquals(channelHotelId, result.getChannelHotelId());
        assertNotNull(result.getChannelProducts());
        assertTrue(result.getChannelProducts().isEmpty());
    }

    @Test
    void testGetChannelProducts_WithComplexProducts_MapsCorrectly() {
        // Given
        String channelId = "EXPEDIA";
        String channelHotelId = "HOTEL_COMPLEX";
        ChannelProductsPO mockPO = createComplexChannelProductsPO();
        
        when(mockRepository.findByChannelIdAndChannelHotelId(channelId, channelHotelId))
            .thenReturn(mockPO);

        // When
        ChannelProductsDTO result = localChannelProductsService.getChannelProducts(channelId, channelHotelId);

        // Then
        assertNotNull(result);
        assertEquals(channelId, result.getChannelId());
        assertEquals(channelHotelId, result.getChannelHotelId());
        assertNotNull(result.getChannelProducts());
        assertEquals(3, result.getChannelProducts().size());
        
        // Verify first product
        ChannelProductsDTO.Product firstProduct = result.getChannelProducts().get(0);
        assertEquals("ROOM_001", firstProduct.getChannelRoomId());
        assertEquals("Standard Room", firstProduct.getChannelRoomName());
        assertEquals("RATE_001", firstProduct.getChannelRateId());
        assertEquals("Standard Rate", firstProduct.getChannelRateName());
        assertEquals("Active", firstProduct.getStatus());
        assertTrue(firstProduct.getAvailStatus());
    }

    @Test
    void testSaveChannelProductCache_WithComplexDTO_HandlesCorrectly() {
        // Given
        ChannelProductsDTO complexDTO = createComplexChannelProductsDTO();
        ChannelProductsPO savedPO = createComplexChannelProductsPO();
        
        when(mockRepository.findByChannelIdAndChannelHotelId(
            complexDTO.getChannelId(), complexDTO.getChannelHotelId()))
            .thenReturn(null);
        when(mockRepository.save(any(ChannelProductsPO.class))).thenReturn(savedPO);

        // When
        ChannelProductsPO result = localChannelProductsService.saveChannelProductCache(complexDTO);

        // Then
        assertNotNull(result);
        assertSame(savedPO, result);
        verify(mockRepository).save(any(ChannelProductsPO.class));
    }

    @Test
    void testGetLastQueryDate_WithMultipleChannels_ReturnsCorrectDate() {
        // Given
        String channelId = "AGODA";
        LocalDateTime expectedDate = LocalDateTime.of(2023, 11, 15, 14, 20, 30);
        ChannelProductsPO mockPO = createTestChannelProductsPO();
        mockPO.setChannelId(channelId);
        mockPO.setRetrieveDate(expectedDate);
        
        when(mockRepository.findFirstByChannelIdOrderByRetrieveDateDesc(channelId))
            .thenReturn(mockPO);

        // When
        LocalDateTime result = localChannelProductsService.getLastQueryDate(channelId);

        // Then
        assertNotNull(result);
        assertEquals(expectedDate, result);
        verify(mockRepository).findFirstByChannelIdOrderByRetrieveDateDesc(channelId);
    }

    @Test
    void testSaveChannelProductCache_WithInvalidDateFormat_ThrowsException() {
        // Given
        ChannelProductsDTO productsCache = createTestChannelProductsDTO();
        productsCache.setRetrieveDate("invalid-date-format");
        
        when(mockRepository.findByChannelIdAndChannelHotelId(
            productsCache.getChannelId(), productsCache.getChannelHotelId()))
            .thenReturn(null);

        // When & Then
        assertThrows(Exception.class, () -> 
            localChannelProductsService.saveChannelProductCache(productsCache));
    }

    @Test
    void testGetChannelProducts_VerifyRepositoryCall() {
        // Given
        String channelId = "BOOKING";
        String channelHotelId = "HOTEL_123";
        
        when(mockRepository.findByChannelIdAndChannelHotelId(channelId, channelHotelId))
            .thenReturn(null);

        // When
        localChannelProductsService.getChannelProducts(channelId, channelHotelId);

        // Then
        verify(mockRepository, times(1)).findByChannelIdAndChannelHotelId(channelId, channelHotelId);
    }

    @Test
    void testGetLastQueryDate_VerifyRepositoryCall() {
        // Given
        String channelId = "BOOKING";
        
        when(mockRepository.findFirstByChannelIdOrderByRetrieveDateDesc(channelId))
            .thenReturn(null);

        // When
        localChannelProductsService.getLastQueryDate(channelId);

        // Then
        verify(mockRepository, times(1)).findFirstByChannelIdOrderByRetrieveDateDesc(channelId);
    }

    @Test
    void testSaveChannelProductCache_VerifyRepositoryCalls() {
        // Given
        ChannelProductsDTO productsCache = createTestChannelProductsDTO();
        ChannelProductsPO existingPO = createTestChannelProductsPO();
        ChannelProductsPO savedPO = createTestChannelProductsPO();
        
        when(mockRepository.findByChannelIdAndChannelHotelId(
            productsCache.getChannelId(), productsCache.getChannelHotelId()))
            .thenReturn(existingPO);
        when(mockRepository.save(existingPO)).thenReturn(savedPO);

        // When
        localChannelProductsService.saveChannelProductCache(productsCache);

        // Then
        verify(mockRepository, times(1)).findByChannelIdAndChannelHotelId(
            productsCache.getChannelId(), productsCache.getChannelHotelId());
        verify(mockRepository, times(1)).save(existingPO);
    }

    private ChannelProductsPO createTestChannelProductsPO() {
        ChannelProductsPO po = ChannelProductsPO.builder()
            .id("test_id")
            .channelId("BOOKING")
            .channelHotelId("HOTEL_123")
            .hotelName("Test Hotel")
            .retrieveDate(LocalDateTime.now())
            .build();

        ChannelProductsPO.Product product1 = new ChannelProductsPO.Product();
        product1.setChannelRoomId("ROOM_001");
        product1.setChannelRoomName("Standard Room");
        product1.setChannelRateId("RATE_001");
        product1.setChannelRateName("Standard Rate");
        product1.setStatus("Active");
        product1.setAvailStatus(true);

        ChannelProductsPO.Product product2 = new ChannelProductsPO.Product();
        product2.setChannelRoomId("ROOM_002");
        product2.setChannelRoomName("Deluxe Room");
        product2.setChannelRateId("RATE_002");
        product2.setChannelRateName("Premium Rate");
        product2.setStatus("Active");
        product2.setAvailStatus(false);

        po.setChannelProducts(new ArrayList<>(Arrays.asList(product1, product2)));
        return po;
    }

    private ChannelProductsDTO createTestChannelProductsDTO() {
        ChannelProductsDTO dto = new ChannelProductsDTO();
        dto.setChannelId("BOOKING");
        dto.setChannelHotelId("HOTEL_123");
        dto.setHotelName("Test Hotel");
        dto.setRetrieveDate("2023-12-25T10:30:45.000");

        ChannelProductsDTO.Product product1 = new ChannelProductsDTO.Product();
        product1.setChannelRoomId("ROOM_001");
        product1.setChannelRoomName("Standard Room");
        product1.setChannelRateId("RATE_001");
        product1.setChannelRateName("Standard Rate");
        product1.setStatus("Active");
        product1.setAvailStatus(true);

        ChannelProductsDTO.Product product2 = new ChannelProductsDTO.Product();
        product2.setChannelRoomId("ROOM_002");
        product2.setChannelRoomName("Deluxe Room");
        product2.setChannelRateId("RATE_002");
        product2.setChannelRateName("Premium Rate");
        product2.setStatus("Active");
        product2.setAvailStatus(false);

        dto.setChannelProducts(new ArrayList<>(Arrays.asList(product1, product2)));
        return dto;
    }

    private ChannelProductsPO createComplexChannelProductsPO() {
        ChannelProductsPO po = ChannelProductsPO.builder()
            .id("complex_id")
            .channelId("EXPEDIA")
            .channelHotelId("HOTEL_COMPLEX")
            .hotelName("Complex Hotel")
            .retrieveDate(LocalDateTime.now())
            .build();

        List<ChannelProductsPO.Product> products = new ArrayList<>(Arrays.asList(
            createProductPO("ROOM_001", "Standard Room", "RATE_001", "Standard Rate", "Active", true),
            createProductPO("ROOM_002", "Deluxe Room", "RATE_002", "Premium Rate", "Active", false),
            createProductPO("ROOM_003", "Suite", "RATE_003", "Suite Rate", "Inactive", false)
        ));

        po.setChannelProducts(products);
        return po;
    }

    private ChannelProductsDTO createComplexChannelProductsDTO() {
        ChannelProductsDTO dto = new ChannelProductsDTO();
        dto.setChannelId("EXPEDIA");
        dto.setChannelHotelId("HOTEL_COMPLEX");
        dto.setHotelName("Complex Hotel");
        dto.setRetrieveDate("2023-12-25T10:30:45.000");

        List<ChannelProductsDTO.Product> products = new ArrayList<>(Arrays.asList(
            createProductDTO("ROOM_001", "Standard Room", "RATE_001", "Standard Rate", "Active", true),
            createProductDTO("ROOM_002", "Deluxe Room", "RATE_002", "Premium Rate", "Active", false),
            createProductDTO("ROOM_003", "Suite", "RATE_003", "Suite Rate", "Inactive", false)
        ));

        dto.setChannelProducts(products);
        return dto;
    }

    private ChannelProductsPO.Product createProductPO(String roomId, String roomName, String rateId, String rateName, String status, Boolean availStatus) {
        ChannelProductsPO.Product product = new ChannelProductsPO.Product();
        product.setChannelRoomId(roomId);
        product.setChannelRoomName(roomName);
        product.setChannelRateId(rateId);
        product.setChannelRateName(rateName);
        product.setStatus(status);
        product.setAvailStatus(availStatus);
        return product;
    }

    private ChannelProductsDTO.Product createProductDTO(String roomId, String roomName, String rateId, String rateName, String status, Boolean availStatus) {
        ChannelProductsDTO.Product product = new ChannelProductsDTO.Product();
        product.setChannelRoomId(roomId);
        product.setChannelRoomName(roomName);
        product.setChannelRateId(rateId);
        product.setChannelRateName(rateName);
        product.setStatus(status);
        product.setAvailStatus(availStatus);
        return product;
    }
}
