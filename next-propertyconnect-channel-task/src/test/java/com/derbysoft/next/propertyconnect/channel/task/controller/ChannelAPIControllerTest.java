package com.derbysoft.next.propertyconnect.channel.task.controller;

import com.derbysoft.next.propertyconenct.channel.common.exception.response.UnifyResult;
import com.derbysoft.next.propertyconnect.channel.task.controller.vo.ChannelVO;
import com.derbysoft.next.propertyconnect.channel.task.service.ChannelAPIService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * Comprehensive unit tests for ChannelAPIController
 * Tests all controller endpoints and error scenarios
 */
@ExtendWith(MockitoExtension.class)
class ChannelAPIControllerTest {

    @Mock
    private ChannelAPIService channelAPIService;

    @InjectMocks
    private ChannelAPIController channelAPIController;

    private ChannelVO testChannelVO;

    @BeforeEach
    void setUp() {
        testChannelVO = new ChannelVO();
        testChannelVO.setChannelId("TEST_CHANNEL");
        testChannelVO.setChannelName("Test Channel");
        testChannelVO.setState(ChannelVO.State.ENABLED);
    }

    @Test
    void testGetChannelSettings_Success() {
        // Given
        String channelId = "TEST_CHANNEL";
        when(channelAPIService.getChannelSettings(channelId)).thenReturn(testChannelVO);

        // When
        UnifyResult<ChannelVO> result = channelAPIController.getChannelSettings(channelId);

        // Then
        assertNotNull(result);
        assertEquals(testChannelVO, result.getObject());
        verify(channelAPIService).getChannelSettings(channelId);
    }

    @Test
    void testGetChannelSettings_WithValidChannelId_ReturnsCorrectData() {
        // Given
        String channelId = "BOOKING";
        ChannelVO expectedChannel = new ChannelVO();
        expectedChannel.setChannelId(channelId);
        expectedChannel.setChannelName("Booking.com");
        expectedChannel.setState(ChannelVO.State.ENABLED);
        
        when(channelAPIService.getChannelSettings(channelId)).thenReturn(expectedChannel);

        // When
        UnifyResult<ChannelVO> result = channelAPIController.getChannelSettings(channelId);

        // Then
        assertNotNull(result);
        assertNotNull(result.getObject());
        assertEquals(channelId, result.getObject().getChannelId());
        assertEquals("Booking.com", result.getObject().getChannelName());
        assertEquals(ChannelVO.State.ENABLED, result.getObject().getState());
        verify(channelAPIService).getChannelSettings(channelId);
    }

    @Test
    void testGetChannelSettings_WithDifferentChannelIds_CallsServiceCorrectly() {
        // Given
        String[] channelIds = {"AGODA", "EXPEDIA", "CTRIP", "HOTELTONIGHT"};
        
        for (String channelId : channelIds) {
            ChannelVO channelVO = new ChannelVO();
            channelVO.setChannelId(channelId);
            when(channelAPIService.getChannelSettings(channelId)).thenReturn(channelVO);
            
            // When
            UnifyResult<ChannelVO> result = channelAPIController.getChannelSettings(channelId);
            
            // Then
            assertNotNull(result);
            assertEquals(channelId, result.getObject().getChannelId());
            verify(channelAPIService).getChannelSettings(channelId);
        }
    }

    @Test
    void testGetChannelSettings_ServiceThrowsException_PropagatesException() {
        // Given
        String channelId = "TEST_CHANNEL";
        when(channelAPIService.getChannelSettings(channelId))
            .thenThrow(new RuntimeException("Service error"));

        // When & Then
        assertThrows(RuntimeException.class, () -> {
            channelAPIController.getChannelSettings(channelId);
        });
        verify(channelAPIService).getChannelSettings(channelId);
    }

    @Test
    void testGetChannelSettings_ServiceReturnsNull_HandlesGracefully() {
        // Given
        String channelId = "NONEXISTENT_CHANNEL";
        when(channelAPIService.getChannelSettings(channelId)).thenReturn(null);

        // When
        UnifyResult<ChannelVO> result = channelAPIController.getChannelSettings(channelId);

        // Then
        assertNotNull(result);
        assertNull(result.getObject());
        verify(channelAPIService).getChannelSettings(channelId);
    }

    @Test
    void testGetChannelSettings_WithSpecialCharacters_HandlesCorrectly() {
        // Given
        String channelId = "TEST-CHANNEL_123";
        ChannelVO channelVO = new ChannelVO();
        channelVO.setChannelId(channelId);
        when(channelAPIService.getChannelSettings(channelId)).thenReturn(channelVO);

        // When
        UnifyResult<ChannelVO> result = channelAPIController.getChannelSettings(channelId);

        // Then
        assertNotNull(result);
        assertEquals(channelId, result.getObject().getChannelId());
        verify(channelAPIService).getChannelSettings(channelId);
    }

    @Test
    void testGetChannelSettings_MultipleCallsSameChannel_CachingBehavior() {
        // Given
        String channelId = "TEST_CHANNEL";
        when(channelAPIService.getChannelSettings(channelId)).thenReturn(testChannelVO);

        // When
        UnifyResult<ChannelVO> result1 = channelAPIController.getChannelSettings(channelId);
        UnifyResult<ChannelVO> result2 = channelAPIController.getChannelSettings(channelId);

        // Then
        assertNotNull(result1);
        assertNotNull(result2);
        assertEquals(result1.getObject().getChannelId(), result2.getObject().getChannelId());
        verify(channelAPIService, times(2)).getChannelSettings(channelId);
    }

    @Test
    void testGetChannelSettings_WithLongChannelId_HandlesCorrectly() {
        // Given
        String longChannelId = "A".repeat(100);
        ChannelVO channelVO = new ChannelVO();
        channelVO.setChannelId(longChannelId);
        when(channelAPIService.getChannelSettings(longChannelId)).thenReturn(channelVO);

        // When
        UnifyResult<ChannelVO> result = channelAPIController.getChannelSettings(longChannelId);

        // Then
        assertNotNull(result);
        assertEquals(longChannelId, result.getObject().getChannelId());
        verify(channelAPIService).getChannelSettings(longChannelId);
    }

    @Test
    void testGetChannelSettings_ConcurrentAccess_ThreadSafe() {
        // Given
        String channelId = "CONCURRENT_TEST";
        when(channelAPIService.getChannelSettings(channelId)).thenReturn(testChannelVO);

        // When & Then
        assertDoesNotThrow(() -> {
            // Simulate concurrent access
            for (int i = 0; i < 10; i++) {
                UnifyResult<ChannelVO> result = channelAPIController.getChannelSettings(channelId);
                assertNotNull(result);
            }
        });
        
        verify(channelAPIService, times(10)).getChannelSettings(channelId);
    }

    @Test
    void testGetChannelSettings_VerifyMethodSignature() {
        // Given
        String channelId = "SIGNATURE_TEST";
        when(channelAPIService.getChannelSettings(channelId)).thenReturn(testChannelVO);

        // When
        UnifyResult<ChannelVO> result = channelAPIController.getChannelSettings(channelId);

        // Then
        assertNotNull(result);
        assertTrue(result instanceof UnifyResult);
        assertTrue(result.getObject() instanceof ChannelVO);
        verify(channelAPIService).getChannelSettings(eq(channelId));
    }

    @Test
    void testGetChannelSettings_WithEmptyChannelId_HandlesCorrectly() {
        // Given
        String channelId = "";
        ChannelVO channelVO = new ChannelVO();
        channelVO.setChannelId(channelId);
        when(channelAPIService.getChannelSettings(channelId)).thenReturn(channelVO);

        // When
        UnifyResult<ChannelVO> result = channelAPIController.getChannelSettings(channelId);

        // Then
        assertNotNull(result);
        assertEquals(channelId, result.getObject().getChannelId());
        verify(channelAPIService).getChannelSettings(channelId);
    }

    @Test
    void testGetChannelSettings_WithWhitespaceChannelId_HandlesCorrectly() {
        // Given
        String channelId = "   ";
        ChannelVO channelVO = new ChannelVO();
        channelVO.setChannelId(channelId);
        when(channelAPIService.getChannelSettings(channelId)).thenReturn(channelVO);

        // When
        UnifyResult<ChannelVO> result = channelAPIController.getChannelSettings(channelId);

        // Then
        assertNotNull(result);
        assertEquals(channelId, result.getObject().getChannelId());
        verify(channelAPIService).getChannelSettings(channelId);
    }

    @Test
    void testGetChannelSettings_VerifyNoSideEffects() {
        // Given
        String channelId = "SIDE_EFFECT_TEST";
        ChannelVO originalChannel = new ChannelVO();
        originalChannel.setChannelId(channelId);
        originalChannel.setChannelName("Original Name");
        
        when(channelAPIService.getChannelSettings(channelId)).thenReturn(originalChannel);

        // When
        UnifyResult<ChannelVO> result = channelAPIController.getChannelSettings(channelId);

        // Then
        assertNotNull(result);
        assertEquals("Original Name", result.getObject().getChannelName());
        // Verify original object is not modified
        assertEquals("Original Name", originalChannel.getChannelName());
        verify(channelAPIService).getChannelSettings(channelId);
    }
}
