package com.derbysoft.next.propertyconnect.channel.task.service.impl.lastminute;

import com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.RemoteChannelService;
import com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.adapter.ChannelHotelActivationCustomizeService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Comprehensive unit tests for LastMinuteAdapterService
 * Tests channel identification and procedure customization
 */
@ExtendWith(MockitoExtension.class)
class LastMinuteAdapterServiceTest {

    private LastMinuteAdapterService lastMinuteAdapterService;

    @BeforeEach
    void setUp() {
        lastMinuteAdapterService = new LastMinuteAdapterService();
    }

    @Test
    void testLastMinuteAdapterService_ImplementsCorrectInterface() {
        // Given & When & Then
        assertTrue(lastMinuteAdapterService instanceof ChannelHotelActivationCustomizeService,
                "LastMinuteAdapterService should implement ChannelHotelActivationCustomizeService");
    }

    @Test
    void testChannel_ReturnsCorrectChannelName() {
        // Given & When
        String channelName = lastMinuteAdapterService.channel();

        // Then
        assertNotNull(channelName, "Channel name should not be null");
        assertEquals("LASTMINUTE", channelName, "Channel name should be LASTMINUTE");
    }

    @Test
    void testChannel_ReturnsConsistentValue() {
        // Given & When
        String channelName1 = lastMinuteAdapterService.channel();
        String channelName2 = lastMinuteAdapterService.channel();

        // Then
        assertEquals(channelName1, channelName2, "Channel name should be consistent");
    }

    @Test
    void testCustomizeProcedure_ReturnsCorrectOperations() {
        // Given & When
        List<RemoteChannelService.Operation> operations = lastMinuteAdapterService.customizeProcedure();

        // Then
        assertNotNull(operations, "Operations list should not be null");
        assertEquals(2, operations.size(), "Should return exactly 2 operations");
        
        assertTrue(operations.contains(RemoteChannelService.Operation.SaveProperty),
                "Should contain SaveProperty operation");
        assertTrue(operations.contains(RemoteChannelService.Operation.TriggerARIRefresh),
                "Should contain TriggerARIRefresh operation");
    }

    @Test
    void testCustomizeProcedure_ReturnsOperationsInCorrectOrder() {
        // Given & When
        List<RemoteChannelService.Operation> operations = lastMinuteAdapterService.customizeProcedure();

        // Then
        assertEquals(RemoteChannelService.Operation.SaveProperty, operations.get(0),
                "First operation should be SaveProperty");
        assertEquals(RemoteChannelService.Operation.TriggerARIRefresh, operations.get(1),
                "Second operation should be TriggerARIRefresh");
    }

    @Test
    void testCustomizeProcedure_ReturnsConsistentResults() {
        // Given & When
        List<RemoteChannelService.Operation> operations1 = lastMinuteAdapterService.customizeProcedure();
        List<RemoteChannelService.Operation> operations2 = lastMinuteAdapterService.customizeProcedure();

        // Then
        assertEquals(operations1, operations2, "Should return consistent operations");
        assertEquals(operations1.size(), operations2.size(), "Should return same number of operations");

        for (int i = 0; i < operations1.size(); i++) {
            assertEquals(operations1.get(i), operations2.get(i),
                    "Operations at index " + i + " should be the same");
        }
    }

    @Test
    void testCustomizeProcedure_DoesNotContainUnnecessaryOperations() {
        // Given & When
        List<RemoteChannelService.Operation> operations = lastMinuteAdapterService.customizeProcedure();

        // Then
        assertFalse(operations.contains(RemoteChannelService.Operation.SaveCredential),
                "Should not contain SaveCredential operation");
        assertFalse(operations.contains(RemoteChannelService.Operation.SaveRoomTypes),
                "Should not contain SaveRoomTypes operation");
        assertFalse(operations.contains(RemoteChannelService.Operation.SaveRatePlans),
                "Should not contain SaveRatePlans operation");
        assertFalse(operations.contains(RemoteChannelService.Operation.SaveProducts),
                "Should not contain SaveProducts operation");
    }

    @Test
    void testService_IsAnnotatedWithSpringService() {
        // Given
        Class<?> serviceClass = LastMinuteAdapterService.class;

        // When
        boolean hasServiceAnnotation = serviceClass.isAnnotationPresent(org.springframework.stereotype.Service.class);

        // Then
        assertTrue(hasServiceAnnotation, "Service should be annotated with @Service");
    }

    @Test
    void testService_CanBeInstantiatedMultipleTimes() {
        // Given & When
        LastMinuteAdapterService service1 = new LastMinuteAdapterService();
        LastMinuteAdapterService service2 = new LastMinuteAdapterService();

        // Then
        assertNotNull(service1, "First instance should not be null");
        assertNotNull(service2, "Second instance should not be null");
        assertNotSame(service1, service2, "Instances should be different objects");
        
        assertEquals(service1.channel(), service2.channel(), "Both instances should return same channel");
        assertEquals(service1.customizeProcedure(), service2.customizeProcedure(),
                "Both instances should return same procedure");
    }

    @Test
    void testService_ThreadSafety() {
        // Given
        int threadCount = 10;
        Thread[] threads = new Thread[threadCount];
        String[] channelResults = new String[threadCount];
        List<RemoteChannelService.Operation>[] procedureResults = new List[threadCount];

        // When
        for (int i = 0; i < threadCount; i++) {
            final int index = i;
            threads[i] = new Thread(() -> {
                channelResults[index] = lastMinuteAdapterService.channel();
                procedureResults[index] = lastMinuteAdapterService.customizeProcedure();
            });
            threads[i].start();
        }

        // Wait for all threads to complete
        for (Thread thread : threads) {
            try {
                thread.join();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }

        // Then
        for (int i = 0; i < threadCount; i++) {
            assertEquals("LASTMINUTE", channelResults[i], "All threads should return same channel");
            assertNotNull(procedureResults[i], "All threads should return valid procedure");
            assertEquals(2, procedureResults[i].size(), "All threads should return same procedure size");
        }
    }

    @Test
    void testService_InheritsDefaultMethods() {
        // Given & When & Then
        // Test that default interface methods are available
        assertDoesNotThrow(() -> {
            // These are default methods from ChannelHotelActivationCustomizeService
            // The actual behavior depends on the interface implementation
            lastMinuteAdapterService.toString(); // Basic object method
        });
    }

    @Test
    void testService_SupportsPolymorphism() {
        // Given
        ChannelHotelActivationCustomizeService service = lastMinuteAdapterService;

        // When
        String channel = service.channel();
        List<RemoteChannelService.Operation> procedure = service.customizeProcedure();

        // Then
        assertEquals("LASTMINUTE", channel, "Polymorphic call should work correctly");
        assertNotNull(procedure, "Polymorphic call should return valid procedure");
        assertEquals(2, procedure.size(), "Polymorphic call should return correct procedure size");
    }

    @Test
    void testService_EqualsAndHashCode() {
        // Given
        LastMinuteAdapterService service1 = new LastMinuteAdapterService();
        LastMinuteAdapterService service2 = new LastMinuteAdapterService();

        // When & Then
        // Note: Default equals/hashCode behavior for objects
        assertNotEquals(service1, service2, "Different instances should not be equal by default");
        assertNotEquals(service1.hashCode(), service2.hashCode(), "Different instances should have different hash codes");
        
        // But their behavior should be the same
        assertEquals(service1.channel(), service2.channel(), "Behavior should be consistent");
        assertEquals(service1.customizeProcedure(), service2.customizeProcedure(), "Behavior should be consistent");
    }

    @Test
    void testService_ToString() {
        // Given & When
        String toString = lastMinuteAdapterService.toString();

        // Then
        assertNotNull(toString, "toString should not return null");
        assertTrue(toString.contains("LastMinuteAdapterService"), "toString should contain class name");
    }
}
