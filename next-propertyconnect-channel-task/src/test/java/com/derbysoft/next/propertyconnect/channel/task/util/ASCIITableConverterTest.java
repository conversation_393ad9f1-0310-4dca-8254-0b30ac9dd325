package com.derbysoft.next.propertyconnect.channel.task.util;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Comprehensive unit tests for ASCIITableConverter
 * Tests ASCII table generation from 2D string arrays with various scenarios
 */
@ExtendWith(MockitoExtension.class)
class ASCIITableConverterTest {

    @Test
    void testConvertToASCIITable_BasicTable_ReturnsFormattedTable() {
        // Given
        String[][] data = {
            {"Name", "Age", "City"},
            {"John", "25", "New York"},
            {"Jane", "30", "London"}
        };
        
        // When
        String result = ASCIITableConverter.convertToASCIITable(data);
        
        // Then
        assertNotNull(result);
        assertTrue(result.contains("Name"));
        assertTrue(result.contains("Age"));
        assertTrue(result.contains("City"));
        assertTrue(result.contains("<PERSON>"));
        assertTrue(result.contains("Jane"));
        assertTrue(result.contains("25"));
        assertTrue(result.contains("30"));
        assertTrue(result.contains("New York"));
        assertTrue(result.contains("London"));
        assertTrue(result.contains("|"));
        assertTrue(result.contains("-"));
    }

    @Test
    void testConvertToASCIITable_SingleRowHeader_ReturnsHeaderWithSeparator() {
        // Given
        String[][] data = {
            {"Header1", "Header2", "Header3"}
        };
        
        // When
        String result = ASCIITableConverter.convertToASCIITable(data);
        
        // Then
        assertNotNull(result);
        assertTrue(result.contains("Header1"));
        assertTrue(result.contains("Header2"));
        assertTrue(result.contains("Header3"));
        assertTrue(result.contains("|"));
        assertTrue(result.contains("-"));
        
        // Should have header line and separator line
        String[] lines = result.split("\n");
        assertEquals(2, lines.length);
    }

    @Test
    void testConvertToASCIITable_DifferentColumnWidths_AlignsCorrectly() {
        // Given
        String[][] data = {
            {"ID", "Name", "Description"},
            {"1", "John Smith", "Software Engineer with 5+ years experience"},
            {"22", "Jane", "Manager"}
        };
        
        // When
        String result = ASCIITableConverter.convertToASCIITable(data);
        
        // Then
        assertNotNull(result);
        String[] lines = result.split("\n");
        
        // All data lines should have the same length (excluding trailing spaces)
        assertTrue(lines.length >= 3);
        
        // Check that columns are properly aligned
        assertTrue(result.contains("Software Engineer with 5+ years experience"));
        assertTrue(result.contains("John Smith"));
        assertTrue(result.contains("Manager"));
    }

    @Test
    void testConvertToASCIITable_EmptyStrings_HandlesCorrectly() {
        // Given
        String[][] data = {
            {"Name", "Value", "Status"},
            {"", "100", "Active"},
            {"Test", "", "Inactive"},
            {"Sample", "200", ""}
        };
        
        // When
        String result = ASCIITableConverter.convertToASCIITable(data);
        
        // Then
        assertNotNull(result);
        assertTrue(result.contains("Name"));
        assertTrue(result.contains("Value"));
        assertTrue(result.contains("Status"));
        assertTrue(result.contains("100"));
        assertTrue(result.contains("Active"));
        assertTrue(result.contains("Test"));
        assertTrue(result.contains("Inactive"));
        assertTrue(result.contains("Sample"));
        assertTrue(result.contains("200"));
    }

    @Test
    void testConvertToASCIITable_SingleColumn_ReturnsCorrectFormat() {
        // Given
        String[][] data = {
            {"Items"},
            {"Apple"},
            {"Banana"},
            {"Cherry"}
        };
        
        // When
        String result = ASCIITableConverter.convertToASCIITable(data);
        
        // Then
        assertNotNull(result);
        assertTrue(result.contains("Items"));
        assertTrue(result.contains("Apple"));
        assertTrue(result.contains("Banana"));
        assertTrue(result.contains("Cherry"));
        assertTrue(result.contains("|"));
        assertTrue(result.contains("-"));
        
        String[] lines = result.split("\n");
        assertEquals(5, lines.length); // Header + separator + 3 data rows
    }

    @Test
    void testConvertToASCIITable_LargeTable_HandlesCorrectly() {
        // Given
        String[][] data = new String[6][4];
        data[0] = new String[]{"ID", "Name", "Department", "Salary"};
        data[1] = new String[]{"001", "Alice Johnson", "Engineering", "75000"};
        data[2] = new String[]{"002", "Bob Smith", "Marketing", "65000"};
        data[3] = new String[]{"003", "Carol Davis", "Human Resources", "70000"};
        data[4] = new String[]{"004", "David Wilson", "Finance", "80000"};
        data[5] = new String[]{"005", "Eve Brown", "Sales", "72000"};
        
        // When
        String result = ASCIITableConverter.convertToASCIITable(data);
        
        // Then
        assertNotNull(result);
        assertTrue(result.contains("Alice Johnson"));
        assertTrue(result.contains("Human Resources"));
        assertTrue(result.contains("Engineering"));
        assertTrue(result.contains("75000"));
        assertTrue(result.contains("80000"));
        
        String[] lines = result.split("\n");
        assertEquals(7, lines.length); // Header + separator + 5 data rows
    }

    @Test
    void testConvertToASCIITable_SpecialCharacters_HandlesCorrectly() {
        // Given
        String[][] data = {
            {"Symbol", "Unicode", "Description"},
            {"@", "U+0040", "At sign"},
            {"#", "U+0023", "Hash/Pound"},
            {"$", "U+0024", "Dollar sign"},
            {"&", "U+0026", "Ampersand"}
        };
        
        // When
        String result = ASCIITableConverter.convertToASCIITable(data);
        
        // Then
        assertNotNull(result);
        assertTrue(result.contains("@"));
        assertTrue(result.contains("#"));
        assertTrue(result.contains("$"));
        assertTrue(result.contains("&"));
        assertTrue(result.contains("U+0040"));
        assertTrue(result.contains("At sign"));
        assertTrue(result.contains("Ampersand"));
    }

    @Test
    void testConvertToASCIITable_NumbersAndText_FormatsCorrectly() {
        // Given
        String[][] data = {
            {"Product", "Price", "Quantity", "Total"},
            {"Laptop", "999.99", "2", "1999.98"},
            {"Mouse", "29.99", "5", "149.95"},
            {"Keyboard", "79.99", "3", "239.97"}
        };
        
        // When
        String result = ASCIITableConverter.convertToASCIITable(data);
        
        // Then
        assertNotNull(result);
        assertTrue(result.contains("Product"));
        assertTrue(result.contains("Price"));
        assertTrue(result.contains("Quantity"));
        assertTrue(result.contains("Total"));
        assertTrue(result.contains("999.99"));
        assertTrue(result.contains("1999.98"));
        assertTrue(result.contains("Laptop"));
        assertTrue(result.contains("Keyboard"));
    }

    @Test
    void testConvertToASCIITable_VeryLongContent_HandlesCorrectly() {
        // Given
        String[][] data = {
            {"Short", "Very Long Content Column"},
            {"A", "This is a very long piece of text that should be handled correctly by the ASCII table converter"},
            {"B", "Another long text entry that tests the column width calculation"}
        };
        
        // When
        String result = ASCIITableConverter.convertToASCIITable(data);
        
        // Then
        assertNotNull(result);
        assertTrue(result.contains("Very Long Content Column"));
        assertTrue(result.contains("This is a very long piece of text"));
        assertTrue(result.contains("Another long text entry"));
        
        // Check that the table structure is maintained
        String[] lines = result.split("\n");
        assertEquals(4, lines.length); // Header + separator + 2 data rows
    }

    @Test
    void testConvertToASCIITable_UniformColumnWidths_FormatsEvenly() {
        // Given
        String[][] data = {
            {"AAA", "BBB", "CCC"},
            {"111", "222", "333"},
            {"XXX", "YYY", "ZZZ"}
        };
        
        // When
        String result = ASCIITableConverter.convertToASCIITable(data);
        
        // Then
        assertNotNull(result);
        assertTrue(result.contains("AAA"));
        assertTrue(result.contains("BBB"));
        assertTrue(result.contains("CCC"));
        assertTrue(result.contains("111"));
        assertTrue(result.contains("222"));
        assertTrue(result.contains("333"));
        assertTrue(result.contains("XXX"));
        assertTrue(result.contains("YYY"));
        assertTrue(result.contains("ZZZ"));
        
        // All columns should have the same width (3 characters)
        String[] lines = result.split("\n");
        assertTrue(lines.length >= 3);
    }

    @Test
    void testConvertToASCIITable_MixedContentTypes_HandlesCorrectly() {
        // Given
        String[][] data = {
            {"Type", "Value", "Valid", "Notes"},
            {"String", "Hello World", "true", "Text data"},
            {"Number", "42", "true", "Integer value"},
            {"Boolean", "false", "true", "Boolean flag"},
            {"Null", "", "false", "Empty value"}
        };
        
        // When
        String result = ASCIITableConverter.convertToASCIITable(data);
        
        // Then
        assertNotNull(result);
        assertTrue(result.contains("Type"));
        assertTrue(result.contains("Value"));
        assertTrue(result.contains("Valid"));
        assertTrue(result.contains("Notes"));
        assertTrue(result.contains("Hello World"));
        assertTrue(result.contains("42"));
        assertTrue(result.contains("false"));
        assertTrue(result.contains("Text data"));
        assertTrue(result.contains("Integer value"));
        assertTrue(result.contains("Boolean flag"));
        assertTrue(result.contains("Empty value"));
    }

    @Test
    void testConvertToASCIITable_TableStructure_HasCorrectFormat() {
        // Given
        String[][] data = {
            {"Col1", "Col2"},
            {"Data1", "Data2"}
        };
        
        // When
        String result = ASCIITableConverter.convertToASCIITable(data);
        
        // Then
        assertNotNull(result);
        String[] lines = result.split("\n");
        assertEquals(3, lines.length);
        
        // First line should be header
        assertTrue(lines[0].contains("Col1"));
        assertTrue(lines[0].contains("Col2"));
        assertTrue(lines[0].contains("|"));
        
        // Second line should be separator with dashes
        assertTrue(lines[1].contains("-"));
        
        // Third line should be data
        assertTrue(lines[2].contains("Data1"));
        assertTrue(lines[2].contains("Data2"));
        assertTrue(lines[2].contains("|"));
    }

    @Test
    void testConvertToASCIITable_WhitespaceInData_PreservesSpaces() {
        // Given
        String[][] data = {
            {"Name", "Full Name"},
            {"John", "John  Smith"},
            {"Jane", "Jane   Doe"}
        };
        
        // When
        String result = ASCIITableConverter.convertToASCIITable(data);
        
        // Then
        assertNotNull(result);
        assertTrue(result.contains("John  Smith"));
        assertTrue(result.contains("Jane   Doe"));
        assertTrue(result.contains("Full Name"));
    }
}
