package com.derbysoft.next.propertyconnect.channel.task.config;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Comprehensive unit tests for SynchronizerProperties
 * Tests synchronizer properties configuration functionality
 */
@ExtendWith(MockitoExtension.class)
class SynchronizerPropertiesTest {

    private SynchronizerProperties synchronizerProperties;

    @BeforeEach
    void setUp() {
        synchronizerProperties = new SynchronizerProperties();
    }

    @Test
    void testSynchronizerProperties_HasConfigurationPropertiesAnnotation() {
        // When
        ConfigurationProperties annotation = SynchronizerProperties.class.getAnnotation(ConfigurationProperties.class);

        // Then
        assertNotNull(annotation);
        assertEquals("app.synchronizer", annotation.prefix());
    }

    @Test
    void testSynchronizerProperties_HasRefreshScopeAnnotation() {
        // When
        RefreshScope annotation = SynchronizerProperties.class.getAnnotation(RefreshScope.class);

        // Then
        assertNotNull(annotation);
    }

    @Test
    void testSynchronizerProperties_DefaultValues() {
        // When & Then
        assertNull(synchronizerProperties.getUrl());
    }

    @Test
    void testSynchronizerProperties_SetAndGetUrl() {
        // Given
        String url = "https://synchronizer2-manager.derbysoftsec.com/";

        // When
        synchronizerProperties.setUrl(url);

        // Then
        assertEquals(url, synchronizerProperties.getUrl());
    }

    @Test
    void testSynchronizerProperties_WithNullUrl() {
        // When
        synchronizerProperties.setUrl(null);

        // Then
        assertNull(synchronizerProperties.getUrl());
    }

    @Test
    void testSynchronizerProperties_WithEmptyUrl() {
        // When
        synchronizerProperties.setUrl("");

        // Then
        assertEquals("", synchronizerProperties.getUrl());
    }

    @Test
    void testSynchronizerProperties_WithWhitespaceUrl() {
        // Given
        String url = "   ";

        // When
        synchronizerProperties.setUrl(url);

        // Then
        assertEquals(url, synchronizerProperties.getUrl());
    }

    @Test
    void testSynchronizerProperties_WithProductionUrl() {
        // Given
        String prodUrl = "https://synchronizer2-manager.derbysoftsec.com/";

        // When
        synchronizerProperties.setUrl(prodUrl);

        // Then
        assertEquals(prodUrl, synchronizerProperties.getUrl());
    }

    @Test
    void testSynchronizerProperties_WithUATUrl() {
        // Given
        String uatUrl = "https://synchronizer2.derbysoft-test.com/";

        // When
        synchronizerProperties.setUrl(uatUrl);

        // Then
        assertEquals(uatUrl, synchronizerProperties.getUrl());
    }

    @Test
    void testSynchronizerProperties_WithDevUrl() {
        // Given
        String devUrl = "https://synchronizer2.derbysoft-test.com/";

        // When
        synchronizerProperties.setUrl(devUrl);

        // Then
        assertEquals(devUrl, synchronizerProperties.getUrl());
    }

    @Test
    void testSynchronizerProperties_WithQAUrl() {
        // Given
        String qaUrl = "https://synchronizer2.qa.derbysoft-test.com/";

        // When
        synchronizerProperties.setUrl(qaUrl);

        // Then
        assertEquals(qaUrl, synchronizerProperties.getUrl());
    }

    @Test
    void testSynchronizerProperties_WithHttpUrl() {
        // Given
        String httpUrl = "http://localhost:8080/synchronizer/";

        // When
        synchronizerProperties.setUrl(httpUrl);

        // Then
        assertEquals(httpUrl, synchronizerProperties.getUrl());
    }

    @Test
    void testSynchronizerProperties_WithHttpsUrl() {
        // Given
        String httpsUrl = "https://secure.synchronizer.com/";

        // When
        synchronizerProperties.setUrl(httpsUrl);

        // Then
        assertEquals(httpsUrl, synchronizerProperties.getUrl());
    }

    @Test
    void testSynchronizerProperties_WithUrlWithPort() {
        // Given
        String urlWithPort = "https://synchronizer.example.com:8443/";

        // When
        synchronizerProperties.setUrl(urlWithPort);

        // Then
        assertEquals(urlWithPort, synchronizerProperties.getUrl());
    }

    @Test
    void testSynchronizerProperties_WithUrlWithPath() {
        // Given
        String urlWithPath = "https://api.example.com/synchronizer/v1/";

        // When
        synchronizerProperties.setUrl(urlWithPath);

        // Then
        assertEquals(urlWithPath, synchronizerProperties.getUrl());
    }

    @Test
    void testSynchronizerProperties_WithUrlWithQueryParams() {
        // Given
        String urlWithQuery = "https://synchronizer.com/api?version=v1&format=json";

        // When
        synchronizerProperties.setUrl(urlWithQuery);

        // Then
        assertEquals(urlWithQuery, synchronizerProperties.getUrl());
    }

    @Test
    void testSynchronizerProperties_WithInvalidUrl() {
        // Given
        String invalidUrl = "not-a-valid-url";

        // When
        synchronizerProperties.setUrl(invalidUrl);

        // Then
        assertEquals(invalidUrl, synchronizerProperties.getUrl());
        // Note: The properties class doesn't validate URL format
    }

    @Test
    void testSynchronizerProperties_WithSpecialCharacters() {
        // Given
        String urlWithSpecialChars = "https://synchronizer.com/api/path%20with%20spaces";

        // When
        synchronizerProperties.setUrl(urlWithSpecialChars);

        // Then
        assertEquals(urlWithSpecialChars, synchronizerProperties.getUrl());
    }

    @Test
    void testSynchronizerProperties_WithUnicodeCharacters() {
        // Given
        String urlWithUnicode = "https://同步器.example.com/";

        // When
        synchronizerProperties.setUrl(urlWithUnicode);

        // Then
        assertEquals(urlWithUnicode, synchronizerProperties.getUrl());
    }

    @Test
    void testSynchronizerProperties_EqualsAndHashCode() {
        // Given
        SynchronizerProperties props1 = new SynchronizerProperties();
        props1.setUrl("https://test.com/");
        
        SynchronizerProperties props2 = new SynchronizerProperties();
        props2.setUrl("https://test.com/");

        // When & Then
        assertEquals(props1, props2);
        assertEquals(props1.hashCode(), props2.hashCode());
    }

    @Test
    void testSynchronizerProperties_NotEquals() {
        // Given
        SynchronizerProperties props1 = new SynchronizerProperties();
        props1.setUrl("https://test1.com/");
        
        SynchronizerProperties props2 = new SynchronizerProperties();
        props2.setUrl("https://test2.com/");

        // When & Then
        assertNotEquals(props1, props2);
    }

    @Test
    void testSynchronizerProperties_ToString() {
        // Given
        synchronizerProperties.setUrl("https://test.com/");

        // When
        String result = synchronizerProperties.toString();

        // Then
        assertNotNull(result);
        assertTrue(result.contains("SynchronizerProperties"));
        assertTrue(result.contains("https://test.com/"));
    }

    @Test
    void testSynchronizerProperties_ToStringWithNullUrl() {
        // Given
        synchronizerProperties.setUrl(null);

        // When
        String result = synchronizerProperties.toString();

        // Then
        assertNotNull(result);
        assertTrue(result.contains("SynchronizerProperties"));
    }

    @Test
    void testSynchronizerProperties_MultipleSetOperations() {
        // Given
        String url1 = "https://first.com/";
        String url2 = "https://second.com/";
        String url3 = "https://third.com/";

        // When
        synchronizerProperties.setUrl(url1);
        assertEquals(url1, synchronizerProperties.getUrl());

        synchronizerProperties.setUrl(url2);
        assertEquals(url2, synchronizerProperties.getUrl());

        synchronizerProperties.setUrl(url3);
        assertEquals(url3, synchronizerProperties.getUrl());

        // Then
        assertEquals(url3, synchronizerProperties.getUrl());
    }

    @Test
    void testSynchronizerProperties_ThreadSafety() {
        // Given
        String baseUrl = "https://thread-test.com/";
        
        // When
        Thread[] threads = new Thread[10];
        Exception[] exceptions = new Exception[10];

        for (int i = 0; i < 10; i++) {
            final int index = i;
            threads[i] = new Thread(() -> {
                try {
                    SynchronizerProperties props = new SynchronizerProperties();
                    props.setUrl(baseUrl + index);
                    assertEquals(baseUrl + index, props.getUrl());
                } catch (Exception e) {
                    exceptions[index] = e;
                }
            });
            threads[i].start();
        }

        // Wait for all threads to complete
        for (Thread thread : threads) {
            try {
                thread.join();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }

        // Then
        for (int i = 0; i < 10; i++) {
            assertNull(exceptions[i], "Thread " + i + " should not have thrown an exception");
        }
    }

    @Test
    void testSynchronizerProperties_WithLongUrl() {
        // Given
        StringBuilder longUrlBuilder = new StringBuilder("https://very-long-synchronizer-url.example.com/");
        for (int i = 0; i < 100; i++) {
            longUrlBuilder.append("path").append(i).append("/");
        }
        String longUrl = longUrlBuilder.toString();

        // When
        synchronizerProperties.setUrl(longUrl);

        // Then
        assertEquals(longUrl, synchronizerProperties.getUrl());
    }

    @Test
    void testSynchronizerProperties_WithTrailingSlash() {
        // Given
        String urlWithSlash = "https://synchronizer.com/";
        String urlWithoutSlash = "https://synchronizer.com";

        // When & Then
        synchronizerProperties.setUrl(urlWithSlash);
        assertEquals(urlWithSlash, synchronizerProperties.getUrl());

        synchronizerProperties.setUrl(urlWithoutSlash);
        assertEquals(urlWithoutSlash, synchronizerProperties.getUrl());
    }

    @Test
    void testSynchronizerProperties_WithDifferentProtocols() {
        // Given
        String[] protocols = {
            "http://synchronizer.com/",
            "https://synchronizer.com/",
            "ftp://synchronizer.com/",
            "file://synchronizer.com/"
        };

        for (String url : protocols) {
            // When
            synchronizerProperties.setUrl(url);

            // Then
            assertEquals(url, synchronizerProperties.getUrl());
        }
    }

    @Test
    void testSynchronizerProperties_PackageStructure() {
        // When
        String packageName = SynchronizerProperties.class.getPackage().getName();

        // Then
        assertEquals("com.derbysoft.next.propertyconnect.channel.task.config", packageName);
    }

    @Test
    void testSynchronizerProperties_ClassSimpleName() {
        // When
        String className = SynchronizerProperties.class.getSimpleName();

        // Then
        assertEquals("SynchronizerProperties", className);
    }

    @Test
    void testSynchronizerProperties_IsPublicClass() {
        // When
        int modifiers = SynchronizerProperties.class.getModifiers();

        // Then
        assertTrue(java.lang.reflect.Modifier.isPublic(modifiers));
    }

    @Test
    void testSynchronizerProperties_ConstructorExists() {
        // When & Then
        assertDoesNotThrow(() -> new SynchronizerProperties());
    }

    @Test
    void testSynchronizerProperties_HasOnlyOneField() {
        // When
        java.lang.reflect.Field[] fields = SynchronizerProperties.class.getDeclaredFields();

        // Then
        assertEquals(1, fields.length);
        assertEquals("url", fields[0].getName());
    }

    @Test
    void testSynchronizerProperties_FieldType() throws Exception {
        // Given
        java.lang.reflect.Field urlField = SynchronizerProperties.class.getDeclaredField("url");

        // When
        Class<?> fieldType = urlField.getType();

        // Then
        assertEquals(String.class, fieldType);
    }

    @Test
    void testSynchronizerProperties_WithRealWorldUrls() {
        // Given
        String[] realWorldUrls = {
            "https://synchronizer2-manager.derbysoftsec.com/",
            "https://synchronizer2.derbysoft-test.com/",
            "https://synchronizer2.qa.derbysoft-test.com/",
            "http://localhost:8080/synchronizer/"
        };

        for (String url : realWorldUrls) {
            // When
            synchronizerProperties.setUrl(url);

            // Then
            assertEquals(url, synchronizerProperties.getUrl());
        }
    }
}
