package com.derbysoft.next.propertyconnect.channel.task.domain.dto;

import com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.RemoteChannelService;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
public class ChannelRoomInfo implements InventoryItemStatus {
	private String code;
	private String name;
	private String description;
	private Occupancy occupancy;
	private Boolean smoking;
	private List<BedsItem> beds;
	private List<ExtraBeddingItem> extraBedding;
	private Map<String, Object> i18n;
	private ItemStatus status;
	private SyncStatus syncStatus;
	private String errorCode;
	private String errorMessage;
	private String lastOperationToken;
	private RemoteChannelService.Operation operation;
	private Map<String, Object> extensions;


	public ChannelRoomInfo(String code) {
		this.code = code;
	}

	@Override
	public String codePattern() {
		return "RoomCode";
	}


	@Data
	public static class Occupancy{
		private Integer maxOccupancy;
		private Integer maxAdult;
		private Integer maxChild;
	}

	@Data
	public static class BedsItem{
		private String type;
		private String size;
		private Map<String, Object> extensions;
	}

	@Data
	public static class ExtraBeddingItem{
		private String type;
		private Integer quantity;
		private Integer chargeAmount;
		private Map<String, Object> extensions;
	}

}
