package com.derbysoft.next.propertyconnect.channel.task.service.mapping;

import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelProductsDTO;

import java.util.List;

/**
 * @Created by <AUTHOR> on 12/6/2023
 */

public interface PredictionReportService {
    void saveChannelProductMappingSnapshotAndReport(String supplierId, String channelId, String channelHotelId);
    void saveAiMappingSnapshot(ChannelProductsDTO currentAiMappingResult, List<Object> supplierRooms, List<Object> supplierRates, ChannelProductsDTO channelProducts);
}
