package com.derbysoft.next.propertyconnect.channel.task.service.impl;

import com.derbysoft.next.propertyconenct.channel.common.mapstruct.BaseMapper;
import com.derbysoft.next.propertyconenct.channel.common.mapstruct.DateTimeMapper;
import com.derbysoft.next.propertyconenct.channel.common.utils.GeneratedMapper;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelProductsDTO;
import com.derbysoft.next.propertyconnect.channel.task.domain.entity.ChannelProductsPO;
import com.derbysoft.next.propertyconnect.channel.task.domain.repository.ChannelProductsCacheRepository;
import com.derbysoft.next.propertyconnect.channel.task.service.ChannelProductsService;
import lombok.RequiredArgsConstructor;
import org.mapstruct.AnnotateWith;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Objects;
import java.util.Optional;

/**
 * @Created by <AUTHOR> on 2023/3/21
 */

@Service
@RequiredArgsConstructor
public class LocalChannelProductsService implements ChannelProductsService {

    private final ChannelProductsCacheRepository repository;
    private final ChannelProductCacheTranslator productCacheMapper = Mappers.getMapper(ChannelProductCacheTranslator.class);

    @Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    @AnnotateWith(GeneratedMapper.class)
    interface ChannelProductCacheTranslator extends BaseMapper<ChannelProductsPO, ChannelProductsDTO>, DateTimeMapper {
    }

    @Override
    public ChannelProductsDTO getChannelProducts(String channelId, String channelHotelId) {
        return productCacheMapper.map(repository.findByChannelIdAndChannelHotelId(channelId, channelHotelId));
    }

    public LocalDateTime getLastQueryDate(String channelId) {
        return Optional.ofNullable(repository.findFirstByChannelIdOrderByRetrieveDateDesc(channelId))
                .map(ChannelProductsPO::getRetrieveDate)
                .orElse(LocalDateTime.MIN);
    }

    public ChannelProductsPO saveChannelProductCache(ChannelProductsDTO productsCache) {
        ChannelProductsPO entity = repository.findByChannelIdAndChannelHotelId(productsCache.getChannelId(), productsCache.getChannelHotelId());
        if (Objects.isNull(entity)) {
            entity = ChannelProductsPO.builder().build();
        }
        productCacheMapper.reverseFill(productsCache, entity);
        entity.setRetrieveDate(LocalDateTime.parse(productsCache.getRetrieveDate()));
        return repository.save(entity);
    }
}
