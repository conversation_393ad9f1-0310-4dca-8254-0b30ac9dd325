package com.derbysoft.next.propertyconnect.channel.task.service;


import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelProductsDTO;

public interface ChannelProductsService extends ChannelService {

    default ChannelProductsDTO getChannelProducts(String channelId, String channelHotelId){
        return null;
    }

    default ChannelProductsDTO getChannelProductsWithExtra(String supplierId, String channelId, String channelHotelId){
        return getChannelProducts(supplierId, channelId, channelHotelId);
    }

    default ChannelProductsDTO getChannelProducts(String supplierId, String channelId, String channelHotelId){
        return getChannelProducts(channelId, channelHotelId);
    }

}
