package com.derbysoft.next.propertyconnect.channel.task.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @Created by <AUTHOR> on 2023/8/11
 */

@ConfigurationProperties("app.hotel.setup")
@RefreshScope
public class HotelSetupConnectionWhiteListConfig {
    String allowedConnections = "PROPERTYCONNECT-*,OPERACONNECTOR-*,CAMPSPOT-AIRBNB";

    public boolean isProhibitedConnection(String supplier, String channel) {
        return StringUtils.hasText(supplier)
                && this.getAllowedConnections().stream().noneMatch(s -> s.equalsIgnoreCase(supplier + "-*") || s.equalsIgnoreCase(supplier + "-" + channel));
    }
    public List<String> getAllowedConnections() {
        return Stream.of(allowedConnections.split(",")).map(String::trim).collect(Collectors.toList());
    }

    public void setAllowedConnections(List<String> allowedConnections) {
        this.allowedConnections = allowedConnections.stream().map(String::trim).collect(Collectors.joining(","));
    }
}
