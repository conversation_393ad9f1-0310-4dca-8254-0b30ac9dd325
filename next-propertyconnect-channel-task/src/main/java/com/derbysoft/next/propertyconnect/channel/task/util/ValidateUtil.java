package com.derbysoft.next.propertyconnect.channel.task.util;

import com.alibaba.fastjson.JSONPath;
import com.thoughtworks.xstream.converters.reflection.MissingFieldException;
import lombok.experimental.UtilityClass;
import org.apache.commons.lang3.reflect.MethodUtils;
import org.springframework.cglib.proxy.Enhancer;
import org.springframework.cglib.proxy.MethodInterceptor;
import org.springframework.cglib.proxy.MethodProxy;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.MethodArgumentNotValidException;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @Created by <AUTHOR> on 2022/2/23
 */

@UtilityClass
public class ValidateUtil {

//    public static <T> void throwInvalidFieldsException(Set<ConstraintViolation<T>> validateResult) {
//        throwInvalidFieldsException(validateResult, null);
//    }
//
//    public static <T> void throwInvalidFieldsException(Set<ConstraintViolation<T>> validateResult, T originalObject) {
//        var errString = formatValidateResult(validateResult);
//
//        if (StringUtils.hasText(errString)){
//            throw new InvalidFieldsException(BusinessException.DEFAULT_SOURCE_DISNEY, errString, originalObject);
//        }
//    }
//
//    public static String formatValidateResult(Set<?> validateResult){
//        return validateResult.stream()
//                .map(constraintViolation -> ((PathImpl)((ConstraintViolation<?>) constraintViolation).getPropertyPath()).getLeafNode().getName()
//                        + ": " + ((ConstraintViolation<?>) constraintViolation).getMessage())
//                .collect(Collectors.joining("; "));
//    }

    public static String formatMethodNotValidException(MethodArgumentNotValidException exception){
        return exception.getBindingResult().getFieldErrors().stream()
                .map(messageSource -> messageSource.getField() + ": " + messageSource.getDefaultMessage())
                .collect(Collectors.joining("; "));
    }

    /**
     * Check if the object is null or empty, use JSONPath to support multi-level check
     * @param object
     * @param nonNullField
     */
    public static void validateNull(Object object, String... nonNullField){
        if (object == null){
            throw new IllegalArgumentException("root");
        }
        Optional.of(Stream.of(nonNullField)
                .filter(fieldPath -> !JSONPath.contains(object, "$." + fieldPath))
                .collect(Collectors.joining(",")))
                .filter(StringUtils::hasText)
                .ifPresent(nullFields -> {
                    throw new IllegalArgumentException("Missing fields: " + nullFields);
                });
    }

    /**
     * Check if the object is null or empty, use CGlib to ensure method exist
     * @param target
     * @param nonNullField
     * @param <T>
     */
    @SafeVarargs
    @SuppressWarnings("unchecked")
    public static <T> void validateNull(T target, Function<T, ?>... nonNullField){
        if (target == null){
            throw new IllegalArgumentException("root");
        }

        var nullFields = new ArrayList<String>();

        var proxyTarget = (T) Enhancer.create(target.getClass(),
                (MethodInterceptor) (Object proxy, Method method, Object[] args, MethodProxy methodProxy) -> {
                    String fieldName = StringUtils.uncapitalize(method.getName().replace("get", ""));
                    var result = MethodUtils.invokeMethod(target, method.getName(), args);
                    if (null == result){
                        nullFields.add(fieldName);
                    }
                    return methodProxy.invokeSuper(proxy, args);
                });

        Stream.of(nonNullField).forEach(field -> field.apply(proxyTarget));

        if (!CollectionUtils.isEmpty(nullFields)){
            throw new IllegalArgumentException("Missing fields: " + String.join(",", nullFields));
        }
    }

    /**
     * Check if the object is null or empty, use CGlib to ensure method exist
     * @param target
     * @param nonNullField
     * @param <T>
     */
    @SafeVarargs
    @SuppressWarnings("unchecked")
    public static <T> boolean hasNullField(T target, Function<T, ?>... nonNullField){
        try {
            validateNull(target, nonNullField);
            return true;
        } catch (MissingFieldException e) {
            return false;
        }
    }
}
