package com.derbysoft.next.propertyconnect.channel.task.service.impl;

import com.derbysoft.next.propertyconnect.channel.task.domain.entity.ChannelHotelPO;
import com.derbysoft.next.propertyconnect.channel.task.domain.repository.ChannelHotelRepository;
import com.derbysoft.next.propertyconnect.channel.task.service.AccountSettingService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Optional;

/**
 * @Created by <AUTHOR> on 2023/4/13
 */


@Service
@RequiredArgsConstructor
public class LocalAccountSettingService implements AccountSettingService {

    private final ChannelHotelRepository channelHotelRepository;

    @Override
    public Map<String, Object> getAccountSettings(String channelId, String channelHotelId) {
        return Optional.ofNullable(channelHotelRepository.findFirstByChannelIdAndChannelHotelId(channelId, channelHotelId))
                .map(ChannelHotelPO::getAccountSettings)
                .orElse(null);
    }

    @Override
    public Map<String, Object> saveOrUpdateAccountSettings(String channelId, String channelHotelId, Map<String, Object> accountSettings) {
        return channelHotelRepository.save(Optional.ofNullable(channelHotelRepository.findFirstByChannelIdAndChannelHotelId(channelId, channelHotelId))
                .map(channelHotel -> {
                    channelHotel.setAccountSettings(accountSettings);
                    return channelHotel;
                })
                .orElseGet(() -> ChannelHotelPO.builder()
                        .channelId(channelId)
                        .channelHotelId(channelHotelId)
                        .accountSettings(accountSettings)
                        .build()
                ))
                .getAccountSettings();
    }

    @Override
    public void deleteAccountSettings(String channelId, String channelHotelId) {
        this.saveOrUpdateAccountSettings(channelId, channelHotelId, null);
    }
}
