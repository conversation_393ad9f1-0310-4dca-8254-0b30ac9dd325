package com.derbysoft.next.propertyconnect.channel.task.domain.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

/**
 * @Created by <AUTHOR> on 2023/5/26
 */

@Data
@Accessors(chain = true)
public class ChannelHotelDTO {
    private String supplierId;
    private String channelId;
    private String channelHotelId;
    private String derbyHotelId;
    private String supplierHotelId;
    private String operationToken;

    private ChannelHotelInfo hotelInfo;
    private ChannelRoomInfo roomInfo;
    private ChannelRateInfo rateInfo;
    private ChannelProductsDTO.Product productInfo;
    private Map<String, Object> accountSettings;
    private List<ChannelRoomInfo> roomsInfo;
    private List<ChannelRateInfo> ratesInfo;
    private List<ChannelProductsDTO.Product> productsInfo;
    private Map<String, Object> extensions;
}
