package com.derbysoft.next.propertyconnect.channel.task.domain.repository;

import com.derbysoft.next.propertyconnect.channel.task.domain.entity.ChannelPO;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.util.Optional;

/**
 * @Created by <AUTHOR> on 2023/4/11
 */

public interface ChannelRepository extends MongoRepository<ChannelPO, String> {

    Optional<ChannelPO> findByChannelId(String channelId);
}
