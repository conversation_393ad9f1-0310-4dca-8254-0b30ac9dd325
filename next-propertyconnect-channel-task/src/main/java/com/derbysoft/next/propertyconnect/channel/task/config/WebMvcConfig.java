package com.derbysoft.next.propertyconnect.channel.task.config;

import com.derbysoft.next.commons.boot.logsupport.inteceptor.StreamLogFilter;
import com.derbysoft.next.commons.boot.logsupport.inteceptor.StreamLogInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.multipart.MultipartResolver;
import org.springframework.web.multipart.support.StandardServletMultipartResolver;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    @Bean(name = "multipartResolver")
    public MultipartResolver multipartResolver() {
        return new StandardServletMultipartResolver();
    }


    @Bean
    public StreamLogInterceptor streamLogInterceptor() {
        return new StreamLogInterceptor();
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(streamLogInterceptor()).addPathPatterns("/**");
    }

    @Bean
    public StreamLogFilter streamLogFilter() {
        return new StreamLogFilter();
    }

}