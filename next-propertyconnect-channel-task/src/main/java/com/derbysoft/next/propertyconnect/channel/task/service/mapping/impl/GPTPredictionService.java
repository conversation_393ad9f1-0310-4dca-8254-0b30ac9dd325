package com.derbysoft.next.propertyconnect.channel.task.service.mapping.impl;

import com.derbysoft.next.propertyconenct.channel.common.utils.JSONUtil;
import com.derbysoft.next.propertyconnect.channel.task.controller.vo.mapping.PredictionModel;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelProductsDTO;
import com.derbysoft.next.propertyconnect.channel.task.service.mapping.PredictionService;
import lombok.RequiredArgsConstructor;
import lombok.experimental.ExtensionMethod;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Created by <AUTHOR> on 2023/5/22
 */

@Service
@ExtensionMethod(JSONUtil.class)
@RequiredArgsConstructor
public class GPTPredictionService implements PredictionService {

    @Override
    public PredictionModel supportModel() {
        return PredictionModel.GPT3_5;
    }

    @Override
    public void prediction(ChannelProductsDTO channelProducts, List<ChannelProductsDTO.Product> candidateRooms, List<ChannelProductsDTO.Product> candidateRates, Double threshold) {


    }
}
