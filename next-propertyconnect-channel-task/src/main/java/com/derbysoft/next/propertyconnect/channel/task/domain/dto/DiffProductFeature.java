package com.derbysoft.next.propertyconnect.channel.task.domain.dto;

/**
 * @Created by <AUTHOR> on 12/6/2023
 */

public interface DiffProductFeature {

    enum DIFF_TYPE {
        NEW, UPDATE, DELETE
    }

    String getRoomId();

    String getRateId();

    String getChannelRoomId();

    String getChannelRateId();

    String getStatus();
//    Map<String, Object> getExtensions();
//    void setExtensions(Map<String, Object> extensions);

    default boolean isSameProduct(DiffProductFeature diffProductFeature) {
        return this.getChannelRoomId().equals(diffProductFeature.getChannelRoomId())
                && this.getChannelRateId().equals(diffProductFeature.getChannelRateId());
    }

    default String getProductMatchSignature() {
        return getChannelRoomId() + "-" + getChannelRateId() + "-" + getStatus();
    }

    default boolean isSameProductMapping(DiffProductFeature diffProductFeature) {
        return this.getProductMatchSignature().equals(diffProductFeature.getProductMatchSignature());
    }
}
