package com.derbysoft.next.propertyconnect.channel.task.controller.vo.rate;

import com.derbysoft.next.propertyconenct.channel.common.validation.BusinessIdFormat;
import com.derbysoft.next.propertyconnect.channel.task.controller.vo.ItemStatus;
import com.derbysoft.next.propertyconnect.channel.task.controller.vo.SyncStatus;
import com.derbysoft.next.propertyconnect.channel.task.controller.vo.hotel.ChannelHotelVO;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.Data;

import java.util.Map;

@Data
@JsonView({ChannelHotelVO.Request.class})
public class ChannelRateInfo {
	@BusinessIdFormat
	private String id;
	private String name;
	private String description;
	private ItemStatus status;
	private SyncStatus syncStatus;
	private String errorCode;
	private String errorMessage;
	private EffectiveDateRange effectiveDateRange;
	private Map<String, Object> i18n;
	private Map<String, Object> extensions;

	@Data
	@JsonView({ChannelHotelVO.Request.class})
	public static class EffectiveDateRange{
		private String effectiveDate;
		private String expireDate;
	}
}
