package com.derbysoft.next.propertyconnect.channel.task.service;

import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelHotelDTO;
import com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.RemoteChannelService;
import com.derbysoft.next.propertyconnect.channel.task.service.customizationservice.ChannelCustomizationService;
import org.apache.commons.lang.NotImplementedException;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.function.Consumer;

/**
 * @Created by <AUTHOR> on 2023/6/12
 */
public interface ChannelHotelSetupProcedure extends ChannelCustomizationService, RemoteChannelService {

    LinkedHashMap<RemoteChannelService.Operation, Consumer<ChannelHotelDTO>> supportOperations();

    List<RemoteChannelService.Operation> setupProcedure(ChannelHotelDTO dto);

    default ChannelHotelDTO execution(Operation operation, ChannelHotelDTO dto){
        var processor = supportOperations().get(operation);

        if (null == processor) {
            throw new NotImplementedException(String.format("%s does not support this operation %s", this.channel(), operation.name()));
        }

        processor.accept(dto);
        return dto;
    }

}
