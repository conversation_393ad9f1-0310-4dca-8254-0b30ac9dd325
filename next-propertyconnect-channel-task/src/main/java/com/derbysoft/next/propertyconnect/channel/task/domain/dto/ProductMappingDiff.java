package com.derbysoft.next.propertyconnect.channel.task.domain.dto;

import com.derbysoft.next.propertyconenct.channel.common.utils.ObjectUtil;
import com.derbysoft.next.propertyconnect.channel.task.util.CloneUtil;
import org.mapstruct.factory.Mappers;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * @Created by <AUTHOR> on 12/6/2023
 */

public interface ProductMappingDiff {

    CloneUtil CLONE_UTIL = Mappers.getMapper(CloneUtil.class);

    enum DIFF_TYPE {
        NEW, UPDATE, DELETE
    }

    List<ChannelProductsDTO.Product> getChannelProducts();

    default List<ChannelProductsDTO.Product> getDiffProducts(List<ChannelProductsDTO.Product> targetProducts) {
        var diffProducts = new ArrayList<ChannelProductsDTO.Product>();
        var sourceProducts = this.getChannelProducts();
        if (sourceProducts == null || sourceProducts.isEmpty() || targetProducts == null || targetProducts.isEmpty()) {
            return sourceProducts;
        }

        //calculate new products
        sourceProducts.stream().filter(sourceProduct -> targetProducts.stream().noneMatch(sourceProduct::isSameProduct))
                .forEach(product -> {
                    var clone = CLONE_UTIL.clone(product);
                    var reference = ObjectUtil.getReference(clone, ChannelProductsDTO.Product::getExtensions, () -> new HashMap<String, Object>());
                    reference.put("diffType", DIFF_TYPE.NEW.name());
                    reference.put("diffFrom", "-");
                    reference.put("diffTo", product.getProductMatchSignature());
                    diffProducts.add(clone);
                });

        //calculate update products
        sourceProducts.stream().filter(sourceProduct -> targetProducts.stream().anyMatch(sourceProduct::isSameProduct))
                .forEach(sourceProduct -> {
                    var targetProduct = targetProducts.stream().filter(sourceProduct::isSameProduct).findFirst().orElse(null);
                    if (targetProduct != null && !sourceProduct.isSameProductMapping(targetProduct)) {
                        var clone = CLONE_UTIL.clone(sourceProduct);
                        var reference = ObjectUtil.getReference(clone, ChannelProductsDTO.Product::getExtensions, () -> new HashMap<String, Object>());
                        reference.put("diffType", DIFF_TYPE.UPDATE.name());
                        reference.put("diffFrom", sourceProduct.getProductMatchSignature());
                        reference.put("diffTo", targetProduct.getProductMatchSignature());
                        diffProducts.add(clone);
                    }
                });

        //calculate delete products
        targetProducts.stream().filter(targetProduct -> sourceProducts.stream().noneMatch(targetProduct::isSameProduct))
                .forEach(product -> {
                    var clone = CLONE_UTIL.clone(product);
                    var reference = ObjectUtil.getReference(clone, ChannelProductsDTO.Product::getExtensions, () -> new HashMap<String, Object>());
                    reference.put("diffType", DIFF_TYPE.DELETE.name());
                    reference.put("diffFrom", product.getProductMatchSignature());
                    reference.put("diffTo", "-");
                    diffProducts.add(clone);
                });

        return diffProducts;
    }


    default CandidateResult getAiRecommendProduct(DiffProductFeature sourceProduct) {
        return this.getChannelProducts()
                .stream()
                .filter(product -> product.isSameProduct(sourceProduct))
                .findFirst()
                .map(CandidateResult::getCandidateDiff)
                .orElse(null);
    }

}
