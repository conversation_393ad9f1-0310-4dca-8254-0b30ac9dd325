package com.derbysoft.next.propertyconnect.channel.task.service.mapping.impl;

import com.alibaba.fastjson2.JSONObject;
import com.derbysoft.next.commons.core.logsupport.PerfLogTemplate;
import com.derbysoft.next.commons.core.logsupport.constant.LogConst;
import com.derbysoft.next.commons.core.logsupport.handler.PerfLogHandler;
import com.derbysoft.next.propertyconenct.channel.common.utils.Constants;
import com.derbysoft.next.propertyconenct.channel.common.utils.JSONUtil;
import com.derbysoft.next.propertyconnect.channel.task.client.alarm.NoticeRequest;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.CandidateResult;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelProductsDTO;
import com.derbysoft.next.propertyconnect.channel.task.domain.entity.AIMappingSnapshotPO;
import com.derbysoft.next.propertyconnect.channel.task.domain.repository.AIMappingSnapshotRepository;
import com.derbysoft.next.propertyconnect.channel.task.service.RemoteService;
import com.derbysoft.next.propertyconnect.channel.task.service.mapping.PredictionReportService;
import com.derbysoft.next.propertyconnect.channel.task.service.mapping.impl.report.MappingResult;
import com.derbysoft.next.propertyconnect.channel.task.service.mapping.impl.report.ReportExcel;
import com.derbysoft.next.propertyconnect.channel.task.service.mapping.impl.report.ReportExcelGenerator;
import com.derbysoft.next.propertyconnect.channel.task.task.hotelsetup.HotelSetupConfigurationProperties;
import com.derbysoft.next.propertyconnect.channel.task.util.BusinessJSONUtil;
import com.derbysoft.next.propertyconnect.channel.task.util.CloneUtil;
import com.derbysoft.next.propertyconnect.channel.task.util.EnvUtil;
import com.derbysoft.next.propertyconnect.channel.task.util.MailContextGenerator;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.experimental.ExtensionMethod;
import lombok.extern.slf4j.Slf4j;
import org.mapstruct.factory.Mappers;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.data.mongodb.core.convert.MappingMongoConverter;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Created by <AUTHOR> on 12/6/2023
 */

@Service
@Slf4j
@ExtensionMethod({JSONUtil.class, BusinessJSONUtil.class})
@EnableConfigurationProperties(PredictionServiceConfigProperties.class)
@RequiredArgsConstructor
public class PredictionReportServiceImpl implements PredictionReportService {

    private final RemoteService remoteService;
    private final AIMappingSnapshotRepository aiMappingSnapshotRepository;
    private final CloneUtil cloneUtil = Mappers.getMapper(CloneUtil.class);
    private final MappingMongoConverter mappingMongoConverter;
    private final S3Client s3Client;
    private final PredictionServiceConfigProperties configProperties;
    private final HotelSetupConfigurationProperties properties;

    @PostConstruct
    public void replaceKeyContainingDot() {
        mappingMongoConverter.setMapKeyDotReplacement("|");
    }


    @Override
    public void saveChannelProductMappingSnapshotAndReport(String supplierId, String channelId, String channelHotelId) {
        PerfLogTemplate.getInstance().record(Constants.Perf.ACCURACY_REPORT, Optional.ofNullable(PerfLogHandler.currentHandler()).map(PerfLogHandler::getToken).orElse(null), perfLogHandler -> {
            perfLogHandler.message(Constants.Perf.CHANNEL, channelId);
            perfLogHandler.message(Constants.Perf.SUPPLIER, supplierId);
            perfLogHandler.message(Constants.Perf.HOTEL_CHANNEL, channelHotelId);
            var rawChannelHotelMapping = remoteService.uniqueChannelHotelMapping(channelId, channelHotelId).orElse(null);
            if (rawChannelHotelMapping == null) {
                perfLogHandler.warn("Channel hotel mapping not found for " + channelId + " " + channelHotelId);
                perfLogHandler.message(LogConst.Parameter.PROCESS_RESULT, Constants.Perf.IGNORE);
                return;
            }

            var cachedRawChannelHotelMapping = aiMappingSnapshotRepository.getAIMappingSnapshot(supplierId, channelId, channelHotelId);

            if (null == cachedRawChannelHotelMapping) {
                perfLogHandler.warn("AI mapping snapshot not found for " + supplierId + " " + channelId + " " + channelHotelId);
                perfLogHandler.message(LogConst.Parameter.PROCESS_RESULT, Constants.Perf.IGNORE);
                return;
            }
            //Map into dto
            var currentChannelHotelMappings = new ChannelProductsDTO();
            currentChannelHotelMappings.setSupplierId(supplierId);
            currentChannelHotelMappings.setChannelId(rawChannelHotelMapping.getString("$.channelId"));
            currentChannelHotelMappings.setChannelHotelId(rawChannelHotelMapping.getString("$.channelHotelId"));
            currentChannelHotelMappings.setHotelId(rawChannelHotelMapping.getString("$.hotelId"));
            currentChannelHotelMappings.setChannelProducts(rawChannelHotelMapping.getCollection("$.productMapping").stream()
                    .map(productMapping -> {
                        String roomId = productMapping.getString("$.roomId");
                        String rateId = productMapping.getString("$.rateId");
                        String channelRoomId = productMapping.getString("$.channelRoomId");
                        String channelRateId = productMapping.getString("$.channelRateId");

                        var rawChannelRoom = cachedRawChannelHotelMapping.getChannelRooms().getObject(String.format("$[?(@.roomId == '%s')][0]", channelRoomId));
                        var rawChannelRate = cachedRawChannelHotelMapping.getChannelRates().getObject(String.format("$[?(@.rateId == '%s')][0]", channelRateId));
                        var rawSupplierRoom = cachedRawChannelHotelMapping.getSupplierRooms().getObject(String.format("$[?(@.roomId == '%s')][0]", roomId));
                        var rawSupplierRate = cachedRawChannelHotelMapping.getSupplierRates().getObject(String.format("$[?(@.rateId == '%s')][0]", rateId));

                        return new ChannelProductsDTO.Product()
                                .setRoomId(roomId)
                                .setRoomName(null == rawSupplierRoom ? null : rawSupplierRoom.getString("$.roomName"))
                                .setRateId(rateId)
                                .setRateName(null == rawSupplierRate ? null : rawSupplierRate.getString("$.rateName"))
                                .setChannelRoomId(channelRoomId)
                                .setChannelRoomName(null == rawChannelRoom ? null : rawChannelRoom.getString("$.roomName"))
                                .setChannelRateId(channelRateId)
                                .setChannelRateName(null == rawChannelRate ? null : rawChannelRate.getString("$.rateName"))
                                .setStatus(productMapping.getString("$.status"));
                    })
                    .collect(Collectors.toList()));
            perfLogHandler.message(Constants.Perf.HOTEL, currentChannelHotelMappings.getHotelId());

            var cachedChannelHotelMapping = AIMappingSnapshotRepository.TRANSLATOR.mapChannelHotelMapping(cachedRawChannelHotelMapping);
            var diffProducts = currentChannelHotelMappings.getDiffProducts(null == cachedChannelHotelMapping ? null : cachedChannelHotelMapping.getChannelProducts());

            if (diffProducts.isEmpty()) {
                perfLogHandler.warn("No diff products found for " + supplierId + " " + channelId + " " + channelHotelId);
                perfLogHandler.message(LogConst.Parameter.PROCESS_RESULT, Constants.Perf.IGNORE);
                return;
            }

            var clone = cloneUtil.clone(currentChannelHotelMappings);
            aiMappingSnapshotRepository.saveLatestChannelProductsMapping(clone);

            report(currentChannelHotelMappings, cachedRawChannelHotelMapping, perfLogHandler);
        });
    }

    @Override
    public void saveAiMappingSnapshot(ChannelProductsDTO currentAiMappingResult, List<Object> supplierRooms, List<Object> supplierRates, ChannelProductsDTO channelProducts) {
        var rawSupplierRooms = supplierRooms.stream().map(room -> flattenMap(JSONObject.from(room))).collect(Collectors.toList());
        var rawSupplierRates = supplierRates.stream().map(rate -> flattenMap(JSONObject.from(rate))).collect(Collectors.toList());

        var rawChannelRooms = channelProducts.getChannelProducts().stream()
                .collect(Collectors.toMap(ChannelProductsDTO.Product::getChannelRoomId, product -> flattenMap(Map.of(
                        "roomId", product.getChannelRoomId(),
                        "roomName", product.getChannelRoomName(),
                        "roomExtension", JSONObject.from(product.getObject("$.extensions.roomExtension"))
                )), (ov, nv) -> nv))
                .values();

        var rawChannelRates = channelProducts.getChannelProducts().stream()
                .collect(Collectors.toMap(ChannelProductsDTO.Product::getChannelRateId, product -> flattenMap(Map.of(
                        "rateId", product.getChannelRateId(),
                        "rateName", product.getChannelRateName(),
                        "rateExtension", JSONObject.from(product.getObject("$.extensions.rateExtension"))
                )), (ov, nv) -> nv))
                .values();
        aiMappingSnapshotRepository.saveAIMappingSnapshot(currentAiMappingResult, rawSupplierRooms, rawSupplierRates, rawChannelRooms, rawChannelRates, JSONUtil.getLong(channelProducts, "$.extensions.predicationTime"));
    }


    private void report(ChannelProductsDTO currentChannelHotelMapping, AIMappingSnapshotPO aiMappingSnapshot, PerfLogHandler perfLogHandler) {
        ChannelProductsDTO cachedAiMappingSnapshot = AIMappingSnapshotRepository.TRANSLATOR.mapAiMappingResult(aiMappingSnapshot);

        List<MappingResult> mappingResults = currentChannelHotelMapping.getChannelProducts().stream()
                .map(product -> {
                    var aiRecommendProduct = Optional.ofNullable(cachedAiMappingSnapshot.getAiRecommendProduct(product)).orElse(new ChannelProductsDTO.Product());
                    return new MappingResult()
                            .setChannelId(currentChannelHotelMapping.getChannelId())
                            .setPCHotelId(currentChannelHotelMapping.getHotelId())
                            .setChannelHotelId(currentChannelHotelMapping.getChannelHotelId())
                            .setPCMappingStatus(product.getStatus())
                            .setChannelRoomId(product.getChannelRoomId())
                            .setChannelRateId(product.getChannelRateId())
                            .setChannelRoomName(product.getChannelRoomName())
                            .setChannelRateName(product.getChannelRateName())
                            .setPCMappedRoomName(product.getRoomName())
                            .setPCMappedRateName(product.getRateName())
                            .setAIRecommendRoomName(aiRecommendProduct.getRoomName())
                            .setAIRecommendRateName(aiRecommendProduct.getRateName())
                            .setPCMappedRoomId(product.getRoomId())
                            .setPCMappedRateId(product.getRateId())
                            .setAIRecommendRoomId(aiRecommendProduct.getRoomId())
                            .setAIRecommendRateId(aiRecommendProduct.getRateId())
                            .setRoomTypeMatch(calculateRoomMatch(product, aiRecommendProduct))
                            .setRatePlanMatch(calculateRateMatch(product, aiRecommendProduct))
                            .setProductMatch(aiRecommendProduct.candidateMatchResult(product).getValue());
                })
                .collect(Collectors.toList());

        var filePath = generateExcelFileName(aiMappingSnapshot.getChannelId(), aiMappingSnapshot.getHotelId());
        statistics(aiMappingSnapshot, mappingResults, filePath, perfLogHandler);
        generateExcel(mappingResults, aiMappingSnapshot, filePath, perfLogHandler);
    }

    private String calculateRoomMatch(CandidateResult product, CandidateResult aiProduct){
        if (product == null || aiProduct == null || product.getRoomId() == null || aiProduct.getRoomId() == null) {
            return "Not Exist";
        } else if (product.getRoomId().equals(aiProduct.getRoomId())) {
            return "Match";
        } else {
            return "Not Match";
        }
    }

    private String calculateRateMatch(CandidateResult product, CandidateResult aiProduct){
        if (product == null || aiProduct == null || product.getRateId() == null || aiProduct.getRateId() == null) {
            return "Not Exist";
        } else if (product.getRateId().equals(aiProduct.getRateId())) {
            return "Match";
        } else {
            return "Not Match";
        }
    }

    private Map<String, Object> flattenMap(Map<String, Object> inputMap) {
        Map<String, Object> flattenedMap = new TreeMap<>(Comparator
                .comparing(i -> i.equals("roomId") ? 0 : i.equals("rateId") ? 1 : 2)
                .thenComparing(i -> i.equals("roomName") ? 0 : i.equals("rateName") ? 1 : 2));
        flattenMap("", inputMap, flattenedMap);
        return flattenedMap;
    }

    private void flattenMap(String prefix, Map<String, Object> inputMap, Map<String, Object> flattenedMap) {
        for (Map.Entry<String, Object> entry : inputMap.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();

            if (value instanceof Map) {
                flattenMap(prefix + key + ".", (Map<String, Object>) value, flattenedMap);
            } else {
                flattenedMap.put(prefix + key, value.toString());
            }
        }
    }

    private void statistics(AIMappingSnapshotPO aiMappingSnapshot, List<MappingResult> mappingResults, String filePath, PerfLogHandler perfLogHandler) {
        var lastOperationTime = aiMappingSnapshot.getLastAiMappingOperationTime();
        var threshold = aiMappingSnapshot.getThreshold();
        var predicationTime = aiMappingSnapshot.getPredicationTime();

        var bothHaveValueCount = mappingResults.stream().filter(mappingResult -> StringUtils.hasText(mappingResult.getAIRecommendRoomId()) && StringUtils.hasText(mappingResult.getAIRecommendRateId())).count();
        var channelNotValidCount = mappingResults.stream().filter(mappingResult -> !StringUtils.hasText(mappingResult.getChannelRoomId()) || !StringUtils.hasText(mappingResult.getChannelRateId()) || "Suspend".equals(mappingResult.getPCMappingStatus())).count();
        var productValidCount = mappingResults.stream().filter(mappingResult -> !mappingResult.getProductMatch().equals("Not Exist")).count();
        var productMatchCount = mappingResults.stream().filter(mappingResult -> mappingResult.getProductMatch().equals("Match")).count();
        var roomValidCount = mappingResults.stream().filter(mappingResult -> !mappingResult.getRoomTypeMatch().equals("Not Exist")).count();
        var roomMatchCount = mappingResults.stream().filter(mappingResult -> mappingResult.getRoomTypeMatch().equals("Match")).count();
        var rateValidCount = mappingResults.stream().filter(mappingResult -> !mappingResult.getRatePlanMatch().equals("Not Exist")).count();
        var rateMatchCount = mappingResults.stream().filter(mappingResult -> mappingResult.getRatePlanMatch().equals("Match")).count();

        var aIRecommendMatch = String.format("%.2f", (double) bothHaveValueCount / (mappingResults.size() - channelNotValidCount) * 100) + "%";
        var roomTypeMatch = roomValidCount != 0 ? String.format("%.2f", (double) roomMatchCount / roomValidCount * 100) + "%" : "0.00%";
        var ratePlanMatch = rateValidCount != 0 ? String.format("%.2f", (double) rateMatchCount / rateValidCount * 100) + "%" : "0.00%";
        var productMatch = productValidCount != 0 ? String.format("%.2f", (double) productMatchCount / productValidCount * 100) + "%" : "0.00%";
        double productMisMatchRate = (1 - (double) productMatchCount / (double) productValidCount) * 100;

        if (Double.compare(productMisMatchRate, Double.NaN) == 0 || Double.compare(productMisMatchRate, Double.POSITIVE_INFINITY) == 0) {
            productMisMatchRate = 100.00;
        }

        perfLogHandler.message(Constants.Perf.MISMATCH_RATE, productMisMatchRate);

        var reportData = new LinkedHashMap<String, String>();
        reportData.put("AIRecommendMatch", aIRecommendMatch);
        reportData.put("RoomTypeMatch", roomTypeMatch);
        reportData.put("RatePlanMatch", ratePlanMatch);
        reportData.put("ProductMatch", productMatch);
        reportData.put("Threshold", String.valueOf(threshold));
        reportData.put("LastAIOperationTime", String.valueOf(lastOperationTime));
        reportData.put("PredictionTime", String.valueOf(predicationTime));
        reportData.put("S3FilePath", filePath);

        perfLogHandler.message(Constants.Perf.CHANGE_MSG, reportData.entrySet().stream().map(entry -> String.format("%s: %s", entry.getKey(), entry.getValue())).collect(Collectors.joining(", ")));

        if (!Boolean.TRUE.equals(configProperties.getSendMail())) {
            return;
        }
        remoteService.notice(NoticeRequest.builder()
                .enableEmail(true)
                .mailTo(properties.getAlarmEmails())
                .mailSubject(String.format("[%s] AIMapping Diagnosis Report", EnvUtil.getEnv().toUpperCase()))
                .alarmContent(MailContextGenerator.generateAiMappingReportMailContent(aiMappingSnapshot.getChannelId(), aiMappingSnapshot.getHotelId(), perfLogHandler.getToken(), reportData))
                .build()
        );
    }

    private void generateExcel(List<MappingResult> mappingResults, AIMappingSnapshotPO aiMappingSnapshot, String filePath, PerfLogHandler perfLogHandler) {
        var reportExcel = new ReportExcel();
        reportExcel.setMappingResult(mappingResults);
        reportExcel.setPCRoomType(aiMappingSnapshot.getSupplierRooms());
        reportExcel.setPCRatePlan(aiMappingSnapshot.getSupplierRates());
        reportExcel.setChannelRoomType(aiMappingSnapshot.getChannelRooms());
        reportExcel.setChannelRatePlan(aiMappingSnapshot.getChannelRates());

        try {
            putS3Object(s3Client, configProperties.getS3BucketName(), filePath, ReportExcelGenerator.generateExcel(reportExcel));
            perfLogHandler.message(Constants.Perf.REPORT_SAVED, Boolean.TRUE.toString());
        }catch (Exception e) {
            perfLogHandler.message(Constants.Perf.REPORT_SAVED, Boolean.FALSE.toString());
            log.error("Error while generating excel", e);
        }
    }

    private String generateExcelFileName(String channelId, String hotelId) {
        var timeNow = LocalDateTime.now();
        return String.format("%s/%s-%s-%s.xlsx", timeNow.format(DateTimeFormatter.ofPattern("yyyy-MM")),channelId, hotelId, timeNow.format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));
    }

    private void putS3Object(S3Client s3, String bucketName, String objectKey, byte[] bytes) {
        Map<String, String> metadata = new HashMap<>();
        PutObjectRequest putOb = PutObjectRequest.builder()
                .bucket(bucketName)
                .key(objectKey)
                .metadata(metadata)
                .build();

        s3.putObject(putOb, RequestBody.fromBytes(bytes));
    }
}
