package com.derbysoft.next.propertyconnect.channel.task.client.connectivityoperationapi;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

@EqualsAndHashCode(callSuper = true)
@SuperBuilder(toBuilder = true)
@Data
public class OperationApiRoomTypeRequest extends OperationApiBasicRequest {
    private String roomCode;
    private String partnerRoomCode;
    private Integer maxOccupancy;
    private Occupancy occupancy;

    @Builder
    @Data
    @AllArgsConstructor
    public static class Occupancy {
        Integer maxAdult;
        Integer maxChild;
        Integer maxOccupancy;
    }
}
