package com.derbysoft.next.propertyconnect.channel.task.client.aimapping;

import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class RatePlanPredicationRequest{
	private List<RightsItem> rights;
	private List<LeftsItem> lefts;
	private Double threshold;
	private String model;

	@Data
	public static class LeftsItem{
		private String distributor_room_id;
		private String distributor_rateplan_id;
		private String distributor_rateplan_name;
		private String distributor_occupancy;
		private String distributor_meal;
		private String distributor_paytype;
		private String distributor_channel_name;
		private String distributor_collect_type;
		private Map<String, Object> distributor_extension;
	}

	@Data
	public static class RightsItem{
		private String supplier_room_id;
		private String supplier_rateplan_id;
		private String supplier_rateplan_name;
		private String supplier_occupancy;
		private String supplier_meal;
		private String supplier_paytype;
		private String supplier_channel_name;
		private Map<String, Object> supplier_extension;
	}
}
