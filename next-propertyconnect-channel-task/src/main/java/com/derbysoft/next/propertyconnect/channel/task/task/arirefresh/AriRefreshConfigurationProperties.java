package com.derbysoft.next.propertyconnect.channel.task.task.arirefresh;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * @Created by <AUTHOR> on 2023/2/28
 */

@RefreshScope
@Data
@ConfigurationProperties(prefix = "app.ari.refresh")
public class AriRefreshConfigurationProperties {
    public final static Pattern DAYS_BEFORE_FORMAT = Pattern.compile("^(\\d+)([h,H]{0,1})$");
    Integer hotelBatchSize = 1;
    Integer maxRefreshDays = 360;
    Integer maxSplitSpan = 30;
    String daysBefore = "12h";
    Integer taskBatch = 1;
    Long taskTimeGap = 0L;
    Long delayExecutionTime = 120000L;

    Map<String, AriRefreshConfigurationProperties> config = new HashMap<>();


    public Integer getHotelBatchSize(String channelId) {
        return config.getOrDefault(channelId, this).getHotelBatchSize();
    }

    public Integer getMaxRefreshDays(String channelId) {
        return config.getOrDefault(channelId, this).getMaxRefreshDays();
    }

    public Integer getMaxSplitSpan(String channelId) {
        return config.getOrDefault(channelId, this).getMaxSplitSpan();
    }

    public String getDaysBefore(String channelId) {
        return config.getOrDefault(channelId, this).getDaysBefore();
    }

    public Integer getTaskBatch(String channelId) {
        return config.getOrDefault(channelId, this).getTaskBatch();
    }

    public Long getTaskTimeGap(String channelId) {
        return config.getOrDefault(channelId, this).getTaskTimeGap();
    }

    public Long getDelayExecutionTime(String channelId) {
        return config.getOrDefault(channelId, this).getDelayExecutionTime();
    }
}
