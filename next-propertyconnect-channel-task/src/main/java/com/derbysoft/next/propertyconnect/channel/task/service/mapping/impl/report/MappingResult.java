package com.derbysoft.next.propertyconnect.channel.task.service.mapping.impl.report;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Created by <AUTHOR> on 12/7/2023
 */

@Data
@JsonPropertyOrder({"ChannelId", "PCHotelId", "ChannelHotelId", "PCMappingStatus", "ChannelRoomId", "ChannelRateId", "ChannelRoomName", "ChannelRateName", "PCMappedRoomName", "PCMappedRateName", "AIRecommendRoomName", "AIRecommendRateName", "PCMappedRoomId", "PCMappedRateId", "AIRecommendRoomId", "AIRecommendRateId", "RoomTypeMatch", "RatePlanMatch", "ProductMatch"})
@Accessors(chain = true)
public class MappingResult {
    @JsonProperty("ChannelId")
    String channelId;
    @JsonProperty("PCHotelId")
    String pCHotelId;
    @JsonProperty("ChannelHotelId")
    String channelHotelId;
    @JsonProperty("PCMappingStatus")
    String pCMappingStatus;
    @JsonProperty("ChannelRoomId")
    String channelRoomId;
    @JsonProperty("ChannelRateId")
    String channelRateId;
    @JsonProperty("ChannelRoomName")
    String channelRoomName;
    @JsonProperty("ChannelRateName")
    String channelRateName;
    @JsonProperty("PCMappedRoomName")
    String pCMappedRoomName;
    @JsonProperty("PCMappedRateName")
    String pCMappedRateName;
    @JsonProperty("AIRecommendRoomName")
    String aIRecommendRoomName;
    @JsonProperty("AIRecommendRateName")
    String aIRecommendRateName;
    @JsonProperty("PCMappedRoomId")
    String pCMappedRoomId;
    @JsonProperty("PCMappedRateId")
    String pCMappedRateId;
    @JsonProperty("AIRecommendRoomId")
    String aIRecommendRoomId;
    @JsonProperty("AIRecommendRateId")
    String aIRecommendRateId;
    @JsonProperty("RoomTypeMatch")
    String roomTypeMatch;
    @JsonProperty("RatePlanMatch")
    String ratePlanMatch;
    @JsonProperty("ProductMatch")
    String productMatch;
}
