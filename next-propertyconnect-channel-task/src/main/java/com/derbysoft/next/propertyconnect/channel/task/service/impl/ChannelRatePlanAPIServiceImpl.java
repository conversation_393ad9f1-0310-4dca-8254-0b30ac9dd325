package com.derbysoft.next.propertyconnect.channel.task.service.impl;

import com.derbysoft.next.commons.core.logsupport.handler.PerfLogHandler;
import com.derbysoft.next.propertyconenct.channel.common.exception.response.UnifyResult;
import com.derbysoft.next.propertyconenct.channel.common.mapstruct.BaseMapper;
import com.derbysoft.next.propertyconenct.channel.common.mapstruct.DateTimeMapper;
import com.derbysoft.next.propertyconenct.channel.common.utils.Constants;
import com.derbysoft.next.propertyconenct.channel.common.utils.GeneratedMapper;
import com.derbysoft.next.propertyconnect.channel.task.controller.vo.ChannelRatePlanSaveVO;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelProductsDTO;
import com.derbysoft.next.propertyconnect.channel.task.service.ChannelRateAPIService;
import com.derbysoft.next.propertyconnect.channel.task.service.RatePlanService;
import lombok.RequiredArgsConstructor;
import org.mapstruct.AnnotateWith;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.stereotype.Service;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Created by <AUTHOR> on 2023/4/28
 */

@Service
@RequiredArgsConstructor
public class ChannelRatePlanAPIServiceImpl implements ChannelRateAPIService {

    private final ObjectProvider<RatePlanService> ratePlanServices;
    private final ChannelRatePlanTranslator translator;

    @Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    @AnnotateWith(GeneratedMapper.class)
    interface ChannelRatePlanTranslator extends BaseMapper<ChannelRatePlanSaveVO, ChannelProductsDTO>, DateTimeMapper {
    }

    @Override
    public UnifyResult<ChannelRatePlanSaveVO> syncRatePlans(String channelId, String channelHotelId, String hotelId) {
        return ratePlanServices.stream().filter(service -> channelId.equals(service.channel()))
                .findFirst()
                .map(service -> UnifyResult.from(() -> translator.reverseMap(service.syncRatePlans(channelId, channelHotelId, new ChannelProductsDTO()
                        .setChannelId(channelId)
                        .setChannelHotelId(channelHotelId)
                        .setHotelId(hotelId))
                )))
                .orElseThrow(() -> new IllegalArgumentException("Channel " + channelId + " not supported"));
    }

    @Override
    public UnifyResult<ChannelRatePlanSaveVO> syncRatePlansCallback(String channelId, String channelHotelId, String hotelId, String callbackData) {
        PerfLogHandler.currentHandler().message(Constants.Perf.ECHO_TOKEN_DERBY, this.getMessageID(callbackData));

        if (this.matchSuccess(callbackData)) {
            return UnifyResult.from(() -> null);
        }

        throw new IllegalStateException(matchErrorCode(callbackData));
    }

    private String getMessageID(String xml) {
        Pattern pattern = Pattern.compile("<htnga:RelatesToCorrelationID>(.*?)</htnga:RelatesToCorrelationID>");
        Matcher matcher = pattern.matcher(xml);
        if (matcher.find()) {
            return matcher.group(1);
        } else {
            return "";
        }
    }

    private String matchErrorCode(String xml) {
        Pattern pattern = Pattern.compile("ShortText=\"(.*?)\".*?Code=\"(.*?)\"");
        Matcher matcher = pattern.matcher(xml);
        if (matcher.find()) {
            String sortText = matcher.group(1);
            String code = matcher.group(2);
            return code + ":" + sortText;
        }
        return "No error message found";
    }

    private boolean matchSuccess(String xml){
        String regex = "<Success\\s*/>";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(xml);
        return matcher.find();
    }
}
