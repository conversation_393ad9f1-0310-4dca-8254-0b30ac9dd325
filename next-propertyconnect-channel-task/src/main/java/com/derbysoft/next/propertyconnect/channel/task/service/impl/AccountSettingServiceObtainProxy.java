package com.derbysoft.next.propertyconnect.channel.task.service.impl;

import com.derbysoft.next.propertyconnect.channel.task.service.AccountSettingService;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Optional;

/**
 * @Created by <AUTHOR> on 2023/5/4
 */

@Service
@Primary
public class AccountSettingServiceObtainProxy implements AccountSettingService {

    private final PCProfileAccountSettingService profileAccountSettingService;
    private final LocalAccountSettingService localAccountSettingService;


    public AccountSettingServiceObtainProxy(PCProfileAccountSettingService profileAccountSettingService, LocalAccountSettingService localAccountSettingService) {
        this.profileAccountSettingService = profileAccountSettingService;
        this.localAccountSettingService = localAccountSettingService;
    }

    @Override
    public Map<String, Object> getAccountSettings(String channelId, String channelHotelId) {
        return Optional.ofNullable(localAccountSettingService.getAccountSettings(channelId, channelHotelId))
                .orElseGet(() -> profileAccountSettingService.getAccountSettings(channelId, channelHotelId));
    }

    @Override
    public Map<String, Object> saveOrUpdateAccountSettings(String channelId, String channelHotelId, Map<String, Object> accountSettings) {
        return localAccountSettingService.saveOrUpdateAccountSettings(channelId, channelHotelId, accountSettings);
    }

    @Override
    public void deleteAccountSettings(String channelId, String channelHotelId) {

    }
}
