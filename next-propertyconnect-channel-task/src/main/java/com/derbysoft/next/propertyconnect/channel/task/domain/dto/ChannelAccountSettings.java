package com.derbysoft.next.propertyconnect.channel.task.domain.dto;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.HashMap;
import java.util.Map;

/**
 * @Created by <AUTHOR> on 5/19/2025
 */

@EqualsAndHashCode(callSuper = true)
@Data
public class ChannelAccountSettings extends HashMap<String, Object>{

    public ChannelAccountSettings() {

    }

    public ChannelAccountSettings(Map<? extends String, ?> m) {
        super(m);
    }

    private  SyncStatus syncStatus;


    @JsonAnyGetter
    Map<String, Object> getMap() {
        return this;
    }

    @JsonAnySetter
    void setMap(String key, Object value) {
        this.put(key, value);
    }
}
