package com.derbysoft.next.propertyconnect.channel.task.domain.dto;

import com.derbysoft.next.propertyconnect.channel.task.controller.vo.mapping.MappingSnapshot;
import com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.RemoteChannelService;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Data
@Accessors(chain = true)
public class ChannelProductsDTO implements MappingSnapshot, ProductMappingDiff {

    private String supplierId;
    private String hotelId;
    private String channelHotelId;
    private String hotelName;
    private String channelId;
    private List<Product> channelProducts;
    private String retrieveDate;
    private LocalDateTime operateDate;
    private Map<String, Object> extensions;
    private SyncStatus mappingStatus;

    @Data
    @Accessors(chain = true)
    @EqualsAndHashCode
    @NoArgsConstructor
    public static class Product implements ProductSnapshot, InventoryItemStatus, CandidateResult, DiffProductFeature {
        private String supplierId;
        private String channelId;
        private String hotelId;
        private String channelHotelId;
        private String roomId;
        private String channelRoomId;
        private String roomName;
        private String channelRoomName;
        private String rateId;
        private String channelRateId;
        private String rateName;
        private String channelRateName;
        private String status;
        private Boolean mapped;
        private Boolean availStatus;
        private String bedType;
        private String mealPlan;
        private String payType;
        private Integer maxOccupancy;
        private Double predictionScore;
        private List<Product> candidateProducts;
        private SyncStatus syncStatus;
        private String errorCode;
        private String errorMessage;
        private String lastOperationToken;
        private RemoteChannelService.Operation operation;
        private Map<String, Object> extensions;

        public void setSyncStatus(SyncStatus syncStatus) {
            this.syncStatus = syncStatus;
        }
        public void setErrorCode(String errorCode) {
            this.errorCode = errorCode;
        }
        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }

        @Override
        public String codePattern() {
            return "RoomCode/RateCode";
        }

        @Override
        public String getCode() {
            return this.channelRoomId + "/" + this.channelRateId;
        }
        @Override
        public String getName() {
            return this.channelRoomName + "/" + this.channelRateName;
        }
    }
}
