package com.derbysoft.next.propertyconnect.channel.task.client.request;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@Builder
public class SendTemplatedEmailRequest {

    private String messageId;

    private String mailFrom;
    private Set<String> mailTo;
    private Set<String> mailCc;
    private String subject;
    private String template;
    private Map<String, Object> templateData;

}
