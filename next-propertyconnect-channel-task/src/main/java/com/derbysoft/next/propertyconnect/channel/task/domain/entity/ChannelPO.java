package com.derbysoft.next.propertyconnect.channel.task.domain.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * @Created by <AUTHOR> on 2023/4/11
 */


@EqualsAndHashCode(callSuper = true)
@Data
@Document(collection = "channel")
public class ChannelPO extends BaseEntity {
    @Id
    private String id;
    @Indexed
    private String channelId;

    private HashMap<String, String> accountSettings;

    public Map<String, String> channelDetails;


    public String getProductQueryUrl(){
        return Optional.ofNullable(channelDetails).map(channelDetails -> channelDetails.get("productQueryUrl")).orElse(null);
    }

}
