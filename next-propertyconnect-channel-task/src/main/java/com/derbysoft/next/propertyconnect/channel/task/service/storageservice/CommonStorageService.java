package com.derbysoft.next.propertyconnect.channel.task.service.storageservice;

import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelHotelDTO;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.StoragePOMapper;
import com.derbysoft.next.propertyconnect.channel.task.domain.entity.ChannelHotelPO;
import com.derbysoft.next.propertyconnect.channel.task.domain.entity.ChannelHotelRatePO;
import com.derbysoft.next.propertyconnect.channel.task.domain.entity.ChannelHotelRoomPO;
import lombok.extern.slf4j.Slf4j;
import org.mapstruct.MappingTarget;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.lang.reflect.InvocationTargetException;
import java.util.List;

/**
 * @Created by <AUTHOR> on 2023/6/7
 */

@Slf4j
public abstract class CommonStorageService<PO> implements ChannelInfoStorageService {
    private final MongoTemplate mongoTemplate;
    private final Class<PO> poClass;
    protected final DTOtoPOTranslator<PO> translator;

    public CommonStorageService(MongoTemplate mongoTemplate, Class<PO> poClass, DTOtoPOTranslator<PO> translator) {
        this.mongoTemplate = mongoTemplate;
        this.poClass = poClass;
        this.translator = translator;
    }

    protected interface DTOtoPOTranslator<PO> extends StoragePOMapper {

        void fillIn(ChannelHotelDTO from, @MappingTarget PO to);
        ChannelHotelDTO reverseMap(PO channelHotelPO);
    }

    protected PO queryChannelHotel(String channelId, String channelHotelId) {
        return mongoTemplate.findOne(
                new Query(Criteria.where("channelId").is(channelId)
                        .and("channelHotelId").is(channelHotelId))
                , poClass
        );
    }

    protected PO queryChannelHotel(String channelId, String channelHotelId, String code) {
        return mongoTemplate.findOne(
                new Query(Criteria.where("channelId").is(channelId)
                        .and("channelHotelId").is(channelHotelId)
                        .and("code").is(code))
                , poClass
        );
    }


    protected List<PO> queryChannelHotels(String channelId, String channelHotelId) {
        return mongoTemplate.find(
                new Query(Criteria.where("channelId").is(channelId)
                        .and("channelHotelId").is(channelHotelId))
                , poClass
        );
    }

    @Override
    public ChannelHotelDTO getChannelHotel(String channelId, String channelHotelId) {
        return translator.reverseMap(queryChannelHotel(channelId, channelHotelId));
    }

    @Override
    public ChannelHotelDTO saveIncrementalChannelProduct(ChannelHotelDTO channelHotel) {
        PO po = null;
        if (poClass.isAssignableFrom(ChannelHotelRoomPO.class)) {
            po = this.queryChannelHotel(channelHotel.getChannelId(), channelHotel.getChannelHotelId(), channelHotel.getRoomInfo().getCode());
        }
        if (poClass.isAssignableFrom(ChannelHotelRatePO.class)){
            po = this.queryChannelHotel(channelHotel.getChannelId(), channelHotel.getChannelHotelId(), channelHotel.getRateInfo().getCode());
        }
        if (poClass.isAssignableFrom(ChannelHotelPO.class)){
            po = this.queryChannelHotel(channelHotel.getChannelId(), channelHotel.getChannelHotelId());
        }
        try {
            if (null == po) {
                po = poClass.getDeclaredConstructor().newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | InvocationTargetException | NoSuchMethodException e) {
            throw new RuntimeException(e);
        }

        translator.fillIn(channelHotel, po);
        return translator.reverseMap(mongoTemplate.save(po));
    }

    @Override
    public void deleteChannelHotel(String channelId, String channelHotelId) {
        mongoTemplate.remove(new Query(Criteria.where("channelId").is(channelId)
                .and("channelHotelId").is(channelHotelId)), poClass);
    }
}
