package com.derbysoft.next.propertyconnect.channel.task.controller.vo;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @Created by <AUTHOR> on 2023/4/18
 */

@Data
public class ChannelProductVO {

    private String channelId;
    private String channelHotelId;
    private String hotelName;
    private List<Product> channelProducts;
    private String retrieveDate;

    @Data
    public static class Product {
        private String roomId;
        private String roomName;
        private String rateId;
        private String rateName;
        private ItemStatus status;
        private Boolean availStatus;
        private Map<String, Object> extensions;
        private SyncStatus syncStatus;
        private String errorCode;
        private String errorMessage;
    }
}
