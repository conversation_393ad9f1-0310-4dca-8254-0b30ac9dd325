package com.derbysoft.next.propertyconnect.channel.task.controller.api;

import com.derbysoft.extension.aggregationtool.controllerservice.ControllerAndService;
import com.derbysoft.next.commons.boot.logsupport.annotation.perf.PerfLog;
import com.derbysoft.next.commons.boot.logsupport.annotation.stream.StreamLog;
import com.derbysoft.next.commons.boot.logsupport.annotation.stream.StreamLogRequest;
import com.derbysoft.next.propertyconenct.channel.common.exception.response.UnifyResult;
import com.derbysoft.next.propertyconnect.channel.task.config.UnifyResultWrapper;
import com.derbysoft.next.propertyconnect.channel.task.controller.vo.ChannelAccountVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

/**
 * @Created by <AUTHOR> on 2023/4/11
 */

@ControllerAndService(wrapper = UnifyResultWrapper.class)
@RequestMapping("/api")
@Tag(name = "ChannelAccountAPI", description = "ChannelAccountAPI", extensions = @Extension(properties = @ExtensionProperty(name = "order", value = "2")))
public interface ChannelAccountAPI {

    @GetMapping(value = {
            "/channels/{channelId}/channelhotels/{channelHotelId}/account"
    })
    @PerfLog("GetSensitiveData")
    @Operation(summary = "GetHotelCredential")
    @StreamLog(inheritPref = true)
    UnifyResult<ChannelAccountVO.AccountSetting> getSpecificChannel(@PathVariable("channelId") String channelId,
                                                                    @PathVariable(value = "channelHotelId") String channelHotelId);

    @PostMapping(value = {
            "/channels/{channelId}/channelhotels/{channelHotelId}/account"
    })
    @PerfLog("SaveSensitiveData")
    @StreamLog(inheritPref = true)
    @Operation(summary = "SaveHotelCredential")
    @StreamLogRequest(body = "#channelDTO")
    UnifyResult<ChannelAccountVO.AccountSetting> updateSpecificChannel(@PathVariable("channelId") String channelId,
                                                                       @PathVariable(value = "channelHotelId", required = false) String channelHotelId,
                                                                       @RequestBody ChannelAccountVO.AccountSetting channelDTO);

    @DeleteMapping(value = {
            "/channels/{channelId}/channelhotels/{channelHotelId}/account"
    })
    @PerfLog("SaveSensitiveData")
    @Operation(summary = "DeleteHotelCredential")
    @StreamLog(inheritPref = true)
    UnifyResult<Void> deleteSpecificChannel(@PathVariable("channelId") String channelId,
                                            @PathVariable(value = "channelHotelId", required = false) String channelHotelId);

}
