package com.derbysoft.next.propertyconnect.channel.task.controller.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(implementation = ExceptionListResponseVO.class)
public class ExceptionListResponseVO {

	@JsonProperty("ratePlan")
	private String ratePlan;

	@JsonProperty("level")
	private String level;

	@JsonProperty("errCode")
	private String errCode;

	@JsonProperty("supplier")
	private String supplier;

	@JsonProperty("channel")
	private String channel;

	@JsonProperty("hotel")
	private String hotel;

	@JsonProperty("roomType")
	private String roomType;

	@JsonProperty("errMessage")
	private String errMessage;

	@JsonProperty("createTimestamp")
	private String createTimestamp;

	public void setRatePlan(String ratePlan){
		this.ratePlan = ratePlan;
	}

	public String getRatePlan(){
		return ratePlan;
	}

	public void setLevel(String level){
		this.level = level;
	}

	public String getLevel(){
		return level;
	}

	public void setErrCode(String errCode){
		this.errCode = errCode;
	}

	public String getErrCode(){
		return errCode;
	}

	public void setSupplier(String supplier){
		this.supplier = supplier;
	}

	public String getSupplier(){
		return supplier;
	}

	public void setChannel(String channel){
		this.channel = channel;
	}

	public String getChannel(){
		return channel;
	}

	public void setHotel(String hotel){
		this.hotel = hotel;
	}

	public String getHotel(){
		return hotel;
	}

	public void setRoomType(String roomType){
		this.roomType = roomType;
	}

	public String getRoomType(){
		return roomType;
	}

	public void setErrMessage(String errMessage){
		this.errMessage = errMessage;
	}

	public String getErrMessage(){
		return errMessage;
	}

	public void setCreateTimestamp(String createTimestamp){
		this.createTimestamp = createTimestamp;
	}

	public String getCreateTimestamp(){
		return createTimestamp;
	}
}
