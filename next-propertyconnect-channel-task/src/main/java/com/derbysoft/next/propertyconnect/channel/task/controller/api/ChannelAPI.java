package com.derbysoft.next.propertyconnect.channel.task.controller.api;

import com.derbysoft.extension.aggregationtool.controllerservice.ControllerAndService;
import com.derbysoft.next.commons.boot.logsupport.annotation.perf.PerfLog;
import com.derbysoft.next.commons.boot.logsupport.annotation.perf.PerfParameter;
import com.derbysoft.next.commons.boot.logsupport.annotation.stream.StreamLog;
import com.derbysoft.next.commons.boot.logsupport.annotation.stream.StreamLogRequest;
import com.derbysoft.next.propertyconenct.channel.common.exception.response.UnifyResult;
import com.derbysoft.next.propertyconenct.channel.common.utils.Constants;
import com.derbysoft.next.propertyconnect.channel.task.config.UnifyResultWrapper;
import com.derbysoft.next.propertyconnect.channel.task.controller.vo.ChannelVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.*;

/**
 * @Created by <AUTHOR> on 2023/5/23
 */

@ControllerAndService(wrapper = UnifyResultWrapper.class)
@RequestMapping("/api")
@Tag(name = "ChannelAPI", description = "ChannelAPI", extensions = @Extension(properties = @ExtensionProperty(name = "order", value = "1")))
public interface ChannelAPI {


    @PerfLog("GetChannel")
    @PerfParameter(name = Constants.Perf.CHANNEL, value = "#channelId")
    @GetMapping("/channel/{channelId}")
    @Operation(summary = "GetChannel", description = "Get channel settings")
    @StreamLog(process = "GetChannel", inheritPref = true, proxy = false, wrapHeaderAsParameter = false)
    @StreamLogRequest(body = "#channelRQ")
    UnifyResult<ChannelVO> getChannelSettings(@PathVariable("channelId") String channelId);


    @PerfLog("SaveChannel")
    @PerfParameter(name = Constants.Perf.CHANNEL, value = "#channelRQ.channelId")
    @PostMapping("/channel/{channelId}")
    @Operation(summary = "SaveChannel", description = "Save channel settings")
    @StreamLog(process = "SaveChannel", inheritPref = true, proxy = false, wrapHeaderAsParameter = false)
    @StreamLogRequest(body = "#channelRQ")
    UnifyResult<ChannelVO> saveChannelSettings(@Valid @RequestBody ChannelVO channelRQ);

}
