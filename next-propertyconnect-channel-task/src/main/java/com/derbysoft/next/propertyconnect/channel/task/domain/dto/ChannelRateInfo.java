package com.derbysoft.next.propertyconnect.channel.task.domain.dto;

import com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.RemoteChannelService;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@NoArgsConstructor
public class ChannelRateInfo implements InventoryItemStatus {
	private String code;
	private String name;
	private String description;
	private EffectiveDateRange effectiveDateRange;
	private Map<String, Object> i18n;
	private ItemStatus status;
	private SyncStatus syncStatus;
	private String errorCode;
	private String errorMessage;
	private String lastOperationToken;
	private RemoteChannelService.Operation operation;
	private Map<String, Object> extensions;

	public ChannelRateInfo(String code) {
		this.code = code;
	}

	@Override
	public String codePattern() {
		return "RateCode";
	}

	@Data
	public static class EffectiveDateRange{
		private String effectiveDate;
		private String expireDate;
	}
}
