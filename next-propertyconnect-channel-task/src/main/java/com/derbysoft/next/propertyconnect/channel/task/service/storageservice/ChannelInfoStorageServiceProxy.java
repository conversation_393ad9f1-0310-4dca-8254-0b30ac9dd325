package com.derbysoft.next.propertyconnect.channel.task.service.storageservice;

import com.derbysoft.next.propertyconenct.channel.common.mapstruct.BaseMapper;
import com.derbysoft.next.propertyconenct.channel.common.utils.GeneratedMapper;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelHotelDTO;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelProductsDTO;
import com.derbysoft.next.propertyconnect.channel.task.service.impl.ctrip.SnapshotService;
import com.derbysoft.next.propertyconnect.channel.task.util.FillUtil;
import lombok.RequiredArgsConstructor;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

/**
 * @Created by <AUTHOR> on 2023/6/12
 */

@Service
@Primary
@RequiredArgsConstructor
public class ChannelInfoStorageServiceProxy implements ChannelInfoStorageService{
    private final ChannelHotelStorageService hotelStorageService;
    private final ChannelRoomStorageService roomStorageService;
    private final ChannelRateStorageService rateStorageService;
    private final SnapshotService snapshotService;
    private final FillUtil fillUtil = FillUtil.INSTANCE;
    private final ChannelProductsToChannelHotel dtoTranslator = Mappers.getMapper(ChannelProductsToChannelHotel.class);

    @Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    @AnnotateWith(GeneratedMapper.class)
    public interface ChannelProductsToChannelHotel extends BaseMapper<ChannelProductsDTO, ChannelHotelDTO> {
        @Mapping(source = "channelProducts", target = "productsInfo")
        @Override
        ChannelHotelDTO map(ChannelProductsDTO channelProductsDTO);

        @InheritInverseConfiguration(name = "map")
        @Override
        ChannelProductsDTO reverseMap(ChannelHotelDTO channelHotelDTO);
    }

    @Override
    public ChannelHotelDTO getChannelHotel(String channelId, String channelHotelId) {
        var channelHotelDTO = new ChannelHotelDTO();
        var hotelInfo = hotelStorageService.getChannelHotel(channelId, channelHotelId);
        var roomInfo = roomStorageService.getChannelHotel(channelId, channelHotelId);
        var rateInfo = rateStorageService.getChannelHotel(channelId, channelHotelId);
        var productsInfo = dtoTranslator.map(snapshotService.get(channelId, channelHotelId));
        fillUtil.fillClone(hotelInfo, channelHotelDTO);
        fillUtil.fillClone(roomInfo, channelHotelDTO);
        fillUtil.fillClone(rateInfo, channelHotelDTO);
        fillUtil.fillClone(productsInfo, channelHotelDTO);
        return channelHotelDTO;
    }

    @Override
    public ChannelHotelDTO saveIncrementalChannelProduct(ChannelHotelDTO channelHotel) {
        var channelHotelDTO = new ChannelHotelDTO();
        if (channelHotel.getHotelInfo() != null || channelHotel.getAccountSettings() != null) {
            var hotelInfo = hotelStorageService.saveIncrementalChannelProduct(channelHotel);
            fillUtil.fillClone(hotelInfo, channelHotelDTO);
        }

        if (channelHotel.getRoomsInfo() != null) {
            var roomInfo = roomStorageService.saveIncrementalChannelProduct(channelHotel);
            fillUtil.fillClone(roomInfo, channelHotelDTO);
        }

        if (channelHotel.getRatesInfo() != null) {
            var rateInfo = rateStorageService.saveIncrementalChannelProduct(channelHotel);
            fillUtil.fillClone(rateInfo, channelHotelDTO);
        }

        if (channelHotel.getProductsInfo() != null){
            var productInfo = snapshotService.incrementalPut(dtoTranslator.reverseMap(channelHotel));
            fillUtil.fillClone(dtoTranslator.map(productInfo), channelHotelDTO);
        }
        return channelHotelDTO;
    }

    @Override
    public ChannelHotelDTO saveAllChannelProducts(ChannelHotelDTO channelHotel) {
        var channelHotelDTO = new ChannelHotelDTO();
        if (channelHotel.getHotelInfo() != null || channelHotel.getAccountSettings() != null) {
            var hotelInfo = hotelStorageService.saveAllChannelProducts(channelHotel);
            fillUtil.fillClone(hotelInfo, channelHotelDTO);
        }

        if (channelHotel.getRoomsInfo() != null) {
            var roomInfo = roomStorageService.saveAllChannelProducts(channelHotel);
            fillUtil.fillClone(roomInfo, channelHotelDTO);
        }

        if (channelHotel.getRatesInfo() != null) {
            var rateInfo = rateStorageService.saveAllChannelProducts(channelHotel);
            fillUtil.fillClone(rateInfo, channelHotelDTO);
        }

        if (channelHotel.getProductsInfo() != null){
            var productInfo = snapshotService.put(dtoTranslator.reverseMap(channelHotel));
            fillUtil.fillClone(dtoTranslator.map(productInfo), channelHotelDTO);
        }
        return channelHotelDTO;
    }

    @Override
    public void deleteChannelHotel(String channelId, String channelHotelId) {
        hotelStorageService.deleteChannelHotel(channelId, channelHotelId);
        roomStorageService.deleteChannelHotel(channelId, channelHotelId);
        rateStorageService.deleteChannelHotel(channelId, channelHotelId);
        snapshotService.delete(new ChannelProductsDTO().setChannelId(channelId).setChannelHotelId(channelHotelId));
    }
}
