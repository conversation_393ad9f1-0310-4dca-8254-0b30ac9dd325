package com.derbysoft.next.propertyconnect.channel.task.controller.vo.mapping;

import com.derbysoft.next.propertyconnect.channel.task.controller.vo.SyncStatus;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * @Created by <AUTHOR> on 2023/4/12
 */

@Data
public class MappingVO {

    public interface BaseView { }
    public interface RequestMappingView { }


    @Schema(example = "CTRIP")
    @JsonView({BaseView.class})
    String channelId;

    @JsonView({BaseView.class, RequestMappingView.class})
    @Schema(example = "CNLUQ5FKPF")
    String hotelId;

    @JsonView({BaseView.class})
    @Schema(example = "430880")
    String channelHotelId;

    @JsonView({BaseView.class})
    @Schema(example = "SUBMITTED")
    SyncStatus mappingStatus;

    LocalDateTime retrieveDate;

    @JsonView({BaseView.class, RequestMappingView.class})
    List<ProductMappingVO> mappings;

    @JsonView({BaseView.class, RequestMappingView.class})
    Map<String, Object> extensions;

    @Data
    public static class ProductMappingVO {
        @JsonView({BaseView.class, RequestMappingView.class})
        @Schema(example = "RM")
        String roomId;

        @JsonView({BaseView.class, RequestMappingView.class})
        @Schema(example = "001826ET")
        String channelRoomId;

        @JsonView({BaseView.class, RequestMappingView.class})
        @Schema(example = "RO")
        String rateId;

        @JsonView({BaseView.class, RequestMappingView.class})
        @Schema(example = "001826xcxf")
        String channelRateId;

        @JsonView({BaseView.class})
        @Schema(example = "true")
        Boolean mapped;

        @JsonView({BaseView.class, RequestMappingView.class})
        Map<String, Object> extensions;
    }
}
