package com.derbysoft.next.propertyconnect.channel.task.service;

import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelHotelDTO;
import org.apache.commons.lang.NotImplementedException;

import java.util.List;

/**
 * @Created by <AUTHOR> on 2023/6/8
 */

public interface ChannelHotelCRUDService {

    default ChannelHotelDTO getByIds(String channelId, String channelHotelId, List<String> ids){
        throw new NotImplementedException(String.format("Channel %s does not support %s query operation", channelId, channelHotelId));
    }

    default ChannelHotelDTO getChannelHotel(String channelId, String channelHotelId){
        throw new NotImplementedException(String.format("Channel %s does not support %s query operation", channelId, channelHotelId));
    }

    default ChannelHotelDTO saveIncrementalChannelProduct(ChannelHotelDTO channelHotel){
        throw new NotImplementedException(String.format("Channel %s does not support %s save operation", channelHotel.getChannelId(), channelHotel.getChannelHotelId()));
    }

    default ChannelHotelDTO saveAllChannelProducts(ChannelHotelDTO channelHotel){
        throw new NotImplementedException(String.format("Channel %s does not support %s save operation", channelHotel.getChannelId(), channelHotel.getChannelHotelId()));
    }

    default void deleteChannelHotel(String channelId, String channelHotelId){
        throw new NotImplementedException(String.format("Channel %s does not support %s delete operation", channelId, channelHotelId));
    }
}
