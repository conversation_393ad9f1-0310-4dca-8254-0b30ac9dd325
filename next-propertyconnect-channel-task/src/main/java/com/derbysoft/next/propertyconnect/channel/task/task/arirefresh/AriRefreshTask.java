package com.derbysoft.next.propertyconnect.channel.task.task.arirefresh;

import com.derbysoft.extension.schedulecentersupport.annotation.ScheduleCenterTaskExecutor;
import com.derbysoft.next.commons.boot.logsupport.annotation.perf.PerfParameter;
import com.derbysoft.next.commons.core.logsupport.constant.LogConst;
import com.derbysoft.next.commons.core.logsupport.handler.LogHandler;
import com.derbysoft.next.commons.core.logsupport.handler.PerfLogHandler;
import com.derbysoft.next.propertyconenct.channel.common.utils.Constants;
import com.derbysoft.next.propertyconenct.channel.common.utils.JSONUtil;
import com.derbysoft.next.propertyconnect.channel.task.config.schedulecenter.ChannelServiceTask;
import com.derbysoft.next.propertyconnect.channel.task.config.schedulecenter.ChannelServiceTypedTask;
import com.derbysoft.next.propertyconnect.channel.task.service.RemoteService;
import com.derbysoft.next.propertyconnect.channel.task.task.ChannelServiceTaskPerfLog;
import com.derbysoft.next.propertyconnect.channel.task.util.CollectionUtil;
import com.derbysoft.next.propertyconnect.channel.task.util.ValidateUtil;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import lombok.experimental.ExtensionMethod;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Created by <AUTHOR> on 2023/2/28
 */

@ExtensionMethod({CollectionUtil.class, JSONUtil.class})
@EnableConfigurationProperties(AriRefreshConfigurationProperties.class)
@RequiredArgsConstructor
@Component
public class AriRefreshTask {
    private final RemoteService remoteService;
    private final AriRefreshConfigurationProperties refreshConfig;
    private final AriRefreshTaskTranslator translator;

    @ScheduleCenterTaskExecutor("PROPERTYCONNECT_CHANNEL.CHANNEL_APT_ARIREFRESH_SCHEDULE.SINGLE")
    public class AriRefreshTaskScheduler implements ChannelServiceTypedTask {


        @Override
        public TaskType taskType() {
            return TaskType.ChannelAptARIRefresh;
        }

        @Override
        public List<ChannelServiceTask> nextTask(ChannelServiceTask task) {
            var nowDateTime = LocalDateTime.now();
            var nowDate = nowDateTime.toLocalDate();
            return customizeTask(task, Optional.ofNullable(task)
                    .map(ChannelServiceTask::getChannel)
                    .map(channelId -> Optional.ofNullable(task.getSupplier())
                            .filter(StringUtils::isNotBlank)
                            .map(supplierId -> remoteService.getActiveGoDistributorHotelIds(supplierId, channelId))
                            .orElseGet(() -> remoteService.getActiveGoDistributorHotelIds(channelId))
                    )
                    .orElseThrow(() -> new IllegalStateException("No active hotel found"))
                    .stream()
                    .distinct()
                    .filter(hotelId -> {
                        if (null == task.getHotels() || task.getHotels().isEmpty()) {
                            return true;
                        }
                        return task.getHotels().contains(hotelId);
                    })
                    .partition(refreshConfig.getHotelBatchSize(task.getChannel()))
                    //TODO: Split first day, minusDay -> minusHours
                    .flatMap(hotelId -> CollectionUtil.mapSpanInDateRange(
                            calculateStartDate(nowDateTime, refreshConfig.getDaysBefore(task.getChannel())),
                            nowDate.plusDays(refreshConfig.getMaxRefreshDays(task.getChannel()).longValue()),
                            refreshConfig.getMaxSplitSpan(task.getChannel()),
                            (startDate, endDate) -> task.toBuilder()
                                    .id(null)
                                    .supplier(null == task.getSupplier() ? "PROPERTYCONNECT" : task.getSupplier())
                                    .hotels(hotelId)
                                    .startDate(startDate.format(DateTimeFormatter.ISO_LOCAL_DATE))
                                    .endDate(endDate.format(DateTimeFormatter.ISO_LOCAL_DATE))
                                    .build())
                            .stream())
                    .collect(Collectors.toList())
            );
        }

        private LocalDate calculateStartDate(LocalDateTime dateTimeNow, String daysBefore){
            if (null == dateTimeNow || StringUtils.isBlank(daysBefore)) {
                return LocalDate.now();
            }
            var daysBeforeFormat = AriRefreshConfigurationProperties.DAYS_BEFORE_FORMAT;
            var daysBeforeMatcher = daysBeforeFormat.matcher(daysBefore);
            if (daysBeforeMatcher.matches()) {
                if (StringUtils.isBlank(daysBeforeMatcher.group(2))) {
                    return dateTimeNow.toLocalDate().minusDays(Long.parseLong(daysBefore));
                }else {
                    return dateTimeNow.minusHours(Long.parseLong(daysBeforeMatcher.group(1))).toLocalDate();
                }
            }
            throw new IllegalStateException("Unsupported datetime configurations " + daysBefore + " for date times " + dateTimeNow.format(DateTimeFormatter.ISO_DATE_TIME));
        }

        private List<ChannelServiceTask> customizeTask(ChannelServiceTask task, List<ChannelServiceTask> tasks) {
            CollectionUtil.forEachWithIndex(CollectionUtil.partitionList(tasks, refreshConfig.getTaskBatch(task.getChannel())),
                    (index, partTasks) -> partTasks.forEach(partTask ->
                        partTask.setDelayExecuteTime((partTask.getDelayExecuteTime() > 0 ? partTask.getDelayExecuteTime() : System.currentTimeMillis()) + (refreshConfig.getTaskTimeGap(task.getChannel()) * index))
                    ));

            return tasks;
        }
    }


    @ScheduleCenterTaskExecutor("PROPERTYCONNECT_CHANNEL.CHANNEL_APT_ARIREFRESH_TASK.SINGLE")
    public class AriRefreshTaskExecutor implements ChannelServiceTypedTask {
        @Override
        public TaskType taskType() {
            return TaskType.ChannelAptARIRefresh;
        }

        @ChannelServiceTaskPerfLog
        @PerfParameter(name = LogConst.Parameter.PROCESS, value = "'TriggerARIRefresh'")
        @PerfParameter(name = Constants.Perf.SUPPLIER, value = "#task.supplier")
        @PerfParameter(name = Constants.Perf.HOTEL, value = "#task.hotels")
        @PerfParameter(name = Constants.Perf.START_DATE, value = "#task.startDate")
        @PerfParameter(name = Constants.Perf.END_DATE, value = "#task.endDate")
        @Override
        public void doExecute(ChannelServiceTask task) {
            ValidateUtil.validateNull(task, ChannelServiceTask::getHotels, ChannelServiceTask::getChannel, ChannelServiceTask::getStartDate, ChannelServiceTask::getEndDate);

            var traceToken = StringUtils.isNotBlank(task.getEchoToken()) ? task.getEchoToken() : Optional.ofNullable(PerfLogHandler.currentHandler()).map(LogHandler::getToken).orElse(null);


            try {
                var result = remoteService.triggerARIRefresh(translator.toTriggerARIRefreshRequest(task), traceToken)
                        .orElseThrow(() -> new IllegalStateException("ARI refresh failed with null response"));

                Optional.ofNullable(result.getString("$.result.failed.errorMessage"))
                        .ifPresent(errorMessage -> {
                            throw new IllegalStateException("ARI refresh failed: " + errorMessage);
                        });

                Optional.of(result.getCollection("$.result.failedHotels.*.hotelCode", String.class))
                        .filter(CollectionUtils::isNotEmpty)
                        .ifPresent(failureHotels -> {
                            throw new IllegalStateException("ARI refresh failed with failed hotels: " + String.join(",", failureHotels));
                        });
            } catch (FeignException fe) {
                throw new IllegalStateException("ARI refresh failed: " + fe.getLocalizedMessage());
            }

        }
    }

}
