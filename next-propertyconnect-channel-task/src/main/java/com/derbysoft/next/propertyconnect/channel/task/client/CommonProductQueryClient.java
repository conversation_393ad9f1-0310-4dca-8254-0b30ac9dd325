package com.derbysoft.next.propertyconnect.channel.task.client;

import com.derbysoft.next.propertyconnect.channel.task.domain.dto.CommonChannelProductResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

import java.net.URI;
import java.util.Optional;

/**
 * @Created by <AUTHOR> on 10/9/2024
 */

//@RemoteService(exceptionOnFailure = true)
@FeignClient(name = "commonProductQueryClient")
public interface CommonProductQueryClient {

    @GetMapping
    Optional<CommonChannelProductResponse> getProductFromChannel(URI url);

    default Optional<CommonChannelProductResponse> getProductFromChannel(String url) {
        return this.getProductFromChannel(URI.create(url));
    }
}