package com.derbysoft.next.propertyconnect.channel.task.client;

import org.springframework.cloud.openfeign.FeignClientFactoryBean;

import java.lang.reflect.InvocationHandler;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.lang.reflect.Proxy;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class FeignClientInvocationHandler implements InvocationHandler {

    private final Map<Class<?>, Object> feignClients;
    // Cache for initialized client instances to avoid repeated initialization
    private final Map<Class<?>, Object> initializedClients = new ConcurrentHashMap<>();

    public FeignClientInvocationHandler(Map<Class<?>, Object> feignClients) {
        this.feignClients = feignClients;
    }

    @Override
    public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
        if (method.getDeclaringClass() == Object.class) {
            if ("hashCode".equals(method.getName())) {
                return System.identityHashCode(proxy);
            } else if ("equals".equals(method.getName())) {
                return proxy == args[0];
            } else if ("toString".equals(method.getName())) {
                return "Proxy for RemoteService with clients: " + feignClients.keySet();
            }
        }

        Class<?> declaringClass = method.getDeclaringClass();
        // Try to get from the cache first
        Object handleClient = initializedClients.get(declaringClass);
        
        if (handleClient == null) {
            // If not in cache, get from original map and initialize
            synchronized (this) {
                // Double-check to avoid race condition
                handleClient = initializedClients.get(declaringClass);
                if (handleClient == null) {
                    handleClient = feignClients.get(declaringClass);
                    if (handleClient == null) {
                        throw new UnsupportedOperationException("No FeignClient found for method: " + method.getName());
                    }
                    
                    if (handleClient instanceof FeignClientFactoryBean) {
                        handleClient = ((FeignClientFactoryBean) handleClient).getObject();
                    }
                    
                    // Store in cache
                    initializedClients.put(declaringClass, handleClient);
                }
            }
        }

        try {
            return method.invoke(handleClient, args);
        } catch (InvocationTargetException e) {
            throw e.getCause() != null ? e.getCause() : e;
        }
    }

    @SuppressWarnings("unchecked")
    public <T> T createProxy(Class<T> proxyInterface) {
        return (T) Proxy.newProxyInstance(
                proxyInterface.getClassLoader(),
                new Class[]{proxyInterface},
                this
        );
    }
}
