package com.derbysoft.next.propertyconnect.channel.task.domain.repository;

import com.derbysoft.next.propertyconnect.channel.task.domain.entity.ChannelProductsMappingPO;
import org.springframework.data.mongodb.repository.MongoRepository;

/**
 * @Created by <AUTHOR> on 2023/4/23
 */

public interface ChannelProductsMappingRepository extends MongoRepository<ChannelProductsMappingPO, String> {
    ChannelProductsMappingPO getByChannelIdAndHotelIdAndChannelHotelId(String channelId, String hotelId, String channelHotelId);

}
