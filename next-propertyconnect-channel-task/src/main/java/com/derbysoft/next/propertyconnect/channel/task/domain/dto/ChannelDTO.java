package com.derbysoft.next.propertyconnect.channel.task.domain.dto;

import lombok.Data;

import java.util.Map;


@Data
public class ChannelDTO {
    private String channelId;
    private String channelName;
    // 20211012 add by yc OPRC-1733 start
    private Map<String, String> nameMultiLang;
    // 20211012 add by yc OPRC-1733 end
    private String status;
    private Boolean defaultMapping;
//    private String description;
    private String category;
    // 20210908 add by yc OPRC-1454 start
    private Map<String, String> descMultiLang;
    private String region;
    private String tag;
    // 20210908 add by yc OPRC-1454 end
    // 20220119 add by yc OPRC-2613 start
    private String ariSource; // EBK/PMS
    // 20220119 add by yc OPRC-2613 end
    private Boolean supportPromo;
    private Boolean resRetryFlg;
    // 2022/8/9-modify by liucl (3012661684) start
    private Boolean availCheckFlg;
    // 2022/8/9-modify by liucl (3012661684) end
    // 20220517 add by yc OPRC-3455 start
    private Boolean connected;
    // 20220517 add by yc OPRC-3455 end
    private Integer resChannelTimeOut;
    private Integer sort;
    private Map<String, Object> settings;

}
