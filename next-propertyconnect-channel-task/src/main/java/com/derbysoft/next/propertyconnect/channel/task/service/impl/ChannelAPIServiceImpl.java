package com.derbysoft.next.propertyconnect.channel.task.service.impl;

import com.derbysoft.next.propertyconenct.channel.common.mapstruct.BaseMapper;
import com.derbysoft.next.propertyconenct.channel.common.utils.GeneratedMapper;
import com.derbysoft.next.propertyconnect.channel.task.controller.vo.ChannelVO;
import com.derbysoft.next.propertyconnect.channel.task.domain.entity.ChannelPO;
import com.derbysoft.next.propertyconnect.channel.task.domain.repository.ChannelRepository;
import com.derbysoft.next.propertyconnect.channel.task.service.ChannelAPIService;
import lombok.RequiredArgsConstructor;
import org.mapstruct.AnnotateWith;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Created by <AUTHOR> on 2023/5/23
 */

@Service
@RequiredArgsConstructor
@RestController
public class ChannelAPIServiceImpl implements ChannelAPIService {

    private final ChannelRepository channelRepository;
    private final ChannelTranslator channelTranslator = Mappers.getMapper(ChannelTranslator.class);


    @Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    @AnnotateWith(GeneratedMapper.class)
    public interface ChannelTranslator extends BaseMapper<ChannelPO, ChannelVO> {
    }


    @Override
    public ChannelVO getChannelSettings(String channelId) {
        return channelTranslator.map(channelRepository.findByChannelId(channelId).orElseThrow(() -> new RuntimeException("Channel not found!")));
    }

    @Override
    public ChannelVO saveChannelSettings(ChannelVO channelRQ) {
        var existChannel = channelRepository.findByChannelId(channelRQ.getChannelId()).orElse(null);

        if (null == existChannel) {
            existChannel = channelTranslator.reverseMap(channelRQ);
        }else {
            channelTranslator.reverseFill(channelRQ, existChannel);
        }

        return channelTranslator.map(channelRepository.save(existChannel));
    }
}
