package com.derbysoft.next.propertyconnect.channel.task.util;

import com.derbysoft.next.propertyconenct.channel.common.utils.GeneratedMapper;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelHotelDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

/**
 * @Created by <AUTHOR> on 2023/6/12
 */

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
@AnnotateWith(GeneratedMapper.class)
public interface FillUtil {
    FillUtil INSTANCE = Mappers.getMapper(FillUtil.class);

    void fillClone(ChannelHotelDTO in, @MappingTarget ChannelHotelDTO out);

}
