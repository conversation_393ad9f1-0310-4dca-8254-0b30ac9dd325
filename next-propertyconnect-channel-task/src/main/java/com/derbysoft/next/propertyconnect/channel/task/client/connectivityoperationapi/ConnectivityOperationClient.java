package com.derbysoft.next.propertyconnect.channel.task.client.connectivityoperationapi;

import com.derbysoft.next.commons.boot.logsupport.annotation.stream.StreamLog;
import com.derbysoft.next.commons.boot.logsupport.annotation.stream.StreamParameter;
import com.derbysoft.next.commons.core.logsupport.constant.LogConst;
import com.derbysoft.next.propertyconnect.channel.task.client.interceptor.apilayer.ApiLayerRequest;
import com.derbysoft.next.propertyconnect.channel.task.config.SynchronizerProperties;
import com.derbysoft.next.propertyconnect.channel.task.util.APILayerDynamicUrlUtil;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import java.net.URI;
import java.util.LinkedHashMap;
import java.util.Optional;

/**
 * @Created by <AUTHOR> on 2023/3/6
 */

@EnableConfigurationProperties(SynchronizerProperties.class)
//@RemoteService(exceptionOnFailure = true)
@FeignClient(name = "connectivityOperationAbilityClient", url = "${app.apilayer.url}")
public interface ConnectivityOperationClient {

    @ApiLayerRequest
    @StreamLog(proxy = false, inheritPref = true, wrapHeaderAsParameter = true)
    @StreamParameter(name = LogConst.Parameter.TRACE_TOKEN, value = "#traceToken")
    @PostMapping("/distributionpartner/{partnerCode}/properties/query")
    Optional<OperationApiQueryResponse> propertiesQuery(URI baseUrl,
                                                        @PathVariable("partnerCode") String distributorId,
                                                        @RequestBody OperationApiBasicRequest request,
                                                        @RequestHeader(LogConst.Stream.HEADER_TRACE_TOKEN) String traceToken);

    default Optional<OperationApiQueryResponse> propertiesQuery(@PathVariable("partnerCode") String distributorId,
                                                                @RequestBody OperationApiBasicRequest request,
                                                                @RequestHeader(LogConst.Stream.HEADER_TRACE_TOKEN) String traceToken) {
        return propertiesQuery(APILayerDynamicUrlUtil.getBaseUrlForDistributor(distributorId), distributorId, request, traceToken);
    }

    @ApiLayerRequest
    @StreamLog(proxy = false, inheritPref = true, wrapHeaderAsParameter = true)
    @StreamParameter(name = LogConst.Parameter.TRACE_TOKEN, value = "#traceToken")
    @PostMapping("/distributionpartner/{partnerCode}/properties/rooms/query")
    Optional<LinkedHashMap<String, Object>> propertiesRoomsQuery(URI baseUrl,
                                                                 @PathVariable("partnerCode") String distributorId,
                                                                 @RequestBody OperationApiBasicRequest request,
                                                                 @RequestHeader(LogConst.Stream.HEADER_TRACE_TOKEN) String traceToken);

    default Optional<LinkedHashMap<String, Object>> propertiesRoomsQuery(@PathVariable("partnerCode") String distributorId,
                                                                         @RequestBody OperationApiBasicRequest request,
                                                                         @RequestHeader(LogConst.Stream.HEADER_TRACE_TOKEN) String traceToken) {
        return propertiesRoomsQuery(APILayerDynamicUrlUtil.getBaseUrlForDistributor(distributorId), distributorId, request, traceToken);
    }

    @ApiLayerRequest
    @StreamLog(proxy = false, inheritPref = true, wrapHeaderAsParameter = true)
    @StreamParameter(name = LogConst.Parameter.TRACE_TOKEN, value = "#traceToken")
    @PostMapping("/distributionpartner/{partnerCode}/properties/rateplans/query")
    Optional<LinkedHashMap<String, Object>> propertiesRatePlansQuery(URI baseUrl,
                                                                     @PathVariable("partnerCode") String distributorId,
                                                                     @RequestBody OperationApiBasicRequest request,
                                                                     @RequestHeader(LogConst.Stream.HEADER_TRACE_TOKEN) String traceToken);

    default Optional<LinkedHashMap<String, Object>> propertiesRatePlansQuery(@PathVariable("partnerCode") String distributorId,
                                                                             @RequestBody OperationApiBasicRequest request,
                                                                             @RequestHeader(LogConst.Stream.HEADER_TRACE_TOKEN) String traceToken) {
        return propertiesRatePlansQuery(APILayerDynamicUrlUtil.getBaseUrlForDistributor(distributorId), distributorId, request, traceToken);
    }

    @ApiLayerRequest
    @StreamLog(proxy = false, inheritPref = true, wrapHeaderAsParameter = true)
    @StreamParameter(name = LogConst.Parameter.TRACE_TOKEN, value = "#traceToken")
    @PostMapping("/distributionpartner/{partnerCode}/credentials")
    Optional<Object> saveCredential(URI baseUrl,
                                    @PathVariable("partnerCode") String distributorId,
                                    @RequestBody OperationApiCredentialRequest request,
                                    @RequestHeader(LogConst.Stream.HEADER_TRACE_TOKEN) String traceToken);

    default Optional<Object> saveCredential(@PathVariable("partnerCode") String distributorId,
                                            @RequestBody OperationApiCredentialRequest request,
                                            @RequestHeader(LogConst.Stream.HEADER_TRACE_TOKEN) String traceToken) {
        return saveCredential(APILayerDynamicUrlUtil.getBaseUrlForDistributor(distributorId), distributorId, request, traceToken);
    }

    @ApiLayerRequest
    @StreamLog(proxy = false, inheritPref = true, wrapHeaderAsParameter = true)
    @StreamParameter(name = LogConst.Parameter.TRACE_TOKEN, value = "#traceToken")
    @PostMapping("/distributionpartner/{partnerCode}/properties/activate")
    Optional<Object> activateProperty(URI baseUrl,
                                      @PathVariable("partnerCode") String distributorId,
                                      @RequestBody OperationApiBasicRequest request,
                                      @RequestHeader(LogConst.Stream.HEADER_TRACE_TOKEN) String traceToken);

    default Optional<Object> activateProperty(@PathVariable("partnerCode") String distributorId,
                                              @RequestBody OperationApiBasicRequest request,
                                              @RequestHeader(LogConst.Stream.HEADER_TRACE_TOKEN) String traceToken) {
        return activateProperty(APILayerDynamicUrlUtil.getBaseUrlForDistributor(distributorId), distributorId, request, traceToken);
    }

    @ApiLayerRequest
    @StreamLog(proxy = false, inheritPref = true, wrapHeaderAsParameter = true)
    @StreamParameter(name = LogConst.Parameter.TRACE_TOKEN, value = "#traceToken")
    @PostMapping("/distributionpartner/{partnerCode}/properties/deactivate")
    Optional<Object> deactivateProperty(URI baseUrl,
                                        @PathVariable("partnerCode") String distributorId,
                                        @RequestBody OperationApiBasicRequest request,
                                        @RequestHeader(LogConst.Stream.HEADER_TRACE_TOKEN) String traceToken);

    default Optional<Object> deactivateProperty(@PathVariable("partnerCode") String distributorId,
                                                @RequestBody OperationApiBasicRequest request,
                                                @RequestHeader(LogConst.Stream.HEADER_TRACE_TOKEN) String traceToken) {
        return deactivateProperty(APILayerDynamicUrlUtil.getBaseUrlForDistributor(distributorId), distributorId, request, traceToken);
    }

    @ApiLayerRequest
    @StreamLog(proxy = false, inheritPref = true, wrapHeaderAsParameter = true)
    @StreamParameter(name = LogConst.Parameter.TRACE_TOKEN, value = "#traceToken")
    @PostMapping("/distributionpartner/{partnerCode}/rooms/activate")
    Optional<Object> activateRoomType(URI baseUrl,
                                      @PathVariable("partnerCode") String distributorId,
                                      @RequestBody OperationApiRoomTypeRequest request,
                                      @RequestHeader(LogConst.Stream.HEADER_TRACE_TOKEN) String traceToken);

    default Optional<Object> activateRoomType(@PathVariable("partnerCode") String distributorId,
                                              @RequestBody OperationApiRoomTypeRequest request,
                                              @RequestHeader(LogConst.Stream.HEADER_TRACE_TOKEN) String traceToken) {
        return activateRoomType(APILayerDynamicUrlUtil.getBaseUrlForDistributor(distributorId), distributorId, request, traceToken);
    }

    @ApiLayerRequest
    @StreamLog(proxy = false, inheritPref = true, wrapHeaderAsParameter = true)
    @StreamParameter(name = LogConst.Parameter.TRACE_TOKEN, value = "#traceToken")
    @PostMapping("/distributionpartner/{partnerCode}/rooms/deactivate")
    Optional<Object> deactivateRoomType(URI baseUrl,
                                        @PathVariable("partnerCode") String distributorId,
                                        @RequestBody OperationApiRoomTypeRequest request,
                                        @RequestHeader(LogConst.Stream.HEADER_TRACE_TOKEN) String traceToken);

    default Optional<Object> deactivateRoomType(@PathVariable("partnerCode") String distributorId,
                                                @RequestBody OperationApiRoomTypeRequest request,
                                                @RequestHeader(LogConst.Stream.HEADER_TRACE_TOKEN) String traceToken) {
        return deactivateRoomType(APILayerDynamicUrlUtil.getBaseUrlForDistributor(distributorId), distributorId, request, traceToken);
    }

    @ApiLayerRequest
    @StreamLog(proxy = false, inheritPref = true, wrapHeaderAsParameter = true)
    @StreamParameter(name = LogConst.Parameter.TRACE_TOKEN, value = "#traceToken")
    @PostMapping("/distributionpartner/{partnerCode}/rateplans/activate")
    Optional<Object> activateRatePlan(URI baseUrl,
                                      @PathVariable("partnerCode") String distributorId,
                                      @RequestBody OperationApiRatePlanRequest request,
                                      @RequestHeader(LogConst.Stream.HEADER_TRACE_TOKEN) String traceToken);

    default Optional<Object> activateRatePlan(@PathVariable("partnerCode") String distributorId,
                                              @RequestBody OperationApiRatePlanRequest request,
                                              @RequestHeader(LogConst.Stream.HEADER_TRACE_TOKEN) String traceToken) {
        return activateRatePlan(APILayerDynamicUrlUtil.getBaseUrlForDistributor(distributorId), distributorId, request, traceToken);
    }

    @ApiLayerRequest
    @StreamLog(proxy = false, inheritPref = true, wrapHeaderAsParameter = true)
    @StreamParameter(name = LogConst.Parameter.TRACE_TOKEN, value = "#traceToken")
    @PostMapping("/distributionpartner/{partnerCode}/rateplans/deactivate")
    Optional<Object> deactivateRatePlan(URI baseUrl,
                                        @PathVariable("partnerCode") String distributorId,
                                        @RequestBody OperationApiRatePlanRequest request,
                                        @RequestHeader(LogConst.Stream.HEADER_TRACE_TOKEN) String traceToken);

    default Optional<Object> deactivateRatePlan(@PathVariable("partnerCode") String distributorId,
                                                @RequestBody OperationApiRatePlanRequest request,
                                                @RequestHeader(LogConst.Stream.HEADER_TRACE_TOKEN) String traceToken) {
        return deactivateRatePlan(APILayerDynamicUrlUtil.getBaseUrlForDistributor(distributorId), distributorId, request, traceToken);
    }

    @ApiLayerRequest
    @StreamLog(proxy = false, inheritPref = true, wrapHeaderAsParameter = true)
    @StreamParameter(name = LogConst.Parameter.TRACE_TOKEN, value = "#traceToken")
    @PostMapping("/distributionpartner/{partnerCode}/products/activate")
    Optional<Object> activateProduct(URI baseUrl,
                                     @PathVariable("partnerCode") String distributorId,
                                     @RequestBody OperationApiProductActivationRequest request,
                                     @RequestHeader(LogConst.Stream.HEADER_TRACE_TOKEN) String traceToken);

    default Optional<Object> activateProduct(@PathVariable("partnerCode") String distributorId,
                                             @RequestBody OperationApiProductActivationRequest request,
                                             @RequestHeader(LogConst.Stream.HEADER_TRACE_TOKEN) String traceToken) {
        return activateProduct(APILayerDynamicUrlUtil.getBaseUrlForDistributor(distributorId), distributorId, request, traceToken);
    }

    @ApiLayerRequest
    @StreamLog(proxy = false, inheritPref = true, wrapHeaderAsParameter = true)
    @StreamParameter(name = LogConst.Parameter.TRACE_TOKEN, value = "#traceToken")
    @PostMapping("/distributionpartner/{partnerCode}/products/deactivate")
    Optional<Object> deactivateProduct(URI baseUrl,
                                       @PathVariable("partnerCode") String distributorId,
                                       @RequestBody OperationApiProductActivationRequest request,
                                       @RequestHeader(LogConst.Stream.HEADER_TRACE_TOKEN) String traceToken);

    default Optional<Object> deactivateProduct(@PathVariable("partnerCode") String distributorId,
                                               @RequestBody OperationApiProductActivationRequest request,
                                               @RequestHeader(LogConst.Stream.HEADER_TRACE_TOKEN) String traceToken) {
        return deactivateProduct(APILayerDynamicUrlUtil.getBaseUrlForDistributor(distributorId), distributorId, request, traceToken);
    }

    @ApiLayerRequest
    @StreamLog(proxy = false, inheritPref = true, wrapHeaderAsParameter = true)
    @StreamParameter(name = LogConst.Parameter.TRACE_TOKEN, value = "#traceToken")
    @PostMapping("/distributionpartner/{partnerCode}/credentials/query")
    Optional<Object> queryCredential(URI baseUrl,
                                     @PathVariable("partnerCode") String distributorId,
                                     @RequestBody OperationApiBasicRequest request,
                                     @RequestHeader(LogConst.Stream.HEADER_TRACE_TOKEN) String traceToken);

    default Optional<Object> queryCredential(@PathVariable("partnerCode") String distributorId,
                                             @RequestBody OperationApiBasicRequest request,
                                             @RequestHeader(LogConst.Stream.HEADER_TRACE_TOKEN) String traceToken) {
        return queryCredential(APILayerDynamicUrlUtil.getBaseUrlForDistributor(distributorId), distributorId, request, traceToken);
    }
}
