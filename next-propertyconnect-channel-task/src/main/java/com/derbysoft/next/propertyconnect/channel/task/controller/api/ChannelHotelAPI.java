package com.derbysoft.next.propertyconnect.channel.task.controller.api;

import com.derbysoft.extension.aggregationtool.controllerservice.ControllerAndService;
import com.derbysoft.next.commons.boot.logsupport.annotation.perf.PerfLog;
import com.derbysoft.next.commons.boot.logsupport.annotation.perf.PerfParameter;
import com.derbysoft.next.commons.boot.logsupport.annotation.stream.StreamLog;
import com.derbysoft.next.commons.boot.logsupport.annotation.stream.StreamLogRequest;
import com.derbysoft.next.propertyconenct.channel.common.exception.response.UnifyResult;
import com.derbysoft.next.propertyconenct.channel.common.utils.Constants;
import com.derbysoft.next.propertyconenct.channel.common.validation.BusinessIdFormat;
import com.derbysoft.next.propertyconnect.channel.task.config.UnifyResultWrapper;
import com.derbysoft.next.propertyconnect.channel.task.controller.vo.hotel.ChannelHotelVO;
import com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.RemoteChannelService;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * @Created by <AUTHOR> on 2023/5/23
 */


@ControllerAndService(wrapper = UnifyResultWrapper.class)
@RequestMapping("/api")
@Tag(name = "ChannelHotelAPI", description = "ChannelHotelAPI", extensions = @Extension(properties = @ExtensionProperty(name = "order", value = "3")))
@Validated
public interface ChannelHotelAPI {
    @GetMapping("/channel/{channelId}/channelhotels/{channelHotelId}")
    @Operation(summary = "GetChannelHotel", description = "Get latest channel hotel detail and status")
    @StreamLog(inheritPref = true, proxy = false)
    UnifyResult<ChannelHotelVO> getChannelHotel(@PathVariable @BusinessIdFormat String channelId,
                                                @PathVariable @BusinessIdFormat String channelHotelId);

    @PerfLog("SaveProperty")
    @PerfParameter(name = Constants.Perf.CHANNEL, value = "#channelId")
    @PerfParameter(name = Constants.Perf.SUPPLIER, value = "#channelHotelRQ?.supplierId")
    @PerfParameter(name = Constants.Perf.HOTEL_CHANNEL, value = "#channelHotelId")
    @PostMapping("/channel/{channelId}/channelhotels/{channelHotelId}")
    @Operation(summary = "SetupChannelHotel", description = "Save and setup channel hotel")
    @StreamLog(process = "SaveProperty", inheritPref = true, proxy = false, wrapHeaderAsParameter = false)
    @StreamLogRequest(body = "#channelHotelRQ")
    UnifyResult<ChannelHotelVO> saveChannelHotel(@PathVariable @BusinessIdFormat String channelId,
                                                 @PathVariable @Valid @BusinessIdFormat String channelHotelId,
                                                 @Valid @RequestBody @JsonView(ChannelHotelVO.Request.class)
                                                 ChannelHotelVO channelHotelRQ,
                                                 @RequestParam(required = false) String notifyUrl,
                                                 @RequestParam(required = false, defaultValue = "false") Boolean syncMode,
                                                 @RequestParam(required = false, defaultValue = "false") Boolean retry,
                                                 @RequestParam(required = false, defaultValue = "false") Boolean fullUpdate,
                                                 @RequestParam(required = false, defaultValue = "false") Boolean saveOnly);


    @Validated
    @PerfLog("SaveProperty")
    @PerfParameter(name = Constants.Perf.CHANNEL, value = "#channelId")
    @PerfParameter(name = Constants.Perf.SUPPLIER, value = "#channelHotelRQ?.supplierId")
    @PerfParameter(name = Constants.Perf.HOTEL_CHANNEL, value = "#channelHotelId")
    @PutMapping({
            "/channel/{channelId}/channelhotels/{channelHotelId}",
            "/channel/{channelId}/channelhotels"
    })
    @StreamLog(process = "SaveProperty", inheritPref = true, proxy = false, wrapHeaderAsParameter = false)
    @Operation(summary = "SyncChannelHotel(s)", description = "Setup channel hotel(s) using data from PCProfile and will not saving to PCChannel. Async mode only")
    UnifyResult<ChannelHotelVO> syncChannelHotel(@PathVariable @BusinessIdFormat String channelId,
                                                 @PathVariable(required = false) @BusinessIdFormat String channelHotelId,
                                                 @Schema(description = "Any of 'SavePropertyCombo,TriggerARIRefresh'", anyOf = RemoteChannelService.Operation.class, type = "string")
                                                 @RequestParam(required = false) String procedure,
                                                 @RequestParam(required = false) String notifyUrl,
                                                 @RequestParam(required = false, defaultValue = "false") Boolean ignorePropertyStatusCheck,
                                                 @RequestParam(required = false, defaultValue = "PROPERTYCONNECT") @BusinessIdFormat String supplierId,
                                                 @RequestBody(required = false) @JsonView(ChannelHotelVO.Request.class)
                                                 ChannelHotelVO channelHotelRQ);


    @Validated
    @Operation(summary = "DeleteChannelHotel", description = "Delete all channel hotel data from PCChannel service storage, currently will not delete channel hotel data from channel side")
    @DeleteMapping("/channel/{channelId}/channelhotels/{channelHotelId}")
    UnifyResult<Void> deleteChannelHotel(@PathVariable @BusinessIdFormat String channelId,
                                         @PathVariable @BusinessIdFormat String channelHotelId);

}
