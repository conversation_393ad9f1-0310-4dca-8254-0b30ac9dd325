package com.derbysoft.next.propertyconnect.channel.task.config;

import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.info.Info;
import io.swagger.v3.oas.models.Paths;
import org.springdoc.core.customizers.OpenApiCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;


@OpenAPIDefinition(
        info = @Info(
                title = "PC Channel"
        )
)
@Configuration
public class OpenAPIOrderSupport {
    @Bean
    public OpenApiCustomizer customOpenApiOrderCustomizer() {
        return openApi -> {
            openApi.setTags(Optional.ofNullable(openApi.getTags()).orElse(List.of()).stream()
                    .sorted(Comparator.comparingInt(value ->
                            Integer.parseInt((String) value.getExtensions().get("x-order"))))
                    .collect(Collectors.toList())
            );

            openApi.setPaths(
                    openApi.getPaths().entrySet()
                            .stream()
                            .sorted(Comparator.comparingInt(pathItem -> pathItem.getValue().readOperations().stream()
                                    .filter(operation -> Optional.ofNullable(operation.getExtensions())
                                            .map(extensions -> extensions.get("x-order"))
                                            .isPresent()
                                    )
                                    .findFirst()
                                    .map(operation -> Integer.parseInt((String) operation.getExtensions().get("x-order")))
                                    .orElse(-1)
                            ))
                            .collect(Collectors.toMap(
                                    Map.Entry::getKey,
                                    Map.Entry::getValue,
                                    (ov, nv) -> nv,
                                    Paths::new)
                            )
            );
        };
    }
}
