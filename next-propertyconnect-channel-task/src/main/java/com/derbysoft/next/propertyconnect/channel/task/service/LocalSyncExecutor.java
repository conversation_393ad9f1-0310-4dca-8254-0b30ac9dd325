package com.derbysoft.next.propertyconnect.channel.task.service;

import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelHotelDTO;
import com.derbysoft.next.propertyconnect.channel.task.service.impl.ChannelRemoteServiceFactory;
import com.derbysoft.next.propertyconnect.channel.task.service.storageservice.ChannelInfoStorageService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

/**
 * @Created by <AUTHOR> on 2023/6/12
 */

@Service
@RequiredArgsConstructor
public class LocalSyncExecutor implements ChannelHotelSetupExecutor {
    private final ChannelRemoteServiceFactory channelRemoteServiceFactory;
    private final ChannelInfoStorageService channelInfoStorageService;

    @Override
    public Mono<ChannelHotelDTO> execute(ChannelHotelDTO channelHotel, ChannelHotelSetupProcedure procedure) {
        if (channelHotel == null || procedure == null) {
            throw new IllegalStateException("Execute channel hotel setup procedure failed, wrong channel hotel or procedure status");
        }
        return Mono.create( sink -> {
            var operations = procedure.setupProcedure(channelHotel);
            operations.forEach((operation) -> channelRemoteServiceFactory.getHandler(channelHotel.getChannelId(), procedure.destination()).execution(operation, channelHotel));
            sink.success(channelHotel);
        });
    }
}
