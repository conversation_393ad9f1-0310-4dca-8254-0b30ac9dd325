package com.derbysoft.next.propertyconnect.channel.task.client.connectivityoperationapi;

import com.derbysoft.next.propertyconenct.channel.common.mapstruct.CommMapper;
import com.derbysoft.next.propertyconenct.channel.common.utils.GeneratedMapper;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.*;
import com.derbysoft.next.propertyconnect.channel.task.service.impl.fliggy.FliggyCustomExtensionFilter;
import org.mapstruct.*;

/**
 * @Created by <AUTHOR> on 2023/6/2
 */


@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
@AnnotateWith(GeneratedMapper.class)
public interface ConnectivityOperationRequestTranslator extends CommMapper, FliggyCustomExtensionFilter {

    @Mapping(target = "propertyOwner", source = "supplierId")
    @Mapping(target = "propertyPartner", source = "channelId")
    @Mapping(target = "propertyCode", source = "supplierHotelId")
    @Mapping(target = "partnerPropertyCode", expression = """
            java(Boolean.TRUE.equals(com.derbysoft.next.propertyconenct.channel.common.utils.JSONUtil.getBoolean(channelHotelDTO, "$.extensions.mapWithoutPrefix")) ? channelHotelDTO.getChannelHotelId() : channelHotelDTO.getSupplierId() + "_" + channelHotelDTO.getChannelHotelId())
            """)
    @Mapping(target = "extensions", ignore = true)
    OperationApiBasicRequest toPropertyBasicRequest(ChannelHotelDTO channelHotelDTO);

    @InheritConfiguration(name = "toPropertyBasicRequest")
    @Mapping(target = "credentials", source = "accountSettings", defaultExpression = "java(new java.util.HashMap<>())")
    OperationApiCredentialRequest toCredentialRequest(ChannelHotelDTO channelHotelDTO);

    @InheritConfiguration(name = "toPropertyBasicRequest")
    @Mapping(target = "extensions", source = "hotelInfo.extensions", defaultExpression = "java(new java.util.HashMap<>())")
    OperationApiBasicRequest toPropertyActivationRequest(ChannelHotelDTO channelHotelDTO);

    @InheritConfiguration(name = "toPropertyBasicRequest")
    @Mapping(target = "roomCode", source = "roomInfo.code")
    @Mapping(target = "partnerRoomCode", source = "roomInfo.code")
    @Mapping(target = "occupancy", source = "roomInfo.occupancy")
    @Mapping(target = "extensions", source = "roomInfo.extensions")
    OperationApiRoomTypeRequest toRoomActivationRequest(ChannelHotelDTO channelHotelDTO);

    @InheritConfiguration(name = "toPropertyBasicRequest")
    @Mapping(target = "ratePlanCode", source = "rateInfo.code")
    @Mapping(target = "partnerRatePlanCode", source = "rateInfo.code")
    @Mapping(target = "extensions", source = "rateInfo.extensions")
    OperationApiRatePlanRequest toRateActivationRequest(ChannelHotelDTO channelHotelDTO);

    @InheritConfiguration(name = "toPropertyBasicRequest")
    @Mapping(target = "roomCode", source = "productInfo.channelRoomId")
    @Mapping(target = "partnerRoomCode", source = "productInfo.channelRoomId", ignore = true)
    @Mapping(target = "ratePlanCode", source = "productInfo.channelRateId")
    @Mapping(target = "partnerRatePlanCode", source = "productInfo.channelRateId", ignore = true)
    @Mapping(target = "extensions", source = "productInfo.extensions")
    OperationApiProductActivationRequest toProductActivationRequest(ChannelHotelDTO channelHotelDTO);

    @InheritInverseConfiguration(name = "toPropertyBasicRequest")
    @Mapping(target = "roomsInfo", source = "rooms")
    @Mapping(target = "ratesInfo", source = "ratePlans")
    @Mapping(target = "productsInfo", source = "products")
    ChannelHotelDTO queryResponseToDto(OperationApiQueryResponse operationApiQueryResponse);

    @Mapping(target = "code", source = "partnerRoomTypeCode")
    ChannelRoomInfo roomToDto(OperationApiQueryResponse.Room operationApiQueryResponse);

    @Mapping(target = "code", source = "partnerRatePlanCode")
    ChannelRateInfo rateToDto(OperationApiQueryResponse.RatePlan operationApiQueryResponse);


    @Mapping(target = "channelRoomId", source = "partnerRoomCode")
    @Mapping(target = "channelRateId", source = "partnerRatePlanCode")
    @Mapping(target = "status", source = "status", qualifiedByName = "mapStatus")
    ChannelProductsDTO.Product map(OperationApiQueryResponse.Product value);

    default ItemStatus map(String status){
        return "Activate".equals(status) ? ItemStatus.Actived : ItemStatus.Deactived;
    }

    @Named("mapStatus")
    default String mapStatus(String status){
        return "Activate".equals(status) ? ItemStatus.Actived.name() : ItemStatus.Deactived.name();
    }

}
