package com.derbysoft.next.propertyconnect.channel.task.controller.api;

import com.derbysoft.extension.aggregationtool.controllerservice.ControllerAndService;
import com.derbysoft.next.propertyconenct.channel.common.exception.response.UnifyResult;
import com.derbysoft.next.propertyconnect.channel.task.config.UnifyResultWrapper;
import com.derbysoft.next.propertyconnect.channel.task.controller.vo.ExceptionListLogQueryResponseVO;
import com.derbysoft.next.propertyconnect.channel.task.controller.vo.ExceptionListResponseVO;
import com.derbysoft.next.propertyconnect.channel.task.task.exceptionlistremove.ChannelHotelProduct;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.Collection;

/**
 * @Created by <AUTHOR> on 2023/3/21
 */


@ControllerAndService(wrapper = UnifyResultWrapper.class)
@RequestMapping("/api")
@Tag(name = "SynchronizerProxyAPI", description = "SynchronizerProxyAPI",extensions = @Extension(properties = @ExtensionProperty(name = "order", value = "8")))
@Validated
public interface SynchronizerProxyAPI {
    @GetMapping({
            "/channels/exceptions/statistics",
            "/channels/{channelId}/exceptions/statistics"
    })
    @Operation(summary = "GetExceptionListRemovalStatistics")
    UnifyResult<Collection<ExceptionListLogQueryResponseVO>> getExceptionListRemovalStatistics(@PathVariable(required = false) String channelId,
                                                                                               @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date,
                                                                                               @RequestParam(required = false) Integer failureGreaterThan);

    @GetMapping({
            "/channels/{channelId}/exceptions",
            "/channels/{channelId}/hotels/{hotelId}/exceptions",
            "/channels/{channelId}/hotels/{hotelId}/rooms/{roomId}/exceptions",
            "/channels/{channelId}/hotels/{hotelId}/rooms/{roomId}/rates/{rateId}/exceptions"
    })
    @Hidden
    UnifyResult<Collection<ExceptionListResponseVO>> getExceptionList(@PathVariable String channelId,
                                                                      @PathVariable(required = false) String hotelId,
                                                                      @PathVariable(required = false) String roomId,
                                                                      @PathVariable(required = false) String rateId);

    @DeleteMapping("/channels/{channelId}/hotels/{hotelId}/rooms/{roomId}/rates/{rateId}/exceptions")
    @Hidden
    UnifyResult<Collection<ExceptionListResponseVO>> deleteSingleExceptionList(@PathVariable String channelId,
                                                                               @PathVariable String hotelId,
                                                                               @PathVariable String roomId,
                                                                               @PathVariable String rateId);

    @DeleteMapping("/channels/{channelId}/exceptions")
    @Hidden
    UnifyResult<Void> batchDeleteExceptionList(@Valid @RequestBody Collection<ChannelHotelProduct> channelHotelProducts);


}
