package com.derbysoft.next.propertyconnect.channel.task.client.aimapping;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * @Created by <AUTHOR> on 2023/4/21
 */

//@RemoteService(exceptionOnFailure = true)
@FeignClient(name = "openAIClient", url = "${app.openai.url}")
public interface OpenAIClient {

    @PostMapping("/v1/completions")
    OpenAIResponse completions(@RequestHeader("Authorization") String token,
                               @RequestBody OpenAIRequest request);
}