package com.derbysoft.next.propertyconnect.channel.task.service.impl;

import com.derbysoft.next.propertyconnect.channel.task.service.ChannelHotelSetupProcedure;
import com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.RemoteChannelService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

/**
 * @Created by <AUTHOR> on 2023/6/8
 */

@Service
@RequiredArgsConstructor
public class ChannelRemoteServiceFactory {
    private final ObjectProvider<RemoteChannelService> remoteChannelServices;
    private final ObjectProvider<ChannelHotelSetupProcedure> channelHotelSetupProcedures;

    @Cacheable(cacheNames = "channelRemoteService", key = "#channel + #destination")
    public RemoteChannelService getHandler(String channel, RemoteChannelService.Destination destination) {
        return remoteChannelServices.stream()
                .filter(remoteChannelService -> remoteChannelService.channel().equals(channel))
                .filter(remoteChannelService -> remoteChannelService.destination().equals(destination))
                .findFirst()
                .orElseGet(() -> remoteChannelServices.stream()
                        .filter(remoteChannelService -> remoteChannelService.channel().equals(ChannelHotelSetupProcedure.ANY_CHANNEL))
                        .filter(remoteChannelService -> remoteChannelService.destination().equals(destination))
                        .findFirst()
                        .orElseThrow(() -> new IllegalArgumentException(String.format("%s %s does not support this setup action", channel, destination)))
                );
    }

    public ChannelHotelSetupProcedure findSetupProcedure(String channel) {
        return channelHotelSetupProcedures.stream()
                .filter(channelHotelSetupProcedure -> channelHotelSetupProcedure.channel().equals(channel))
                .findFirst()
                .orElseGet(() -> channelHotelSetupProcedures.stream()
                        .filter(channelHotelSetupProcedure -> channelHotelSetupProcedure.channel().equals(ChannelHotelSetupProcedure.ANY_CHANNEL))
                        .findFirst()
                        .orElseThrow(() -> new IllegalArgumentException(String.format("%s does not support setup procedure", channel)))
                );
    }

}
