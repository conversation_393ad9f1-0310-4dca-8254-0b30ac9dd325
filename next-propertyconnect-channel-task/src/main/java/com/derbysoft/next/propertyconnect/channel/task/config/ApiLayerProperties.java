package com.derbysoft.next.propertyconnect.channel.task.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;

import java.util.HashMap;
import java.util.Map;

/**
 * @Created by <AUTHOR> on 2022/5/12
 */

@Data
@RefreshScope
@ConfigurationProperties(prefix = "app.api-layer")
public class ApiLayerProperties {
    String url;
    String token;
    Map<String, CustomUrl> custom = new HashMap<>();

    @Data
    public static class CustomUrl {
        String url;
        String token;
    }
}
