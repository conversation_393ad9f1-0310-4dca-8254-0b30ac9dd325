package com.derbysoft.next.propertyconnect.channel.task.controller.vo;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * @Created by <AUTHOR> on 2023/5/23
 */

@Data
public class ChannelVO {

    public enum State {
        ENABLED, DISABLED, DRAFT
    }

    @NotBlank
    public String channelId;
    public String channelName;
    public State state;

    @JsonIgnore
    public Map<String, String> channelDetails = new LinkedHashMap<>();

    @JsonAnySetter
    public void setMagicMap(String key, String value) {
        channelDetails.put(key, value);
    }

    @JsonAnyGetter
    public Map<String, String> getMagicMap() {
        return channelDetails;
    }

}
