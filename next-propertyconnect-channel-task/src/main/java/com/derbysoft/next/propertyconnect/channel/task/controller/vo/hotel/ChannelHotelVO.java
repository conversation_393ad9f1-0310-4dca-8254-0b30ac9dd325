package com.derbysoft.next.propertyconnect.channel.task.controller.vo.hotel;

import com.derbysoft.next.propertyconenct.channel.common.validation.BusinessIdFormat;
import com.derbysoft.next.propertyconnect.channel.task.controller.vo.ChannelAccountVO;
import com.derbysoft.next.propertyconnect.channel.task.controller.vo.ItemStatus;
import com.derbysoft.next.propertyconnect.channel.task.controller.vo.SyncStatus;
import com.derbysoft.next.propertyconnect.channel.task.controller.vo.rate.ChannelRateInfo;
import com.derbysoft.next.propertyconnect.channel.task.controller.vo.room.ChannelRoomInfo;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class ChannelHotelVO {

	public interface Request {}

	public interface Response {}

	public interface SetupResponse {}


	@JsonView({SetupResponse.class})
	@BusinessIdFormat
	@Schema(example = "FLIGGYHOTEL")
	private String channelId;
	@JsonView({Request.class, SetupResponse.class})
	@Schema(example = "PROPERTYCONNECT")
	@BusinessIdFormat
	@NotBlank
	private String supplierId;
	@JsonView({Request.class, SetupResponse.class})
	@Schema(example = "CNFLAPDSKD")
	@BusinessIdFormat
	private String channelHotelId;

//	@JsonView({Request.class, Response.class})
//	@Schema(example = "CNFLAPDSKD")
//	private String supplierHotelId;
	@JsonView({SetupResponse.class})
	@Schema(example = "f5d2ce11e1954f76852b814d2a83b02a")
	private String operationToken;
	@JsonView({SetupResponse.class})
	@Schema(example = "SUBMITTED", description = "Only on AsyncMode")
	private SyncStatus operationStatus;
	@JsonView({Request.class})
	private ChannelAccountVO.AccountSetting hotelAccount;
	@JsonView({Request.class})
	@Valid
	private ChannelHotelInfo hotelInfo;
	@JsonView({Request.class})
	@Valid
	private List<ChannelRoomInfo> roomsInfo;
	@JsonView({Request.class})
	@Valid
	private List<ChannelRateInfo> ratesInfo;
	@JsonView({Request.class})
	@Valid
	private List<ChannelProductInfo> productsInfo;
	@JsonView({Request.class})
	@Valid
	private Map<String, Object> extensions;

	@Data
	@JsonView({Request.class})
	public static class ChannelProductInfo {
		@Schema(example = "BK1")
		@BusinessIdFormat
		private String channelRoomId;
		@Schema(example = "OCBNB-FLGY")
		private String channelRateId;
		@Schema(example = "ACTIVATED")
		private ItemStatus status;
		private SyncStatus syncStatus;
		private String errorCode;
		private String errorMessage;
		private Map<String, Object> extensions;
	}

}
