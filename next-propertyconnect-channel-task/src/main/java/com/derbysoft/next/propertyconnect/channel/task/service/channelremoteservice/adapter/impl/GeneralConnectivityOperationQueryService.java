package com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.adapter.impl;

import com.alibaba.fastjson2.JSON;
import com.derbysoft.extension.aggregationtool.remoteservice.HttpIOException;
import com.derbysoft.next.commons.core.logsupport.handler.PerfLogHandler;
import com.derbysoft.next.propertyconenct.channel.common.utils.JSONUtil;
import com.derbysoft.next.propertyconnect.channel.task.client.connectivityoperationapi.ConnectivityOperationRequestTranslator;
import com.derbysoft.next.propertyconnect.channel.task.client.connectivityoperationapi.OperationApiQueryResponse;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelHotelDTO;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelProductsDTO;
import com.derbysoft.next.propertyconnect.channel.task.service.RemoteService;
import com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.RemoteChannelProductsService;
import com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.adapter.ConnectivityOperationQueryService;
import lombok.RequiredArgsConstructor;
import lombok.experimental.ExtensionMethod;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * @Created by <AUTHOR> on 2023/7/21
 */

@Service
@ExtensionMethod(JSONUtil.class)
@RequiredArgsConstructor
public class GeneralConnectivityOperationQueryService implements ConnectivityOperationQueryService, RemoteChannelProductsService {
    protected final ConnectivityOperationRequestTranslator translator;
    protected final RemoteService remoteService;
    protected final RemoteChannelProductsService.ProductDtoToHotelDto productTranslator = RemoteChannelProductsService.ProductDtoToHotelDto.INSTANCE;

    @Override
    public ChannelProductsDTO getChannelProducts(String supplierId, String channelId, String channelHotelId) {
        var channelProductsDTO = productTranslator.reverseMap(this.propertyQuery(supplierId, channelId, channelHotelId, channelHotelId));
        channelProductsDTO.setChannelId(channelId);
        channelProductsDTO.setSupplierId(supplierId);
        channelProductsDTO.setChannelHotelId(channelHotelId);
        return channelProductsDTO;
    }

    @Override
    public ChannelHotelDTO propertyQuery(String supplierId, String channelId, String supplierHotelId, String channelHotelId) {
        var traceToken = Optional.ofNullable(PerfLogHandler.currentHandler()).map(PerfLogHandler::getToken).orElse(null);
        return remoteService.propertiesQuery(channelId, translator.toPropertyBasicRequest(
                        new ChannelHotelDTO()
                                .setSupplierId(supplierId)
                                .setChannelId(channelId)
                                .setSupplierHotelId(supplierHotelId)
                                .setChannelHotelId(channelHotelId))
                        , traceToken)
                .map(this::checkResponse)
                .map(translator::queryResponseToDto)
                .orElse(null);
    }

    @Override
    public ChannelHotelDTO propertyRoomTypeQuery(String supplierId, String channelId, String supplierHotelId, String channelHotelId, List<String> channelRoomIds) {
        throw new UnsupportedOperationException();
    }

    @Override
    public ChannelHotelDTO propertyRatePlanQuery(String supplierId, String channelId, String supplierHotelId, String channelHotelId, List<String> channelRateIds) {
        throw new UnsupportedOperationException();
    }

    private OperationApiQueryResponse checkResponse(OperationApiQueryResponse response) {
        if (!Boolean.TRUE.toString().equals(response.getString("$.responseHeader.Success"))) {
            throw new HttpIOException(200, JSON.toJSONString(response));
        }
        return response;
    }

    @Override
    public String channel() {
        return ANY_CHANNEL_CSP;
    }
}
