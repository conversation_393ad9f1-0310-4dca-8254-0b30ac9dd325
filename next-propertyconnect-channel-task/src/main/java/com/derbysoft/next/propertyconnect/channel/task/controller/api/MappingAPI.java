package com.derbysoft.next.propertyconnect.channel.task.controller.api;

import com.derbysoft.extension.aggregationtool.controllerservice.ControllerAndService;
import com.derbysoft.next.commons.boot.logsupport.annotation.perf.PerfLog;
import com.derbysoft.next.commons.boot.logsupport.annotation.perf.PerfParameter;
import com.derbysoft.next.propertyconenct.channel.common.exception.response.UnifyResult;
import com.derbysoft.next.propertyconenct.channel.common.utils.Constants;
import com.derbysoft.next.propertyconnect.channel.task.controller.vo.mapping.MappingVO;
import com.derbysoft.next.propertyconnect.channel.task.controller.vo.mapping.PredictionMappingVO;
import com.derbysoft.next.propertyconnect.channel.task.controller.vo.mapping.PredictionModel;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Created by <AUTHOR> on 2023/4/12
 */


@ControllerAndService
@RequestMapping("/api")
@Tag(name = "ChannelMappingAPI", description = "ChannelMappingAPI", extensions = @Extension(properties = @ExtensionProperty(name = "order", value = "7")))
public interface MappingAPI {

    @GetMapping(value = {
            "channelmapping/{channelId}/channelhotels/{channelHotelId}/prediction"
    })
    @PerfLog(operation = Constants.Perf.EXPORT_SUB_MAPPING)
    @PerfParameter(name = Constants.Perf.CHANNEL, value = "#channelId")
    @PerfParameter(name = Constants.Perf.HOTEL_CHANNEL, value = "#channelHotelId")
    @Operation(summary = "GetAIMapping", description = "Get AIMapping result, Data source from PropertyConnector and Channel database")
    UnifyResult<PredictionMappingVO> getPredictMapping(@PathVariable("channelId") String channelId,
                                                       @PathVariable("channelHotelId") String channelHotelId,
                                                       @RequestParam(value = "hotelId", required = false) String hotelId,
                                                       @RequestParam(value = "predictionModel", required = false) PredictionModel predictionModel,
                                                       @Schema(description = "If the value is 'False', ChannelService will obtain the latest GetChannelProduct result from database as channel products, otherwise will request the channel directly", requiredMode = Schema.RequiredMode.NOT_REQUIRED, implementation = Boolean.class, defaultValue = "true")
                                                       @RequestParam(value = "refresh", required = false, defaultValue = "true") Boolean refresh,
                                                       @Schema(description = "Threshold should between 0.00-1.00 with two decimal places precision<br/> <span style=\"color: orange\">If not set will use ChannelService SGC threshold config by default</span>", requiredMode = Schema.RequiredMode.NOT_REQUIRED, implementation = Double.class)
                                                       @RequestParam(value = "threshold", required = false) Double threshold,
                                                       @RequestParam(value = "candidateCount", required = false) Integer candidateCount,
                                                       @RequestHeader(name = "Enable-Erase-Header", defaultValue = "false") Boolean enableEraseHeader,
                                                       @Schema(description = "If header Enable-Erase-Header is true, this header need to be set.", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
                                                       @RequestHeader(name = "Echo-Token", required = false) String echoToken);

    @Hidden
    @PostMapping(value = {
            "channelmapping/{channelId}/channelhotels/{channelHotelId}/prediction"
    })
    @Operation(summary = "GetAIMapping")
    UnifyResult<PredictionMappingVO> doPredictMapping(@PathVariable("channelId") String channelId,
                                                      @PathVariable("channelHotelId") String channelHotelId,
                                                      @RequestParam(value = "predictionModel", required = false) PredictionModel predictionModel,
                                                      @RequestParam(value = "candidateCount", required = false) Integer candidateCount,
                                                      @RequestBody List<PredictionMappingVO.PredictionProductMappingVO> channelProducts);


    @GetMapping(value = {
            "channelmapping/{channelId}/channelhotels/{channelHotelId}"
    })
    @Operation(summary = "GetHotelMapping")
    UnifyResult<MappingVO> getMapping(@PathVariable("channelId") String channelId,
                                      @PathVariable("channelHotelId") String channelHotelId,
                                      @RequestParam("snapshot") Boolean snapshot);

    @PostMapping(value = {
            "channelmapping/{channelId}/channelhotels/{channelHotelId}"
    })
    @Operation(summary = "SaveHotelMapping")
    UnifyResult<MappingVO> updateMapping(@PathVariable("channelId") String channelId,
                                         @PathVariable("channelHotelId") String channelHotelId,
                                         @RequestBody @JsonView(MappingVO.RequestMappingView.class) MappingVO mappingVO);

    @DeleteMapping(value = {
            "channelmapping/{channelId}/channelhotels/{channelHotelId}"
    })
    @Operation(summary = "DeleteHotelMapping")
    UnifyResult<Void> deleteMapping(@PathVariable("channelId") String channelId,
                                    @PathVariable("channelHotelId") String channelHotelId);
}
