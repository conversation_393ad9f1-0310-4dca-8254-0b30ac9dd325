package com.derbysoft.next.propertyconnect.channel.task.domain.dto;

import java.util.List;

/**
 * @Created by <AUTHOR> on 12/6/2023
 */

public interface CandidateResult {
    String getRoomId();
    String getRoomName();
    String getRateId();
    String getRateName();
    <T extends CandidateResult> List<T> getCandidateProducts();

    default <T extends CandidateResult>  T getFirstCandidateProduct() {
        var candidateProducts = getCandidateProducts();
        if (candidateProducts == null || candidateProducts.isEmpty()) {
            return null;
        }
        return (T) getCandidateProducts().get(0);
    }

    default CandidateResult getCandidateDiff() {
        return getFirstCandidateProduct();
    }

    enum CandidateResultType {
        MATCH("Match"),NOT_MATCH("Not Match"),NOT_EXIST("Not Exist");
        private final String value;
        CandidateResultType(String value) {
            this.value = value;
        }
        public String getValue() {
            return value;
        }
    }
    default CandidateResultType candidateMatchResult(CandidateResult target) {
        if (target == null) {
            return CandidateResultType.NOT_EXIST;
        }
        if (null == getRoomId() || null == getRateId() || null == target.getRoomId() || null == target.getRateId()) {
            return CandidateResultType.NOT_EXIST;
        }
        return getRoomId().equals(target.getRoomId()) && getRateId().equals(target.getRateId()) ? CandidateResultType.MATCH : CandidateResultType.NOT_MATCH;
    }
}
