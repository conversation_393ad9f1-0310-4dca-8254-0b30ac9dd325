package com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice;

import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelHotelDTO;
import com.derbysoft.next.propertyconnect.channel.task.service.ChannelService;
import com.derbysoft.next.propertyconnect.channel.task.service.customizationservice.ChannelCustomizationService;

/**
 * @Created by <AUTHOR> on 2023/5/26
 */

public interface RemoteChannelService extends ChannelService, ChannelCustomizationService {
    enum Destination {
        Adapter, Channel
    }
    enum Operation { Unknown, GetCredential, SaveCredential, GetProperty, SaveProperty, SavePropertyCombo, DeleteProperty, GetRoomType, SaveRoomTypes, DeleteRoomType, GetRatePlan, SaveRatePlans, DeleteRatePlan, GetProduct, SaveProducts, DeleteProduct, TriggerARIRefresh, AfterAll }

    Destination destination();

    ChannelHotelDTO execution(Operation operation, ChannelHotelDTO dto);

}
