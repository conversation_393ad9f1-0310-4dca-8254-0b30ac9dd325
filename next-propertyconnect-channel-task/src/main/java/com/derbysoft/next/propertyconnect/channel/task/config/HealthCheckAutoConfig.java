package com.derbysoft.next.propertyconnect.channel.task.config;

import io.swagger.v3.oas.annotations.Hidden;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Created by <AUTHOR> on 2022/11/7
 */

@RestController
@Hidden
public class HealthCheckAutoConfig {
    @RequestMapping(value = "/actuator/serviceStats", produces = "application/json", method = {RequestMethod.GET, RequestMethod.POST})
    public String check() {
        return "{\"status\": \"UP\"}";
    }

    @RequestMapping(value = "/status.ci", produces = "application/json", method = {RequestMethod.GET, RequestMethod.POST})
    public String status() {
        return "{\"status\": \"UP\"}";
    }
}
