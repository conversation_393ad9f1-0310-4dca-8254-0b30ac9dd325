package com.derbysoft.next.propertyconnect.channel.task.domain.dto;

import com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.RemoteChannelService;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
public class ChannelHotelInfo implements InventoryItemStatus {
	private String id;
	private String name;
	private String description;
	private String currency;
	private String timezone;
	private Address address;
	private Position position;
	private String ariType;
	private String rateType;
	private List<ContactsItem> contacts;
	private Map<String, Object> i18n;
	private ItemStatus status;
	private SyncStatus syncStatus;
	private String errorCode;
	private String errorMessage;
	private String lastOperationToken;
	private RemoteChannelService.Operation operation;
	private Map<String, Object> settings;
	private Map<String, Object> extensions;

	public ChannelHotelInfo(String id) {
		this.id = id;
	}

	@Override
	public String codePattern() {
		return "HotelCode";
	}

	@Override
	public String getCode() {
		return this.id;
	}

	@Data
	public static class Address{
		private String countryCode;
		private String stateName;
		private String cityCode;
		private String postalCode;
		private String line1;
		private String line2;
	}

	@Data
	public static class Position{
		private String logitude;
		private String latitude;
	}

	@Data
	public static class ContactsItem{
		private String surName;
		private String givenName;
		private List<String> phones;
		private List<String> emails;
		private Map<String, Object> extensions;
	}
}
