package com.derbysoft.next.propertyconnect.channel.task.domain.entity;

import lombok.*;
import lombok.experimental.FieldNameConstants;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDate;
import java.util.Comparator;
import java.util.Set;
import java.util.TreeSet;

/**
 * @Created by <AUTHOR> on 2023/3/23
 */


/**
 * The following IDs are channel-level IDs
 */
@FieldNameConstants
@EqualsAndHashCode(callSuper = true)
@Data
@Document(collection = "synchronizer_exception_list_log")
public class SynchronizerExceptionListLogPO extends BaseEntity {

    @Id
    private String id;
    private String channelId;
    private String hotelId;
    private String roomId;
    private String rateId;
    private LocalDate date;
    private Integer totalErrorCount;
    private Set<ErrorSummary> errorDetails = new TreeSet<>(Comparator.comparing(ErrorSummary::getCode));


    @Data
    @Builder
    public static class ErrorSummary {
        private Integer count;
        private String code;
        private String message;
    }
}
