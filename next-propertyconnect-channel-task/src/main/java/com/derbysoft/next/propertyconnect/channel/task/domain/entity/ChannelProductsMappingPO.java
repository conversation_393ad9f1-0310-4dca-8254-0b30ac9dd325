package com.derbysoft.next.propertyconnect.channel.task.domain.entity;

import com.derbysoft.next.propertyconnect.channel.task.domain.dto.SyncStatus;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * @Created by <AUTHOR> on 2023/4/17
 */

@EqualsAndHashCode(callSuper = true)
@Builder
@Data
@Document(collection = "channel_products_mapping")
@FieldNameConstants
public class ChannelProductsMappingPO extends BaseEntity {

    @Id
    private String id;
    private String supplierId;
    private String channelId;
    private String hotelId;
    private String channelHotelId;
    private SyncStatus mappingStatus;
    private LocalDateTime operationDate;
    private List<Product> channelProductsMapping;



    @Data
    public static class Product {
        private String roomId;
        private String rateId;

        private String channelRoomId;
        private String channelRateId;

        private Boolean mapped;
        private String status;
        private SyncStatus syncStatus;
        private String errorCode;
        private String errorMessage;
        private String lastOperationToken;
        private Map<String, Object> extensions;
    }}
