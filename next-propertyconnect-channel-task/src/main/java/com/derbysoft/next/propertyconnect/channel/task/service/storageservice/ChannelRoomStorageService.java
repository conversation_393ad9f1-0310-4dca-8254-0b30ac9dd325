package com.derbysoft.next.propertyconnect.channel.task.service.storageservice;

import com.derbysoft.next.propertyconenct.channel.common.utils.GeneratedMapper;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelHotelDTO;
import com.derbysoft.next.propertyconnect.channel.task.domain.entity.ChannelHotelRoomPO;
import com.derbysoft.next.propertyconnect.channel.task.util.CloneUtil;
import com.derbysoft.next.propertyconnect.channel.task.util.CollectionUtil;
import com.google.common.collect.Lists;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * @Created by <AUTHOR> on 2023/6/7
 */

@Service
public class ChannelRoomStorageService extends CommonStorageService<ChannelHotelRoomPO>{
    private final CloneUtil cloneUtil = CloneUtil.INSTANCE;
    public ChannelRoomStorageService(MongoTemplate mongoTemplate) {
        super(mongoTemplate, ChannelHotelRoomPO.class, Mappers.getMapper(DTOtoRoomPOTranslator.class));
    }

    @Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    @AnnotateWith(GeneratedMapper.class)
    public interface DTOtoRoomPOTranslator extends DTOtoPOTranslator<ChannelHotelRoomPO> {
        @Mapping(source = "roomInfo.code", target = "code")
        @Mapping(source = "roomInfo.status", target = "status")
        @Mapping(source = "roomInfo.syncStatus", target = "syncStatus")
        @Mapping(source = "roomInfo.lastOperationToken", target = "lastOperationToken")
        @Override
        void fillIn(ChannelHotelDTO from, @MappingTarget ChannelHotelRoomPO to);
    }

    @Override
    public ChannelHotelDTO saveIncrementalChannelProduct(ChannelHotelDTO channelHotel) {
//        channelHotel.getRoomsInfo().forEach(roomInfo -> {
//            var clone = cloneUtil.clone(channelHotel);
//            clone.setRoomInfo(roomInfo);
//            roomInfo = super.saveIncrementalChannelProduct(clone).getRoomInfo();
//        });

        if (null == channelHotel.getRoomsInfo()){
            return channelHotel;
        }

        CollectionUtil.forEachWithIndex(channelHotel.getRoomsInfo(), (index, roomInfo) -> {
            var clone = cloneUtil.clone(channelHotel);
            clone.setRoomInfo(roomInfo);
            roomInfo = super.saveIncrementalChannelProduct(clone).getRoomInfo();
            channelHotel.getRoomsInfo().set(index, roomInfo);
        });
        return channelHotel;
    }

    @Override
    public ChannelHotelDTO getChannelHotel(String channelId, String channelHotelId) {
        return this.queryChannelHotels(channelId, channelHotelId)
                .stream()
                .map(translator::reverseMap)
                .map(dto -> {
                    if (null == dto.getRoomsInfo() || null != dto.getRoomInfo()){
                        dto.setRoomsInfo(Lists.newArrayList(dto.getRoomInfo()));
                        dto.setRoomInfo(null);
                    }
                    return dto;
                })
                .reduce((ov, nv) -> {
                    Optional.ofNullable(ov.getRoomsInfo())
                            .ifPresent(
                                    rooms -> rooms.addAll(nv.getRoomsInfo())
                            );
                    return ov;
                })
                .orElse(null);
    }
}
