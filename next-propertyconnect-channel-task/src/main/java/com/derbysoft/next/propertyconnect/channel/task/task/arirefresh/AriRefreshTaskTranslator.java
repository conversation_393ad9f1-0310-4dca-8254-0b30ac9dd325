package com.derbysoft.next.propertyconnect.channel.task.task.arirefresh;

import com.derbysoft.next.propertyconenct.channel.common.utils.GeneratedMapper;
import com.derbysoft.next.propertyconnect.channel.task.config.schedulecenter.ChannelServiceTask;
import org.mapstruct.AnnotateWith;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

/**
 * @Created by <AUTHOR> on 2023/3/1
 */

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
@AnnotateWith(GeneratedMapper.class)
public interface AriRefreshTaskTranslator {

    @Mapping(source = "fetchTask.channel", target = "distributor")
    @Mapping(source = "fetchTask.supplier", target = "supplier", defaultValue = "PROPERTYCONNECT")
    @Mapping(source = "fetchTask.startDate", target = "dateRange.start")
    @Mapping(source = "fetchTask.endDate", target = "dateRange.end")
    @Mapping(source = "hotels", target = "hotelCodes")
    TriggerARIRefreshRequest toTriggerARIRefreshRequest(ChannelServiceTask fetchTask);


//    @Named("hotelStringToListMapper")
//    default List<String> mapHotelCodes(String hotels) {
//        return List.of(hotels.split(","));
//    }
}
