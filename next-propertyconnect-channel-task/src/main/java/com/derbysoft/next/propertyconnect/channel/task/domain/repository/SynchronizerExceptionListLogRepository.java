package com.derbysoft.next.propertyconnect.channel.task.domain.repository;

import com.derbysoft.next.propertyconnect.channel.task.domain.entity.SynchronizerExceptionListLogPO;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.time.LocalDate;
import java.util.Optional;

/**
 * @Created by <AUTHOR> on 2023/3/23
 */

public interface SynchronizerExceptionListLogRepository extends MongoRepository<SynchronizerExceptionListLogPO, String> {
    Optional<SynchronizerExceptionListLogPO> findByChannelIdAndHotelIdAndRoomIdAndRateIdAndDate(String channelId,
                                                                                                String hotelId,
                                                                                                String roomId,
                                                                                                String rateId,
                                                                                                LocalDate date);

}
