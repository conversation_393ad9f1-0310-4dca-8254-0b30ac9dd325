package com.derbysoft.next.propertyconnect.channel.task.util;

import com.derbysoft.next.propertyconnect.channel.task.service.RemoteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.atomic.AtomicReference;

/**
 * @Created by <AUTHOR> on 1/8/2025
 */

@Component
public class RemoteServiceUtil {
    private static final AtomicReference<RemoteService> REMOTE_SERVICE = new AtomicReference<>();

    @Autowired
    public void setRemoteService(RemoteService remoteService) {
        RemoteServiceUtil.REMOTE_SERVICE.set(remoteService);
    }

    public static RemoteService getInstance() {
        return RemoteServiceUtil.REMOTE_SERVICE.get();
    }

}
