package com.derbysoft.next.propertyconnect.channel.task.client;

import com.derbysoft.next.commons.boot.logsupport.annotation.stream.StreamLog;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.net.URI;
import java.util.List;

/**
 * @Created by <AUTHOR> on 11/16/2023
 */
@FeignClient(name = "webhookAPI", url = "http://")
public interface WebhookAPI {

    @StreamLog(proxy = false, inheritPref = true, wrapHeaderAsParameter = true)
    @DeleteMapping
    String webhookDeleteCall(URI url);

    default String webhookDeleteCall(String url) {
        return webhookDeleteCall(URI.create(url));
    }

    @StreamLog(proxy = false, inheritPref = true, wrapHeaderAsParameter = true)
    @PostMapping(produces = MediaType.APPLICATION_JSON_VALUE)
    Object webhookPostCall(URI url, @RequestBody List<?> body);

    default Object webhookPostCall(String url, List<?> body) {
        return webhookPostCall(URI.create(url), body);
    }
}