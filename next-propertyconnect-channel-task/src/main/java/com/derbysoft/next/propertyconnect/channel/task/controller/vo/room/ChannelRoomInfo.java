package com.derbysoft.next.propertyconnect.channel.task.controller.vo.room;

import com.derbysoft.next.propertyconenct.channel.common.validation.BusinessIdFormat;
import com.derbysoft.next.propertyconnect.channel.task.controller.vo.ItemStatus;
import com.derbysoft.next.propertyconnect.channel.task.controller.vo.SyncStatus;
import com.derbysoft.next.propertyconnect.channel.task.controller.vo.hotel.ChannelHotelVO;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
@JsonView({ChannelHotelVO.Request.class})
public class ChannelRoomInfo {
	@Schema(example = "BK1")
	@BusinessIdFormat
	private String id;
	@Schema(example = "Executive Sea View Queen Room")
	private String name;
	private String description;
	private ItemStatus status;
	private SyncStatus syncStatus;
	private String errorCode;
	private String errorMessage;
	private Occupancy occupancy;
	private Boolean smoking;
	private List<BedsItem> beds;
	private List<ExtraBeddingItem> extraBedding;
	private Map<String, Object> i18n;
	private Map<String, Object> extensions;


	@Data
	@JsonView({ChannelHotelVO.Request.class})
	public static class Occupancy{
		@Schema(example = "2")
		private Integer maxAdult;
		@Schema(example = "3")
		private Integer maxOccupancy;
		@Schema(example = "1")
		private Integer maxChild;
	}

	@Data
	@JsonView({ChannelHotelVO.Request.class})
	public static class BedsItem{
		@Schema(example = "Single")
		private String type;
		@Schema(example = "King")
		private String size;
		private Map<String, Object> extensions;
	}

	@Data
	@JsonView({ChannelHotelVO.Request.class})
	public static class ExtraBeddingItem{
		private String type;
		private Integer quantity;
		private Integer chargeAmount;
		private Map<String, Object> extensions;
	}

}
