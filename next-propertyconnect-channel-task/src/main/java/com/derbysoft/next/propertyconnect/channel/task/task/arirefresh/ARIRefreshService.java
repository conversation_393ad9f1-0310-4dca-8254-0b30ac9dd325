package com.derbysoft.next.propertyconnect.channel.task.task.arirefresh;

import com.derbysoft.next.commons.core.logsupport.handler.PerfLogHandler;
import com.derbysoft.next.propertyconnect.channel.task.config.schedulecenter.ChannelServiceTask;
import com.derbysoft.next.propertyconnect.channel.task.task.hotelsetup.HotelSetupConfigurationProperties;
import com.derbysoft.schedulecenter.rpc.client.HttpScheduleCenterService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * @Created by <AUTHOR> on 2023/8/4
 */

@Service
@RequiredArgsConstructor(onConstructor_ = @Lazy)
public class ARIRefreshService {

    @Lazy
    private final AriRefreshTask.AriRefreshTaskScheduler scheduler;
    @Lazy
    private final AriRefreshTask.AriRefreshTaskExecutor executor;
    @Lazy
    private final HttpScheduleCenterService scheduleCenterService;
    @Lazy
    private final AriRefreshConfigurationProperties refreshConfig;
    @Lazy
    private final HotelSetupConfigurationProperties hotelSetupConfig;

    public void submitARIRefresh(String supplier, String channel, List<String> hotelIds) {
        this.submitARIRefresh(supplier, channel, hotelIds, null);
    }

    public void submitARIRefresh(String supplier, String channel, List<String> hotelIds, String traceToken) {
        if (!hotelSetupConfig.allowARIRefresh(channel)) {
            Optional.ofNullable(PerfLogHandler.currentHandler()).ifPresent(handler -> handler.warn( "ARI not refreshed, current channel is not allowed to refresh."));
            return;
        }
        scheduler.afterExecute(scheduleCenterService, ChannelServiceTask.builder()
                .supplier(supplier)
                .hotels(hotelIds)
                .channel(channel)
                .echoToken(StringUtils.isNotBlank(traceToken) ? traceToken : Optional.ofNullable(PerfLogHandler.currentHandler()).map(PerfLogHandler::getToken).orElse(null))
                .delayExecuteTime(System.currentTimeMillis() + refreshConfig.getDelayExecutionTime(channel))
                .build()
                .toTask()
        );
    }

    public void triggerARIRefresh(String supplier, String channel, List<String> hotelIds, String startDate, String endDate) {
        executor.doExecute(ChannelServiceTask.builder()
                .supplier(supplier)
                .hotels(hotelIds)
                .channel(channel)
                .startDate(startDate)
                .endDate(endDate)
                .build()
                .toTask()
        );
    }
}
