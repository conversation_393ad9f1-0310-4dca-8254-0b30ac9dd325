package com.derbysoft.next.propertyconnect.channel.task.client.interceptor.retry;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.METHOD})
public @interface Retry {
    int maxAttempts() default 3;
    long delayMillis() default 1000;
    Class<? extends Throwable>[] retryOn() default {};
}
