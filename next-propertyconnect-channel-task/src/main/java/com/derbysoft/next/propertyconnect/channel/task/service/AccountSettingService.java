package com.derbysoft.next.propertyconnect.channel.task.service;

import com.derbysoft.next.propertyconnect.channel.task.service.storageservice.ChannelStorageService;

import java.util.Map;

/**
 * @Created by <AUTHOR> on 2023/4/13
 */


public interface AccountSettingService extends ChannelStorageService {

    Map<String, Object> getAccountSettings(String channelId, String channelHotelId);

    Map<String, Object> saveOrUpdateAccountSettings(String channelId, String channelHotelId, Map<String, Object> accountSettings);

    void deleteAccountSettings(String channelId, String channelHotelId);

}

