package com.derbysoft.next.propertyconnect.channel.task.util;

import com.google.common.collect.Lists;
import lombok.Cleanup;
import lombok.NonNull;
import lombok.experimental.UtilityClass;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ForkJoinPool;
import java.util.function.*;
import java.util.stream.Collector;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

/**
 * @Created by <AUTHOR> on 2022/4/5
 */

@UtilityClass
public class CollectionUtil {

    public static <T> Collector<T, ?, T> toSingle(Supplier<? extends RuntimeException> exceptionSupplier) {
        return Collectors.collectingAndThen(
                Collectors.toList(),
                list -> {
                    if (list.size() != 1) {
                        throw exceptionSupplier.get();
                    }
                    return list.get(0);
                }
        );
    }

    public static <T> Collector<T, ?, T> toSingle() {
        return toSingle(() -> new IllegalStateException("The collection must contains only one element"));
    }

    public static <T> void forEachWithIndex(Iterable<T> iterable, BiConsumer<Integer, T> consumer) {
        int index = 0;
        for (T t : iterable) {
            consumer.accept(index, t);
            index++;
        }
    }

    public static <T> Collection<T> mapDaysInRange(LocalDate start, LocalDate end, BiFunction<Integer, LocalDate, T> mapper) {
        List<T> result = Lists.newArrayList();
        int index = 0;
        while(!start.isAfter(end)) {
            var mappedItem = mapper.apply(index, start);
            if (Objects.nonNull(mappedItem)) {
                result.add(mappedItem);
            }
            start = start.plusDays(1);
            index++;
        }
        return result;
    }

    public static <T> Collection<T> mapDaysInRange(LocalDate start, LocalDate end, Function<LocalDate, T> mapper) {
        return mapDaysInRange(start, end, (index, date) -> mapper.apply(date));
    }

    public static void eachDaysInRange(LocalDate start, LocalDate end, BiConsumer<Integer, LocalDate> consumer) {
        mapDaysInRange(start, end, (index, date) -> {
            consumer.accept(index, date);
            return null;
        });
    }

    public static void eachDaysInRange(LocalDate start, LocalDate end, Consumer<LocalDate> consumer) {
        eachDaysInRange(start, end, (index, date) -> consumer.accept(date));
    }

    public static <T> void mapBuilderList(List<T> builderList, UnaryOperator<T> eachBuilder) {
        builderList.replaceAll(eachBuilder);
    }

    public static void eachSpanInDateRange(@NonNull LocalDate startDate,@NonNull LocalDate endDate, int span, @NonNull BiConsumer<LocalDate, LocalDate> consumer) {
        mapSpanInDateRange(startDate, endDate, span, (start, end) -> {
            consumer.accept(start, end);
            return null;
        });
    }

    public static <T> Collection<T> mapSpanInDateRange(@NonNull LocalDate startDate,@NonNull LocalDate endDate, int span, @NonNull BiFunction<LocalDate, LocalDate, T> mapper) {
        assert span > 1;
        List<T> result = new ArrayList<>();
        LocalDate start = startDate;
        while (!start.isAfter(endDate)) {
            LocalDate end = start.plus(span - 1, ChronoUnit.DAYS);
            if (end.isAfter(endDate)) {
                end = endDate;
            }
            result.add(mapper.apply(start, end));
            start = end.plus(1, ChronoUnit.DAYS);
        }
        return result;
    }

    public static <T> void eachParallel(Collection<T> list, int parallelism, Consumer<T> handler) {
        mapEachParallel(list, parallelism, t -> {
            handler.accept(t);
            return null;
        });
    }

    public static <T, R> Collection<R> mapEachParallel(Collection<T> list, int parallelism,  Function<T, R> handler) {
        @Cleanup("shutdown") ForkJoinPool forkJoinPool = new ForkJoinPool(parallelism);
        try {
            return forkJoinPool.submit(() -> list.parallelStream().map(handler)).get().collect(Collectors.toList());
        } catch (ExecutionException | InterruptedException e) {
            throw new RuntimeException(e);
        }
    }

    public static <T> Stream<List<T>> partition(Stream<T> stream, int partitionSize) {
        return Lists.partition(stream.collect(Collectors.toList()), partitionSize).stream();
    }

    public static <T> List<List<T>> partitionList(List<T> list, int partitionCount) {
        int size = list.size();
        int partitionSize = (int) Math.ceil((double) size / partitionCount);

        return IntStream.range(0, partitionCount)
                .mapToObj(i -> list.subList(i * partitionSize, Math.min(size, (i + 1) * partitionSize)))
                .collect(Collectors.toList());
    }

}
