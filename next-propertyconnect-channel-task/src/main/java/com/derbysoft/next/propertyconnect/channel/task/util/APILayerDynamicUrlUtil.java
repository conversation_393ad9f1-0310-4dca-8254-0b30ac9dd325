package com.derbysoft.next.propertyconnect.channel.task.util;

import com.derbysoft.next.propertyconnect.channel.task.config.ApiLayerProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Component;

import java.net.URI;
import java.util.concurrent.atomic.AtomicReference;

/**
 * @Created by <AUTHOR> on 5/23/2025
 */

@Component
@EnableConfigurationProperties(ApiLayerProperties.class)
public class APILayerDynamicUrlUtil {

    private static final AtomicReference<ApiLayerProperties> PROPERTIES = new AtomicReference<>();

    public APILayerDynamicUrlUtil(ApiLayerProperties apiLayerProperties) {
        PROPERTIES.set(apiLayerProperties);
    }

    public static URI getBaseUrlForDistributor(String distributorId) {
        ApiLayerProperties properties = PROPERTIES.get();
        var customUrlMap = properties.getCustom();

        if (customUrlMap != null) {
            ApiLayerProperties.CustomUrl custom = customUrlMap.get(distributorId);
            if (custom != null && custom.getUrl() != null && !custom.getUrl().isBlank()) {
                return URI.create(custom.getUrl().trim());
            }
        }

        String defaultUrl = properties.getUrl();
        if (defaultUrl != null && !defaultUrl.isBlank()) {
            return URI.create(defaultUrl);
        }
        throw new IllegalStateException("No valid URL found for distributor: " + distributorId);
    }
}
