package com.derbysoft.next.propertyconnect.channel.task.service.impl;

import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelProductsDTO;
import com.derbysoft.next.propertyconnect.channel.task.service.ChannelProductsService;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.temporal.ChronoUnit;
import java.util.Objects;
import java.util.concurrent.RejectedExecutionException;

/**
 * @Created by <AUTHOR> on 2023/3/21
 */

@Service
@Primary
@RequiredArgsConstructor
public class ChannelProductsServiceProxy implements ChannelProductsService {

    private final ChannelProductsRemoteHandlerFactory channelProductsRemoteHandlerFactory;
    private final LocalChannelProductsService localChannelProductsService;


    @Override
    public ChannelProductsDTO getChannelProducts(String channelId, String channelHotelId) {
        return getChannelProducts("PROPERTYCONNECT", channelId, channelHotelId);
    }

    @Override
    public ChannelProductsDTO getChannelProductsWithExtra(String supplierId, String channelId, String channelHotelId) {
        return getChannelProducts(supplierId, channelId, channelHotelId, true, true);
    }

    public ChannelProductsDTO getChannelProducts(String supplierId, String channelId, String channelHotelId, Boolean isForceUpdate, Boolean withExtra) {
        var remoteService = channelProductsRemoteHandlerFactory.getInstance(channelId);
        var cachedProducts = localChannelProductsService.getChannelProducts(supplierId, channelId, channelHotelId);

        if (Objects.isNull(cachedProducts) || Boolean.TRUE.equals(isForceUpdate)) {
            if (localChannelProductsService.getLastQueryDate(channelId).plus(remoteService.requestSpan(), ChronoUnit.MILLIS).isAfter(LocalDateTime.now(ZoneOffset.UTC))){
                throw new RejectedExecutionException(String.format("Request too frequently. For the %s, it must be accessed after %s seconds", channelId, remoteService.requestSpan() / 1000 ));
            }

            var remoteProducts = remoteService.getChannelProductsWithExtra(supplierId, channelId, channelHotelId);
            if (Objects.nonNull(remoteProducts)) {
                localChannelProductsService.saveChannelProductCache(remoteProducts);
                removeExtra(withExtra, remoteProducts);
                return remoteProducts;
            }
        }

        removeExtra(withExtra, cachedProducts);
        return cachedProducts;
    }

    private void removeExtra(Boolean withExtra, ChannelProductsDTO channelProductsDTO) {
        if (Boolean.TRUE.equals(withExtra) || null == channelProductsDTO) {
            return;
        }

        var channelProducts = channelProductsDTO.getChannelProducts();
        if (Objects.isNull(channelProducts)) {
            return;
        }

        channelProducts.forEach(product -> {
            var extensions = product.getExtensions();
            if (Objects.nonNull(extensions)) {
                extensions.remove("roomExtension");
                extensions.remove("rateExtension");
            }
        });

    }
}
