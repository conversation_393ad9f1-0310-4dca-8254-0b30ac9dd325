package com.derbysoft.next.propertyconnect.channel.task.domain.dto;

import com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.RemoteChannelService;

/**
 * @Created by <AUTHOR> on 2023/6/10
 */

public interface InventoryItemStatus {
    RemoteChannelService.Operation getOperation();
    String codePattern();
    String getCode();
    String getName();
    SyncStatus getSyncStatus();
    String getErrorCode();
    String getErrorMessage();
    void setSyncStatus(SyncStatus syncStatus);
    void setErrorCode(String errorCode);
    void setErrorMessage(String errorMessage);
}
