package com.derbysoft.next.propertyconnect.channel.task.controller.api;

import com.derbysoft.extension.aggregationtool.controllerservice.ControllerAndService;
import com.derbysoft.next.propertyconenct.channel.common.exception.response.UnifyResult;
import com.derbysoft.next.propertyconnect.channel.task.config.UnifyResultWrapper;
import com.derbysoft.next.propertyconnect.channel.task.controller.vo.room.ChannelRoomVO;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

/**
 * @Created by <AUTHOR> on 2023/5/25
 */

@ControllerAndService(wrapper = UnifyResultWrapper.class)
@Hidden
@RequestMapping("/api")
@Tag(name = "ChannelRoomAPI", description = "ChannelRoomAPI", extensions = @Extension(properties = @ExtensionProperty(name = "order", value = "4")))
public interface ChannelRoomAPI {

    @GetMapping("/channel/{channelId}/channelhotels/{channelHotelId}/roomtypes")
    UnifyResult<ChannelRoomVO> getChannelRoom(@PathVariable String channelId,
                                              @PathVariable String channelHotelId);


    @PostMapping("/channel/{channelId}/channelhotels/{channelHotelId}/roomtypes")
    UnifyResult<ChannelRoomVO> saveChannelRoom(@PathVariable String channelId,
                                               @PathVariable String channelHotelId,
                                               @RequestBody ChannelRoomVO channelRoom,
                                               @RequestParam(required = false, defaultValue = "true") Boolean async,
                                               @Schema(description = "roomsInfo.code is required for retry mode", defaultValue = "false")
                                               @RequestParam(required = false, defaultValue = "false") Boolean retry);

    @DeleteMapping("/channel/{channelId}/channelhotels/{channelHotelId}/roomtypes")
    UnifyResult<Void> deleteChannelRoom(@PathVariable String channelId,
                                        @PathVariable String channelHotelId);
}
