package com.derbysoft.next.propertyconnect.channel.task.service.impl;

import com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.RemoteChannelProductsService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.stereotype.Component;

/**
 * @Created by <AUTHOR> on 2023/3/21
 */

@Component
@RequiredArgsConstructor
public class ChannelProductsRemoteHandlerFactory {

    private final ObjectProvider<RemoteChannelProductsService> remoteChannelProductsServices;

    public RemoteChannelProductsService getInstance(String distributorId) {
        return remoteChannelProductsServices.stream()
                .filter(service -> distributorId.equalsIgnoreCase(service.channel()))
                .findFirst()
                .orElseGet(() -> remoteChannelProductsServices.stream()
                        .filter(service -> RemoteChannelProductsService.ANY_CHANNEL.equalsIgnoreCase(service.channel()))
                        .findFirst()
                        .orElseThrow(() -> new IllegalArgumentException("[" + distributorId + "] Not support getting products directly from channel")));
    }
}
