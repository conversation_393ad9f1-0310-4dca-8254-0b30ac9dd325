package com.derbysoft.next.propertyconnect.channel.task.task.exceptionlistremove;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.validation.constraints.NotBlank;
import lombok.Builder;
import lombok.Data;

/**
 * @Created by <AUTHOR> on 2023/3/2
 */

@Data
@Builder(toBuilder = true)
public class ChannelHotelProduct {

    @NotBlank
    String channel;

    @NotBlank
    String hotel;

    @NotBlank
    String roomType;

    @NotBlank
    String ratePlan;

    String errCode;
    String errMessage;

    @JsonIgnore
    boolean deletable = false;

    public String toExceptionListFormat() {
        return String.format("%s|PROPERTYCONNECT|%s|%s|%s", channel, hotel, ratePlan, roomType);
    }
}
