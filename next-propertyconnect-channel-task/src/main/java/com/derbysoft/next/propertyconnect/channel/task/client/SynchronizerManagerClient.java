package com.derbysoft.next.propertyconnect.channel.task.client;

import com.derbysoft.next.propertyconnect.channel.task.config.SynchronizerProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Collection;
import java.util.Optional;

/**
 * @Created by <AUTHOR> on 2023/3/2
 */

@EnableConfigurationProperties(SynchronizerProperties.class)
//@RemoteService(exceptionOnFailure = true)
@FeignClient(name = "synchronizerManagerClient", url = "${app.synchronizer.url}synchronizer2/")
public interface SynchronizerManagerClient {

    @GetMapping("data/exceptionlist/list.jsp?page=1&supplier=PROPERTYCONNECT&level=&startDate=&endDate=")
    Optional<Object> getExceptionList(@RequestParam("pageSize") Integer pageSize,
                                      @RequestParam("channel") String channel,
                                      @RequestParam("hotel") String hotel,
                                      @RequestParam("ratePlan") String ratePlan,
                                      @RequestParam("roomType") String roomType);

    @PostMapping(value = "data/exceptionlist/remove_roomrate.jsp", consumes = "application/x-www-form-urlencoded")
    Object removeFromExceptionList(@RequestParam("value") String value);


    default Optional<Object> getExceptionList(String channel) {
        return this.getExceptionList(Integer.MAX_VALUE, channel, null, null, null);
    }

    default void removeFromExceptionList(Collection<String> channelHotelProducts) {
        this.removeFromExceptionList(String.join(System.getProperty("line.separator"), channelHotelProducts));
    }

}