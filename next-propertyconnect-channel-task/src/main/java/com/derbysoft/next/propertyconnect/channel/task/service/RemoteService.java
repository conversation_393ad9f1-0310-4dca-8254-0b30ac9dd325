package com.derbysoft.next.propertyconnect.channel.task.service;

import com.derbysoft.next.propertyconnect.channel.task.client.CommonProductQueryClient;
import com.derbysoft.next.propertyconnect.channel.task.client.PCProfileClient;
import com.derbysoft.next.propertyconnect.channel.task.client.StandardAPIClient;
import com.derbysoft.next.propertyconnect.channel.task.client.SynchronizerManagerClient;
import com.derbysoft.next.propertyconnect.channel.task.client.aimapping.MappingPredictionClient;
import com.derbysoft.next.propertyconnect.channel.task.client.aimapping.OpenAIClient;
import com.derbysoft.next.propertyconnect.channel.task.client.alarm.AlarmClient;
import com.derbysoft.next.propertyconnect.channel.task.client.connectivityoperationapi.ConnectivityOperationClient;
import org.springframework.cloud.context.config.annotation.RefreshScope;

/**
 * @Created by <AUTHOR> on 2023/2/28
 */

@RefreshScope
public interface RemoteService extends
        AlarmClient,
        CommonProductQueryClient,
        ConnectivityOperationClient,
        MappingPredictionClient,
        OpenAIClient,
        PCProfileClient,
        StandardAPIClient,
        SynchronizerManagerClient {

}
