package com.derbysoft.next.propertyconnect.channel.task.config;

import com.derbysoft.extension.aggregationtool.controllerservice.Wrapper;
import com.derbysoft.next.propertyconenct.channel.common.exception.response.UnifyResult;

import java.util.function.Supplier;

/**
 * @Created by <AUTHOR> on 2023/4/3
 */

public class UnifyResultWrapper implements Wrapper<UnifyResult<?>> {

    @Override
    public <T> UnifyResult<T> wrap(Supplier<T> supplier) {
        return UnifyResult.from(supplier);
    }
}
