package com.derbysoft.next.propertyconnect.channel.task.client.connectivityoperationapi;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

/**
 * @Created by <AUTHOR> on 2023/6/11
 */

@EqualsAndHashCode(callSuper = true)
@SuperBuilder(toBuilder = true)
@Data
public class OperationApiProductCreationRequest extends OperationApiBasicRequest {
    String roomCode;
    String ratePlanCode;
}
