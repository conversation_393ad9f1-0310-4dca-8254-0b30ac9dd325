package com.derbysoft.next.propertyconnect.channel.task.domain.dto;

import com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.RemoteChannelService;
import lombok.Data;

/**
 * @Created by <AUTHOR> on 1/9/2025
 */

@Data
public class ErrorInventoryItem implements InventoryItemStatus {
    private RemoteChannelService.Operation operation;
    private String code;
    private String name;
    private SyncStatus syncStatus;
    private String errorCode;
    private String errorMessage;

    public static ErrorInventoryItem fromInventoryItemStatus(InventoryItemStatus inventoryItemStatus) {
        var errorInventoryItem = new ErrorInventoryItem();
        errorInventoryItem.setOperation(inventoryItemStatus.getOperation());
        errorInventoryItem.setCode(inventoryItemStatus.getCode());
        errorInventoryItem.setName(inventoryItemStatus.getName());
        errorInventoryItem.setSyncStatus(inventoryItemStatus.getSyncStatus());
        errorInventoryItem.setErrorCode(inventoryItemStatus.getErrorCode());
        errorInventoryItem.setErrorMessage(inventoryItemStatus.getErrorMessage());
        return errorInventoryItem;
    }

    @Override
    public String codePattern() {
        return "";
    }
}
