package com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.adapter;

import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelHotelDTO;
import com.derbysoft.next.propertyconnect.channel.task.service.ChannelHotelSetupProcedure;
import com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.RemoteChannelService;
import org.apache.commons.lang.NotImplementedException;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.function.Consumer;

/**
 * @Created by <AUTHOR> on 2023/5/26
 */

public interface ConnectivityOperationActivationService extends RemoteChannelAdapterService, ChannelHotelSetupProcedure {

    @Override
    default String channel(){
        return ANY_CHANNEL;
    }

    @Override
    default LinkedHashMap<RemoteChannelService.Operation, Consumer<ChannelHotelDTO>> supportOperations(){
        var procedure = new LinkedHashMap<Operation, Consumer<ChannelHotelDTO>>();
        procedure.put(Operation.SaveCredential, this::credentialActivation);
        procedure.put(Operation.SaveProperty, this::propertyActivation);
        procedure.put(Operation.SaveRoomTypes, this::roomTypeActivation);
        procedure.put(Operation.SaveRatePlans, this::ratePlanActivation);
        procedure.put(Operation.TriggerARIRefresh, this::triggerARIRefresh);
        procedure.put(Operation.AfterAll, this::afterAll);
        return procedure;
    }

    @Override
    default List<Operation> setupProcedure(ChannelHotelDTO dto){
        return new ArrayList<>(supportOperations().keySet());
    }

    @Override
    default ChannelHotelDTO execution(Operation operation, ChannelHotelDTO dto) {
        var processor = supportOperations().get(operation);

        if (null == processor) {
            throw new NotImplementedException(String.format("%s does not support this operation %s", this.channel(), operation.name()));
        }

        processor.accept(dto);
        return dto;
    }

    default ChannelHotelDTO execution(ChannelHotelDTO dto) {
        setupProcedure(dto).forEach(operation -> execution(operation, dto));
        return dto;
    }


    default void credentialActivation(ChannelHotelDTO dto) {}
    void propertyActivation(ChannelHotelDTO dto);
    void roomTypeActivation(ChannelHotelDTO dto);
    void ratePlanActivation(ChannelHotelDTO dto);
    void triggerARIRefresh(ChannelHotelDTO dto);
    void afterAll(ChannelHotelDTO dto);
}
