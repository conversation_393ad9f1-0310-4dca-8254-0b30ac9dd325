package com.derbysoft.next.propertyconnect.channel.task.service;

import com.derbysoft.next.propertyconnect.channel.task.client.connectivityoperationapi.ConnectivityOperationRequestTranslator;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelHotelDTO;
import com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.adapter.ChannelHotelActivationCustomizeService;
import com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.adapter.impl.GeneralChannelHotelActivationService;
import com.derbysoft.next.propertyconnect.channel.task.service.storageservice.ChannelInfoStorageService;
import com.derbysoft.next.propertyconnect.channel.task.task.arirefresh.ARIRefreshService;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @Created by <AUTHOR> on 5/28/2025
 */
@Component
public class RequestSetupProcedure extends GeneralChannelHotelActivationService {

    public RequestSetupProcedure(RemoteService remoteService, ARIRefreshService ariRefreshService, ChannelInfoStorageService channelInfoStorageService, ConnectivityOperationRequestTranslator translator, List<ChannelHotelActivationCustomizeService> activationCustomizers) {
        super(remoteService, ariRefreshService, channelInfoStorageService, translator, activationCustomizers);
    }

    @Override
    public List<Operation> setupProcedure(ChannelHotelDTO dto) {
        var customProcedure = super.getCustomizer(dto);
        if (null != customProcedure) {
            var operations = customProcedure.customizeProcedure();
            if (null != operations && !operations.isEmpty()) {
                return operations;
            }
        }
        var operations = new ArrayList<Operation>();
        if (null != dto.getAccountSettings() && !dto.getAccountSettings().isEmpty()) {
            operations.add(Operation.SaveCredential);
        }
        if (null != dto.getHotelInfo()) {
            operations.add(Operation.SaveProperty);
        }
        if (null != dto.getRoomsInfo() && !dto.getRoomsInfo().isEmpty()) {
            operations.add(Operation.SaveRoomTypes);
        }
        if (null != dto.getRatesInfo() && !dto.getRatesInfo().isEmpty()) {
            operations.add(Operation.SaveRatePlans);
        }
        if (null != dto.getProductsInfo() && !dto.getProductsInfo().isEmpty()) {
            operations.add(Operation.SaveProducts);
        }
        operations.add(Operation.TriggerARIRefresh);

        return operations;
    }
}
