package com.derbysoft.next.propertyconnect.channel.task.service.impl;

import com.derbysoft.next.commons.core.logsupport.constant.LogConst;
import com.derbysoft.next.commons.core.logsupport.handler.PerfLogHandler;
import com.derbysoft.next.propertyconenct.channel.common.mapstruct.BaseMapper;
import com.derbysoft.next.propertyconenct.channel.common.utils.GeneratedMapper;
import com.derbysoft.next.propertyconnect.channel.task.client.WebhookAPI;
import com.derbysoft.next.propertyconnect.channel.task.client.alarm.NoticeRequest;
import com.derbysoft.next.propertyconnect.channel.task.config.HotelSetupConnectionWhiteListConfig;
import com.derbysoft.next.propertyconnect.channel.task.config.schedulecenter.ChannelServiceTask;
import com.derbysoft.next.propertyconnect.channel.task.config.schedulecenter.ChannelServiceTypedTask;
import com.derbysoft.next.propertyconnect.channel.task.controller.vo.hotel.ChannelHotelVO;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.*;
import com.derbysoft.next.propertyconnect.channel.task.service.ChannelHotelAPIService;
import com.derbysoft.next.propertyconnect.channel.task.service.ChannelHotelSetupExecutor;
import com.derbysoft.next.propertyconnect.channel.task.service.RemoteService;
import com.derbysoft.next.propertyconnect.channel.task.service.RequestSetupProcedure;
import com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.RemoteChannelService;
import com.derbysoft.next.propertyconnect.channel.task.service.mapping.PredictionReportService;
import com.derbysoft.next.propertyconnect.channel.task.service.storageservice.ChannelInfoStorageService;
import com.derbysoft.next.propertyconnect.channel.task.task.hotelsetup.DistributorHotelSetupTask;
import com.derbysoft.next.propertyconnect.channel.task.task.hotelsetup.HotelSetupConfigurationProperties;
import com.derbysoft.next.propertyconnect.channel.task.util.*;
import com.derbysoft.schedulecenter.rpc.client.HttpScheduleCenterService;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.experimental.ExtensionMethod;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.web.util.UriComponentsBuilder;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Created by <AUTHOR> on 2023/5/25
 */

@Service
@ExtensionMethod({CollectionUtil.class, InventoryItemErrorUtil.class})
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
@EnableConfigurationProperties(HotelSetupConnectionWhiteListConfig.class)
@Slf4j
public class ChannelHotelAPIServiceImpl implements ChannelHotelAPIService {
    private final RequestSetupProcedure requestSetupProcedure;
    private final ChannelInfoStorageService hotelStorageService;
    private final ChannelHotelSetupExecutor channelHotelSetupExecutor;
    private final RemoteService remoteService;
    private final HotelSetupConfigurationProperties properties;
    private final CloneUtil cloneUtil = CloneUtil.INSTANCE;
    private final ObjectProvider<HttpScheduleCenterService> httpScheduleCenterServiceProvider;
    @Lazy
    private final DistributorHotelSetupTask distributorHotelSetupTask;
    private final ChannelHotelVoTranslator translator = Mappers.getMapper(ChannelHotelVoTranslator.class);
    private final SyncStatusOverwriteTranslator syncStatusOverwriteTranslator = Mappers.getMapper(SyncStatusOverwriteTranslator.class);
    private final HotelSetupConnectionWhiteListConfig supplierWhiteList;
    private final PredictionReportService predictionReportService;
    private final WebhookAPI webhookAPI;
    private final ObjectMapper objectMapper;

    @Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    @AnnotateWith(GeneratedMapper.class)
    public interface ChannelHotelVoTranslator extends BaseMapper<ChannelHotelDTO, ChannelHotelVO> {
        @InheritInverseConfiguration(name = "map")
        @Override
        ChannelHotelDTO reverseMap(ChannelHotelVO channelHotelVO);

        @Mapping(source = "accountSettings", target = "hotelAccount")
        @Named("map")
        @Override
        ChannelHotelVO map(ChannelHotelDTO channelHotelDTO);

        @Mapping(target = "hotelInfo", ignore = true)
        @Mapping(target = "hotelAccount", ignore = true)
        @Mapping(target = "roomsInfo", ignore = true)
        @Mapping(target = "ratesInfo", ignore = true)
        @Mapping(target = "productsInfo", ignore = true)
        @Mapping(target = "extensions", ignore = true)
        @Named("mapWithOutInfo")
        ChannelHotelVO mapWithOutInfo(ChannelHotelDTO channelHotelDTO);

        @IterableMapping(qualifiedByName = "map")
        @Override
        List<ChannelHotelVO> map(List<ChannelHotelDTO> list);

        @IterableMapping(qualifiedByName = "mapWithOutInfo")
        List<ChannelHotelVO> mapWithOutInfos(List<ChannelHotelDTO> list);

        @Mapping(source = "accountSettings", target = "hotelAccount")
        @Override
        void fillIn(ChannelHotelDTO from, @MappingTarget ChannelHotelVO to);

        @Mapping(source = "accountSettings", target = "hotelAccount")
        @Override
        void reverseFill(@MappingTarget ChannelHotelVO to, ChannelHotelDTO from);

        @Mapping(target = "syncStatus", constant = "DRAFT")
        @Mapping(target = "status", defaultValue = "Actived")
        ChannelHotelInfo mapInfo(com.derbysoft.next.propertyconnect.channel.task.controller.vo.hotel.ChannelHotelInfo in);

        @Mapping(source = "id", target = "code")
        @Mapping(target = "syncStatus", constant = "DRAFT")
        @Mapping(target = "status", defaultValue = "Actived")
        ChannelRoomInfo mapInfo(com.derbysoft.next.propertyconnect.channel.task.controller.vo.room.ChannelRoomInfo in);

        @Mapping(source = "id", target = "code")
        @Mapping(target = "syncStatus", constant = "DRAFT")
        @Mapping(target = "status", defaultValue = "Actived")
        ChannelRateInfo mapInfo(com.derbysoft.next.propertyconnect.channel.task.controller.vo.rate.ChannelRateInfo in);

        @Mapping(target = "syncStatus", constant = "DRAFT")
        @Mapping(target = "status", defaultValue = "Actived")
        ChannelProductsDTO.Product mapInfo(ChannelHotelVO.ChannelProductInfo in);

        @InheritInverseConfiguration
        @Mapping(target = "errorCode", conditionExpression = "java(null != in.getSyncStatus() && in.getSyncStatus().isFail())")
        @Mapping(target = "errorMessage", conditionExpression = "java(null != in.getSyncStatus() && in.getSyncStatus().isFail())")
        @Mapping(target = "status", ignore = true)
        @Mapping(target = "syncStatus", source = "syncStatus")
        com.derbysoft.next.propertyconnect.channel.task.controller.vo.hotel.ChannelHotelInfo inverseMap(ChannelHotelInfo in);

        @InheritInverseConfiguration
        @Mapping(target = "errorCode", conditionExpression = "java(null != in.getSyncStatus() && in.getSyncStatus().isFail())")
        @Mapping(target = "errorMessage", conditionExpression = "java(null != in.getSyncStatus() && in.getSyncStatus().isFail())")
        @Mapping(target = "status", ignore = true)
        @Mapping(target = "syncStatus", source = "syncStatus")
        com.derbysoft.next.propertyconnect.channel.task.controller.vo.room.ChannelRoomInfo inverseMap(ChannelRoomInfo in);

        @InheritInverseConfiguration
        @Mapping(target = "errorCode", conditionExpression = "java(null != in.getSyncStatus() && in.getSyncStatus().isFail())")
        @Mapping(target = "errorMessage", conditionExpression = "java(null != in.getSyncStatus() && in.getSyncStatus().isFail())")
        @Mapping(target = "status", ignore = true)
        @Mapping(target = "syncStatus", source = "syncStatus")
        com.derbysoft.next.propertyconnect.channel.task.controller.vo.rate.ChannelRateInfo inverseMap(ChannelRateInfo in);

        @InheritInverseConfiguration
        @Mapping(target = "errorCode", conditionExpression = "java(null != in.getSyncStatus() && in.getSyncStatus().isFail())")
        @Mapping(target = "errorMessage", conditionExpression = "java(null != in.getSyncStatus() && in.getSyncStatus().isFail())")
        @Mapping(target = "status", ignore = true)
        @Mapping(target = "syncStatus", source = "syncStatus")
        ChannelHotelVO.ChannelProductInfo inverseMap(ChannelProductsDTO.Product in);
    }

    @Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    @AnnotateWith(GeneratedMapper.class)
    public interface SyncStatusOverwriteTranslator extends ChannelHotelVoTranslator {
        @Mapping(source = "syncStatus", target = "syncStatus")
        @Override
        ChannelHotelInfo mapInfo(com.derbysoft.next.propertyconnect.channel.task.controller.vo.hotel.ChannelHotelInfo in);

        @Mapping(source = "id", target = "code")
        @Mapping(source = "syncStatus", target = "syncStatus")
        @Override
        ChannelRoomInfo mapInfo(com.derbysoft.next.propertyconnect.channel.task.controller.vo.room.ChannelRoomInfo in);

        @Mapping(source = "id", target = "code")
        @Mapping(source = "syncStatus", target = "syncStatus")
        @Override
        ChannelRateInfo mapInfo(com.derbysoft.next.propertyconnect.channel.task.controller.vo.rate.ChannelRateInfo in);

        @Mapping(source = "syncStatus", target = "syncStatus")
        @Override
        ChannelProductsDTO.Product mapInfo(ChannelHotelVO.ChannelProductInfo in);
    }

    @Override
    public ChannelHotelVO getChannelHotel(String channelId, String channelHotelId) {
        return translator.map(hotelStorageService.getChannelHotel(channelId, channelHotelId));
    }

    @Override
    public ChannelHotelVO saveChannelHotel(String channelId, String channelHotelId, ChannelHotelVO channelHotelRQ, String notifyUrl, Boolean syncMode, Boolean retry, Boolean fullUpdate, Boolean saveOnly) {
        if (supplierWhiteList.isProhibitedConnection(channelHotelRQ.getSupplierId(), channelId)) {
            PerfLogHandler.currentHandler().message(LogConst.Parameter.WARN_MSG, "Supplier is not allowed to sync, Jumped!");
            return getAsyncResponse(channelId, channelHotelId, channelHotelRQ.getSupplierId(), PerfLogHandler.currentHandler().getToken());
        }

        channelHotelRQ.setChannelId(channelId);
        channelHotelRQ.setChannelHotelId(channelHotelId);
        var dto = saveOnly
                ? syncStatusOverwriteTranslator.reverseMap(channelHotelRQ)
                : translator.reverseMap(channelHotelRQ);

        if (retry) {
            handleRetry(channelId, channelHotelId, dto);
        }
        var hotelAllInfo = fullUpdate ? hotelStorageService.saveAllChannelProducts(dto) : hotelStorageService.saveIncrementalChannelProduct(dto);

        if (null == channelHotelRQ.getHotelInfo()) {
            hotelAllInfo.setHotelInfo(null);
        }
        if (null == channelHotelRQ.getHotelAccount()) {
            hotelAllInfo.setAccountSettings(null);
        }

        if (saveOnly) {
            return translator.map(hotelAllInfo);
        }

        var perfHandler = PerfLogHandler.currentHandler();
        hotelAllInfo.setOperationToken(perfHandler.getToken());
        hotelAllInfo.setExtensions(dto.getExtensions());
        var execute = channelHotelSetupExecutor.execute(hotelAllInfo, requestSetupProcedure).share();
        if (!syncMode) {
            var result = translator.mapWithOutInfo(hotelAllInfo);
            result.setOperationToken(PerfLogHandler.currentHandler().getToken());
            execute.subscribeOn(Schedulers.boundedElastic())
                    .onErrorResume(e -> {
                        log.error("Failed on execution setup procedure for channel [{}] and channel hotel [{}]", hotelAllInfo.getChannelId(), hotelAllInfo.getChannelHotelId(), e);
                        return Mono.just(hotelAllInfo);
                    })
                    .subscribe(cb -> doAfterExecution(cb, notifyUrl));
            return result;
        }
        var syncResult = execute.block(Duration.ofMinutes(5));
        doAfterExecution(Optional.ofNullable(syncResult).orElseGet(ChannelHotelDTO::new), perfHandler, false, notifyUrl);
        return translator.map(syncResult);
    }

    private void handleRetry(String channelId, String channelHotelId, ChannelHotelDTO dto) {
        var localChannelHotel = hotelStorageService.getChannelHotel(channelId, channelHotelId);

        if (dto.getHotelInfo() != null && dto.getHotelInfo().getId() != null) {
            dto.setHotelInfo(localChannelHotel.getHotelInfo());
        }

        if (dto.getRoomsInfo() != null) {
            var localRooms = Optional.ofNullable(localChannelHotel.getRoomsInfo())
                    .map(roomInfos -> roomInfos.stream().collect(Collectors.toMap(ChannelRoomInfo::getCode, Function.identity())))
                    .orElse(Map.of());
            dto.setRoomsInfo(dto.getRoomsInfo().stream().map(roomInfo -> {
                var localRoom = localRooms.get(roomInfo.getCode());
                if (localRoom == null) {
                    roomInfo.roomNotFound();
                    return roomInfo;
                }
                return localRoom;
            }).collect(Collectors.toList()));
        }

        if (dto.getRatesInfo() != null) {
            var localRates = Optional.ofNullable(localChannelHotel.getRatesInfo())
                    .map(rateInfos -> rateInfos.stream().collect(Collectors.toMap(ChannelRateInfo::getCode, Function.identity())))
                    .orElse(Map.of());
            dto.setRatesInfo(dto.getRatesInfo().stream().map(rateInfo -> {
                var localRate = localRates.get(rateInfo.getCode());
                if (localRate == null) {
                    rateInfo.rateNotFound();
                    return rateInfo;
                }
                return localRate;
            }).collect(Collectors.toList()));
        }

        if (dto.getProductsInfo() != null) {
            var localProducts = Optional.ofNullable(localChannelHotel.getProductsInfo())
                    .map(productInfos -> productInfos.stream()
                            .collect(Collectors.toMap(ChannelProductsDTO.Product::getCode, Function.identity())))
                    .orElse(Map.of());
            dto.setProductsInfo(dto.getProductsInfo().stream().map(productInfo -> {
                var localProduct = localProducts.get(productInfo.getCode());
                if (localProduct == null) {
                    productInfo.itemNotFound();
                    return productInfo;
                }
                return localProduct;
            }).collect(Collectors.toList()));
        }
    }

    @Override
    public void deleteChannelHotel(String channelId, String channelHotelId) {
        hotelStorageService.deleteChannelHotel(channelId, channelHotelId);
    }

    private void doAfterExecution(ChannelHotelDTO callback, String notifyUrl) {
        var sendMail = Optional.ofNullable(callback.getExtensions())
                .map(ext -> ext.get("ignoreSendEmail"))
                .map(ignore -> !Boolean.TRUE.equals(ignore))
                .orElse(true);
        doAfterExecution(callback, PerfLogHandler.currentHandler(), sendMail, notifyUrl);
    }

    private void doAfterExecution(ChannelHotelDTO callback, PerfLogHandler perfLogHandler, boolean sendMail, String notifyUrl) {
        List<Pair<RemoteChannelService.Operation, InventoryItemStatus>> failedItems = new ArrayList<>();
        var hotelInfo = callback.getHotelInfo();
        if (hotelInfo != null) {
            if (hotelInfo.getSyncStatus().isProcessingStatus()) {
                hotelInfo.unprocessed();
            }
            if (hotelInfo.getSyncStatus().isFail()) {
                failedItems.add(Pair.of(RemoteChannelService.Operation.SaveProperty, cloneUtil.clone(hotelInfo)));
            }
        }

        if (callback.getRoomsInfo() != null) {
            callback.getRoomsInfo().forEach(roomInfo -> {
                if (roomInfo.getSyncStatus().isProcessingStatus()) {
                    roomInfo.unprocessed();
                }
                if (roomInfo.getSyncStatus().isFail()) {
                    failedItems.add(Pair.of(RemoteChannelService.Operation.SaveRoomTypes, cloneUtil.clone(roomInfo)));
                }
            });
        }

        if (callback.getRatesInfo() != null) {
            callback.getRatesInfo().forEach(rateInfo -> {
                if (rateInfo.getSyncStatus().isProcessingStatus()) {
                    rateInfo.unprocessed();
                }
                if (rateInfo.getSyncStatus().isFail()) {
                    failedItems.add(Pair.of(RemoteChannelService.Operation.SaveRatePlans, cloneUtil.clone(rateInfo)));
                }
            });
        }

        if (callback.getProductsInfo() != null) {
            callback.getProductsInfo().forEach(productInfo -> {
                if (productInfo.getSyncStatus().isProcessingStatus()) {
                    productInfo.unprocessed();
                }
                if (productInfo.getSyncStatus().isFail()) {
                    failedItems.add(Pair.of(RemoteChannelService.Operation.SaveProducts, cloneUtil.clone(productInfo)));
                }
            });
        }

        try {
            hotelStorageService.saveIncrementalChannelProduct(callback);
        } catch (Exception e) {
            log.error("Save final syncState error: " + e.getMessage());
        }
        sendNotify(notifyUrl, failedItems.stream().map(Pair::getRight).collect(Collectors.toList()), perfLogHandler);
        if (failedItems.isEmpty()) {
            return;
        }
        if (perfLogHandler != null) {
            perfLogHandler.fail(new IllegalStateException("BusinessFailed"));
        }
        if (!sendMail) {
            return;
        }
        remoteService.notice(NoticeRequest.builder()
                .enableEmail(true)
                .mailTo(properties.getAlarmEmails())
                .mailSubject(String.format("[%s]%s Setup Failure Alarm", EnvUtil.getEnv().toUpperCase(), Optional.ofNullable(callback.getChannelId()).map(cid -> " " + cid).orElse("")))
                .alarmContent(MailContextGenerator.generateSetupMailContent(callback.getChannelId(), callback.getChannelHotelId(), callback.getOperationToken(), failedItems))
                .build()
        );
    }

    private void sendNotify(String notifyUrl, List<InventoryItemStatus> failItems, PerfLogHandler perfLogHandler) {
        if (StringUtils.isBlank(notifyUrl)) {
            return;
        }
        var result = failItems.isEmpty();
        var url = UriComponentsBuilder.fromHttpUrl(notifyUrl)
                .queryParam("result", String.valueOf(result))
                .build()
                .toUriString();

        try {
            webhookAPI.webhookPostCall(url, result ? List.of() : failItems.stream().map(ErrorInventoryItem::fromInventoryItemStatus).toList());
            Optional.ofNullable(perfLogHandler).ifPresent(handler -> handler.message("ext_notified", true));
        } catch (Exception e) {
            Optional.ofNullable(perfLogHandler).ifPresent(handler -> handler.message("ext_notified", false));
            log.error("Failed to notify external system.", e);
        }
    }


    static class SingleHotelSetupTask implements ChannelServiceTypedTask {

        private final String supplierId;
        private final String channelId;
        private final String channelHotelId;
        private final String echoToken;
        private final Boolean ignorePropertyStatusCheck;
        private final List<String> procedure;
        private final String notifyUrl;

        SingleHotelSetupTask(String supplierId, String channelId, String channelHotelId, List<String> procedure, String notifyUrl, String echoToken, Boolean ignorePropertyStatusCheck) {
            this.supplierId = supplierId;
            this.channelId = channelId;
            this.channelHotelId = channelHotelId;
            this.procedure = procedure;
            this.notifyUrl = notifyUrl;
            this.echoToken = echoToken;
            this.ignorePropertyStatusCheck = ignorePropertyStatusCheck;
        }

        SingleHotelSetupTask(String supplierId, String channelId, String channelHotelId, String echoToken) {
            this(supplierId, channelId, channelHotelId, null, null, echoToken, null);
        }

        public void triggerNewTask(HttpScheduleCenterService httpScheduleCenterService) {
            this.afterExecute(httpScheduleCenterService, null);
        }

        @Override
        public TaskType taskType() {
            return TaskType.ChannelAptHotelSetup;
        }

        @Override
        public List<ChannelServiceTask> nextTask(ChannelServiceTask task) {
            return List.of(ChannelServiceTask.builder()
                    .supplier(supplierId)
                    .channel(channelId)
                    .channelHotel(channelHotelId)
                    .echoToken(echoToken)
                    .ignorePropertyStatusCheck(ignorePropertyStatusCheck)
                    .procedure(procedure)
                    .notifyUrl(notifyUrl)
                    .build());
        }
    }

    @Override
    public ChannelHotelVO syncChannelHotel(String channelId, String channelHotelId, String procedure, String notifyUrl, Boolean ignoreStatusCheck, String supplierId, ChannelHotelVO channelHotelVO) {
        var operationToken = PerfLogHandler.currentHandler().getToken();

        predictionReportService.saveChannelProductMappingSnapshotAndReport(supplierId, channelId, channelHotelId);
        if (supplierWhiteList.isProhibitedConnection(supplierId, channelId)) {
            PerfLogHandler.currentHandler().message(LogConst.Parameter.WARN_MSG, "Supplier is not allowed to sync, Jumped!");
            return this.getAsyncResponse(channelId, channelHotelId, supplierId, operationToken);
        }

        var httpScheduleCenterService = httpScheduleCenterServiceProvider.getIfAvailable();
        if (null == httpScheduleCenterService) {
            distributorHotelSetupTask.doExecute(ChannelServiceTask.builder()
                    .supplier(supplierId)
                    .channel(channelId)
                    .channelHotel(channelHotelId)
                    .echoToken(PerfLogHandler.currentHandler().getToken())
                    .ignorePropertyStatusCheck(ignoreStatusCheck)
                    .procedure(Optional.ofNullable(procedure).map(proc -> List.of(proc.split(","))).orElse(null))
                    .notifyUrl(notifyUrl)
                    .build());
            return this.getAsyncResponse(channelId, channelHotelId, supplierId, operationToken);
        }
        new SingleHotelSetupTask(supplierId, channelId, channelHotelId, Optional.ofNullable(procedure).map(proc -> List.of(proc.split(","))).orElse(null), notifyUrl, operationToken, ignoreStatusCheck).triggerNewTask(httpScheduleCenterService);
        return this.getAsyncResponse(channelId, channelHotelId, supplierId, operationToken);
    }

    private ChannelHotelVO getAsyncResponse(String channelId, String channelHotelId, String supplierId, String operationToken) {
        return translator.mapWithOutInfo(new ChannelHotelDTO()
                .setSupplierId(supplierId)
                .setChannelId(channelId)
                .setChannelHotelId(channelHotelId)
                .setOperationToken(operationToken)
        );
    }
}
