package com.derbysoft.next.propertyconnect.channel.task.client.connectivityoperationapi;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * @Created by <AUTHOR> on 2023/6/10
 */


@NoArgsConstructor
@AllArgsConstructor
@Data
public class OperationApiQueryResponse {
    String propertyOwner;//Required
    String propertyPartner;  //Required
    String propertyCode; //Required
    String partnerPropertyCode; // Optional
    Map<String, Object> responseHeader;
    List<Room> rooms;
    List<RatePlan> ratePlans;
    List<Product> products;


    @Data
    public static class Room {
        String name;
        String partnerRoomTypeCode;
        String status;
    }

    @Data
    public static class RatePlan {
        String name;
        String partnerRatePlanCode;
        String status;
    }

    @Data
    public static class Product {
        String partnerRoomCode;
        String partnerRatePlanCode;
        String status;
    }

}
