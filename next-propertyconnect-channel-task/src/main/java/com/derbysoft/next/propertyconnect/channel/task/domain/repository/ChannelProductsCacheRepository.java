package com.derbysoft.next.propertyconnect.channel.task.domain.repository;

import com.derbysoft.next.propertyconnect.channel.task.domain.entity.ChannelProductsPO;
import org.springframework.data.mongodb.repository.MongoRepository;


public interface ChannelProductsCacheRepository extends MongoRepository<ChannelProductsPO, String> {

    ChannelProductsPO findByChannelIdAndChannelHotelId(String channelId, String channelHotelId);

    ChannelProductsPO findFirstByChannelIdOrderByRetrieveDateDesc(String channelId);

}
