package com.derbysoft.next.propertyconnect.channel.task.controller.vo.hotel;

import com.derbysoft.next.propertyconenct.channel.common.validation.BusinessIdFormat;
import com.derbysoft.next.propertyconenct.channel.common.validation.PositionFormat;
import com.derbysoft.next.propertyconnect.channel.task.controller.vo.ItemStatus;
import com.derbysoft.next.propertyconnect.channel.task.controller.vo.SyncStatus;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Email;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
@JsonView({ChannelHotelVO.Request.class})
public class ChannelHotelInfo {
	@Schema(example = "PROPERTYCONNECT-CNFLAPDSKD", description = "Required at retry=true")
	@BusinessIdFormat
	private String id;
	@Schema(example = "Test Hotel")
	private String name;
	@Schema(example = "desp")
	private String description;
	private ItemStatus status;
	private SyncStatus syncStatus;
	private String errorCode;
	private String errorMessage;
	@Schema(example = "USD")
	private String currency;
	@Schema(example = "America/New_York")
	private String timezone;
	@Valid
	private Address address;
	@Valid
	private Position position;
	@Valid
	private List<ContactsItem> contacts;
	private Map<String, Object> i18n;
	private Map<String, Object> extensions;

	@Data
	@JsonView({ChannelHotelVO.Request.class})
	public static class Address{
		@Schema(example = "US")
		private String countryCode;
		@Schema(example = "NewYork")
		private String stateName;
		@Schema(example = "NYC")
		private String cityCode;
		@Schema(example = "10001")
		private String postalCode;
		@Schema(example = "Times Square, New York, NY 10036, United States")
		private String line1;
		private String line2;
	}

	@Data
	@JsonView({ChannelHotelVO.Request.class})
	public static class Position{
		@Schema(example = "40.7589")
		@PositionFormat
		private String logitude;
		@Schema(example = "73.9851")
		@PositionFormat
		private String latitude;
	}

	@Data
	@JsonView({ChannelHotelVO.Request.class})
	public static class ContactsItem{
		@Schema(example = "Picard")
		private String surName;
		@Schema(example = "Jean-Luc")
		private String givenName;
		@ArraySchema(schema = @Schema(example = "+1#5551234567"))
		private List<String> phones;
		@ArraySchema(schema = @Schema(example = "<EMAIL>"))
		@Email
		private List<String> emails;
		private Map<String, Object> extensions;
	}
}
