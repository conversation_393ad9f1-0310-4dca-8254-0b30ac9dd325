//package com.derbysoft.next.propertyconnect.channel.task.client.interceptor.retry;
//
//import okhttp3.Interceptor;
//import okhttp3.Request;
//import okhttp3.Response;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.core.annotation.AnnotationUtils;
//import retrofit2.Invocation;
//
//import java.io.IOException;
//import java.util.Optional;
//
//@Configuration
//public class RetryInterceptor implements Interceptor {
//
//    @Override
//    public Response intercept(Chain chain) throws IOException {
//        Request request = chain.request();
//        var methodOptional = Optional.ofNullable(request.tag(Invocation.class))
//                .map(Invocation::method);
//
//        var retryAnnotation = methodOptional
//                .map(method -> AnnotationUtils.getAnnotation(method, Retry.class))
//                .orElse(null);
//
//        if (null == retryAnnotation) {
//            return chain.proceed(request);
//        }
//
//        int maxAttempts = retryAnnotation.maxAttempts();
//        long delayMillis = retryAnnotation.delayMillis();
//        Class<? extends Throwable>[] retryOnExceptions = retryAnnotation.retryOn();
//
//        Response response = null;
//        IOException lastException = null;
//
//        for (int attempt = 0; attempt < maxAttempts; attempt++) {
//            try {
//                response = chain.proceed(request);
//                if (response.isSuccessful()) {
//                    return response;
//                }
//            } catch (IOException e) {
//                lastException = e;
//            }
//
//            boolean shouldRetry = false;
//            if (lastException != null) {
//                for (Class<? extends Throwable> exceptionClass : retryOnExceptions) {
//                    if (exceptionClass.isInstance(lastException)) {
//                        shouldRetry = true;
//                        break;
//                    }
//                }
//            }
//
//            if (!shouldRetry) {
//                break;
//            }
//
//            try {
//                Thread.sleep(delayMillis);
//            } catch (InterruptedException e) {
//                Thread.currentThread().interrupt();
//                break;
//            }
//        }
//
//        if (lastException != null) {
//            throw lastException;
//        }
//
//        return response;
//    }
//}
