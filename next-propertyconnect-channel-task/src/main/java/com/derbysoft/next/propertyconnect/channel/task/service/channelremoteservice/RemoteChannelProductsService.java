package com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice;

import com.derbysoft.next.propertyconenct.channel.common.mapstruct.BaseMapper;
import com.derbysoft.next.propertyconenct.channel.common.utils.DateTimeUtil;
import com.derbysoft.next.propertyconenct.channel.common.utils.GeneratedMapper;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelHotelDTO;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelProductsDTO;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelRateInfo;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelRoomInfo;
import com.derbysoft.next.propertyconnect.channel.task.service.ChannelProductsService;
import com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.direct.RemoteChannelDirectService;
import com.derbysoft.next.propertyconnect.channel.task.util.ValidateUtil;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Created by <AUTHOR> on 2023/3/21
 */

public interface RemoteChannelProductsService extends RemoteChannelDirectService, ChannelProductsService {
    default Integer requestSpan() { return 0; }
    static String currentDate(){
        return LocalDateTime.now(ZoneOffset.UTC).format(DateTimeUtil.ISO_LOCAL_DATE_TIME_WITH_MILLISECOND);
    }


    @Mapper
    @AnnotateWith(GeneratedMapper.class)
    interface ProductDtoToHotelDto extends BaseMapper<ChannelProductsDTO, ChannelHotelDTO> {
        ProductDtoToHotelDto INSTANCE = Mappers.getMapper(ProductDtoToHotelDto.class);
        @Mapping(target = "productsInfo", source = "channelProducts")
        @Override
        ChannelHotelDTO map(ChannelProductsDTO channelProductsDTO);

        @InheritInverseConfiguration(name = "map")
        @Mapping(target = "retrieveDate", expression = "java(RemoteChannelProductsService.currentDate())")
        @Override
        ChannelProductsDTO reverseMap(ChannelHotelDTO channelHotelDTO);

        @AfterMapping
        default void afterReverseMap(ChannelHotelDTO source, @MappingTarget ChannelProductsDTO target) {
            Optional.ofNullable(target.getChannelProducts())
                    .ifPresent(channelProducts -> {
                        var rooms = Optional.ofNullable(source.getRoomsInfo())
                                .map(roomsInfo -> roomsInfo.stream().collect(Collectors.toMap(ChannelRoomInfo::getCode, roomInfo -> roomInfo)))
                                .orElse(Map.of());
                        var rates = Optional.ofNullable(source.getRatesInfo())
                                .map(ratesInfo -> ratesInfo.stream().collect(Collectors.toMap(ChannelRateInfo::getCode, rateInfo -> rateInfo)))
                                .orElse(Map.of());
                        channelProducts.forEach(product -> {
                            product.setChannelRoomName(Optional.ofNullable(product.getChannelRoomId()).map(rooms::get).map(ChannelRoomInfo::getName).orElse(null));
                            product.setChannelRateName(Optional.ofNullable(product.getChannelRateId()).map(rates::get).map(ChannelRateInfo::getName).orElse(null));
                        });
                    });
        }
    }

    @Override
    default ChannelHotelDTO execution(Operation operation, ChannelHotelDTO dto) {
        ValidateUtil.validateNull(dto, ChannelHotelDTO::getChannelId, ChannelHotelDTO::getChannelHotelId);
        if (Operation.GetProduct.equals(operation)) {
            return ProductDtoToHotelDto.INSTANCE.map(getChannelProducts(dto.getChannelId(), dto.getChannelHotelId()));
        }
        return null;
    }
}
