package com.derbysoft.next.propertyconnect.channel.task.util;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.concurrent.atomic.AtomicReference;

/**
 * @Created by <AUTHOR> on 2023/8/18
 */

@Component
public class EnvUtil {
    private static final AtomicReference<String> env = new AtomicReference<>();

    @Autowired
    void setEnv(@Value("${spring.profiles.active}") String env) {
        EnvUtil.env.set(env);
    }

    public static String getEnv() {
        return EnvUtil.env.get();
    }
}
