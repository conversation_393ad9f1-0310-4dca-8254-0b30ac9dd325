package com.derbysoft.next.propertyconnect.channel.task.service.mapping.impl.report;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.experimental.UtilityClass;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.ByteArrayOutputStream;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * @Created by <AUTHOR> on 12/8/2023
 */
@UtilityClass
public class ReportExcelGenerator {
    public static byte[] generateExcel(ReportExcel reportExcel) {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setConfig(objectMapper.getSerializationConfig()
                .with(MapperFeature.SORT_PROPERTIES_ALPHABETICALLY));
        ObjectNode excelData = objectMapper.valueToTree(reportExcel);

        try (Workbook workbook = new XSSFWorkbook()) {
            for (Iterator<Map.Entry<String, JsonNode>> it = excelData.fields(); it.hasNext(); ) {
                var entry = it.next();
                exportJsonToExcel(workbook, entry.getKey(), (ArrayNode) entry.getValue());
            }

            //workbook write to bytebuffer
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            workbook.write(byteArrayOutputStream);
            return byteArrayOutputStream.toByteArray();

        } catch (Exception e) {
            e.printStackTrace();
        }
        return new byte[0];
    }

    public static void exportJsonToExcel(Workbook workbook, String sheetName, ArrayNode jsonArray) throws Exception {
        Sheet sheet = workbook.createSheet(sheetName);

        Row headerRow = sheet.createRow(0);
        ObjectNode firstObject = (ObjectNode) jsonArray.get(0);

        // Use LinkedHashMap to maintain the order of fields
        Map<String, Cell> headerCells = new LinkedHashMap<>();
        int columnIndex = 0;

        for (Iterator<Map.Entry<String, JsonNode>> it = firstObject.fields(); it.hasNext(); ) {
            var field = it.next();
            Cell cell = headerRow.createCell(columnIndex++);
            cell.setCellValue(field.getKey());
            headerCells.put(field.getKey(), cell);
        }

        int rowIndex = 1;

        for (int i = 0; i < jsonArray.size(); i++) {
            ObjectNode jsonObject = (ObjectNode) jsonArray.get(i);
            Row dataRow = sheet.createRow(rowIndex++);
            columnIndex = 0;

            for (var field : headerCells.keySet()) {
                Cell cell = dataRow.createCell(columnIndex++);
                if (jsonObject.has(field)) {
                    var fieldValue = jsonObject.get(field);
                    if (fieldValue.isInt()) {
                        cell.setCellValue(fieldValue.asInt());
                    } else if (fieldValue.isTextual()) {
                        cell.setCellValue(fieldValue.asText());
                    } else if (fieldValue.isArray()) {
                        cell.setCellValue(fieldValue.toString());
                    } else if (fieldValue.isObject()) {
                        cell.setCellValue(fieldValue.toString());
                    }
                }
            }
        }

        for (int i = 0; i < firstObject.size(); i++) {
            sheet.autoSizeColumn(i);
        }
    }
}
