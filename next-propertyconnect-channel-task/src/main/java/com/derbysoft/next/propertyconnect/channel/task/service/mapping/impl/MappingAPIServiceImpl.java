package com.derbysoft.next.propertyconnect.channel.task.service.mapping.impl;

import com.derbysoft.next.commons.core.logsupport.handler.PerfLogHandler;
import com.derbysoft.next.propertyconenct.channel.common.exception.response.UnifyResult;
import com.derbysoft.next.propertyconenct.channel.common.mapstruct.BaseMapper;
import com.derbysoft.next.propertyconenct.channel.common.mapstruct.DateTimeMapper;
import com.derbysoft.next.propertyconenct.channel.common.utils.Constants;
import com.derbysoft.next.propertyconenct.channel.common.utils.GeneratedMapper;
import com.derbysoft.next.propertyconnect.channel.task.controller.vo.mapping.MappingVO;
import com.derbysoft.next.propertyconnect.channel.task.controller.vo.mapping.PredictionMappingVO;
import com.derbysoft.next.propertyconnect.channel.task.controller.vo.mapping.PredictionModel;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelProductsDTO;
import com.derbysoft.next.propertyconnect.channel.task.service.MappingAPIService;
import com.derbysoft.next.propertyconnect.channel.task.service.mapping.MappingPredictionService;
import com.derbysoft.next.propertyconnect.channel.task.service.mapping.MappingService;
import lombok.RequiredArgsConstructor;
import org.mapstruct.*;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * @Created by <AUTHOR> on 2023/4/12
 */

@RequiredArgsConstructor
@Service
public class MappingAPIServiceImpl implements MappingAPIService {

    private final MappingService mappingService;
    private final MappingVOTranslator mappingVOTranslator;
    private final PredictionMappingVOTranslator predictionTranslator;
    private final MappingPredictionService mappingPredictionService;

    @Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    @AnnotateWith(GeneratedMapper.class)
    public interface MappingVOTranslator extends BaseMapper<ChannelProductsDTO, MappingVO>, DateTimeMapper {
        @Mapping(source = "channelProducts", target = "mappings")
        @Override
        MappingVO map(ChannelProductsDTO channelProductsDTO);

        @Mapping(source = "mappings", target = "channelProducts")
        @Override
        ChannelProductsDTO reverseMap(MappingVO mappingVO);
    }


    @Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    @AnnotateWith(GeneratedMapper.class)
    public interface PredictionMappingVOTranslator extends BaseMapper<ChannelProductsDTO, PredictionMappingVO> {
        @Mapping(source = "channelProducts", target = "mappings")
        @Override
        PredictionMappingVO map(ChannelProductsDTO channelProductsDTO);
    }

    @Override
    public UnifyResult<PredictionMappingVO> getPredictMapping(String channelId, String channelHotelId, String hotelId, PredictionModel predictionModel, Boolean refresh, Double threshold, Integer candidateCount, Boolean enableEraseHeader, String echoToken) {
        if (StringUtils.hasText(echoToken) && null != PerfLogHandler.currentHandler()) {
            var token = PerfLogHandler.currentHandler().getToken();
            PerfLogHandler.currentHandler().message(Constants.Perf.ECHO_TOKEN_DERBY, token);
            PerfLogHandler.currentHandler().setToken(echoToken);
        }
        return UnifyResult.from(() -> predictionTranslator.map(mappingPredictionService.getAIMapping(channelId, channelHotelId, hotelId, refresh, threshold)));
    }

    @Override
    public UnifyResult<MappingVO> getMapping(String channelId, String channelHotelId, Boolean snapshot) {
        return UnifyResult.from(() -> mappingVOTranslator.map(mappingService.getChannelProductsMapping(channelId, channelHotelId, snapshot)));
    }

    @Override
    public UnifyResult<MappingVO> updateMapping(String channelId, String channelHotelId, MappingVO mappingVO) {
        return UnifyResult.from(() -> mappingVOTranslator.map(mappingService.setChannelProductsMapping(channelId, channelHotelId, mappingVOTranslator.reverseMap(mappingVO))));
    }

}
