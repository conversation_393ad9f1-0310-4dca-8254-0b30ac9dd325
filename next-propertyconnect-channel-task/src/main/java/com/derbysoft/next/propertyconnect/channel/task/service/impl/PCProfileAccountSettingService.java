package com.derbysoft.next.propertyconnect.channel.task.service.impl;

import com.derbysoft.next.propertyconenct.channel.common.utils.JSONUtil;
import com.derbysoft.next.propertyconnect.channel.task.controller.vo.ChannelAccountVO;
import com.derbysoft.next.propertyconnect.channel.task.service.AccountSettingService;
import com.derbysoft.next.propertyconnect.channel.task.service.RemoteService;
import lombok.RequiredArgsConstructor;
import lombok.experimental.ExtensionMethod;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Map;
import java.util.Optional;

/**
 * @Created by <AUTHOR> on 2023/5/4
 */

@Service
@RequiredArgsConstructor
@ExtensionMethod(JSONUtil.class)
public class PCProfileAccountSettingService implements AccountSettingService {

    public static final String KEY_API_KEY = "apiKey";
    public static final String KEY_USER_NAME = "userName";
    public static final String KEY_PASSWORD = "password";
    private final RemoteService remoteService;

    @Override
    public Map<String, Object> getAccountSettings(String channelId, String channelHotelId) {
        return Optional.ofNullable(getGroupHotelLevelCredentials(channelId, channelHotelId))
                .orElseGet(() -> getChannelLevelAccountSetting(channelId));
    }

    @Nullable
    private ChannelAccountVO.AccountSetting getChannelLevelAccountSetting(String channelId) {
        return Optional.ofNullable(channelId)
                .filter(StringUtils::hasText)
                .flatMap(arg -> remoteService.getDistributorDetails(channelId))
                .map(channelSettings -> channelSettings.getMap("$.settings", String.class))
                .filter(channelSettings -> channelSettings.containsKey(KEY_API_KEY)
                        || (channelSettings.containsKey(KEY_USER_NAME) && channelSettings.containsKey(KEY_PASSWORD)))
                .map(channelSettings -> new ChannelAccountVO.AccountSetting()
                        .setUsername(Optional.ofNullable(channelSettings.get(KEY_USER_NAME)).map(Object::toString).orElse(null))
                        .setPassword(Optional.ofNullable(channelSettings.get(KEY_PASSWORD)).map(Object::toString).orElse(null))
                        .setApiKey(Optional.ofNullable(channelSettings.get(KEY_API_KEY)).map(Object::toString).orElse(null))
                )
                .orElse(null);
    }

    private ChannelAccountVO.AccountSetting getGroupHotelLevelCredentials(String channelId, String channelHotelId) {
        return Optional.ofNullable(channelHotelId)
                .filter(StringUtils::hasText)
                .flatMap(arg -> remoteService.uniqueHotelConnections(channelId, channelHotelId))
                .map(hotelMapping -> hotelMapping.getMap("$.channelSettings", String.class))
                .filter(channelSettings -> "Group".equals(channelSettings.get("hotelType")))
                .filter(channelSettings -> channelSettings.containsKey(KEY_API_KEY)
                        || (channelSettings.containsKey(KEY_USER_NAME) && channelSettings.containsKey(KEY_PASSWORD)))
                .map(channelSettings -> new ChannelAccountVO.AccountSetting()
                        .setUsername(Optional.ofNullable(channelSettings.get(KEY_USER_NAME)).map(Object::toString).orElse(null))
                        .setPassword(Optional.ofNullable(channelSettings.get(KEY_PASSWORD)).map(Object::toString).orElse(null))
                        .setApiKey(Optional.ofNullable(channelSettings.get(KEY_API_KEY)).map(Object::toString).orElse(null))
                )
                .orElse(null);
    }

    @Override
    public Map<String, Object> saveOrUpdateAccountSettings(String channelId, String channelHotelId, Map<String, Object> accountSettings) {
        throw new UnsupportedOperationException("Account in Profile Service can not be updated.");
    }

    @Override
    public void deleteAccountSettings(String channelId, String channelHotelId) {
        throw new UnsupportedOperationException("Account in Profile Service can not be updated.");
    }
}
