package com.derbysoft.next.propertyconnect.channel.task.config;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * @Created by <AUTHOR> on 2023/6/8
 */

public class EnhancedObjectMapper extends ObjectMapper {
    public EnhancedObjectMapper() {
        super();
        this.setSerializationInclusion(JsonInclude.Include.NON_EMPTY);
        this.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

    }
}
