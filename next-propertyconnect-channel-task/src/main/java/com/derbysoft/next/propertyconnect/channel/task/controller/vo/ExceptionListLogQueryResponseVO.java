package com.derbysoft.next.propertyconnect.channel.task.controller.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDate;
import java.util.Comparator;
import java.util.Set;
import java.util.TreeSet;

/**
 * @Created by <AUTHOR> on 2023/3/23
 */

@Data
public class ExceptionListLogQueryResponseVO {
    @Schema(example = "AGODA")
    private String channelId;
    @Schema(example = "HOTEL001")
    private String hotelId;
    @Schema(example = "ROOM001")
    private String roomId;
    @Schema(example = "RATEPLAN001")
    private String rateId;
    @Schema(example = "2000-01-01")
    private LocalDate date;
    @Schema(example = "15")
    private Integer totalErrorCount;
    private final Set<ErrorSummary> errorDetails = new TreeSet<>(Comparator.comparing(ErrorSummary::getCode));

    @Data
    @Builder
    public static class ErrorSummary {
        @Schema(example = "15")
        private Integer count;
        @Schema(example = "2209")
        private String code;
        @Schema(example = "Rate plan: RATEPLAN001 does not exist")
        private String message;
    }

}
