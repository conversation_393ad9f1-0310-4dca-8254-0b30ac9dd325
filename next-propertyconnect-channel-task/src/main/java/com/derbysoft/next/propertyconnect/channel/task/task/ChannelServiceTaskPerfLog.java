package com.derbysoft.next.propertyconnect.channel.task.task;

import com.derbysoft.next.commons.core.logsupport.constant.LogConst;
import com.derbysoft.next.commons.boot.logsupport.annotation.perf.PerfLog;
import com.derbysoft.next.commons.boot.logsupport.annotation.perf.PerfParameter;
import com.derbysoft.next.propertyconenct.channel.common.utils.Constants;

import java.lang.annotation.*;

/**
 * @Created by <AUTHOR> on 2023/6/2
 */


@Inherited
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
@PerfLog(Constants.Perf.CONSUME_TASK)
@PerfParameter(name = LogConst.Parameter.ECHO_TOKEN, value = "#task?.echoToken")
@PerfParameter(name = Constants.Perf.CHANNEL, value = "#task?.channel")
@PerfParameter(name = Constants.Perf.SUPPLIER, value = "#task?.supplier")
@PerfParameter(name = Constants.Perf.HOTEL_SUPPLIER, value = "#task?.supplierHotel")
@PerfParameter(name = Constants.Perf.HOTEL_CHANNEL, value = "#task?.channelHotel")
@PerfParameter(name = Constants.Perf.START_DATE, value = "#task?.startDate")
@PerfParameter(name = Constants.Perf.END_DATE, value = "#task?.endDate")
@PerfParameter(name = Constants.Perf.HOTEL, value = "#task?.hotel")
@PerfParameter(name = Constants.Perf.ROOMTYPE_CHANNEL, value = "#task?.channelRooms")
@PerfParameter(name = Constants.Perf.RATEPLAN_CHANNEL, value = "#task?.channelRates")
@PerfParameter(name = Constants.Perf.PRODUCT_CODE, value = "#task?.channelProducts")
public @interface ChannelServiceTaskPerfLog {
}
