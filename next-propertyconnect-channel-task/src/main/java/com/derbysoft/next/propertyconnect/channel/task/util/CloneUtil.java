package com.derbysoft.next.propertyconnect.channel.task.util;

import com.derbysoft.next.propertyconenct.channel.common.utils.GeneratedMapper;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.*;
import org.mapstruct.AnnotateWith;
import org.mapstruct.Mapper;
import org.mapstruct.control.DeepClone;
import org.mapstruct.factory.Mappers;

@Mapper(mappingControl = DeepClone.class)
@AnnotateWith(GeneratedMapper.class)
public interface CloneUtil {

    CloneUtil INSTANCE = Mappers.getMapper(CloneUtil.class);

    ChannelProductsDTO clone(ChannelProductsDTO in);

    ChannelProductsDTO.Product clone(ChannelProductsDTO.Product in);

    ChannelHotelDTO clone(ChannelHotelDTO in);

    ChannelHotelInfo clone(ChannelHotelInfo in);

    ChannelRoomInfo clone(ChannelRoomInfo in);

    ChannelRateInfo clone(ChannelRateInfo in);

}
