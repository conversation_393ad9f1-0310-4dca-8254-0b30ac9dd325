package com.derbysoft.next.propertyconnect.channel.task.service.impl;

import com.derbysoft.next.propertyconenct.channel.common.mapstruct.BaseMapper;
import com.derbysoft.next.propertyconenct.channel.common.utils.GeneratedMapper;
import com.derbysoft.next.propertyconenct.channel.common.utils.JSONUtil;
import com.derbysoft.next.propertyconnect.channel.task.controller.vo.ExceptionListLogQueryResponseVO;
import com.derbysoft.next.propertyconnect.channel.task.controller.vo.ExceptionListResponseVO;
import com.derbysoft.next.propertyconnect.channel.task.domain.entity.SynchronizerExceptionListLogPO;
import com.derbysoft.next.propertyconnect.channel.task.service.RemoteService;
import com.derbysoft.next.propertyconnect.channel.task.service.SynchronizerProxyAPIService;
import com.derbysoft.next.propertyconnect.channel.task.task.exceptionlistremove.ChannelHotelProduct;
import lombok.RequiredArgsConstructor;
import lombok.experimental.ExtensionMethod;
import org.mapstruct.AnnotateWith;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Created by <AUTHOR> on 2023/3/27
 */

@ExtensionMethod(JSONUtil.class)
@Service
@RequiredArgsConstructor
public class SynchronizerProxyServiceImpl implements SynchronizerProxyAPIService {

    @Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
    @AnnotateWith(GeneratedMapper.class)
    interface SynchronizerProxyTranslator extends BaseMapper<SynchronizerExceptionListLogPO, ExceptionListLogQueryResponseVO> { }
    private final SynchronizerProxyTranslator translator;
    private final MongoTemplate mongoTemplate;
    private final RemoteService remoteService;

    @Override
    public Collection<ExceptionListLogQueryResponseVO> getExceptionListRemovalStatistics(String channelId, LocalDate date, Integer greaterThan) {
        return translator.map(this.findByChannelAndDate(channelId, date, greaterThan));
    }

    public List<SynchronizerExceptionListLogPO> findByChannelAndDate(String channel, LocalDate date, Integer greaterThan) {
        Query query = new Query();
        if (channel != null) {
            query.addCriteria(Criteria.where(SynchronizerExceptionListLogPO.Fields.channelId).is(channel));
        }
        if (date != null) {
            query.addCriteria(Criteria.where(SynchronizerExceptionListLogPO.Fields.date).is(date));
        }
        if (greaterThan != null) {
            query.addCriteria(Criteria.where(SynchronizerExceptionListLogPO.Fields.totalErrorCount).gte(greaterThan));
        }
        return mongoTemplate.find(query, SynchronizerExceptionListLogPO.class);
    }

    @Override
    public Collection<ExceptionListResponseVO> getExceptionList(String channelId, String hotelId, String roomId, String rateId) {
        return remoteService.getExceptionList(Integer.MAX_VALUE, channelId, hotelId, roomId, rateId)
                .map(result -> result.getCollection("$.value", ExceptionListResponseVO.class))
                .orElse(List.of());
    }

    @Override
    public Collection<ExceptionListResponseVO> deleteSingleExceptionList(String channelId, String hotelId, String roomId, String rateId) {
        remoteService.removeFromExceptionList(ChannelHotelProduct.builder().channel(channelId).hotel(hotelId).roomType(roomId).ratePlan(rateId).build().toExceptionListFormat());
        return this.getExceptionList(channelId, hotelId, roomId, rateId);
    }

    @Override
    public void batchDeleteExceptionList(Collection<ChannelHotelProduct> channelHotelProducts) {
        remoteService.removeFromExceptionList(channelHotelProducts.stream().map(ChannelHotelProduct::toExceptionListFormat).collect(Collectors.toList()));
    }
}
