package com.derbysoft.next.propertyconnect.channel.task.util;

import lombok.Getter;

import java.util.List;

/**
 * @Created by <AUTHOR> on 2022/6/24
 */

@Getter
public class ConsumeException extends RuntimeException {
    private final List<?> failedItems;

    public ConsumeException(String message, List<?> failedItems) {
        super(message + failedItems.toString());
        this.failedItems = failedItems;
    }
}
