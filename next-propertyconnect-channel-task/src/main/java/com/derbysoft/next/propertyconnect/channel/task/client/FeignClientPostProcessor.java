package com.derbysoft.next.propertyconnect.channel.task.client;

import com.derbysoft.next.propertyconnect.channel.task.service.RemoteService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.cloud.openfeign.FeignAutoConfiguration;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.FeignClientFactoryBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@AutoConfigureBefore(FeignAutoConfiguration.class)
@Configuration
public class FeignClientPostProcessor implements BeanPostProcessor {

    private final Map<Class<?>, Object> feignClients = new HashMap<>();

    @Override
    public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
        if (!bean.getClass().isAssignableFrom(FeignClientFactoryBean.class)) {
            return bean;
        }
        try {
            var beanClass = Class.forName(beanName);
            if (beanClass.isAnnotationPresent(FeignClient.class)) {
                log.info("FeignClient [{}] registered for dynamic routing", beanName);
                feignClients.put(beanClass, bean);
            }
        } catch (ClassNotFoundException e) {
            //ignore
        }
        return bean;
    }

    @Bean
    public RemoteService remoteServiceProxy() {
        FeignClientInvocationHandler handler = new FeignClientInvocationHandler(feignClients);
        return handler.createProxy(RemoteService.class);
    }

}
