package com.derbysoft.next.propertyconnect.channel.task.service.mapping.impl.report;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.Data;

import java.util.List;

/**
 * @Created by <AUTHOR> on 12/7/2023
 */

@Data
@JsonPropertyOrder({"MappingResult", "PCRoomType", "PCRatePlan", "ChannelRoomType", "ChannelRatePlan"})
public class ReportExcel {
    @JsonProperty("MappingResult")
    List<MappingResult> mappingResult;
    @JsonProperty("PCRoomType")
    Object pCRoomType;
    @JsonProperty("PCRatePlan")
    Object pCRatePlan;
    @JsonProperty("ChannelRoomType")
    Object channelRoomType;
    @JsonProperty("ChannelRatePlan")
    Object channelRatePlan;
}
