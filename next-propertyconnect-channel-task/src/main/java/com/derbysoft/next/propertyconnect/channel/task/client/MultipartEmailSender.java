package com.derbysoft.next.propertyconnect.channel.task.client;

import com.fasterxml.jackson.databind.ObjectMapper;
import okhttp3.*;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

@Configuration
public class MultipartEmailSender {

    private static final ObjectMapper objectMapper = new ObjectMapper();
    private static final OkHttpClient client = new OkHttpClient();

    public static String sendRawEmailWithAttachment(
            String url,
            Object sendRawEmailRequestJson,
            List<MultipartFile> attachments,
            String traceToken
    ) throws IOException {
        String mailJson = objectMapper.writeValueAsString(sendRawEmailRequestJson);

        MultipartBody.Builder multipartBuilder = new MultipartBody.Builder().setType(MultipartBody.FORM);

        multipartBuilder.addFormDataPart(
                "mail",
                null,
                RequestBody.create(mailJson, MediaType.parse("application/json; charset=UTF-8"))
        );

        if (attachments != null) {
            for (MultipartFile file : attachments) {
                multipartBuilder.addFormDataPart(
                        "attachments",
                        file.getOriginalFilename(),
                        RequestBody.create(file.getBytes(),
                                MediaType.parse(file.getContentType() != null ? file.getContentType() : "application/octet-stream"))
                );
            }
        }

        Request request = new Request.Builder()
                .url(url)
                .addHeader("traceToken", traceToken)
                .post(multipartBuilder.build())
                .build();

        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) throw new IOException("Unexpected code " + response);
            return response.body().string();
        }
    }
}
