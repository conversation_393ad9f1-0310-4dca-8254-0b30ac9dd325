package com.derbysoft.next.propertyconnect.channel.task.service.storageservice;

import com.derbysoft.next.propertyconenct.channel.common.utils.GeneratedMapper;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelHotelDTO;
import com.derbysoft.next.propertyconnect.channel.task.domain.entity.ChannelHotelPO;
import com.derbysoft.next.propertyconnect.channel.task.service.PreProcessingService;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;

/**
 * @Created by <AUTHOR> on 2023/6/5
 */

@Service
public class ChannelHotelStorageService extends CommonStorageService<ChannelHotelPO> {

    private final ObjectProvider<PreProcessingService> preProcessingProvider;

    public ChannelHotelStorageService(MongoTemplate mongoTemplate, ObjectProvider<PreProcessingService> preProcessingProvider) {
        super(mongoTemplate, ChannelHotelPO.class, Mappers.getMapper(ChannelHotelDtoTranslator.class));
        this.preProcessingProvider = preProcessingProvider;
    }

    @Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    @AnnotateWith(GeneratedMapper.class)
    public interface ChannelHotelDtoTranslator extends DTOtoPOTranslator<ChannelHotelPO> {
        @Mapping(source = "hotelInfo.status", target = "status")
        @Mapping(source = "hotelInfo.syncStatus", target = "syncStatus")
        @Mapping(source = "hotelInfo.lastOperationToken", target = "lastOperationToken")
        @Override
        void fillIn(ChannelHotelDTO from,@MappingTarget ChannelHotelPO to);
    }


    @Override
    public ChannelHotelDTO getChannelHotel(String channelId, String channelHotelId) {
        var channelHotel = super.getChannelHotel(channelId, channelHotelId);
        preProcessingProvider.stream()
                .filter(preProcessing -> preProcessing.channel().equals(channelId))
                .forEach(preProcessing -> preProcessing.postProcess(channelHotel));
        return channelHotel;
    }

    @Override
    public ChannelHotelDTO saveIncrementalChannelProduct(ChannelHotelDTO channelHotel) {
        preProcessingProvider.stream()
                .filter(preProcessing -> preProcessing.channel().equals(channelHotel.getChannelId()))
                .forEach(preProcessing -> preProcessing.preProcess(channelHotel));
        return super.saveIncrementalChannelProduct(channelHotel);
    }
}
