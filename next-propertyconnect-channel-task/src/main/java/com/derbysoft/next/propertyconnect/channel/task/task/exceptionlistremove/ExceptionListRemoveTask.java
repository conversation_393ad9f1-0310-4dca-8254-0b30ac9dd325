package com.derbysoft.next.propertyconnect.channel.task.task.exceptionlistremove;

import com.derbysoft.extension.schedulecentersupport.annotation.ScheduleCenterTaskExecutor;
import com.derbysoft.next.commons.boot.logsupport.annotation.perf.PerfParameter;
import com.derbysoft.next.commons.core.logsupport.constant.LogConst;
import com.derbysoft.next.commons.core.logsupport.handler.PerfLogHandler;
import com.derbysoft.next.propertyconenct.channel.common.utils.Constants;
import com.derbysoft.next.propertyconenct.channel.common.utils.JSONUtil;
import com.derbysoft.next.propertyconnect.channel.task.config.schedulecenter.ChannelServiceTask;
import com.derbysoft.next.propertyconnect.channel.task.config.schedulecenter.ChannelServiceTypedTask;
import com.derbysoft.next.propertyconnect.channel.task.domain.repository.SynchronizerExceptionListLogRepository;
import com.derbysoft.next.propertyconnect.channel.task.service.RemoteService;
import com.derbysoft.next.propertyconnect.channel.task.task.ChannelServiceTaskPerfLog;
import com.derbysoft.next.propertyconnect.channel.task.util.BusinessJSONUtil;
import com.derbysoft.next.propertyconnect.channel.task.util.ValidateUtil;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import lombok.experimental.ExtensionMethod;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Created by <AUTHOR> on 2023/2/28
 */

@ScheduleCenterTaskExecutor("PROPERTYCONNECT_CHANNEL.CHANNEL_APT_EXCEPTIONLIST_REMOVE_SCHEDULE.SINGLE")
@ExtensionMethod({JSONUtil.class, BusinessJSONUtil.class})
@RequiredArgsConstructor
public class ExceptionListRemoveTask implements ChannelServiceTypedTask {

    private final RemoteService remoteService;
    private final SynchronizerExceptionListLogRepository exceptionListRepository;
    private final ChannelHotelProductTranslator translator;

    @Override
    public TaskType taskType() {
        return TaskType.ChannelAptExceptionListRemove;
    }

    @ChannelServiceTaskPerfLog
    @PerfParameter(name = LogConst.Parameter.PROCESS, value = "'Delete'")
    @Override
    public void doExecute(ChannelServiceTask task) {
        ValidateUtil.validateNull(task, ChannelServiceTask::getChannel);

        var perfTracer = PerfLogHandler.currentHandler();
        var productToBeRemoved = getProductToBeRemoved(task.getChannel(), perfTracer);
        perfTracer.message(Constants.Perf.PRODUCT_CNT, productToBeRemoved.size());
        remoteService.removeFromExceptionList(productToBeRemoved);
    }

    private Collection<ChannelHotelProduct> getExceptionListProducts(String distributorId) {
        return remoteService.getExceptionList(distributorId)
                .filter(result -> result.getBoolean("$.success"))
                .map(result -> result.getCollection("$.value.data"))
                .map(exceptionList -> exceptionList.stream()
                        .map(exceptionItem -> ChannelHotelProduct.builder()
                                .channel(distributorId)
                                .hotel(exceptionItem.getString("$.hotel"))
                                .roomType(exceptionItem.getString("$.roomType"))
                                .ratePlan(exceptionItem.getString("$.ratePlan"))
                                .errCode(exceptionItem.getString("$.errCode"))
                                .errMessage(exceptionItem.getString("$.errMessage"))
                                .build())
                        .collect(Collectors.toSet()))
                .orElseThrow(() -> new IllegalStateException("No exception list found for channel: " + distributorId));
    }

    private Collection<String> getProductToBeRemoved(String distributorId, PerfLogHandler perfTracer) {
        return getExceptionListProducts(distributorId).stream()
                .collect(Collectors.groupingBy(ChannelHotelProduct::getHotel))
                .entrySet()
                .stream()
                .flatMap(entry -> {
                    var distributorHotelId = entry.getKey();
                    List<String> pcProducts;
                    try {
                        pcProducts = remoteService.goDistributorHotel(distributorHotelId, distributorId)
                                .filter(result -> result.isActive())
                                .map(result -> result.getCollection("$.products[?(@.status == 'Actived')]").stream()
                                        .map(product -> product.getString("$.roomId") + product.getString("$.rateId"))
                                        .distinct()
                                        .collect(Collectors.toList()))
                                .orElseGet(List::of);
                    } catch (FeignException e) {
                        perfTracer.message(Constants.Perf.ERR_CODE, Constants.UNKNOWN);
                        perfTracer.messages(Constants.Perf.ERR_MSG, distributorId + "-" + distributorHotelId);
                        perfTracer.fail();
                        return entry.getValue().stream();
                    }
                    var exceptionProducts = entry.getValue();
                    for (ChannelHotelProduct exceptionProduct : exceptionProducts) {
                        if (pcProducts.contains(exceptionProduct.getRoomType() + exceptionProduct.getRatePlan())) {
                            exceptionProduct.setDeletable(true);
                        }
                    }
                    return entry.getValue().stream();
                })
                .filter(ChannelHotelProduct::isDeletable)
                .map(product -> {
                    perfTracer.messages(Constants.Perf.PRODUCT_CODE, String.format("%s-%s-%s", product.getHotel(), product.getRoomType(), product.getRatePlan()));
                    saveDeleteProductLog(product);
                    return product;
                })
                .map(ChannelHotelProduct::toExceptionListFormat)
                .collect(Collectors.toList());
    }

    private void saveDeleteProductLog(ChannelHotelProduct product) {
        var mergedLog = exceptionListRepository.findByChannelIdAndHotelIdAndRoomIdAndRateIdAndDate(product.getChannel(), product.getHotel(), product.getRoomType(), product.getRatePlan(), LocalDate.now(ZoneId.of("UTC")))
                .map(repo -> {
                    translator.fillWithSummary(product, repo);
                    return repo;
                })
                .orElseGet(() -> {
                    var synchronizerExceptionListLogPO = translator.map(product);
                    translator.mapSummary(product, synchronizerExceptionListLogPO);
                    return synchronizerExceptionListLogPO;
                });
        exceptionListRepository.save(mergedLog);
    }

}
