package com.derbysoft.next.propertyconnect.channel.task.service.mapping.impl;

import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelProductsDTO;
import com.derbysoft.next.propertyconnect.channel.task.service.RemoteService;
import com.derbysoft.next.propertyconnect.channel.task.service.mapping.MappingService;
import lombok.RequiredArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * @Created by <AUTHOR> on 2023/4/19
 * Default mapping service retrieve product mappings from PC Profile service
 */

@Service
@RequiredArgsConstructor
@Primary
public class DefaultMappingServiceImpl implements MappingService {
    private final RemoteService remoteService;
    private final ObjectProvider<MappingService> mappingServices;

    @Override
    public String channel() {
        return "ALL";
    }

    @NotNull
    private Optional<MappingService> getHandler(String channelId) {
        return mappingServices.stream()
                .filter(mappingService -> mappingService.channel().equals(channelId))
                .findFirst();
    }

    @Override
    public ChannelProductsDTO getChannelProductsMapping(String channelId, String channelHotelId, Boolean snapshot) {
        return this.getHandler(channelId)
                .map(mappingService -> mappingService.getChannelProductsMapping(channelId, channelHotelId, snapshot))
                .orElse(null);
    }

    @Override
    public ChannelProductsDTO setChannelProductsMapping(String channelId, String channelHotelId, ChannelProductsDTO channelProductsDTO) {
        channelProductsDTO.setChannelId(channelId);
        channelProductsDTO.setChannelHotelId(channelHotelId);
        return this.getHandler(channelId)
                .map(mappingService -> mappingService.setChannelProductsMapping(channelId, channelHotelId, channelProductsDTO))
                .orElseThrow(() -> new UnsupportedOperationException("Distributor " + channelId + " currently does not support changing product mapping"));
    }

    @Override
    public ChannelProductsDTO deleteChannelProductsMapping(String channelId, String channelHotelId, ChannelProductsDTO channelProductsDTO) {
        throw new UnsupportedOperationException("Distributor " + channelId + " currently does not support delete product mapping");
    }
}
