package com.derbysoft.next.propertyconnect.channel.task.controller.vo.mapping;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Map;

/**
 * @Created by <AUTHOR> on 2023/4/19
 */

@Data
public class PredictionMappingVO {

    @Schema(example = "EXPEDIA")
    String channelId;

    @Schema(example = "CNLUQ5FKPF")
    String hotelId;

    @Schema(example = "430880")
    String channelHotelId;

    List<PredictionProductMappingVO> mappings;

    Map<String, Object> extensions;

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class PredictionProductMappingVO extends MappingVO.ProductMappingVO {

        @JsonIgnore
        Map<String, Object> extensions;
        String channelRoomName;
        String channelRateName;

        List<PredictionProductVO> candidateProducts;
        @Data
        public static class PredictionProductVO {
            @Schema(example = "RM")
            String roomId;
            String roomName;
            @Schema(example = "RO")
            String rateId;
            String rateName;
            @Schema(example = "0.8")
            Float predictionScore;

            Map<String, Object> extensions;
        }
    }
}
