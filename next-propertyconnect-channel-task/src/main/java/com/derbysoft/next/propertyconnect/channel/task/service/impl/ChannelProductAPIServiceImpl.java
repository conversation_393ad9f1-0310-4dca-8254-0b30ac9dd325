package com.derbysoft.next.propertyconnect.channel.task.service.impl;

import com.derbysoft.next.propertyconenct.channel.common.mapstruct.BaseMapper;
import com.derbysoft.next.propertyconenct.channel.common.mapstruct.DateTimeMapper;
import com.derbysoft.next.propertyconenct.channel.common.utils.GeneratedMapper;
import com.derbysoft.next.propertyconnect.channel.task.controller.vo.ChannelProductVO;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelProductsDTO;
import com.derbysoft.next.propertyconnect.channel.task.service.ChannelProductAPIService;
import lombok.RequiredArgsConstructor;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

/**
 * @Created by <AUTHOR> on 2023/6/5
 */

@Service
@RequiredArgsConstructor
public class ChannelProductAPIServiceImpl implements ChannelProductAPIService {
    private final ChannelProductsServiceProxy channelProductsService;
    private final LocalChannelProductsService localChannelProductsService;
    private final ChannelProductResponseTranslator responseTranslator = ChannelProductResponseTranslator.INSTANCE;
    private final LocalChannelProductsService.ChannelProductCacheTranslator productCacheMapper = Mappers.getMapper(LocalChannelProductsService.ChannelProductCacheTranslator.class);
    private final ProductCacheUploadTranslator uploadTranslator;

    @Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    @AnnotateWith(GeneratedMapper.class)
    interface ChannelProductResponseTranslator extends BaseMapper<ChannelProductsDTO, ChannelProductVO>, DateTimeMapper {
        ChannelProductResponseTranslator INSTANCE = Mappers.getMapper(ChannelProductResponseTranslator.class);
        @Mapping(source = "channelRoomId", target = "roomId")
        @Mapping(source = "channelRoomName", target = "roomName")
        @Mapping(source = "channelRateId", target = "rateId")
        @Mapping(source = "channelRateName", target = "rateName")
        ChannelProductVO.Product productMapper(ChannelProductsDTO.Product channelProductsDTO);
    }

    @Override
    public ChannelProductVO getChannelProducts(String channelId, String channelHotelId, String supplierId, String hotelSystemConnectionId, Boolean refresh, Boolean ignoreError) {
        return responseTranslator.map(channelProductsService.getChannelProducts(supplierId, channelId, channelHotelId, refresh, false));
    }

    @Override
    public ChannelProductVO getChannelProductsWithExtra(String channelId, String channelHotelId, String supplierId) {
        return responseTranslator.map(channelProductsService.getChannelProductsWithExtra(supplierId, channelId, channelHotelId));
    }

    @Override
    public ChannelProductVO uploadChannelProducts(String channelId, String channelHotelId, MultipartFile file) {
        var channelProductsPO = localChannelProductsService.saveChannelProductCache(uploadTranslator.processExcelFile(file));
        var dto = productCacheMapper.map(channelProductsPO);
        return responseTranslator.map(dto);
    }
}
