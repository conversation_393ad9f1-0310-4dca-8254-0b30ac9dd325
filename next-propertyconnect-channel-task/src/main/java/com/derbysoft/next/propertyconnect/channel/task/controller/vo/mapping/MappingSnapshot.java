package com.derbysoft.next.propertyconnect.channel.task.controller.vo.mapping;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Created by <AUTHOR> on 2023/4/25
 */

public interface MappingSnapshot {
    String getChannelId();
    String getChannelHotelId();
    List<? extends ProductSnapshot> getChannelProducts();

    interface ProductSnapshot {
        Boolean getMapped();
        String getChannelRoomId();
        Map<String, Object> getExtensions();
    }

    default List<String> getSnapshotSignature(Function<Map<String, Object>, String> extensionHandler) {
        return getChannelProducts()
                .stream()
                .map(productSnapshot -> getChannelId()
                        + getChannelHotelId()
                        + productSnapshot.getChannelRoomId()
                        + Boolean.TRUE.equals(productSnapshot.getMapped())
                        + extensionHandler.apply(productSnapshot.getExtensions())
                )
//                .map(DigestUtils::md5Hex)
                .collect(Collectors.toList());
    }
}
