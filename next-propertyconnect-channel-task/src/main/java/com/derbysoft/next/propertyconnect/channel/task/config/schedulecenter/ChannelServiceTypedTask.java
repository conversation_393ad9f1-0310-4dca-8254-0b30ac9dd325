package com.derbysoft.next.propertyconnect.channel.task.config.schedulecenter;

import com.derbysoft.extension.schedulecentersupport.ScheduleCenterTask;
import com.derbysoft.extension.schedulecentersupport.task.FlowingTask;
import com.derbysoft.schedulecenter.rpc.protocol.Task;

import java.util.List;

/**
 * @Created by <AUTHOR> on 2023/6/1
 */

public interface ChannelServiceTypedTask extends FlowingTask {

    enum TaskType {
        ChannelAptARIRefresh("PROPERTYCONNECT_CHANNEL.CHANNEL_APT_ARIREFRESH_TASK.SINGLE"),
        ChannelAptExceptionListRemove(null),
        ChannelAptHotelSetup("PROPERTYCONNECT_CHANNEL.CHANNEL_APT_HOTEL_SETUP_SCHEDULE.SINGLE");

        private final String nextHandler;
        public String getNextHandler() {
            return nextHandler;
        }
        TaskType(String nextHandler) {
            this.nextHandler = nextHandler;
        }
    }

    TaskType taskType();

    @Override
    default NextTasks nextTask(Task task) {
        var nextTasks = new NextTasks();
        nextTasks.setTarget(taskType().getNextHandler());
        var nextSCTask = nextTask(ChannelServiceTask.fromTask(task));
        if (null == nextSCTask) return null;
        nextTasks.setTasks(ChannelServiceTask.toTasks(nextSCTask));
        return nextTasks;
    }

    @Override
    default <T extends ScheduleCenterTask> void doExecute(T proxySelf, Task task) {
        ((ChannelServiceTypedTask) proxySelf).doExecute(ChannelServiceTask.fromTask(task));
    }

    default void doExecute(ChannelServiceTask task){}

    @Override
    default void doExecute(Task task){}

    default List<ChannelServiceTask> nextTask(ChannelServiceTask task){return null;}
}
