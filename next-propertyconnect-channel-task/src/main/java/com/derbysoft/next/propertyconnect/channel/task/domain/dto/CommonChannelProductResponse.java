package com.derbysoft.next.propertyconnect.channel.task.domain.dto;

import lombok.Data;

import java.util.List;

/**
 * @Created by <AUTHOR> on 10/9/2024
 */
@Data
public class CommonChannelProductResponse {
    private Header header;
    private String channelId;
    private String channelHotelId;
    private String channelHotelName;
    private List<RoomInfo> roomsInfo;
    private List<RateInfo> ratesInfo;
    private List<ProductInfo> productsInfo;

    @Data
    public static class Header {
        private String timestamp;
        private String echoToken;
    }

    @Data
    public static class RoomInfo {
        private String channelRoomId;
        private String roomName;
        private ItemStatus status;
    }

    @Data
    public static class RateInfo {
        private String channelRateId;
        private String rateName;
        private ItemStatus status;
    }

    @Data
    public static class ProductInfo {
        private String channelRoomId;
        private String channelRateId;
        private Boolean availStatus;
        private ItemStatus status;
    }

}
