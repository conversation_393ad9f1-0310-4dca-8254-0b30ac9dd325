package com.derbysoft.next.propertyconnect.channel.task.client.connectivityoperationapi;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

import java.util.HashMap;

/**
 * @Created by <AUTHOR> on 1/16/2025
 */

@EqualsAndHashCode(callSuper = true)
@SuperBuilder(toBuilder = true)
@Data
public class OperationApiCredentialRequest extends OperationApiBasicRequest{
    private HashMap<String, Object> credentials;
}
