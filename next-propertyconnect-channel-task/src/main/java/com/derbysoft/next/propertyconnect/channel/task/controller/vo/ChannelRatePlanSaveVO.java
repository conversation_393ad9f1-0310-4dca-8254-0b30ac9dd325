package com.derbysoft.next.propertyconnect.channel.task.controller.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Map;

/**
 * @Created by <AUTHOR> on 2023/4/28
 */

@Data
public class ChannelRatePlanSaveVO {

    public interface Request {}
    public interface Response {}

    @Schema(example = "SYNXIS")
    @JsonView({Response.class})
    private String channelId;

    @Schema(example = "HOTEL001")
    @JsonView({Response.class, Request.class})
    private String hotelId;

    @Schema(example = "1234")
    @JsonView({Response.class})
    private String channelHotelId;

    @JsonView({Response.class})
    @JsonProperty("ratePlans")
    private List<RatePlans> channelProducts;

    @JsonView({Response.class, Request.class})
    private Map<String, Object> extensions;

    @Data
    @EqualsAndHashCode
    @JsonView({Response.class, Request.class})
    public static class RatePlans {
        @Schema(example = "RATE001")
        private String channelRateId;
        @Schema(example = "SUCCESS")
        private String status;
        private Map<String, Object> extensions;
    }
}
