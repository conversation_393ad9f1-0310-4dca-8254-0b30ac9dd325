package com.derbysoft.next.propertyconnect.channel.task.service.mapping.impl;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;

/**
 * @Created by <AUTHOR> on 11/23/2023
 */


@Data
@RefreshScope
@ConfigurationProperties(prefix = "app.aimapping")
public class PredictionServiceConfigProperties {
    Double defaultThreshold = 0.0;
    String s3BucketName = "mapping4pc-feedback-data";
    String s3Region = "ap-southeast-1";
    String s3Arn = "";
    Boolean sendMail = true;
}
