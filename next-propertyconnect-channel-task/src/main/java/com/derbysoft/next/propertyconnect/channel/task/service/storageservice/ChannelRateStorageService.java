package com.derbysoft.next.propertyconnect.channel.task.service.storageservice;

import com.derbysoft.next.propertyconenct.channel.common.utils.GeneratedMapper;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelHotelDTO;
import com.derbysoft.next.propertyconnect.channel.task.domain.entity.ChannelHotelRatePO;
import com.derbysoft.next.propertyconnect.channel.task.util.CloneUtil;
import com.derbysoft.next.propertyconnect.channel.task.util.CollectionUtil;
import com.google.common.collect.Lists;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * @Created by <AUTHOR> on 2023/6/7
 */

@Service
public class ChannelRateStorageService extends CommonStorageService<ChannelHotelRatePO>{
    private final CloneUtil cloneUtil = CloneUtil.INSTANCE;
    protected ChannelRateStorageService(MongoTemplate mongoTemplate) {
        super(mongoTemplate, ChannelHotelRatePO.class, Mappers.getMapper(DTOtoRatePOTranslator.class));
    }

    @Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    @AnnotateWith(GeneratedMapper.class)
    public interface DTOtoRatePOTranslator extends DTOtoPOTranslator<ChannelHotelRatePO> {
        @Mapping(source = "rateInfo.code", target = "code")
        @Mapping(source = "rateInfo.status", target = "status")
        @Mapping(source = "rateInfo.syncStatus", target = "syncStatus")
        @Mapping(source = "rateInfo.lastOperationToken", target = "lastOperationToken")
        @Override
        void fillIn(ChannelHotelDTO from, @MappingTarget ChannelHotelRatePO to);
    }

    @Override
    public ChannelHotelDTO saveIncrementalChannelProduct(ChannelHotelDTO channelHotel) {
        if (channelHotel.getRatesInfo() == null) {
            return channelHotel;
        }
        CollectionUtil.forEachWithIndex(channelHotel.getRatesInfo(), (index, rateInfo) -> {
            var clone = cloneUtil.clone(channelHotel);
            clone.setRateInfo(rateInfo);
            rateInfo = super.saveIncrementalChannelProduct(clone).getRateInfo();
            channelHotel.getRatesInfo().set(index, rateInfo);
        });
        return channelHotel;
    }

    @Override
    public ChannelHotelDTO getChannelHotel(String channelId, String channelHotelId) {
        return this.queryChannelHotels(channelId, channelHotelId)
                .stream()
                .map(translator::reverseMap)
                .map(dto -> {
                    if (null != dto.getRateInfo()){
                        dto.setRatesInfo(Lists.newArrayList(dto.getRateInfo()));
                        dto.setRateInfo(null);
                    }
                    return dto;
                })
                .reduce((ov, nv) -> {
                    Optional.ofNullable(ov.getRatesInfo())
                            .ifPresent(rooms -> rooms.addAll(nv.getRatesInfo()));
                    return ov;
                })
                .orElse(null);
    }
}
