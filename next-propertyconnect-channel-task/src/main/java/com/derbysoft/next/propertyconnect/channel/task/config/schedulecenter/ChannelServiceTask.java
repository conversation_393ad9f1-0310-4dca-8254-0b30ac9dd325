package com.derbysoft.next.propertyconnect.channel.task.config.schedulecenter;

import com.derbysoft.next.propertyconenct.channel.common.mapstruct.BaseMapper;
import com.derbysoft.next.propertyconenct.channel.common.utils.GeneratedMapper;
import com.derbysoft.next.propertyconnect.channel.task.config.EnhancedObjectMapper;
import com.derbysoft.schedulecenter.rpc.protocol.Task;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.*;
import org.mapstruct.AnnotateWith;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.List;
import java.util.Map;

/**
 * @Created by <AUTHOR> on 2023/6/1
 */
@Builder(toBuilder = true)
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ChannelServiceTask {
    String id;
    String echoToken;
    String channel;
    String supplier;
    String supplierHotel;
    String channelHotel;
    String startDate;
    String endDate;
    String hotel;
    Integer priority;
    Boolean ignorePropertyStatusCheck;
    List<String> procedure;
    String notifyUrl;
    List<String> hotels;
    List<String> channelRooms;
    List<String> channelRates;
    List<String> channelProducts;
    Long createTime;
    Long delayExecuteTime = 0L;

    @Mapper
    @AnnotateWith(GeneratedMapper.class)
    public interface ChannelServiceTaskMapper extends BaseMapper<ChannelServiceTask, Task> {
        ChannelServiceTaskMapper INSTANCE = org.mapstruct.factory.Mappers.getMapper(ChannelServiceTaskMapper.class);
        ObjectMapper OBJECT_MAPPER = new EnhancedObjectMapper();

        @Mapping(source = "task.parametersMap.echoToken", target = "echoToken")
        @Mapping(source = "task.parametersMap.channel", target = "channel")
        @Mapping(source = "task.parametersMap.supplier", target = "supplier")
        @Mapping(source = "task.parametersMap.supplierHotel", target = "supplierHotel")
        @Mapping(source = "task.parametersMap.channelHotel", target = "channelHotel")
        @Mapping(source = "task.parametersMap.startDate", target = "startDate")
        @Mapping(source = "task.parametersMap.endDate", target = "endDate")
        @Mapping(source = "task.parametersMap.hotel", target = "hotel")
        @Mapping(source = "task.parametersMap.ignorePropertyStatusCheck", target = "ignorePropertyStatusCheck")
        @Mapping(source = "task.parametersMap.procedure", target = "procedure")
        @Mapping(source = "task.parametersMap.notifyUrl", target = "notifyUrl")
        @Mapping(source = "task.parametersMap.hotels", target = "hotels")
        @Mapping(source = "task.parametersMap.channelRooms", target = "channelRooms")
        @Mapping(source = "task.parametersMap.channelRates", target = "channelRates")
        @Mapping(source = "task.parametersMap.channelProducts", target = "channelProducts")
        @Override
        ChannelServiceTask reverseMap(Task task);

        @Mapping(target = "parametersMap", source = ".", qualifiedByName = "toParametersMap")
        @Override
        Task map(ChannelServiceTask channelServiceTask);

        @Named("toParametersMap")
        default Map<String, Object> mapFieldsToParametersMap(ChannelServiceTask source) {
            return OBJECT_MAPPER.convertValue(source, OBJECT_MAPPER.getTypeFactory().constructMapType(Map.class, String.class, Object.class));
        }

        default String objectToString(Object value) {
            return value == null ? null : value.toString();
        }

        default List<String> objectToStringArray(Object value) {
            return value == null ? null : value instanceof List ? (List<String>) value : null;
        }
    }


    public Task toTask() {
        return ChannelServiceTaskMapper.INSTANCE.map(this);
    }

    public static List<Task> toTasks(List<ChannelServiceTask> tasks) {
        return  ChannelServiceTaskMapper.INSTANCE.map(tasks);
    }
    public static ChannelServiceTask fromTask(Task task) {
        return ChannelServiceTaskMapper.INSTANCE.reverseMap(task);
    }

    public static List<ChannelServiceTask> fromTasks(List<Task> task) {
        return ChannelServiceTaskMapper.INSTANCE.reverseMap(task);
    }
}
