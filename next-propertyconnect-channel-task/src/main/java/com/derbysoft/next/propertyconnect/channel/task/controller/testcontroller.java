package com.derbysoft.next.propertyconnect.channel.task.controller;

import ch.qos.logback.classic.Logger;
import ch.qos.logback.classic.LoggerContext;
import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.Appender;
import com.derbysoft.next.propertyconnect.channel.task.service.RemoteService;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Iterator;

/**
 * @Created by <AUTHOR> on 5/23/2025
 */

@RestController
@RequestMapping("/test")
public class testcontroller {

    @Autowired
    RemoteService remoteService;

    @GetMapping("/tt")
    public String test() {

        LoggerContext ctx = (LoggerContext) LoggerFactory.getILoggerFactory();
        Logger logger = ctx.getLogger("http.StreamLog"); // 替换为你要看的 logger 名字

        System.out.println("Logger: " + logger.getName());
        for (Iterator<Appender<ILoggingEvent>> it = logger.iteratorForAppenders(); it.hasNext();) {
            Appender<ILoggingEvent> appender = it.next();
            System.out.println("  Appender name: " + appender.getName() + ", class: " + appender.getClass().getName());
        }

        logger.info("test");
        return "ok";
    }

}
