package com.derbysoft.next.propertyconnect.channel.task.domain.dto;

import com.derbysoft.next.propertyconnect.channel.task.config.EnhancedObjectMapper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.Map;

/**
 * @Created by <AUTHOR> on 2023/6/7
 */

public interface StoragePOMapper {
    ObjectMapper OBJECT_MAPPER = new EnhancedObjectMapper();

    default Map<String, Object> toMap(Object object) {
        return OBJECT_MAPPER.convertValue(object, new TypeReference<Map<String, Object>>() {});
    }

    default ChannelHotelInfo toHotelInfo(Map<String, Object> map) {
        return OBJECT_MAPPER.convertValue(map, ChannelHotelInfo.class);
    }

    default ChannelRoomInfo toRoomInfo(Map<String, Object> map) {
        return OBJECT_MAPPER.convertValue(map, ChannelRoomInfo.class);
    }

    default ChannelRateInfo toRateInfo(Map<String, Object> map) {
        return OBJECT_MAPPER.convertValue(map, ChannelRateInfo.class);
    }
}
