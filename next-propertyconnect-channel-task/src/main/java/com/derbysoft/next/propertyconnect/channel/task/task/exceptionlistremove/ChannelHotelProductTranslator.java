package com.derbysoft.next.propertyconnect.channel.task.task.exceptionlistremove;

import com.derbysoft.next.propertyconenct.channel.common.mapstruct.BaseMapper;
import com.derbysoft.next.propertyconenct.channel.common.utils.GeneratedMapper;
import com.derbysoft.next.propertyconnect.channel.task.domain.entity.SynchronizerExceptionListLogPO;
import org.mapstruct.*;

/**
 * @Created by <AUTHOR> on 2023/3/23
 */


@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
@AnnotateWith(GeneratedMapper.class)
interface ChannelHotelProductTranslator extends BaseMapper<ChannelHotelProduct, SynchronizerExceptionListLogPO> {

    @Mapping(target = "date", expression = "java(java.time.LocalDate.now(java.time.ZoneOffset.UTC))")
    @Mapping(source = "channel", target = "channelId")
    @Mapping(source = "hotel", target = "hotelId")
    @Mapping(source = "roomType", target = "roomId")
    @Mapping(source = "ratePlan", target = "rateId")
    @interface ChannelHotelProductToSynchronizerExceptionListLogPO { }


    @ChannelHotelProductToSynchronizerExceptionListLogPO
    @Override
    void fillIn(ChannelHotelProduct channelHotelProduct, @MappingTarget SynchronizerExceptionListLogPO synchronizerExceptionListLogPO);

    default void fillWithSummary(ChannelHotelProduct channelHotelProduct, SynchronizerExceptionListLogPO synchronizerExceptionListLogPO){
        this.fillIn(channelHotelProduct, synchronizerExceptionListLogPO);
        this.mapSummary(channelHotelProduct, synchronizerExceptionListLogPO);
    }

    @ChannelHotelProductToSynchronizerExceptionListLogPO
    @Override
    SynchronizerExceptionListLogPO map(ChannelHotelProduct channelHotelProduct);

    @Named("mapSummary")
    default void mapSummary(ChannelHotelProduct channelHotelProduct, @MappingTarget SynchronizerExceptionListLogPO synchronizerExceptionListLogPO) {
        var summaries = synchronizerExceptionListLogPO.getErrorDetails();
        summaries.stream()
                .filter(summary -> summary.getCode().equals(channelHotelProduct.getErrCode()))
                .findAny()
                .ifPresentOrElse(
                        summary -> summary.setCount(summary.getCount() + 1),
                        () -> summaries.add(SynchronizerExceptionListLogPO.ErrorSummary.builder()
                                .code(channelHotelProduct.getErrCode())
                                .message(channelHotelProduct.getErrMessage())
                                .count(1)
                                .build())
                );

        synchronizerExceptionListLogPO.setTotalErrorCount(summaries.stream().mapToInt(SynchronizerExceptionListLogPO.ErrorSummary::getCount).sum());
    }
}
