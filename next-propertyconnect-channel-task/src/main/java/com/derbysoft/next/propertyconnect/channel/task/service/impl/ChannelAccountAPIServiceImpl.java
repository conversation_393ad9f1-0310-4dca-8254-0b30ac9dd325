package com.derbysoft.next.propertyconnect.channel.task.service.impl;

import com.derbysoft.next.propertyconenct.channel.common.exception.response.RequestDataNotFoundException;
import com.derbysoft.next.propertyconenct.channel.common.mapstruct.BaseMapper;
import com.derbysoft.next.propertyconenct.channel.common.mapstruct.DateTimeMapper;
import com.derbysoft.next.propertyconenct.channel.common.utils.GeneratedMapper;
import com.derbysoft.next.propertyconnect.channel.task.controller.vo.ChannelAccountVO;
import com.derbysoft.next.propertyconnect.channel.task.service.AccountSettingService;
import com.derbysoft.next.propertyconnect.channel.task.service.ChannelAccountAPIService;
import lombok.RequiredArgsConstructor;
import org.mapstruct.AnnotateWith;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Map;
import java.util.Optional;

/**
 * @Created by <AUTHOR> on 2023/4/11
 */

@Service
@RequiredArgsConstructor
public class ChannelAccountAPIServiceImpl implements ChannelAccountAPIService {
    private final AccountSettingService accountSettingService;
    private final AccountSettingsTranslator accountSettingsTranslator;

    @Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    @AnnotateWith(GeneratedMapper.class)
    interface AccountSettingsTranslator extends BaseMapper<Map<String, Object>, ChannelAccountVO.AccountSetting>, DateTimeMapper {
    }


    @Override
    public ChannelAccountVO.AccountSetting getSpecificChannel(String channelId, String channelHotelId) {
        return Optional.ofNullable(accountSettingService.getAccountSettings(channelId, channelHotelId))
                .map(accountSettingsTranslator::map)
                .orElseThrow(() -> new RequestDataNotFoundException(channelId + (StringUtils.hasText(channelHotelId) ? " - " + channelHotelId : "")));
    }

    @Override
    public ChannelAccountVO.AccountSetting updateSpecificChannel(String channelId, String channelHotelId, ChannelAccountVO.AccountSetting channelDTO) {
        return Optional.ofNullable(accountSettingService.saveOrUpdateAccountSettings(channelId, channelHotelId, accountSettingsTranslator.reverseMap(channelDTO)))
                .map(accountSettingsTranslator::map)
                .orElseThrow(() -> new RequestDataNotFoundException(channelId + (StringUtils.hasText(channelHotelId) ? " - " + channelHotelId : "")));
    }

    @Override
    public void deleteSpecificChannel(String channelId, String channelHotelId) {
        accountSettingService.deleteAccountSettings(channelId, channelHotelId);
    }
}
