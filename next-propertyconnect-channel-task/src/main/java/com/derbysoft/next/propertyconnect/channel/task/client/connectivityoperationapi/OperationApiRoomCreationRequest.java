package com.derbysoft.next.propertyconnect.channel.task.client.connectivityoperationapi;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

import java.util.Map;

/**
 * @Created by <AUTHOR> on 2023/6/2
 */

@EqualsAndHashCode(callSuper = true)
@SuperBuilder(toBuilder = true)
@Data
public class OperationApiRoomCreationRequest extends OperationApiBasicRequest {
    Map<String, Object> roomInfo;
}
