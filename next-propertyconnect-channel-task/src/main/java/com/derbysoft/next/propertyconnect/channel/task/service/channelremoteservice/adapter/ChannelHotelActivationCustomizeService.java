package com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.adapter;

import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelHotelDTO;
import com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.RemoteChannelService;
import com.derbysoft.next.propertyconnect.channel.task.service.customizationservice.ChannelCustomizationService;

import java.util.List;

/**
 * @Created by <AUTHOR> on 1/7/2025
 */

public interface ChannelHotelActivationCustomizeService extends ChannelCustomizationService {

    default boolean saveChannelHotel() {return false;}

    default boolean fillCredentialToHotelExtension() {return true;}

    default List<RemoteChannelService.Operation> customizeProcedure() {return List.of();}

    default void customizeCredential(ChannelHotelDTO channelHotelDTO) {}

    default void customizeProperty(ChannelHotelDTO channelHotelDTO) {}

    default void customizeRoomType(ChannelHotelDTO channelHotelDTO) {}

    default void customizeRatePlan(ChannelHotelDTO channelHotelDTO) {}

    default void doAfterActivation(ChannelHotelDTO channelHotelDTO) {}

}
