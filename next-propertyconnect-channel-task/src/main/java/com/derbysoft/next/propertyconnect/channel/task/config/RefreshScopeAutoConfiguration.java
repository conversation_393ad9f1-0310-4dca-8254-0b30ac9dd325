package com.derbysoft.next.propertyconnect.channel.task.config;

import org.springframework.cloud.context.scope.refresh.RefreshScope;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @Created by <AUTHOR> on 5/9/2025
 */

@Configuration
public class RefreshScopeAutoConfiguration {

    @Bean
    RefreshScope refreshScope(){
        return new RefreshScope();
    }
}
