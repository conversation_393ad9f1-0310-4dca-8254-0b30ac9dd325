package com.derbysoft.next.propertyconnect.channel.task.client.alarm;

import com.derbysoft.next.commons.boot.logsupport.annotation.stream.StreamLog;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @Created by <AUTHOR> on 2023/6/15
 */

//@RemoteService(exceptionOnFailure = true)
@FeignClient(name = "alarmClient", url = "${app.pc.apigateway-url}pcalarm/")
public interface AlarmClient {

    @PostMapping("/alarm/notice")
    @StreamLog(proxy = false, inheritPref = true, wrapHeaderAsParameter = true)
    Object notice(@RequestBody NoticeRequest object);
}