package com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.direct;

import com.derbysoft.next.commons.core.logsupport.handler.PerfLogHandler;
import com.derbysoft.next.propertyconenct.channel.common.mapstruct.BaseMapper;
import com.derbysoft.next.propertyconenct.channel.common.utils.GeneratedMapper;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.*;
import com.derbysoft.next.propertyconnect.channel.task.domain.repository.ChannelRepository;
import com.derbysoft.next.propertyconnect.channel.task.service.RemoteService;
import com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.RemoteChannelProductsService;
import com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.adapter.ConnectivityOperationQueryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;
import org.springframework.stereotype.Service;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.List;

/**
 * @Created by <AUTHOR> on 10/9/2024
 */

@Service
@Slf4j
@RequiredArgsConstructor
public class CommonProductQueryService implements ConnectivityOperationQueryService, RemoteChannelProductsService {
    private final RemoteService remoteService;
    private final ChannelRepository channelRepository;
    private final CommonChanelProductTranslator translator = Mappers.getMapper(CommonChanelProductTranslator.class);
    protected final RemoteChannelProductsService.ProductDtoToHotelDto productTranslator = RemoteChannelProductsService.ProductDtoToHotelDto.INSTANCE;


    @Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    @AnnotateWith(GeneratedMapper.class)
    public interface CommonChanelProductTranslator extends BaseMapper<CommonChannelProductResponse, ChannelHotelDTO> {

        @Mapping(target = "code", source = "channelRoomId")
        @Mapping(target = "name", source = "roomName")
        @Mapping(target = "status", source = "status")
        ChannelRoomInfo mapRoomInfo(CommonChannelProductResponse.RoomInfo roomInfo);


        @Mapping(target = "code", source = "channelRateId")
        @Mapping(target = "name", source = "rateName")
        @Mapping(target = "status", source = "status")
        ChannelRateInfo mapRateInfo(CommonChannelProductResponse.RateInfo rateInfo);

    }

    @Override
    public ChannelProductsDTO getChannelProducts(String supplierId, String channelId, String channelHotelId) {
        var channelHotelDTO = this.propertyQuery(supplierId, channelId, channelHotelId, channelHotelId);
        if (null == channelHotelDTO) {
            return null;
        }
        var channelProductsDTO = productTranslator.reverseMap(channelHotelDTO);
        channelProductsDTO.setChannelId(channelId);
        channelProductsDTO.setSupplierId(supplierId);
        channelProductsDTO.setChannelHotelId(channelHotelId);
        return channelProductsDTO;
    }

    @Override
    public ChannelHotelDTO propertyQuery(String supplierId, String channelId, String supplierHotelId, String channelHotelId) {
        CommonChannelProductResponse productFromChannel;
        try {
            var fullProductQueryUrl = getQueryUrlFromStorage(channelId, channelHotelId);
            productFromChannel = remoteService.getProductFromChannel(fullProductQueryUrl).orElse(null);
        } catch (Exception exception){
            log.error(exception.getMessage());
            PerfLogHandler.currentHandler().fail(exception);
            productFromChannel = null;
        }


        return translator.map(productFromChannel);
    }

    private @NotNull String getQueryUrlFromStorage(String channelId, String channelHotelId) {
        var channelDetail = channelRepository.findByChannelId(channelId)
                .orElseThrow(() -> new IllegalArgumentException(String.format("Channel [%s] 'productQueryUrl' from 'channelDetails' not found", channelId)));

        var productQueryUrl = channelDetail.getProductQueryUrl();

        return UriComponentsBuilder.fromHttpUrl(productQueryUrl)
                .pathSegment("api", "channelmapping", channelId, "channelhotels", channelHotelId)
                .build()
                .toUriString();
    }

    @Override
    public ChannelHotelDTO propertyRoomTypeQuery(String supplierId, String channelId, String supplierHotelId, String channelHotelId, List<String> channelRoomIds) {
        throw new UnsupportedOperationException();
    }

    @Override
    public ChannelHotelDTO propertyRatePlanQuery(String supplierId, String channelId, String supplierHotelId, String channelHotelId, List<String> channelRateIds) {
        throw new UnsupportedOperationException();
    }

    @Override
    public String channel() {
        return ANY_CHANNEL;
    }
}
