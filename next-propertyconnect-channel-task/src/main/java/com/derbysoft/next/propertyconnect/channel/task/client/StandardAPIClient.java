package com.derbysoft.next.propertyconnect.channel.task.client;

import com.derbysoft.next.commons.boot.logsupport.annotation.stream.StreamLog;
import com.derbysoft.next.commons.boot.logsupport.annotation.stream.StreamParameter;
import com.derbysoft.next.commons.core.logsupport.constant.LogConst;
import com.derbysoft.next.propertyconnect.channel.task.client.interceptor.apilayer.ApiLayerRequest;
import com.derbysoft.next.propertyconnect.channel.task.task.arirefresh.TriggerARIRefreshRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.Optional;

/**
 * @Created by <AUTHOR> on 2023/2/28
 */

//@RemoteService(wrapBodyOnFailure = true)
@FeignClient(name = "standardAPIClient", url = "${app.apilayer.url}")
public interface StandardAPIClient {

    @ApiLayerRequest
    @StreamLog(proxy = false, inheritPref = true, wrapHeaderAsParameter = true)
    @StreamParameter(name = LogConst.Parameter.TRACE_TOKEN, value = "#traceToken")
    @PostMapping("/suppliers/ari/trigger_refresh")
    Optional<Object> triggerARIRefresh(
            @RequestBody TriggerARIRefreshRequest request,
            @RequestHeader(LogConst.Stream.HEADER_TRACE_TOKEN) String traceToken
    );
}