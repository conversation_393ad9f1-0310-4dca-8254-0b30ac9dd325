package com.derbysoft.next.propertyconnect.channel.task.service.mapping.impl;

import com.derbysoft.next.propertyconenct.channel.common.utils.GeneratedMapper;
import com.derbysoft.next.propertyconenct.channel.common.utils.JSONUtil;
import com.derbysoft.next.propertyconnect.channel.task.client.aimapping.RatePlanPredicationRequest;
import com.derbysoft.next.propertyconnect.channel.task.client.aimapping.RoomTypePredicationRequest;
import com.derbysoft.next.propertyconnect.channel.task.controller.vo.mapping.PredictionModel;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelProductsDTO;
import com.derbysoft.next.propertyconnect.channel.task.service.RemoteService;
import com.derbysoft.next.propertyconnect.channel.task.service.mapping.PredictionService;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.experimental.ExtensionMethod;
import org.apache.commons.lang3.tuple.Pair;
import org.mapstruct.*;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * @Created by <AUTHOR> on 2023/5/16
 */
@Primary
@Service
@ExtensionMethod(JSONUtil.class)
@RequiredArgsConstructor
public class DerbyPredictionService implements PredictionService {

    private final RemoteService remoteService;
    private final RoomTypeTranslator roomTypeTranslator;
    private final RatePlanTranslator ratePlanTranslator;

    @Override
    public PredictionModel supportModel() {
        return PredictionModel.DERBY;
    }


    @Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    @AnnotateWith(GeneratedMapper.class)
    interface RoomTypeTranslator {
        @Mapping(target = "distributor_hotel_id", expression = "java(stringFromObject(product,\"$.extensions.roomExtension.distributor_hotel_id\"))")
        @Mapping(target = "distributor_room_id", expression = "java(stringFromObject(product,\"$.extensions.roomExtension.distributor_room_id\"))")
        @Mapping(target = "distributor_room_name", expression = "java(stringFromObject(product,\"$.extensions.roomExtension.distributor_room_name\"))")
        @Mapping(target = "distributor_bedtype", expression = "java(stringFromObject(product,\"$.extensions.roomExtension.distributor_bedtype\"))")
        @Mapping(target = "distributor_occupancy", expression = "java(stringFromObject(product,\"$.extensions.roomExtension.distributor_occupancy\"))")
        RoomTypePredicationRequest.LeftsItem mapLeft(ChannelProductsDTO.Product product);

        @Mapping(target = "supplier_hotel_id", source = "product.hotelId")
        @Mapping(target = "supplier_room_id", source = "product.roomId")
        @Mapping(target = "supplier_room_name", source = "product.roomName")
        @Mapping(target = "supplier_bedtype", source = "product.bedType")
        @Mapping(target = "supplier_occupancy", source = "product.maxOccupancy")
        RoomTypePredicationRequest.RightsItem mapRight(ChannelProductsDTO.Product product);

        @Mapping(target = "lefts", source = "channel")
        @Mapping(target = "rights", source = "derby")
        @Mapping(target = "threshold", constant = "0.0")
        @Mapping(target = "model", ignore = true)
        RoomTypePredicationRequest map(List<ChannelProductsDTO.Product> channel, List<ChannelProductsDTO.Product> derby);

        default String objToString(Object object) {return null == object ? null : object.toString();}

        default String stringFromObject(Object object, String path) {
            return JSONUtil.getString(object, path);
        }
    }

    @Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    @AnnotateWith(GeneratedMapper.class)
    interface RatePlanTranslator {
        @Mapping(target = "distributor_room_id", ignore = true)
        @Mapping(target = "distributor_rateplan_id", expression = "java(stringFromObject(product,\"$.extensions.rateExtension.distributor_rateplan_id\"))")
        @Mapping(target = "distributor_rateplan_name", expression = "java(stringFromObject(product,\"$.extensions.rateExtension.distributor_rateplan_name\"))")
        @Mapping(target = "distributor_occupancy", expression = "java(stringFromObject(product,\"$.extensions.rateExtension.distributor_occupancy\"))")
        @Mapping(target = "distributor_meal", expression = "java(stringFromObject(product,\"$.extensions.rateExtension.distributor_meal\"))")
        @Mapping(target = "distributor_paytype", expression = "java(stringFromObject(product,\"$.extensions.rateExtension.distributor_paytype\"))")
        @Mapping(target = "distributor_channel_name", expression = "java(stringFromObject(product,\"$.extensions.rateExtension.distributor_channel_name\"))")
        @Mapping(target = "distributor_collect_type", expression = "java(stringFromObject(product,\"$.extensions.rateExtension.distributor_collect_type\"))")
        @Mapping(target = "distributor_extension", ignore = true)
        RatePlanPredicationRequest.LeftsItem mapLeft(ChannelProductsDTO.Product product);

        @Mapping(target = "supplier_room_id", ignore = true)
        @Mapping(target = "supplier_rateplan_id", source = "product.rateId")
        @Mapping(target = "supplier_rateplan_name", source = "product.rateName")
        @Mapping(target = "supplier_occupancy", ignore = true)
        @Mapping(target = "supplier_meal", source = "product.mealPlan")
        @Mapping(target = "supplier_paytype", source = "product.payType")
        @Mapping(target = "supplier_channel_name", source = "product.channelId")
        @Mapping(target = "supplier_extension", ignore = true)
        RatePlanPredicationRequest.RightsItem mapRight(ChannelProductsDTO.Product product);

        @Mapping(target = "lefts", source = "channel")
        @Mapping(target = "rights", source = "derby")
        @Mapping(target = "threshold", constant = "0.0")
        @Mapping(target = "model", ignore = true)
        RatePlanPredicationRequest map(List<ChannelProductsDTO.Product> channel, List<ChannelProductsDTO.Product> derby);

        default String objToString(Object object) {
            return null == object ? null : object.toString();
        }

        default String stringFromObject(Object object, String path) {
            return JSONUtil.getString(object, path);
        }
    }

    @Override
    public void prediction(ChannelProductsDTO channelProducts, List<ChannelProductsDTO.Product> candidateRooms, List<ChannelProductsDTO.Product> candidateRates, Double threshold) {
        var hotelProducts = remoteService.hotelProducts(channelProducts.getHotelId());
        var supplierRooms = candidateRooms.stream()
                .collect(Collectors.groupingBy(
                        ChannelProductsDTO.Product::getRoomId,
                        LinkedHashMap::new,
                        Collectors.mapping(ChannelProductsDTO.Product::getRoomName, Collectors.toCollection(TreeSet::new))
                ));

        var supplierRates = candidateRates.stream()
                .collect(Collectors.groupingBy(
                        ChannelProductsDTO.Product::getRateId,
                        LinkedHashMap::new,
                        Collectors.mapping(ChannelProductsDTO.Product::getRateName, Collectors.toCollection(TreeSet::new))
                ));

        var roomMappings = getRoomMapping(channelProducts, candidateRooms, supplierRooms.keySet());
        var rateMappings = getRateMapping(channelProducts, candidateRates, supplierRates.keySet());

        filterRoomAndRateByExistingProducts(roomMappings, rateMappings, hotelProducts);
        filterRoomOrRateByThreshold(roomMappings, threshold);
        filterRoomOrRateByThreshold(rateMappings, threshold);
        var productMappings = new HashMap<String, ChannelProductsDTO.Product>();

        roomMappings.forEach((room, roomMapping) -> rateMappings.forEach((rate, rateMapping) -> productMappings.put(room + "-" + rate,
                new ChannelProductsDTO.Product()
                        .setRoomId(room)
                        .setRateId(rate)
                        .setCandidateProducts(this.multiplyMaps(roomMapping, rateMapping, new ArrayList<>())
                                .entrySet()
                                .stream()
                                .limit(3)
                                .map(entry -> {
                                    var roomId = entry.getKey().getLeft();
                                    var rateId = entry.getKey().getRight();
                                    var roomNames = supplierRooms.get(roomId);
                                    var rateNames = supplierRates.get(rateId);

                                    return new ChannelProductsDTO.Product()
                                            .setRoomId(roomId)
                                            .setRoomName(Optional.ofNullable(roomNames).map(TreeSet::first).orElse(null))
                                            .setRateId(rateId)
                                            .setRateName(Optional.ofNullable(rateNames).map(TreeSet::first).orElse(null))
                                            .setPredictionScore(entry.getValue())
                                            .setExtensions(Map.of(
                                                    "candidateRoomCount", Optional.ofNullable(roomNames).map(Set::size).orElse(0),
                                                    "candidateRateCount", Optional.ofNullable(rateNames).map(Set::size).orElse(0)
                                            ));
                                })
                                .collect(Collectors.toList())
                        )
        )));


        channelProducts.getChannelProducts().forEach(product -> {
            product.setExtensions(null);
            product.setCandidateProducts(Optional.ofNullable(productMappings.get(product.getChannelRoomId() + "-" + product.getChannelRateId())).map(ChannelProductsDTO.Product::getCandidateProducts).orElse(List.of()));
        });
    }

    private void filterRoomAndRateByExistingProducts(Map<String, Map<String, Double>> roomMappings, Map<String, Map<String, Double>> rateMappings, List<Object> hotelProducts) {
        var activedRooms = hotelProducts.getCollection("$[?(@.status == 'Actived')].roomId", String.class).stream().distinct().collect(Collectors.toList());
        var activedRates = hotelProducts.getCollection("$[?(@.status == 'Actived')].rateId", String.class).stream().distinct().collect(Collectors.toList());

        roomMappings.keySet().removeIf(item -> {
            Optional.ofNullable(roomMappings.get(item))
                    .ifPresent(map -> map.keySet().removeIf(key -> !activedRooms.contains(key)));

            return roomMappings.get(item).isEmpty();
        });

        rateMappings.keySet().removeIf(item -> {
            Optional.ofNullable(rateMappings.get(item))
                    .ifPresent(map -> map.keySet().removeIf(key -> !activedRates.contains(key)));

            return rateMappings.get(item).isEmpty();
        });
    }

    private void filterRoomOrRateByThreshold(Map<String, Map<String, Double>> mappings, Double threshold) {
        mappings.keySet().removeIf(item -> {
            Optional.ofNullable(mappings.get(item))
                    .ifPresent(map -> map.entrySet().removeIf(entry -> entry.getValue() < threshold));

            return mappings.get(item).isEmpty();
        });
    }

    @SuppressWarnings("unchecked")
    private Map<String, Map<String, Double>> getRoomMapping(ChannelProductsDTO channelProducts, List<ChannelProductsDTO.Product> candidateRooms, Collection<String> supplierRooms) {
        var channelRoomProduct = channelProducts.getChannelProducts().stream().collect(Collectors.toMap(ChannelProductsDTO.Product::getChannelRoomId, Function.identity(), (a, b) -> b, LinkedHashMap::new)).values();
        var channelRooms = channelRoomProduct.stream().map(ChannelProductsDTO.Product::getChannelRoomId).distinct().collect(Collectors.toList());

        var roomTypePredicationRequest = roomTypeTranslator.map(new ArrayList<>(channelRoomProduct), candidateRooms);
        return remoteService.getRoomTypeMapping(roomTypePredicationRequest)
                .map(response -> this.matchingResult(channelRooms, Lists.newArrayList(response.getCollection("$.data.pred_scores")))
                        .entrySet().stream()
                        .collect(Collectors.toMap(Map.Entry::getKey, entry -> this.sortMapByValue(this.matchingResult(Lists.newArrayList(supplierRooms), (List<Double>) entry.getValue())), (a, b) -> b, LinkedHashMap::new))
                ).orElse(null);
    }


    private Map<String, Map<String, Double>> getRateMapping(ChannelProductsDTO channelProducts, List<ChannelProductsDTO.Product> candidateRates, Collection<String> supplierRates) {
        var channelRateProduct = channelProducts.getChannelProducts().stream().collect(Collectors.toMap(ChannelProductsDTO.Product::getChannelRateId, Function.identity(), (a, b) -> b, LinkedHashMap::new)).values();
        var channelRates = channelRateProduct.stream().map(ChannelProductsDTO.Product::getChannelRateId).distinct().collect(Collectors.toList());

        var ratePlanPredicationRequest = ratePlanTranslator.map(new ArrayList<>(channelRateProduct), candidateRates);

        return remoteService.getRatePlanMapping(ratePlanPredicationRequest)
                .map(response -> this.matchingResult(channelRates, Lists.newArrayList(response.getCollection("$.data.pred_scores")))
                        .entrySet().stream()
                        .collect(Collectors.toMap(Map.Entry::getKey, entry -> this.sortMapByValue(this.matchingResult(Lists.newArrayList(supplierRates), (List<Double>) entry.getValue())), (a, b) -> b, LinkedHashMap::new)))
                .orElse(null);
    }


    public <K, V> Map<K, V> matchingResult(List<K> keys, List<V> values) {
        if (keys.size() != values.size()) {
            throw new IllegalArgumentException("Lists must have the same size");
        }

        return IntStream.range(0, keys.size())
                .boxed()
                .collect(Collectors.toMap(keys::get, values::get, (a, b) -> b, LinkedHashMap::new));
    }

    private <K> Map<K, Double> sortMapByValue(Map<K, Double> map) {
        return map.entrySet().stream()
                .sorted(Map.Entry.comparingByValue(Comparator.reverseOrder()))
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (oldValue, newValue) -> oldValue,
                        LinkedHashMap::new
                ));
    }

    private Map<Pair<String, String>, Double> multiplyMaps(Map<String, Double> map1, Map<String, Double> map2, List<Object> hotelProducts) {
        Map<Pair<String, String>, Double> result = new LinkedHashMap<>();

        for (Map.Entry<String, Double> entry1 : map1.entrySet()) {
            String key1 = entry1.getKey();
            Double value1 = entry1.getValue();

            for (Map.Entry<String, Double> entry2 : map2.entrySet()) {
                String key2 = entry2.getKey();
                //FIXME: Backend filter (May cause performance issue)
//                var existMapping = hotelProducts.stream()
//                        .map(LinkedHashMap.class::cast)
//                        .filter(productsItem -> productsItem.get("status").equals("Actived")
//                                && productsItem.get("roomName").equals(key1)
//                                && productsItem.get("rateName").equals(key2))
//                        .collect(Collectors.toList());
//
//                if (existMapping.isEmpty()) {
//                    continue;
//                }
                Double value2 = entry2.getValue();

                Pair<String, String> combinedKey = Pair.of(key1, key2);
                Double combinedValue = value1 + value2;

                result.put(combinedKey, combinedValue);
            }
        }

        return this.sortMapByValue(result);
    }


}
