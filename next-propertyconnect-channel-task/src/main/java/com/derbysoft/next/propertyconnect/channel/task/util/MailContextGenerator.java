package com.derbysoft.next.propertyconnect.channel.task.util;

import com.derbysoft.next.propertyconnect.channel.task.domain.dto.InventoryItemStatus;
import com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.RemoteChannelService;
import lombok.SneakyThrows;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Component;
import org.springframework.util.StreamUtils;

import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

/**
 * @Created by <AUTHOR> on 2023/8/18
 */

@Component
public class MailContextGenerator {
    private static final AtomicReference<Resource> setupMailTemplate = new AtomicReference<>();
    private static final AtomicReference<Resource> aiMappingMailTemplate = new AtomicReference<>();
    private static final AtomicReference<String> kibanaUrl = new AtomicReference<>();
    private static final AtomicReference<String> cspUrl = new AtomicReference<>();
    @Autowired
    public void setSetupMailTemplate(@Value("classpath:setup-mail-template.html") Resource setupMailTemplate,
                                     @Value("classpath:ai-report-mail-template.html") Resource aiMappingMailTemplate,
                                     @Value("${mail.log.kibana-url}") String kibanaUrl,
                                     @Value("${mail.log.csp-url}") String cspUrl) {
        MailContextGenerator.aiMappingMailTemplate.set(aiMappingMailTemplate);
        MailContextGenerator.setupMailTemplate.set(setupMailTemplate);
        MailContextGenerator.kibanaUrl.set(kibanaUrl);
        MailContextGenerator.cspUrl.set(cspUrl);
    }

    @SneakyThrows
    public static String generateSetupMailContent(String channel, String hotelCode, String operationToken, List<Pair<RemoteChannelService.Operation, InventoryItemStatus>> failedItems) {
        return StreamUtils.copyToString(setupMailTemplate.get().getInputStream(), StandardCharsets.UTF_8)
                .replace("{{env}}", EnvUtil.getEnv().toUpperCase())
                .replace("{{title}}", channel + " Hotel Setup Failure Alarm")
                .replace("{{linkPerf}}", MailContextGenerator.getKibanaLink(channel, hotelCode, operationToken))
                .replace("{{linkStream}}", MailContextGenerator.getCspLink(operationToken))
                .replace("{{channelHotelCode}}", hotelCode)
                .replace("{{operationToken}}", operationToken)
                .replace("{{failKeys}}", Stream.of("Operation", "Item", "SyncStatus", "ErrorCode", "ErrorMessage")
                        .map(s -> String.format("<th style='border: 1px solid #ddd;padding: 8px;user-select: none;padding-top: 12px;padding-bottom: 12px;text-align: center;background-color: #004771;color: white'>%s</th>", s))
                        .collect(Collectors.joining()))
                .replace("{{failDetails}}", IntStream.range(0, failedItems.size())
                        .mapToObj(i -> String.format("<tr style='%s'><td style='border: 1px solid #ddd;padding: 8px;user-select: none;'>%s</td><td style='border: 1px solid #ddd;padding: 8px;user-select: none;'>%s</td><td style='border: 1px solid #ddd;padding: 8px;user-select: none;'>%s</td><td style='border: 1px solid #ddd;padding: 8px;user-select: none;'>%s</td><td style='border: 1px solid #ddd;padding: 8px;user-select: none;'>%s</td></tr>",
                                i % 2 == 0 ? "background-color: #f2f2f2" : "",
                                failedItems.get(i).getLeft().name(),
                                failedItems.get(i).getRight().getCode(),
                                failedItems.get(i).getRight().getSyncStatus(),
                                failedItems.get(i).getRight().getErrorCode(),
                                failedItems.get(i).getRight().getErrorMessage()))
                        .collect(Collectors.joining()));
    }

    @SneakyThrows
    public static String generateAiMappingReportMailContent(String channel, String hotelCode, String operationToken, LinkedHashMap<String, String> diagnosisData) {
        return StreamUtils.copyToString(aiMappingMailTemplate.get().getInputStream(), StandardCharsets.UTF_8)
                .replace("{{env}}", EnvUtil.getEnv().toUpperCase())
                .replace("{{title}}", channel + " AIMapping Diagnosis Report")
                .replace("{{linkPerf}}", MailContextGenerator.getKibanaLink(channel, hotelCode, operationToken))
                .replace("{{linkStream}}", MailContextGenerator.getCspLink(operationToken))
                .replace("{{channelHotelCode}}", hotelCode)
                .replace("{{operationToken}}", operationToken)
                .replace("{{formItems}}", diagnosisData.entrySet().stream()
                        .map(entry -> String.format("<tr style=\"height: 5vh\"><td style=\"border: 1px solid lightgray; text-align: right;background-color: #f5f5f5;font-weight: bold\">%s</td><td style=\"width: 2vw; border-top-color: lightgray; border-top-width: 1px; border-top-style: solid; border-bottom-color: lightgray; border-bottom-width: 1px; border-bottom-style: solid; border-left-style: hidden;border-right-style: hidden\"><table cellpadding=\"0\" cellspacing=\"0\"><tbody><tr style=\"height: 5vh\" ><td style=\"width: 1vh; background-color: #f5f5f5\"></td><td style=\"width: 1vh\"></td></tr></tbody></table></td><td style=\"border: 1px solid lightgray; text-align: left;\">%s</td></tr>", entry.getKey(), entry.getValue()))
                        .collect(Collectors.joining("")));
    }

    private static String getKibanaLink(String channelId, String channelHotelId, String echoToken){
        var dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
        return new StringBuilder(kibanaUrl.get())
                .append("app/kibana#/discover?_g=(refreshInterval:(pause:!t,value:0),time:(from:'")
                .append(dateTimeFormatter.format(LocalDateTime.now(ZoneId.of("UTC")).minusMinutes(30)))
                .append("',mode:absolute,to:'")
                .append(dateTimeFormatter.format(LocalDateTime.now(ZoneId.of("UTC")).plusMinutes(1)))
                .append("'))&_a=(columns:!(_source),filters:!(),index:'4ecc32e0-d884-11eb-b125-bf50fdd58c14',interval:auto,query:(language:lucene,query:'app_name:pcchannel%20AND%20process:SaveDistributorHotel%20AND%20channel:")
                .append(channelId)
                .append("%20AND%20hotel_channel:")
                .append(channelHotelId)
                .append("%20AND%20echo_token:")
                .append(echoToken)
                .append("'),sort:!('@timestamp',desc))")
                .toString();
    }

    private static String getCspLink(String traceToken){
        return new StringBuilder(cspUrl.get())
                .append("cs-platform-web/#/general/streamLog/streamLog?client=propertyconnect&date=")
                .append(DateTimeFormatter.ISO_LOCAL_DATE.format(LocalDateTime.now(ZoneId.of("UTC"))))
                .append("&trace_token=")
                .append(traceToken)
                .toString();
    }
}
