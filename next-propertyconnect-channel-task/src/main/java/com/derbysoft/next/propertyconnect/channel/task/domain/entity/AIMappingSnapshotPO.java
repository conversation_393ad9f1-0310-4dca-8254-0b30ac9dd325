package com.derbysoft.next.propertyconnect.channel.task.domain.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * @Created by <AUTHOR> on 12/4/2023
 */

@EqualsAndHashCode(callSuper = true)
@Data
@FieldNameConstants
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "aimapping_snapshot")
public class AIMappingSnapshotPO extends BaseEntity {
    @Id
    private String id;
    @Indexed
    private String supplierId;
    @Indexed
    private String hotelId;
    @Indexed
    private String channelId;
    @Indexed
    private String channelHotelId;

    private List<Product> lastChannelHotelMapping;

    private List<Product> aiMappingResult;

    private Object supplierRooms;

    private Object supplierRates;

    private Object channelRooms;

    private Object channelRates;

    private Double threshold;

    private Long predicationTime;

    private LocalDateTime lastAiMappingOperationTime;

    @Data
    public static class Product{
        private String roomId;
        private String channelRoomId;
        private String roomName;
        private String channelRoomName;
        private String rateId;
        private String channelRateId;
        private String rateName;
        private String channelRateName;
        private String bedType;
        private Integer maxOccupancy;
        private String mealPlan;
        private String payType;
        private String status;
        private Boolean availStatus;
        private List<Product> candidateProducts;
        private Map<String, Object> extensions;
    }
}
