package com.derbysoft.next.propertyconnect.channel.task.task.hotelsetup;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;

import java.util.Arrays;

/**
 * @Created by <AUTHOR> on 2023/3/27
 */

@Data
@RefreshScope
@ConfigurationProperties(prefix = "app.task.hotel-setup")
public class HotelSetupConfigurationProperties {
    public Boolean enableAutoDeactivate = false;

    public String alarmEmails = "";

    public String blockAriRefresh = "";

    public boolean allowARIRefresh(String channel) {
        return Arrays.stream(blockAriRefresh.split(",")).noneMatch(s -> s.equalsIgnoreCase(channel));
    }
}
