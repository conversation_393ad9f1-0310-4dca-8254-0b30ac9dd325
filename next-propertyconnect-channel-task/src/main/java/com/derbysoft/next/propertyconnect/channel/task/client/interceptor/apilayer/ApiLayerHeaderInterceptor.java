package com.derbysoft.next.propertyconnect.channel.task.client.interceptor.apilayer;

import com.derbysoft.next.propertyconnect.channel.task.config.ApiLayerProperties;
import feign.MethodMetadata;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.util.StringUtils;

import java.lang.reflect.Method;
import java.util.Optional;

@Configuration
@RequiredArgsConstructor
@EnableConfigurationProperties(ApiLayerProperties.class)
public class ApiLayerHeaderInterceptor implements RequestInterceptor {

    private final ApiLayerProperties apiLayerProperties;

    @Override
    public void apply(RequestTemplate template) {
        Optional<Method> methodOptional = Optional.ofNullable(template.methodMetadata())
                .map(MethodMetadata::method);

        var annotation = methodOptional
                .map(method -> AnnotationUtils.getAnnotation(method, ApiLayerRequest.class));

        annotation
                .flatMap(anno -> Optional.ofNullable(apiLayerProperties.getToken()))
                .filter(StringUtils::hasText)
                .ifPresent(token -> template.header("Authorization", token));
    }
}
