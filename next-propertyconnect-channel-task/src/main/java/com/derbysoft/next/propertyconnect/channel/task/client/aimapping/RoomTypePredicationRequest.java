package com.derbysoft.next.propertyconnect.channel.task.client.aimapping;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class RoomTypePredicationRequest {
	private List<RightsItem> rights;
	private List<LeftsItem> lefts;
	private Double threshold;
	private String model;

	@Data
	public static class LeftsItem{
		private String distributor_hotel_id;
		private String distributor_room_id;
		private String distributor_room_name;
		private String distributor_bedtype;
		private String distributor_occupancy;
	}

	@Data
	public static class RightsItem{
		private String supplier_hotel_id;
		private String supplier_room_id;
		private String supplier_room_name;
		private String supplier_bedtype;
		private String supplier_occupancy;
	}
}
