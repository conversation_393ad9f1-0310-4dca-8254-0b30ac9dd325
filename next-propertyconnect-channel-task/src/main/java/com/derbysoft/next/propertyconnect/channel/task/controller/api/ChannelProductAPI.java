package com.derbysoft.next.propertyconnect.channel.task.controller.api;

import com.derbysoft.extension.aggregationtool.controllerservice.ControllerAndService;
import com.derbysoft.next.commons.boot.logsupport.annotation.perf.PerfLog;
import com.derbysoft.next.commons.boot.logsupport.annotation.perf.PerfParameter;
import com.derbysoft.next.commons.boot.logsupport.annotation.stream.StreamLog;
import com.derbysoft.next.propertyconenct.channel.common.exception.response.UnifyResult;
import com.derbysoft.next.propertyconenct.channel.common.utils.Constants;
import com.derbysoft.next.propertyconnect.channel.task.config.UnifyResultWrapper;
import com.derbysoft.next.propertyconnect.channel.task.controller.vo.ChannelProductVO;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * @Created by <AUTHOR> on 2023/3/21
 */

@ControllerAndService(wrapper = UnifyResultWrapper.class)
@RequestMapping("/api")
@Tag(name = "ChannelProductAPI", description = "ChannelProductAPI", extensions = @Extension(properties = @ExtensionProperty(name = "order", value = "6")))
public interface ChannelProductAPI {

    @PerfLog(Constants.Perf.CHANGE_PROFILE)
    @PerfParameter(name = Constants.Perf.PROCESS_TYPE, value = "'getChannelProducts'")
    @PerfParameter(name = Constants.Perf.HOTEL_SYSTEM_CONNECTION, value = "#hotelSystemConnectionId")
    @PerfParameter(name = Constants.Perf.EXT_PARAM, value = "#refresh")
    @PerfParameter(name = Constants.Perf.HOTEL_CHANNEL, value = "#channelHotelId")
    @PerfParameter(name = Constants.Perf.CHANNEL, value = "#channelId")
    @PerfParameter(name = Constants.Perf.SUPPLIER, value = "#supplierId")
    @GetMapping(value = "/channels/{channelId}/channelhotels/{channelHotelId}/products")
    @StreamLog(proxy = false, inheritPref = true, wrapHeaderAsParameter = true)
    @Operation(summary = "OPENAPI-GetChannelProducts")
    UnifyResult<ChannelProductVO> getChannelProducts(@PathVariable("channelId") String channelId,
                                                     @PathVariable("channelHotelId") String channelHotelId,
                                                     @RequestParam(required = false, defaultValue = "PROPERTYCONNECT") String supplierId,
                                                     @RequestParam(required = false) String hotelSystemConnectionId,
                                                     @RequestParam(required = false, defaultValue = "true") Boolean refresh,
                                                     @RequestParam(required = false, defaultValue = "true") Boolean ignoreError);

    @PerfLog(Constants.Perf.CHANGE_PROFILE)
    @PerfParameter(name = Constants.Perf.PROCESS_TYPE, value = "'internelGetChannelProducts'")
    @PerfParameter(name = Constants.Perf.HOTEL_CHANNEL, value = "#channelHotelId")
    @PerfParameter(name = Constants.Perf.CHANNEL, value = "#channelId")
    @PerfParameter(name = Constants.Perf.SUPPLIER, value = "#supplierId")
    @GetMapping(value = "/channels/{channelId}/channelhotels/{channelHotelId}/products/extras")
    @StreamLog(proxy = false, inheritPref = true, wrapHeaderAsParameter = true)
    @Operation(summary = "INTERNAL-GetChannelProducts")
    UnifyResult<ChannelProductVO> getChannelProductsWithExtra(@PathVariable("channelId") String channelId,
                                                              @PathVariable("channelHotelId") String channelHotelId,
                                                              @RequestParam(required = false, defaultValue = "PROPERTYCONNECT") String supplierId);

    @PerfLog(Constants.Perf.CHANGE_PROFILE)
    @PerfParameter(name = Constants.Perf.PROCESS_TYPE, value = "'getChannelProducts'")
    @PerfParameter(name = Constants.Perf.HOTEL_SYSTEM_CONNECTION, value = "#hotelSystemConnectionId")
    @PerfParameter(name = Constants.Perf.EXT_PARAM, value = "#refresh")
    @PerfParameter(name = Constants.Perf.HOTEL_CHANNEL, value = "#channelHotelId")
    @PerfParameter(name = Constants.Perf.CHANNEL, value = "#channelId")
    @PostMapping(value = "/channels/{channelId}/channelhotels/{channelHotelId}/products")
    @Operation(summary = "SaveChannelProducts")
    @StreamLog(proxy = false, inheritPref = true, wrapHeaderAsParameter = true)
    @Hidden
    UnifyResult<ChannelProductVO> saveChannelProducts(@PathVariable("channelId") String channelId,
                                                      @PathVariable("channelHotelId") String channelHotelId,
                                                      @RequestBody ChannelProductVO channelProduct);


    @PerfLog(Constants.Perf.CHANGE_PROFILE)
    @PerfParameter(name = Constants.Perf.PROCESS_TYPE, value = "'uploadChannelProducts'")
    @PerfParameter(name = Constants.Perf.HOTEL_CHANNEL, value = "#channelHotelId")
    @PerfParameter(name = Constants.Perf.CHANNEL, value = "#channelId")
    @PostMapping(value = "/channels/{channelId}/channelhotels/{channelHotelId}/products/upload")
    @StreamLog(proxy = false, inheritPref = true, wrapHeaderAsParameter = true)
    @Operation(summary = "UploadChannelProducts")
    UnifyResult<ChannelProductVO> uploadChannelProducts(@PathVariable("channelId") String channelId,
                                                        @PathVariable("channelHotelId") String channelHotelId,
                                                        @RequestPart("file") MultipartFile file);

}
