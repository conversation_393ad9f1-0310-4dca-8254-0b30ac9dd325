package com.derbysoft.next.propertyconnect.channel.task;

import com.derbysoft.extension.springdocopenapi.EnableOpenAPI;
import com.derbysoft.next.propertyconenct.channel.common.exception.response.EnableUnifyResponseSupport;
import com.derbysoft.next.propertyconnect.channel.task.config.FeignConfig;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.data.mongodb.config.EnableMongoAuditing;

/**
 * @Created by <AUTHOR> on 2023/2/27
 */

@EnableFeignClients(defaultConfiguration = FeignConfig.class)
@EnableCaching
@EnableMongoAuditing
@EnableUnifyResponseSupport
@EnableOpenAPI
@EnableAspectJAutoProxy(exposeProxy = true)
@SpringBootApplication
public class ChannelTaskApplication extends SpringBootServletInitializer {

    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
        return application.sources(ChannelTaskApplication.class);
    }
    public static void main(String[] args) {
        SpringApplication.run(ChannelTaskApplication.class, args);
    }
}
