package com.derbysoft.next.propertyconnect.channel.task.controller.vo;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * @Created by <AUTHOR> on 2023/5/25
 */

public enum SyncStatus {

    @Schema(description = "Initialization state")
    DRAFT,

    @Schema(description = "Preprocessing failed like data invalid, Different channels may have different pre-check processes")
    PREPROCESS_FAILED,

    @Schema(description = "Successfully submitted to the channel")
    SUBMITTED,

    @Schema(description = "The channel returned an error when submitting to the channel, or failed to submit to the channel")
    SUBMIT_FAILED,

    @Schema(description = "Successfully synchronized to the channel")
    SYNCED,

    @Schema(description = "The channel returned an error when synchronizing to the channel")
    SYNC_FAILED,

    @Schema(description = "This item is ignored for any processes")
    IGNORED;

    public boolean isSuccess(){
        return this == SYNCED || this == IGNORED;
    }
    public boolean isFail(){
        return this == SYNC_FAILED || this == SUBMIT_FAILED || this == PREPROCESS_FAILED;
    }
    public boolean isFinalStatus(){
        return isSuccess() || isFail();
    }
    public boolean isProcessingStatus(){
        return this == DRAFT || this == SUBMITTED;
    }

}
