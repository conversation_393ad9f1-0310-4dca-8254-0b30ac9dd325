package com.derbysoft.next.propertyconnect.channel.task.domain.repository;

import com.derbysoft.next.propertyconenct.channel.common.mapstruct.DateTimeMapper;
import com.derbysoft.next.propertyconenct.channel.common.utils.GeneratedMapper;
import com.derbysoft.next.propertyconenct.channel.common.utils.JSONUtil;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelProductsDTO;
import com.derbysoft.next.propertyconnect.channel.task.domain.entity.AIMappingSnapshotPO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Created by <AUTHOR> on 12/6/2023
 */

public interface AIMappingSnapshotRepository extends MongoRepository<AIMappingSnapshotPO, String> {
    AIMappingSnapshotPO findFirstBySupplierIdAndChannelIdAndChannelHotelId(String supplierId, String channelId, String channelHotelId);


    AIMappingSnapshotTranslator TRANSLATOR = Mappers.getMapper(AIMappingSnapshotTranslator.class);
    @Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    @AnnotateWith(GeneratedMapper.class)
    interface AIMappingSnapshotTranslator extends DateTimeMapper {
        @Mapping(target = "channelProducts", source = "lastChannelHotelMapping")
        ChannelProductsDTO mapChannelHotelMapping(AIMappingSnapshotPO aiMappingSnapshotPO);

        @Mapping(target = "channelProducts", source = "aiMappingResult")
        ChannelProductsDTO mapAiMappingResult(AIMappingSnapshotPO aiMappingSnapshotPO);

        @Mapping(target = "aiMappingResult", source = "channelProducts")
        void fillAIMappingResult(ChannelProductsDTO from, @MappingTarget AIMappingSnapshotPO to);

        @Mapping(target = "lastChannelHotelMapping", source = "channelProducts")
        void fillLastChannelProductsMapping(ChannelProductsDTO from, @MappingTarget AIMappingSnapshotPO to);

        List<ChannelProductsDTO.Product> productListToProductList(List<AIMappingSnapshotPO.Product> list);

        List<AIMappingSnapshotPO.Product> productListToProductList1(List<ChannelProductsDTO.Product> list);
    }

    default AIMappingSnapshotPO getAIMappingSnapshot(String supplierId, String channelId, String channelHotelId) {
        return findFirstBySupplierIdAndChannelIdAndChannelHotelId(supplierId, channelId, channelHotelId);
    }

    default AIMappingSnapshotPO getOrNewAIMappingSnapshot(String supplierId, String channelId, String channelHotelId) {
        var aiMappingSnapshot = getAIMappingSnapshot(supplierId, channelId, channelHotelId);
        if (aiMappingSnapshot == null) {
            aiMappingSnapshot = new AIMappingSnapshotPO();
        }
        return aiMappingSnapshot;
    }

    default void saveAIMappingSnapshot(ChannelProductsDTO currentAiMappingResult, Object supplierRooms, Object supplierRates, Object channelRooms, Object channelRates, Long predicationTime) {
        var aiMappingSnapshot = getOrNewAIMappingSnapshot(currentAiMappingResult.getSupplierId(), currentAiMappingResult.getChannelId(), currentAiMappingResult.getChannelHotelId());
        TRANSLATOR.fillAIMappingResult(currentAiMappingResult, aiMappingSnapshot);
        aiMappingSnapshot.setLastChannelHotelMapping(null);
        aiMappingSnapshot.setSupplierRooms(supplierRooms);
        aiMappingSnapshot.setSupplierRates(supplierRates);
        aiMappingSnapshot.setChannelRooms(channelRooms);
        aiMappingSnapshot.setChannelRates(channelRates);
        aiMappingSnapshot.setPredicationTime(predicationTime);
        aiMappingSnapshot.setLastAiMappingOperationTime(LocalDateTime.now());
        aiMappingSnapshot.setThreshold(JSONUtil.getDouble(currentAiMappingResult, "$.extensions.threshold"));
        save(aiMappingSnapshot);
    }

    default void saveLatestChannelProductsMapping(ChannelProductsDTO currentChannelProductsMapping) {
        var aiMappingSnapshot = getAIMappingSnapshot(currentChannelProductsMapping.getSupplierId(), currentChannelProductsMapping.getChannelId(), currentChannelProductsMapping.getChannelHotelId());
        if (null == aiMappingSnapshot) {
            return;
        }
        TRANSLATOR.fillLastChannelProductsMapping(currentChannelProductsMapping, aiMappingSnapshot);
        save(aiMappingSnapshot);
    }
}
