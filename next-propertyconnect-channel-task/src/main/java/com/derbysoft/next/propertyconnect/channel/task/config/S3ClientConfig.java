package com.derbysoft.next.propertyconnect.channel.task.config;

import com.derbysoft.next.propertyconnect.channel.task.service.mapping.impl.PredictionServiceConfigProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;


/**
 * @Created by <AUTHOR> on 12/8/2023
 */

@Configuration
@EnableConfigurationProperties(PredictionServiceConfigProperties.class)
public class S3ClientConfig {
    @Bean
    public S3Client defualtS3Client(PredictionServiceConfigProperties configProperties) {
        return S3Client.builder()
                .region(Region.of(configProperties.getS3Region()))
                .build();
    }
}
