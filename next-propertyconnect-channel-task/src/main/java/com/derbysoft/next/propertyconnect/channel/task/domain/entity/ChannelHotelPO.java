package com.derbysoft.next.propertyconnect.channel.task.domain.entity;

import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ItemStatus;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.SyncStatus;
import lombok.*;
import lombok.experimental.FieldNameConstants;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Map;

/**
 * @Created by <AUTHOR> on 2023/3/21
 */


@EqualsAndHashCode(callSuper = true)
@Builder
@Data
@FieldNameConstants
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "channel_hotel")
public class ChannelHotelPO extends BaseEntity {
    @Id
    private String id;
    @Indexed
    private String supplierId;
    @Indexed
    private String channelId;
    @Indexed
    private String channelHotelId;
    private ItemStatus status;
    private SyncStatus syncStatus;
    private String lastOperationToken;
    private Map<String, Object> accountSettings;
    private Map<String, Object> hotelInfo;
}
