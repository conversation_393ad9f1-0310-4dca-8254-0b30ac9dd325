package com.derbysoft.next.propertyconnect.channel.task.util;

import com.derbysoft.next.propertyconenct.channel.common.exception.ErrorCode;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelHotelDTO;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.InventoryItemStatus;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.SyncStatus;
import com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.RemoteChannelService;
import lombok.experimental.UtilityClass;
import org.mapstruct.factory.Mappers;

import java.util.ArrayList;
import java.util.List;

/**
 * @Created by <AUTHOR> on 2023/6/14
 */
@UtilityClass
public class InventoryItemErrorUtil {
    public static <T extends InventoryItemStatus> void roomNotFound(T inventoryItem) {
        inventoryItem.setSyncStatus(SyncStatus.PREPROCESS_FAILED);
        inventoryItem.setErrorCode(ErrorCode.BusinessFailed.name());
        inventoryItem.setErrorMessage(ErrorCode.BusinessFailed.getErrorMessage(inventoryItem.getCode(), "Room not found"));
    }

    public static <T extends InventoryItemStatus> void rateNotFound(T inventoryItem) {
        inventoryItem.setSyncStatus(SyncStatus.PREPROCESS_FAILED);
        inventoryItem.setErrorCode(ErrorCode.BusinessFailed.name());
        inventoryItem.setErrorMessage(ErrorCode.BusinessFailed.getErrorMessage(inventoryItem.getCode(), "Rate not found"));
    }

    public static <T extends InventoryItemStatus> void itemNotFound(T inventoryItem) {
        inventoryItem.setSyncStatus(SyncStatus.PREPROCESS_FAILED);
        inventoryItem.setErrorCode(ErrorCode.BusinessFailed.name());
        inventoryItem.setErrorMessage(ErrorCode.BusinessFailed.getErrorMessage(inventoryItem.getCode(), "Not found"));
    }
    public static <T extends InventoryItemStatus> void associatedItemStatusError(T inventoryItem, String message) {
        inventoryItem.setSyncStatus(SyncStatus.PREPROCESS_FAILED);
        inventoryItem.setErrorCode(ErrorCode.BusinessFailed.name());
        inventoryItem.setErrorMessage(ErrorCode.BusinessFailed.getErrorMessage(inventoryItem.getCode(), message));
    }

    public static <T extends InventoryItemStatus> void requestNameDuplicateItemError(T inventoryItem) {
        inventoryItem.setSyncStatus(SyncStatus.PREPROCESS_FAILED);
        inventoryItem.setErrorCode(ErrorCode.BusinessFailed.name());
        inventoryItem.setErrorMessage(ErrorCode.BusinessFailed.getErrorMessage(inventoryItem.getCode(), "Duplicate in request"));
    }

    public static <T extends InventoryItemStatus> void partnerDuplicateItemError(T inventoryItem) {
        inventoryItem.setSyncStatus(SyncStatus.PREPROCESS_FAILED);
        inventoryItem.setErrorCode(ErrorCode.BusinessFailed.name());
        inventoryItem.setErrorMessage(ErrorCode.BusinessFailed.getErrorMessage(inventoryItem.getCode(), "Duplicate with partner"));
    }

    public static <T extends InventoryItemStatus> void unprocessed(T inventoryItem) {
        inventoryItem.setSyncStatus(SyncStatus.SYNC_FAILED);
        inventoryItem.setErrorCode(ErrorCode.BusinessFailed.name());
        inventoryItem.setErrorMessage(ErrorCode.BusinessFailed.getErrorMessage(inventoryItem.getCode(), "Unprocessed"));
    }

    public static <T extends InventoryItemStatus> void queryFromPartnerFailed(T inventoryItem, String responseString) {
        inventoryItem.setSyncStatus(SyncStatus.PREPROCESS_FAILED);
        inventoryItem.setErrorCode(ErrorCode.BusinessFailed.name());
        inventoryItem.setErrorMessage(ErrorCode.BusinessFailed.getErrorMessage(inventoryItem.getCode(), "Query with partner failed: " + responseString));
    }

    public static <T extends InventoryItemStatus> void partnerResponseError(T inventoryItem, String responseString) {
        inventoryItem.setSyncStatus(SyncStatus.SYNC_FAILED);
        inventoryItem.setErrorCode(ErrorCode.Unknown.name());
        inventoryItem.setErrorMessage(ErrorCode.Unknown.getErrorMessage(responseString));
    }

    public static <T extends InventoryItemStatus> void partnerPrecheckResponseError(T inventoryItem, String responseString) {
        inventoryItem.setSyncStatus(SyncStatus.PREPROCESS_FAILED);
        inventoryItem.setErrorCode(ErrorCode.Unknown.name());
        inventoryItem.setErrorMessage(ErrorCode.Unknown.getErrorMessage(responseString));
    }

//    public static void credentialCreateError(Map<String, String> inventoryItem, String responseString) {
//        inventoryItem.put("syncStatus", SyncStatus.SYNC_FAILED.name());
//        inventoryItem.put("errorCode", ErrorCode.Unknown.name());
//        inventoryItem.put("errorMessage", ErrorCode.Unknown.getErrorMessage(responseString));
//    }

    public static <T extends InventoryItemStatus> void credentialCreateError(T inventoryItem, String responseString) {
        inventoryItem.setSyncStatus(SyncStatus.SYNC_FAILED);
        inventoryItem.setErrorCode(ErrorCode.Unknown.name());
        inventoryItem.setErrorMessage(ErrorCode.Unknown.getErrorMessage(responseString));
    }

    public static <T extends InventoryItemStatus> void hotelSyncFailed(T inventoryItem, String responseString) {
        inventoryItem.setSyncStatus(SyncStatus.PREPROCESS_FAILED);
        inventoryItem.setErrorCode(ErrorCode.BusinessFailed.name());
        inventoryItem.setErrorMessage(ErrorCode.BusinessFailed.getErrorMessage(inventoryItem.getCode(), "Fail because hotel sync failed" + (null != responseString ? ": " + responseString : "")));
    }

    public static <T extends InventoryItemStatus> void systemError(T inventoryItem, Throwable ex) {
        inventoryItem.setSyncStatus(SyncStatus.PREPROCESS_FAILED);
        inventoryItem.setErrorCode(ErrorCode.System.name());
        inventoryItem.setErrorMessage(ErrorCode.SystemRaw.getErrorMessage(inventoryItem.getCode(), ex.getMessage()));
    }

    public static <T extends InventoryItemStatus> void systemError(T inventoryItem, String errorMessage) {
        inventoryItem.setSyncStatus(SyncStatus.PREPROCESS_FAILED);
        inventoryItem.setErrorCode(ErrorCode.System.name());
        inventoryItem.setErrorMessage(ErrorCode.SystemRaw.getErrorMessage(errorMessage));
    }

    public record InventoryItemResult(
            List<InventoryItemStatus> failedItems,
            List<InventoryItemStatus> successItems
    ) {}

    public static InventoryItemResult countByOperation(List<RemoteChannelService.Operation> operations, ChannelHotelDTO dto) {
        CloneUtil cloneUtil = Mappers.getMapper(CloneUtil.class);
        List<InventoryItemStatus> failedItems = new ArrayList<>();
        List<InventoryItemStatus> successItems = new ArrayList<>();

        if (null == operations || operations.isEmpty()){
            return new InventoryItemResult(failedItems, successItems);
        }
        var hotelInfo = dto.getHotelInfo();
        if (hotelInfo != null && operations.contains(RemoteChannelService.Operation.SaveProperty)) {
            if (hotelInfo.getSyncStatus().isProcessingStatus()) {
                InventoryItemErrorUtil.unprocessed(hotelInfo);
            }
            if (hotelInfo.getSyncStatus().isFail()) {
                failedItems.add(cloneUtil.clone(hotelInfo));
            }
            if (hotelInfo.getSyncStatus().isSuccess()) {
                successItems.add(cloneUtil.clone(hotelInfo));
            }
        }

        if (dto.getRoomsInfo() != null && operations.contains(RemoteChannelService.Operation.SaveRoomTypes)) {
            dto.getRoomsInfo().forEach(roomInfo -> {
                if (roomInfo.getSyncStatus().isProcessingStatus()) {
                    InventoryItemErrorUtil.unprocessed(roomInfo);
                }
                if (roomInfo.getSyncStatus().isFail()) {
                    failedItems.add(cloneUtil.clone(roomInfo));
                }
                if (roomInfo.getSyncStatus().isSuccess()) {
                    successItems.add(cloneUtil.clone(roomInfo));
                }
            });
        }

        if (dto.getRatesInfo() != null && operations.contains(RemoteChannelService.Operation.SaveRatePlans)) {
            dto.getRatesInfo().forEach(rateInfo -> {
                if (rateInfo.getSyncStatus().isProcessingStatus()) {
                    InventoryItemErrorUtil.unprocessed(rateInfo);
                }
                if (rateInfo.getSyncStatus().isFail()) {
                    failedItems.add(cloneUtil.clone(rateInfo));
                }
                if (rateInfo.getSyncStatus().isSuccess()) {
                    successItems.add(cloneUtil.clone(rateInfo));
                }
            });
        }

        if (dto.getProductsInfo() != null && operations.contains(RemoteChannelService.Operation.SaveProducts)) {
            dto.getProductsInfo().forEach(productInfo -> {
                if (productInfo.getSyncStatus().isProcessingStatus()) {
                    InventoryItemErrorUtil.unprocessed(productInfo);
                }
                if (productInfo.getSyncStatus().isFail()) {
                    failedItems.add(cloneUtil.clone(productInfo));
                }
                if (productInfo.getSyncStatus().isSuccess()) {
                    successItems.add(cloneUtil.clone(productInfo));
                }
            });
        }

        return new InventoryItemResult(failedItems, successItems);
    }

}
