package com.derbysoft.next.propertyconnect.channel.task.client.aimapping;

import com.derbysoft.next.commons.boot.logsupport.annotation.stream.StreamLog;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Optional;

/**
 * @Created by <AUTHOR> on 2023/4/20
 */

//@RemoteService(exceptionOnFailure = true)
@FeignClient(name = "mappingPredictionClient", url = "${app.aimapping.url}")
public interface MappingPredictionClient {

    @PostMapping("/v1/roomtype/mapping")
    @StreamLog(proxy = false, inheritPref = true, wrapHeaderAsParameter = true)
    Optional<Object> getRoomTypeMapping(@RequestBody RoomTypePredicationRequest body);

    @PostMapping("/v1/rateplan/mapping")
    @StreamLog(proxy = false, inheritPref = true, wrapHeaderAsParameter = true)
    Optional<Object>getRatePlanMapping(@RequestBody RatePlanPredicationRequest body);
}