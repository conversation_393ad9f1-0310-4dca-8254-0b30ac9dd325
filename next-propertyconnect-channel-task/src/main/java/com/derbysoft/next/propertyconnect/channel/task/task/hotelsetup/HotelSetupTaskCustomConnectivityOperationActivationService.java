package com.derbysoft.next.propertyconnect.channel.task.task.hotelsetup;

import com.derbysoft.next.commons.core.logsupport.constant.LogConst;
import com.derbysoft.next.commons.core.logsupport.handler.LogHandler;
import com.derbysoft.next.commons.core.logsupport.handler.PerfLogHandler;
import com.derbysoft.next.propertyconenct.channel.common.utils.Constants;
import com.derbysoft.next.propertyconenct.channel.common.utils.JSONUtil;
import com.derbysoft.next.propertyconnect.channel.task.client.WebhookAPI;
import com.derbysoft.next.propertyconnect.channel.task.client.alarm.NoticeRequest;
import com.derbysoft.next.propertyconnect.channel.task.config.PerfConst;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.*;
import com.derbysoft.next.propertyconnect.channel.task.service.RemoteService;
import com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.RemoteChannelService;
import com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.adapter.impl.GeneralChannelHotelActivationService;
import com.derbysoft.next.propertyconnect.channel.task.util.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Builder;
import lombok.RequiredArgsConstructor;
import lombok.Value;
import lombok.experimental.ExtensionMethod;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @Created by <AUTHOR> on 2023/3/6
 */

@Service
@Slf4j
@RequiredArgsConstructor
@ExtensionMethod({JSONUtil.class, CollectionUtil.class, BusinessJSONUtil.class})
public class HotelSetupTaskCustomConnectivityOperationActivationService {
    private final RemoteService remoteService;
    private final HotelSetupConfigurationProperties configuration;
    private final GeneralChannelHotelActivationService generalChannelHotelActivationService;
    private final WebhookAPI webhookAPI;
    private final ObjectMapper objectMapper;


    public void setup(String channelId, PerfLogHandler perfLogHandler) {
        this.setup(channelId, this.hotelLists(channelId), null, List.of(), null, perfLogHandler);
    }

    public void setup(String supplierId, String channelId, String channelHotelId, List<RemoteChannelService.Operation> procedure, Boolean ignorePropertyStatusCheck, String notifyUrl, PerfLogHandler perfLogHandler) {
        var hotelId = remoteService.uniqueChannelHotelMapping(channelId, channelHotelId)
                .map(mapping -> mapping.getString("$.hotelId"))
                .orElseThrow(() -> new IllegalStateException(String.format("Channel [%s] hotel [%s] mapping not found in PCProfile", channelId, channelHotelId)));

        remoteService.goDistributorHotel(supplierId, channelHotelId, channelId)
                .ifPresent(hotelItem -> {
                    hotelItem.set("$.supplierId", supplierId);
                    hotelItem.set("$.pcHotelId", hotelId);
                    this.setup(channelId, List.of(hotelItem), ignorePropertyStatusCheck, procedure, notifyUrl, perfLogHandler);
                });
    }

    public void setup(String channelId, Collection<?> hotels, Boolean ignorePropertyStatusCheck, List<RemoteChannelService.Operation> procedure, String notifyUrl, PerfLogHandler perfLogHandler) {
        Assert.notNull(channelId, "HotelSetupProcedure failed with null distributor id");
        var operationToken = Optional.ofNullable(PerfLogHandler.currentHandler()).map(LogHandler::getToken).orElse(null);

        var failedItems = new ArrayList<InventoryItemStatus>();
        var successItems = new ArrayList<InventoryItemStatus>();

        hotels.forEach(hotelItem -> {
            var channelHotelDTO = buildChannelHotelDTO(channelId, hotelItem, operationToken, ignorePropertyStatusCheck);

            var currentProcedure = getSetupProcedureForChannel(procedure, channelHotelDTO);
            currentProcedure.forEach(proc -> generalChannelHotelActivationService.execution(proc, channelHotelDTO));

            var inventoryItemResult = InventoryItemErrorUtil.countByOperation(currentProcedure, channelHotelDTO);

            failedItems.addAll(inventoryItemResult.failedItems());
            successItems.addAll(inventoryItemResult.successItems());
        });

        if (hotels.size() == 1 && !failedItems.isEmpty()) {
            sendMail(channelId, hotels, operationToken, failedItems, perfLogHandler);
        }
        sendNotify(notifyUrl, failedItems, perfLogHandler);

        if (!successItems.isEmpty()) {
            perfLogHandler.messages(Constants.Perf.MESSAGE_CNT, successItems.size());
            perfLogHandler.message(PerfConst.HOTEL, successItems.stream().map(InventoryItemStatus::getCode).collect(Collectors.joining(",")));
        }

        if (!failedItems.isEmpty()) {
            perfLogHandler.messages(Constants.Perf.MESSAGE_FAIL_CNT, failedItems.size());
            perfLogHandler.message(LogConst.Parameter.WARN_MSG, "SetupFailed: " + failedItems.stream().map(InventoryItemStatus::getCode).collect(Collectors.joining(",")));
        }
    }

    private @NotNull List<RemoteChannelService.Operation> getSetupProcedureForChannel(List<RemoteChannelService.Operation> procedure, ChannelHotelDTO channelHotelDTO) {
        List<RemoteChannelService.Operation> channelProcedure = generalChannelHotelActivationService.setupProcedure(channelHotelDTO);

        Set<RemoteChannelService.Operation> operationsToAdd = EnumSet.of(
                RemoteChannelService.Operation.SaveProperty,
                RemoteChannelService.Operation.SaveRoomTypes,
                RemoteChannelService.Operation.SaveRatePlans
        );

        return (procedure == null || procedure.isEmpty())
                ? new ArrayList<>(channelProcedure)
                : Stream.concat(
                procedure.contains(RemoteChannelService.Operation.SavePropertyCombo)
                        ? channelProcedure.stream().filter(operationsToAdd::contains)
                        : Stream.empty(),
                procedure.stream().filter(op -> op != RemoteChannelService.Operation.SavePropertyCombo)
        ).toList();
    }

    private void sendNotify(String notifyUrl, List<InventoryItemStatus> failItems, PerfLogHandler perfLogHandler) {
        if (StringUtils.isBlank(notifyUrl)) {
            return;
        }
        var result = failItems.isEmpty();

        try {
            var url = UriComponentsBuilder.fromUriString(notifyUrl)
                    .queryParam("result", String.valueOf(result))
                    .build()
                    .toUriString();
            webhookAPI.webhookPostCall(url, result ? List.of() : failItems.stream().map(ErrorInventoryItem::fromInventoryItemStatus).toList());
            Optional.ofNullable(perfLogHandler).ifPresent(handler -> handler.message("ext_notified", true));
        } catch (Exception e) {
            Optional.ofNullable(perfLogHandler).ifPresent(handler -> handler.message("ext_notified", false));
            log.error("Failed to notify external system.", e);
        }
    }

    private void sendMail(String channelId, Collection<?> hotels, String operationToken, ArrayList<InventoryItemStatus> failedItems, PerfLogHandler perfLogHandler) {
        try{
            remoteService.notice(NoticeRequest.builder()
                    .enableEmail(true)
                    .mailTo(configuration.getAlarmEmails())
                    .mailSubject(String.format("[%s] %s Setup Failure Alarm", EnvUtil.getEnv().toUpperCase(), channelId))
                    .alarmContent(MailContextGenerator.generateSetupMailContent(channelId, hotels.stream().map(hotel -> hotel.getString("$.hotelId")).findFirst().orElse(null), operationToken, failedItems.stream()
                            .map(item -> {
                                if (item instanceof ChannelHotelInfo) {
                                    return Pair.of(RemoteChannelService.Operation.SaveProperty, item);
                                }
                                if (item instanceof ChannelRoomInfo) {
                                    return Pair.of(RemoteChannelService.Operation.SaveRoomTypes, item);
                                }
                                if (item instanceof ChannelRateInfo) {
                                    return Pair.of(RemoteChannelService.Operation.SaveRatePlans, item);
                                }
                                return Pair.of(RemoteChannelService.Operation.Unknown, item);
                            })
                            .collect(Collectors.toList())
                    ))
                    .build()
            );
            perfLogHandler.message("ext_mailed", true);
        }catch (Exception e){
            perfLogHandler.message("ext_mailed", false);
            log.error("Failed to send failed mail.", e);
        }
    }

    private @NotNull ChannelHotelDTO buildChannelHotelDTO(String channelId, Object hotelItem, String operationToken, Boolean ignorePropertyStatusCheck) {
        var channelHotelDTO = new ChannelHotelDTO()
                .setOperationToken(operationToken)
                .setSupplierId(hotelItem.getString("$.supplierId"))
                .setSupplierHotelId(hotelItem.getString("$.pcHotelId"))
                .setChannelId(channelId)
                .setChannelHotelId(hotelItem.getString("$.hotelId"))
                .setExtensions(new HashMap<>());

        JSONUtil.set(channelHotelDTO, "$.extensions.mapWithoutPrefix", true);

        var channelHotelInfo = new ChannelHotelInfo();
        channelHotelInfo.setId(channelHotelDTO.getChannelHotelId());
        channelHotelInfo.setStatus(hotelItem.isActive()
                ? ItemStatus.Actived : Boolean.TRUE.equals(ignorePropertyStatusCheck)
                ? ItemStatus.Actived : ItemStatus.Deactived);
        channelHotelInfo.setAriType(hotelItem.getString("$.ariType"));
        channelHotelInfo.setRateType(hotelItem.getString("$.rateType"));
        channelHotelInfo.setTimezone(hotelItem.getString("$.timezone"));
        channelHotelInfo.setSyncStatus(SyncStatus.DRAFT);
        channelHotelInfo.setSettings(hotelItem.getObject("$.settings", Map.class));
        channelHotelInfo.setExtensions(new HashMap<>());

        if (!configuration.getEnableAutoDeactivate() && ItemStatus.Deactived == channelHotelInfo.getStatus()){
            channelHotelInfo.setSyncStatus(SyncStatus.IGNORED);
        }

        channelHotelDTO.setHotelInfo(channelHotelInfo);

        this.handleRoomTypes(hotelItem, channelHotelDTO);
        this.handleRatePlans(hotelItem, channelHotelDTO);
        return channelHotelDTO;
    }

    protected Collection<?> hotelLists(String channelId) {
        var distributorHotelMappings = remoteService.getDistributorHotelMappings(channelId);
        return remoteService.getAllGoDistributorHotelIds(channelId)
                .stream()
                .distinct()
                .filter(distributorHotelMappings::containsKey)
                .collect(Collectors.toList())
                .mapEachParallel(100, hotelId -> remoteService.goDistributorHotel(hotelId, channelId).orElse(null))
                .stream()
                .map(hotelItem -> hotelItem.set("$.pcHotelId", distributorHotelMappings.get(hotelItem.getString("$.hotelId"))))
                .collect(Collectors.toList());
    }


    public void handleRoomTypes(Object hotelItem, ChannelHotelDTO channelHotelDTO) {
        var roomsInfo = new ArrayList<ChannelRoomInfo>();

        JSONUtil.getCollection(hotelItem, "$.products").stream()
                .collect(Collectors.groupingBy(
                        product -> product.getString("$.roomId"),
                        Collectors.mapping(product -> ProductSummary.builder()
                                .name(product.getString("$.roomName"))
                                .active(product.isActive())
                                .occupancy(product.getObject("$.occupancy", ChannelRoomInfo.Occupancy.class))
                                .build(), Collectors.toList())
                ))
                .forEach((roomId, products) -> {
                    var occupancy = products.stream()
                            .reduce((now, next) -> {
                                if (now.getOccupancy().equals(next.getOccupancy())) {
                                    return now;
                                }
                                throw new IllegalStateException("RoomTypeSetup failed due to the ambiguous occupancy.");
                            })
                            .map(ProductSummary::getOccupancy);
                    var channelRoomInfo = new ChannelRoomInfo();
                    channelRoomInfo.setCode(roomId);
                    channelRoomInfo.setName(products.stream().findFirst().map(ProductSummary::getName).orElse(null));
                    channelRoomInfo.setStatus(getItemStatus(products));
                    channelRoomInfo.setSyncStatus(SyncStatus.DRAFT);
                    channelRoomInfo.setOccupancy(occupancy.orElse(null));

                    if (!configuration.getEnableAutoDeactivate() && ItemStatus.Deactived == channelRoomInfo.getStatus()) {
                        channelRoomInfo.setSyncStatus(SyncStatus.IGNORED);
                    }
                    roomsInfo.add(channelRoomInfo);
                });

        channelHotelDTO.setRoomsInfo(roomsInfo);
    }

    public void handleRatePlans(Object hotelItem, ChannelHotelDTO channelHotelDTO) {
        var ratesInfo = new ArrayList<ChannelRateInfo>();

        hotelItem.getCollection("$.products").stream()
                .collect(Collectors.groupingBy(
                        product -> product.getString("$.rateId"),
                        Collectors.mapping(product -> ProductSummary.builder()
                                .name(product.getString("$.rateName"))
                                .active(product.isActive())
                                .build(), Collectors.toList())
                ))
                .forEach((rateId, products) -> {
                    var channelRateInfo = new ChannelRateInfo();
                    channelRateInfo.setCode(rateId);
                    channelRateInfo.setName(products.stream().findFirst().map(ProductSummary::getName).orElse(null));
                    channelRateInfo.setStatus(getItemStatus(products));
                    channelRateInfo.setSyncStatus(SyncStatus.DRAFT);

                    if (!configuration.getEnableAutoDeactivate() && ItemStatus.Deactived == channelRateInfo.getStatus()) {
                        channelRateInfo.setSyncStatus(SyncStatus.IGNORED);
                    }
                    ratesInfo.add(channelRateInfo);
                });

        channelHotelDTO.setRatesInfo(ratesInfo);
    }

    private ItemStatus getItemStatus(List<ProductSummary> productsSummary){
        if (productsSummary.stream().anyMatch(product -> Boolean.TRUE.equals(product.active))) {
            return ItemStatus.Actived;
        }
        if (productsSummary.stream().allMatch(product -> Boolean.FALSE.equals(product.active))) {
            return ItemStatus.Deactived;
        }
        throw new IllegalStateException("Room/Rate Setup failed due to the ambiguous status.");
    }


    @Value
    @Builder
    private static class ProductSummary {
        String name;
        Boolean active;
        ChannelRoomInfo.Occupancy occupancy;
    }

}
