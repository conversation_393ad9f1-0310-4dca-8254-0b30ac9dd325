package com.derbysoft.next.propertyconnect.channel.task.domain.repository;

import com.derbysoft.next.propertyconnect.channel.task.domain.entity.ChannelHotelPO;
import org.springframework.data.mongodb.repository.MongoRepository;

/**
 * @Created by <AUTHOR> on 2023/3/21
 */
public interface ChannelHotelRepository extends MongoRepository<ChannelHotelPO, String> {

    ChannelHotelPO findFirstByChannelIdAndChannelHotelId(String channelId, String channelHotelId);

}
