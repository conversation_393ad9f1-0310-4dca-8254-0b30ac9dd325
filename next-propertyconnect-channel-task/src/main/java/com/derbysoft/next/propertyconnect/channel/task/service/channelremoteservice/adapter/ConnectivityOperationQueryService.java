package com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.adapter;

import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelHotelDTO;

import java.util.List;

/**
 * @Created by <AUTHOR> on 2023/7/21
 */

public interface ConnectivityOperationQueryService {

    ChannelHotelDTO propertyQuery(String supplierId, String channelId, String supplierHotelId, String channelHotelId);

    ChannelHotelDTO propertyRoomTypeQuery(String supplierId, String channelId, String supplierHotelId, String channelHotelId, List<String> channelRoomIds);

    ChannelHotelDTO propertyRatePlanQuery(String supplierId, String channelId, String supplierHotelId, String channelHotelId, List<String> channelRateIds);

}
