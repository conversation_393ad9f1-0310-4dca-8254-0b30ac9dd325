package com.derbysoft.next.propertyconnect.channel.task.service.mapping;

import com.derbysoft.next.propertyconnect.channel.task.controller.vo.mapping.PredictionModel;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelProductsDTO;

import java.util.List;

/**
 * @Created by <AUTHOR> on 2023/4/21
 */

public interface PredictionService {

    PredictionModel supportModel();

    void prediction(ChannelProductsDTO channelProducts, List<ChannelProductsDTO.Product> candidateRooms, List<ChannelProductsDTO.Product> candidateRates, Double threshold);
}
