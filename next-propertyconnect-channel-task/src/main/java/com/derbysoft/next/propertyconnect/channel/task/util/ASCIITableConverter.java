package com.derbysoft.next.propertyconnect.channel.task.util;

public class ASCIITableConverter {

    public static String convertToASCIITable(String[][] array) {
        StringBuilder table = new StringBuilder();

        int rows = array.length;
        int cols = array[0].length;

        int[] colWidths = new int[cols];

        // 计算每列的最大宽度
        for (int col = 0; col < cols; col++) {
            int maxWidth = 0;
            for (int row = 0; row < rows; row++) {
                int elementWidth = array[row][col].length();
                if (elementWidth > maxWidth) {
                    maxWidth = elementWidth;
                }
            }
            colWidths[col] = maxWidth;
        }

        // 构建表头
        for (int col = 0; col < cols; col++) {
            table.append(formatCell(array[0][col], colWidths[col]));
            table.append(" | ");
        }
        table.append("\n");

        // 添加分隔线
        for (int col = 0; col < cols; col++) {
            table.append("-".repeat(Math.max(0, colWidths[col])));
            table.append("- ");
            if (col < cols - 1) {
                table.append("-");
            }
        }
        table.append("\n");

        // 添加数据行
        for (int row = 1; row < rows; row++) {
            for (int col = 0; col < cols; col++) {
                table.append(formatCell(array[row][col], colWidths[col]));
                table.append(" | ");
            }
            table.append("\n");
        }

        return table.toString();
    }

    // 格式化单元格内容，使其填充到指定宽度
    private static String formatCell(String content, int width) {
        StringBuilder cell = new StringBuilder(content);
        while (cell.length() < width) {
            cell.append(" ");
        }
        return cell.toString();
    }

}
