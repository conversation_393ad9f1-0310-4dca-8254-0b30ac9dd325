package com.derbysoft.next.propertyconnect.channel.task.client;

import com.derbysoft.next.commons.boot.logsupport.annotation.stream.StreamLog;
import com.derbysoft.next.propertyconenct.channel.common.utils.JSONUtil;
import com.derbysoft.next.propertyconnect.channel.task.client.request.ProfileShellStatusRequest;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Created by <AUTHOR> on 2023/2/27
 */

//@RemoteService(exceptionOnFailure = true)
@FeignClient(name = "pcProfileClient", url = "${app.pc.apigateway-url}pcprofile/")
public interface PCProfileClient {

    @GetMapping("sources/PROPERTYCONNECT/hotels")
    List<Object> goDistributorHotels(@RequestParam("distributorId") String distributorId);

    @GetMapping("sources/{supplierId}/hotels")
    List<Object> goDistributorHotels(@PathVariable("supplierId") String supplierId,
                                     @RequestParam("distributorId") String distributorId);

    @GetMapping("sources/PROPERTYCONNECT/hotel/{hotelId}")
    Optional<Object> goDistributorHotel(@PathVariable("hotelId") String distributorHotelId,
                                        @RequestParam("distributorId") String distributorId);

    @GetMapping("sources/{supplierId}/hotel/{hotelId}")
    Optional<Object> goDistributorHotel(@PathVariable("supplierId") String supplierId,
                              @PathVariable("hotelId") String distributorHotelId,
                              @RequestParam("distributorId") String distributorId);

    @GetMapping("api/hotelChannelMappings")
    List<Object> channelHotelMapping(@RequestParam("channelId") String distributorId,
                                     @RequestParam("hotelId") String pcHotelId);

    @GetMapping("api/hotelChannelMappings/unique")
    Optional<Object> uniqueChannelHotelMapping(@RequestParam("channelId") String channelId,
                                     @RequestParam("channelHotelId") String channelHotelId);

    @GetMapping("api/channels/{channelId}")
    Optional<ChannelDTO> getDistributorDetails(@PathVariable("channelId") String distributorId);

    @GetMapping("api/{hotelId}/products")
    List<Object> hotelProducts(@PathVariable("hotelId") String pcHotelId);

    @GetMapping("api/{hotelId}/roomTypes")
    @StreamLog(proxy = false, inheritPref = true, wrapHeaderAsParameter = true)
    List<Object> hotelRoomTypes(@PathVariable("hotelId") String pcHotelId);

    @GetMapping("api/{hotelId}/ratePlans")
    @StreamLog(proxy = false, inheritPref = true, wrapHeaderAsParameter = true)
    List<Object> hotelRatePlans(@PathVariable("hotelId") String pcHotelId);

    @GetMapping("api/{hotelId}/ratePlans/{ratePlanId}")
    @StreamLog(proxy = false, inheritPref = true, wrapHeaderAsParameter = true)
    List<Object> hotelRatePlan(@PathVariable("hotelId") String pcHotelId,
                               @PathVariable("ratePlanId") String ratePlanId);

    @GetMapping("api/hotelConnections/unique")
    @StreamLog(proxy = false, inheritPref = true, wrapHeaderAsParameter = true)
    Optional<Object> uniqueHotelConnections(@RequestParam("channelId") String channelId,
                                  @RequestParam("channelHotelId") String channelHotelId);

    @GetMapping("api/hotelConnections")
    Optional<List<Object>> hotelConnections(@RequestParam("channelId") String channelId,
                                  @RequestParam("hotelId") String hotelId);

    @GetMapping("api/hotels/{hotelId}")
    Optional<Object> getHotelDetails(@PathVariable("hotelId") String pcHotelId);

    @PostMapping("api/channelconnection/shell/status")
    @StreamLog(proxy = false, inheritPref = true, wrapHeaderAsParameter = true)
    Optional<Object> pushShellStatus(@RequestBody ProfileShellStatusRequest request);


    default Collection<String> getActiveGoDistributorHotelIds(String distributorId) {
        return JSONUtil.getCollection(this.goDistributorHotels(distributorId), "$[?(@.status=='Actived')].hotelId", String.class);
    }

    default Collection<String> getActiveGoDistributorHotelIds(String supplierId, String distributorId) {
        return JSONUtil.getCollection(this.goDistributorHotels(supplierId, distributorId), "$[?(@.status=='Actived')].hotelId", String.class);
    }

    default Collection<String> getAllGoDistributorHotelIds(String distributorId) {
        return JSONUtil.getCollection(this.goDistributorHotels(distributorId), "$.*.hotelId", String.class);
    }

    default Map<String, String> getDistributorHotelMappings(String distributorId) {
        return this.channelHotelMapping(distributorId, null)
                .stream()
                .collect(Collectors.toMap(
                        item -> JSONUtil.getString(item, "$.channelHotelId"),
                        item -> JSONUtil.getString(item, "$.hotelId"),
                        (v1, v2) -> {
                            if (v1.equals(v2)) {
                                return v1;
                            }
                            throw new IllegalArgumentException("Duplicate channel hotel mappings for pc hotel " + v1 + " and " + v2);
                        }
                ));
    }
}