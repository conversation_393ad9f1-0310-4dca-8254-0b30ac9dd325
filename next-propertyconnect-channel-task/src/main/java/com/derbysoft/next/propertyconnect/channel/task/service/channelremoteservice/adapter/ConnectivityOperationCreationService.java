package com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.adapter;

import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelHotelDTO;
import com.derbysoft.next.propertyconnect.channel.task.service.ChannelHotelSetupProcedure;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.function.Consumer;

/**
 * @Created by <AUTHOR> on 2023/5/26
 */

public interface ConnectivityOperationCreationService extends RemoteChannelAdapterService, ChannelHotelSetupProcedure {

    @Override
    default LinkedHashMap<Operation, Consumer<ChannelHotelDTO>> supportOperations(){
        var map = new LinkedHashMap<Operation, Consumer<ChannelHotelDTO>>();
        map.put(Operation.SaveCredential, this::credentialCreation);
        map.put(Operation.SaveProperty, this::propertyCreation);
        map.put(Operation.SaveRoomTypes, this::roomTypeCreation);
        map.put(Operation.SaveRatePlans, this::ratePlanCreation);
        map.put(Operation.SaveProducts, this::productCreation);
        map.put(Operation.TriggerARIRefresh, this::triggerARIRefresh);
        return map;
    }

    @Override
    default List<Operation> setupProcedure(ChannelHotelDTO dto){
        return new ArrayList<>(supportOperations().keySet());
    }

    @Override
    default ChannelHotelDTO execution(Operation operation, ChannelHotelDTO dto) {
        return ChannelHotelSetupProcedure.super.execution(operation, dto);
    }

    void credentialCreation(ChannelHotelDTO dto);
    ChannelHotelDTO propertyQuery(ChannelHotelDTO dto);
    void propertyCreation(ChannelHotelDTO dto);
    void roomTypeCreation(ChannelHotelDTO dto);
    void ratePlanCreation(ChannelHotelDTO dto);
    void productCreation(ChannelHotelDTO dto);
    void triggerARIRefresh(ChannelHotelDTO dto);

}
