package com.derbysoft.next.propertyconnect.channel.task.domain.dto;

/**
 * @Created by <AUTHOR> on 2023/5/25
 */

public enum SyncStatus {
    DRAFT, PREPROCESS_FAILED, SUBMITTED, <PERSON><PERSON><PERSON>IT_FAILED, SYNC<PERSON>, S<PERSON>NC_FAILED, IGNORED;

    public boolean isSuccess(){
        return this == SYNCED || this == IGNORED;
    }
    public boolean isFail(){
        return this == SYNC_FAILED || this == SUBMIT_FAILED || this == PREPROCESS_FAILED;
    }
    public boolean isFinalStatus(){
        return isSuccess() || isFail();
    }
    public boolean isProcessingStatus(){
        return this == DRAFT || this == SUBMITTED;
    }

}
