package com.derbysoft.next.propertyconnect.channel.task.controller.api;

import com.derbysoft.extension.aggregationtool.controllerservice.ControllerAndService;
import com.derbysoft.next.commons.boot.logsupport.annotation.perf.PerfLog;
import com.derbysoft.next.commons.boot.logsupport.annotation.perf.PerfParameter;
import com.derbysoft.next.commons.boot.logsupport.annotation.stream.StreamLog;
import com.derbysoft.next.commons.boot.logsupport.annotation.stream.StreamLogRequest;
import com.derbysoft.next.propertyconenct.channel.common.exception.response.UnifyResult;
import com.derbysoft.next.propertyconenct.channel.common.utils.Constants;
import com.derbysoft.next.propertyconnect.channel.task.controller.vo.ChannelRatePlanSaveVO;
import com.derbysoft.next.propertyconnect.channel.task.controller.vo.rate.ChannelRateVO;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

/**
 * @Created by <AUTHOR> on 2023/4/27
 */


@ControllerAndService
@RequestMapping("/api")
@Tag(name = "ChannelRatePlanAPI", description = "ChannelRatePlanAPI", extensions = @Extension(properties = @ExtensionProperty(name = "order", value = "5")))
public interface ChannelRateAPI {


    @PerfLog("SaveRatePlans")
    @StreamLog(inheritPref = true)
    @PerfParameter(name = Constants.Perf.CHANNEL, value = "#channelId")
    @PerfParameter(name = Constants.Perf.HOTEL_CHANNEL, value = "#channelHotelId")
    @PerfParameter(name = Constants.Perf.HOTEL_SUPPLIER, value = "#hotelId")
    @PostMapping({
            "/channels/{channelId}/rateplans",
            "/channels/{channelId}/channelhotels/{channelHotelId}/rateplans"
    })
    @Operation(summary = "SyncRatePlans")
    UnifyResult<ChannelRatePlanSaveVO> syncRatePlans(@PathVariable String channelId,
                                                     @PathVariable(required = false) String channelHotelId,
                                                     @RequestParam(required = false) @Schema(description = "** HotelId is required for SYNXISDISTRIBUTOR")
                                                     String hotelId);

    @PerfLog("AsyncNotify")
    @StreamLog(inheritPref = true)
    @StreamLogRequest(body = "#callbackData")
    @PerfParameter(name = Constants.Perf.MESSAGE_TYPE, value = "'SaveRatePlans'")
    @PerfParameter(name = Constants.Perf.CHANNEL, value = "#channelId")
    @PerfParameter(name = Constants.Perf.HOTEL_CHANNEL, value = "#channelHotelId")
    @PerfParameter(name = Constants.Perf.HOTEL_SUPPLIER, value = "#hotelId")
    @PostMapping(value = "/channels/{channelId}/channelhotels/{channelHotelId}/rateplans/callback")
    @Hidden
    UnifyResult<ChannelRatePlanSaveVO> syncRatePlansCallback(@PathVariable String channelId,
                                                             @PathVariable String channelHotelId,
                                                             @RequestParam String hotelId,
                                                             @RequestBody String callbackData);

    @GetMapping("/channels/{channelId}/channelhotels/{channelHotelId}/rateplans")
    @Hidden
    UnifyResult<ChannelRateVO> getRatePlans(@PathVariable String channelId,
                                            @PathVariable String channelHotelId);

    @PostMapping("/channels/{channelId}/channelhotels/{channelHotelId}/rateplans")
    @Hidden
    UnifyResult<ChannelRateVO> saveRatePlans(@PathVariable String channelId,
                                             @PathVariable String channelHotelId,
                                             @RequestBody ChannelRateVO channelRateVO,
                                             @RequestParam(required = false, defaultValue = "true") Boolean async,
                                             @Schema(description = "ratesInfo.code is required for retry mode", defaultValue = "false")
                                             @RequestParam(required = false, defaultValue = "false") Boolean retry);
    @DeleteMapping("/channels/{channelId}/channelhotels/{channelHotelId}/rateplans")
    @Hidden
    UnifyResult<Void> deleteRatePlans(@PathVariable String channelId,
                                      @PathVariable String channelHotelId);

}
