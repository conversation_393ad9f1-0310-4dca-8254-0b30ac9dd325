package com.derbysoft.next.propertyconnect.channel.task.domain.entity;

import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;


@EqualsAndHashCode(callSuper = true)
@Builder
@Data
@Document(collection = "channel_products")
public class ChannelProductsPO extends BaseEntity {

    @Id
    private String id;
    private String channelId;
    private String hotelName;
    private String channelHotelId;
    private List<Product> channelProducts;
    private LocalDateTime retrieveDate;

    @Data
    public static class Product {
        private String channelRoomId;
        private String channelRoomName;
        private String channelRateId;
        private String channelRateName;
        private String status;
        private Boolean availStatus;
        private Map<String, Object> extensions;
    }

}
