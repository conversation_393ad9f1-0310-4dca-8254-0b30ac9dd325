package com.derbysoft.next.propertyconnect.channel.task.service.mapping.impl;

import com.derbysoft.next.propertyconenct.channel.common.exception.response.RequestDataNotFoundException;
import com.derbysoft.next.propertyconenct.channel.common.utils.JSONUtil;
import com.derbysoft.next.propertyconenct.channel.common.utils.ObjectUtil;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelProductsDTO;
import com.derbysoft.next.propertyconnect.channel.task.service.RemoteService;
import com.derbysoft.next.propertyconnect.channel.task.service.impl.ChannelProductsServiceProxy;
import com.derbysoft.next.propertyconnect.channel.task.service.mapping.MappingPredictionService;
import com.derbysoft.next.propertyconnect.channel.task.service.mapping.PredictionReportService;
import com.derbysoft.next.propertyconnect.channel.task.service.mapping.PredictionService;
import com.derbysoft.next.propertyconnect.channel.task.util.BusinessJSONUtil;
import com.derbysoft.next.propertyconnect.channel.task.util.CloneUtil;
import lombok.RequiredArgsConstructor;
import lombok.experimental.ExtensionMethod;
import org.mapstruct.factory.Mappers;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.stream.Collectors;

/**
 * @Created by <AUTHOR> on 2023/4/20
 */

@Service
@ExtensionMethod({JSONUtil.class, BusinessJSONUtil.class, ObjectUtil.class})
@EnableConfigurationProperties(PredictionServiceConfigProperties.class)
@RequiredArgsConstructor
public class MappingPredictionServiceImpl implements MappingPredictionService {

    private final RemoteService remoteService;
    private final ChannelProductsServiceProxy channelProductsService;
    private final PredictionService predictionService;
    private final PredictionServiceConfigProperties predictionServiceConfigProperties;
    private final PredictionReportService predictionReportService;
    private final CloneUtil cloneUtil = Mappers.getMapper(CloneUtil.class);

    @Override
    public ChannelProductsDTO getAIMapping(String channelId, String channelHotelId, String hotelId, Boolean refresh, Double threshold) {
        if (null != threshold && (threshold < 0 || threshold > 1)) {
            throw new IllegalArgumentException("Threshold must be between 0.00 and 1.00");
        }
        var pcHotelId = StringUtils.hasText(hotelId) ? hotelId : remoteService.uniqueHotelConnections(channelId, channelHotelId)
                .map(hotelConnection -> hotelConnection.getString("$.hotelId"))
                .filter(StringUtils::hasText)
                .orElseThrow(() -> new IllegalStateException("Hotel connections not found for " + channelId + " " + channelHotelId));

        var rawRooms = remoteService.hotelRoomTypes(pcHotelId);
        var activeRooms = rawRooms.stream().filter(roomType -> roomType.isActive())
                .map(room -> new ChannelProductsDTO.Product()
                        .setChannelId(channelId)
                        .setHotelId(pcHotelId)
                        .setRoomId(room.getString("$.roomId"))
                        .setRoomName(room.getString("$.roomName"))
                        .setBedType(room.getString("$.bedType"))
                        .setMaxOccupancy(room.getInteger("$.occupancy.maxOccupancy"))
                )
                .filter(room -> StringUtils.hasText(room.getRoomId()) && StringUtils.hasText(room.getRoomName()))
                .distinct()
                .collect(Collectors.toList());

        var rawRates = remoteService.hotelRatePlans(pcHotelId);
        var activeRates = rawRates.stream().filter(ratePlan -> ratePlan.isActive())
                .map(rate -> new ChannelProductsDTO.Product()
                        .setChannelId(channelId)
                        .setHotelId(pcHotelId)
                        .setRateId(rate.getString("$.rateId"))
                        .setRateName(rate.getString("$.rateName"))
                        .setMealPlan(rate.getString("$.defaultMealPlan"))
                        .setPayType(rate.getString("$.paymentType"))
                )
                .filter(rate -> StringUtils.hasText(rate.getRateId()) && StringUtils.hasText(rate.getRateName()))
                .distinct()
                .collect(Collectors.toList());

        var channelProducts = channelProductsService.getChannelProducts("PROPERTYCONNECT", channelId, channelHotelId, refresh, true);
        if (null == channelProducts || null == channelProducts.getChannelProducts() || channelProducts.getChannelProducts().isEmpty()) {
            throw new RequestDataNotFoundException("Channel products for Supplier [PROPERTYCONNECT] Channel [" + channelId + "] ChannelHotel [" + channelHotelId + "] SupplierHotel [" + pcHotelId + "]");
        }
        channelProducts.setSupplierId("PROPERTYCONNECT");
        channelProducts.setChannelId(channelId);
        channelProducts.setHotelId(pcHotelId);

        if (null == threshold) {
            threshold = predictionServiceConfigProperties.getDefaultThreshold();
        }
        var snapshotChannelProducts = cloneUtil.clone(channelProducts);
        var timeBeforePrediction = System.currentTimeMillis();
        predictionService.prediction(channelProducts, activeRooms, activeRates, threshold);
        var timeAfterPrediction = System.currentTimeMillis();
        channelProducts.getReference(ChannelProductsDTO::getExtensions, () -> new HashMap<String, Object>()).put("threshold", threshold);
        snapshotChannelProducts.getReference(ChannelProductsDTO::getExtensions, () -> new HashMap<String, Object>()).put("predicationTime", timeAfterPrediction - timeBeforePrediction);
        predictionReportService.saveAiMappingSnapshot(channelProducts, rawRooms, rawRates, snapshotChannelProducts);
        return channelProducts;
    }
}
