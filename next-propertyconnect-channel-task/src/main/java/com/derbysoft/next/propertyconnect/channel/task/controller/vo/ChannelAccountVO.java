package com.derbysoft.next.propertyconnect.channel.task.controller.vo;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Getter;
import lombok.experimental.FieldNameConstants;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.function.BiConsumer;
import java.util.function.BiFunction;
import java.util.function.Function;

/**
 * @Created by <AUTHOR> on 2023/4/12
 */

@Data
public class ChannelAccountVO {
    @Schema(example = "BOOKINGCOM")
    private String channelId;
    @Schema(example = "Booking.com")
    private String channelName;

    private AccountSetting accountSettings;

    @FieldNameConstants
    public static class AccountSetting implements Map<String, Object> {

        private final HashMap<String, Object> map = new HashMap<>();

        @Schema(example = "*****CLIENT_ID*****")
        @Getter
        private String username;

        @Schema(example = "*****CLIENT_SECRET*****")
        @Getter
        private String password;

        @Schema(example = "*****API_KEY*****")
        @Getter
        private String apiKey;

        @JsonIgnore
        public Map<String, Object> getInnerMap() {
            return map;
        }

        @JsonAnySetter
        public void setMagicMap(String key, String value) {
            map.put(key, value);
        }

        @JsonAnyGetter
        public Map<String, Object> getMagicMap() {
            return map;
        }

        public AccountSetting() {}

        public AccountSetting setUsername(String username) {
            this.username = username;
            map.put(Fields.username, username);
            return this;
        }

        public AccountSetting setPassword(String password) {
            this.password = password;
            map.put(Fields.password, password);
            return this;
        }

        public AccountSetting setApiKey(String apiKey) {
            this.apiKey = apiKey;
            map.put(Fields.apiKey, apiKey);
            return this;
        }

        @Override
        public int size() {
            return map.size();
        }

        @JsonIgnore
        @Override
        public boolean isEmpty() {
            return map.isEmpty();
        }

        @Override
        public Object get(Object key) {
            return map.get(key);
        }

        @Override
        public boolean containsKey(Object key) {
            return map.containsKey(key);
        }

        @Nullable
        @Override
        public Object put(String key, Object value) {
            return map.put(key, value);
        }

        @Override
        public void putAll(Map<? extends String, ?> m) {
            map.putAll(m);
        }

        @Override
        public Object remove(Object key) {
            return map.remove(key);
        }

        @Override
        public void clear() {
            map.clear();
        }

        @Override
        public boolean containsValue(Object value) {
            return map.containsValue(value);
        }

        @NotNull
        @Override
        public Set<String> keySet() {
            return map.keySet();
        }

        @NotNull
        @Override
        public Collection<Object> values() {
            return map.values();
        }

        @NotNull
        @Override
        public Set<Entry<String, Object>> entrySet() {
            return map.entrySet();
        }

        @Override
        public Object getOrDefault(Object key, Object defaultValue) {
            return map.getOrDefault(key, defaultValue);
        }

        @Nullable
        @Override
        public Object putIfAbsent(String key, Object value) {
            return map.putIfAbsent(key, value);
        }

        @Override
        public boolean remove(Object key, Object value) {
            return map.remove(key, value);
        }

        @Override
        public boolean replace(String key, Object oldValue, Object newValue) {
            return map.replace(key, oldValue, newValue);
        }

        @Nullable
        @Override
        public Object replace(String key, Object value) {
            return map.replace(key, value);
        }

        @Override
        public Object computeIfAbsent(String key, Function<? super String, ?> mappingFunction) {
            return map.computeIfAbsent(key, mappingFunction);
        }

        @Override
        public Object computeIfPresent(String key, BiFunction<? super String, ? super Object, ?> remappingFunction) {
            return map.computeIfPresent(key, remappingFunction);
        }

        @Override
        public Object compute(String key, BiFunction<? super String, ? super Object, ?> remappingFunction) {
            return map.compute(key, remappingFunction);
        }

        @Override
        public Object merge(String key, Object value, BiFunction<? super Object, ? super Object, ?> remappingFunction) {
            return map.merge(key, value, remappingFunction);
        }

        @Override
        public void forEach(BiConsumer<? super String, ? super Object> action) {
            map.forEach(action);
        }

        @Override
        public void replaceAll(BiFunction<? super String, ? super Object, ?> function) {
            map.replaceAll(function);
        }

        @Override
        public Object clone() {
            return map.clone();
        }
    }

}
