package com.derbysoft.next.propertyconnect.channel.task.service;

import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelHotelDTO;
import com.derbysoft.next.propertyconnect.channel.task.service.customizationservice.ChannelCustomizationService;

public interface PreProcessingService extends ChannelCustomizationService {
    void preProcess(ChannelHotelDTO channelHotel);

    void postProcess(ChannelHotelDTO channelHotel);
}
