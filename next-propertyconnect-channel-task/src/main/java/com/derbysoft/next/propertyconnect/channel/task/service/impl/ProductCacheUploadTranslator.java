package com.derbysoft.next.propertyconnect.channel.task.service.impl;

import com.derbysoft.next.propertyconenct.channel.common.utils.DateTimeUtil;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelProductsDTO;
import org.apache.poi.ss.usermodel.DataFormatter;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * @Created by <AUTHOR> on 10/10/2024
 */

@Component
public class ProductCacheUploadTranslator {

    private final DataFormatter formatter = new DataFormatter();

    public ChannelProductsDTO processExcelFile(MultipartFile file) {
        ChannelProductsDTO channelProductsDTO = new ChannelProductsDTO();
        List<ChannelProductsDTO.Product> productList = new ArrayList<>();

        try (Workbook workbook = new XSSFWorkbook(file.getInputStream())) {
            Sheet sheet = workbook.getSheetAt(0);
            boolean isFirstRow = true;
            for (Row row : sheet) {
                if (isFirstRow) {
                    isFirstRow = false;
                    continue;
                }

                ChannelProductsDTO.Product product = new ChannelProductsDTO.Product();
                product.setChannelRoomId(formatter.formatCellValue(row.getCell(3)));
                product.setChannelRoomName(formatter.formatCellValue(row.getCell(4)));
                product.setChannelRateId(formatter.formatCellValue(row.getCell(5)));
                product.setChannelRateName(formatter.formatCellValue(row.getCell(6)));
                product.setAvailStatus(Optional.ofNullable(formatter.formatCellValue(row.getCell(7)))
                        .map(Boolean::valueOf)
                        .orElse(null));

                if (null == product.getChannelRoomId() || product.getChannelRoomId().isEmpty()
                        || null == product.getChannelRateId() || product.getChannelRateId().isEmpty()) {
                    continue;
                }
                productList.add(product);
            }

            channelProductsDTO.setChannelId(formatter.formatCellValue(sheet.getRow(1).getCell(0)));
            channelProductsDTO.setChannelHotelId(formatter.formatCellValue(sheet.getRow(1).getCell(1)));
            channelProductsDTO.setHotelName(formatter.formatCellValue(sheet.getRow(1).getCell(2)));
        } catch (IOException e) {
            e.printStackTrace();
        }

        channelProductsDTO.setChannelProducts(productList);
        channelProductsDTO.setRetrieveDate(LocalDateTime.now(ZoneOffset.UTC).format(DateTimeUtil.ISO_LOCAL_DATE_TIME_WITH_MILLISECOND));
        return channelProductsDTO;
    }
}
