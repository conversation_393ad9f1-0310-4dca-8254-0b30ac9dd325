package com.derbysoft.next.propertyconnect.channel.task.task.hotelsetup;

import com.derbysoft.extension.schedulecentersupport.annotation.ScheduleCenterTaskExecutor;
import com.derbysoft.next.commons.boot.logsupport.annotation.perf.PerfParameter;
import com.derbysoft.next.commons.core.logsupport.constant.LogConst;
import com.derbysoft.next.commons.core.logsupport.handler.PerfLogHandler;
import com.derbysoft.next.propertyconenct.channel.common.utils.JSONUtil;
import com.derbysoft.next.propertyconnect.channel.task.config.schedulecenter.ChannelServiceTask;
import com.derbysoft.next.propertyconnect.channel.task.config.schedulecenter.ChannelServiceTypedTask;
import com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.RemoteChannelService;
import com.derbysoft.next.propertyconnect.channel.task.task.ChannelServiceTaskPerfLog;
import com.derbysoft.next.propertyconnect.channel.task.util.CollectionUtil;
import com.derbysoft.next.propertyconnect.channel.task.util.ValidateUtil;
import lombok.RequiredArgsConstructor;
import lombok.experimental.ExtensionMethod;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.util.StringUtils;

import java.util.Optional;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * @Created by <AUTHOR> on 2023/3/6
 */

@EnableConfigurationProperties(HotelSetupConfigurationProperties.class)
@ExtensionMethod({JSONUtil.class, CollectionUtil.class})
@RequiredArgsConstructor

@ScheduleCenterTaskExecutor("PROPERTYCONNECT_CHANNEL.CHANNEL_APT_HOTEL_SETUP_SCHEDULE.SINGLE")
public class DistributorHotelSetupTask implements ChannelServiceTypedTask {
    private final HotelSetupTaskCustomConnectivityOperationActivationService activationService;

    public TaskType taskType() {
        return TaskType.ChannelAptHotelSetup;
    }

    @ChannelServiceTaskPerfLog
    @PerfParameter(name = LogConst.Parameter.PROCESS, value = "'SaveDistributorHotel'")
    @Override
    public void doExecute(ChannelServiceTask task) {
        ValidateUtil.validateNull(task, ChannelServiceTask::getChannel);
        Consumer<HotelSetupTaskCustomConnectivityOperationActivationService> executor = serv -> {
            if (StringUtils.hasText(task.getSupplier()) && StringUtils.hasText(task.getChannelHotel())) {
                var setupProcedure = Optional.ofNullable(task.getProcedure()).map(proc -> proc.stream().map(RemoteChannelService.Operation::valueOf).collect(Collectors.toList())).orElse(null);
                serv.setup(task.getSupplier(), task.getChannel(), task.getChannelHotel(), setupProcedure, task.getIgnorePropertyStatusCheck(), task.getNotifyUrl(), PerfLogHandler.currentHandler());
            } else {
                serv.setup(task.getChannel(), PerfLogHandler.currentHandler());
            }
        };
        executor.accept(activationService);
    }
}
