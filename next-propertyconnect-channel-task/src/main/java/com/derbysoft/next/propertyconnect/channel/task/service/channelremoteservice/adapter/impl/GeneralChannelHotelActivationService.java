package com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.adapter.impl;

import com.alibaba.fastjson2.JSON;
import com.derbysoft.extension.aggregationtool.remoteservice.HttpIOException;
import com.derbysoft.next.commons.core.logsupport.handler.PerfLogHandler;
import com.derbysoft.next.propertyconenct.channel.common.utils.JSONUtil;
import com.derbysoft.next.propertyconenct.channel.common.utils.ObjectUtil;
import com.derbysoft.next.propertyconnect.channel.task.client.connectivityoperationapi.ConnectivityOperationRequestTranslator;
import com.derbysoft.next.propertyconnect.channel.task.client.request.ProfileShellStatusRequest;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.*;
import com.derbysoft.next.propertyconnect.channel.task.service.RemoteService;
import com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.adapter.ChannelHotelActivationCustomizeService;
import com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.adapter.ConnectivityOperationActivationService;
import com.derbysoft.next.propertyconnect.channel.task.service.storageservice.ChannelInfoStorageService;
import com.derbysoft.next.propertyconnect.channel.task.task.arirefresh.ARIRefreshService;
import com.derbysoft.next.propertyconnect.channel.task.util.CloneUtil;
import com.derbysoft.next.propertyconnect.channel.task.util.InventoryItemErrorUtil;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import lombok.experimental.ExtensionMethod;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Optional;
import java.util.function.Consumer;
import java.util.function.Supplier;

/**
 * @Created by <AUTHOR> on 2023/6/13
 */


@Service
@Slf4j
@ExtensionMethod(JSONUtil.class)
@Transactional
@RequiredArgsConstructor
public class GeneralChannelHotelActivationService implements ConnectivityOperationActivationService {
    protected final RemoteService remoteService;
    protected final ARIRefreshService ariRefreshService;
    protected final ChannelInfoStorageService channelInfoStorageService;
    protected final ConnectivityOperationRequestTranslator translator;
    protected final CloneUtil cloneUtil = CloneUtil.INSTANCE;
    private final List<ChannelHotelActivationCustomizeService> activationCustomizers;

    @Override
    public List<Operation> setupProcedure(ChannelHotelDTO dto) {
        var customizer = getCustomizer(dto);
        if (null == customizer) {
            return ConnectivityOperationActivationService.super.setupProcedure(dto);
        }
        var customProcedure = customizer.customizeProcedure();
        if (customProcedure.isEmpty()){
            return ConnectivityOperationActivationService.super.setupProcedure(dto);
        }
        return customProcedure;
    }

    @Override
    public void credentialActivation(ChannelHotelDTO dto) {
        activationCustomizer(dto, customizer -> customizer.customizeCredential(dto));

        var accountSettings = dto.getAccountSettings();
        if (null == accountSettings) {
            return;
        }

        var request = translator.toCredentialRequest(dto);
        var syncStatus = Optional.ofNullable(dto.getHotelInfo()).map(ChannelHotelInfo::getSyncStatus).orElse(SyncStatus.IGNORED);
        if (SyncStatus.DRAFT.equals(syncStatus)){
            request.setPropertyCode(dto.getChannelHotelId());
            callAndRecord(dto.getHotelInfo(), () -> remoteService.saveCredential(dto.getChannelId(), request, getOperationToken(dto)));
        }
        if (jumpStorage(dto)) return;
        channelInfoStorageService.saveIncrementalChannelProduct(dto);
    }

    @Override
    public void propertyActivation(ChannelHotelDTO dto) {
        fillAccountSettingToHotelInfo(dto);
        if (null == dto.getHotelInfo()) {
            return;
        }
        var hotelInfo = dto.getHotelInfo();
        if (SyncStatus.IGNORED.equals(hotelInfo.getSyncStatus())) {
            return;
        }
        hotelInfo.setOperation(Operation.SaveProperty);
        activationCustomizer(dto, customizer -> customizer.customizeProperty(dto));
        var request = translator.toPropertyActivationRequest(dto);
        if (null == hotelInfo.getSyncStatus() || SyncStatus.DRAFT.equals(hotelInfo.getSyncStatus())){
            request.setPropertyCode(dto.getChannelHotelId());
            if (ItemStatus.Actived == hotelInfo.getStatus()) {
                callAndRecord(hotelInfo, () -> remoteService.activateProperty(dto.getChannelId(), request, getOperationToken(dto)));
            } else if (ItemStatus.Deactived == hotelInfo.getStatus()){
                callAndRecord(hotelInfo, () -> remoteService.deactivateProperty(dto.getChannelId(), request, getOperationToken(dto)));
            }
        }

        sendShellStatus(dto);

        if (jumpStorage(dto)) return;
        channelInfoStorageService.saveIncrementalChannelProduct(dto);
    }

    private void sendShellStatus(ChannelHotelDTO dto) {
        var request = ProfileShellStatusRequest.builder()
                .channelHotelId(dto.getChannelHotelId())
                .channelId(dto.getChannelId())
                .shellStatus(SyncStatus.SYNCED.equals(dto.getHotelInfo().getSyncStatus()) ? ProfileShellStatusRequest.ShellStatus.Success : ProfileShellStatusRequest.ShellStatus.Failed)
                .uuid(Optional.ofNullable(PerfLogHandler.currentHandler()).map(PerfLogHandler::getToken).orElse(null))
                .build();

        try {
            remoteService.pushShellStatus(request);
            Optional.ofNullable(PerfLogHandler.currentHandler()).ifPresent(perf -> perf.message("ext_shell_status", ProfileShellStatusRequest.ShellStatus.Success.name()));
        } catch (Exception e) {
            Optional.ofNullable(PerfLogHandler.currentHandler()).ifPresent(perf -> perf.message("ext_shell_status", ProfileShellStatusRequest.ShellStatus.Failed.name()));
            log.error("Send shell status [{}] failed", dto.getChannelHotelId(), e);
        }
    }

    private boolean jumpStorage(ChannelHotelDTO dto) {
        var customizer = getCustomizer(dto);
        return null != customizer && !customizer.saveChannelHotel();
    }

    protected void fillAccountSettingToHotelInfo(ChannelHotelDTO dto) {
        var customizer = getCustomizer(dto);
        if (null != customizer && !customizer.fillCredentialToHotelExtension()) {
            return;
        }

        if (null != dto.getAccountSettings()) {
            var hotelInfo = ObjectUtil.getReference(dto, ChannelHotelDTO::getHotelInfo, ChannelHotelInfo::new);

            if (hotelInfo.getId() == null) {
                hotelInfo.setId(dto.getChannelHotelId());
            }
            if (hotelInfo.getStatus() == null) {
                hotelInfo.setStatus(ItemStatus.Actived);
            }

            ObjectUtil.getReference(hotelInfo, ChannelHotelInfo::getExtensions, () -> new HashMap<String, Object>())
                    .putAll(dto.getAccountSettings());
        }
    }

    @Override
    public void roomTypeActivation(ChannelHotelDTO dto) {
        if (dto.getRoomsInfo() == null) {
            return;
        }
        dto.getRoomsInfo().forEach(roomInfo -> {
            if (SyncStatus.IGNORED.equals(roomInfo.getSyncStatus())) {
                return;
            }
            var cloneDto = cloneUtil.clone(dto);
            roomInfo.setOperation(Operation.SaveRoomTypes);
            cloneDto.setRoomInfo(roomInfo);
            activationCustomizer(dto, customizer -> customizer.customizeRoomType(cloneDto));
            var request = translator.toRoomActivationRequest(cloneDto);
            if (null == roomInfo.getSyncStatus() || SyncStatus.DRAFT.equals(roomInfo.getSyncStatus())){
                request.setPropertyCode(dto.getChannelHotelId());
                if (ItemStatus.Actived == roomInfo.getStatus()) {
                    callAndRecord(roomInfo, () -> remoteService.activateRoomType(dto.getChannelId(), request, getOperationToken(dto)));
                } else if (ItemStatus.Deactived == roomInfo.getStatus()){
                    callAndRecord(roomInfo, () -> remoteService.deactivateRoomType(dto.getChannelId(), request, getOperationToken(dto)));
                }
            }
        });
        if (jumpStorage(dto)) return;
        channelInfoStorageService.saveIncrementalChannelProduct(dto);
    }

    @Override
    public void ratePlanActivation(ChannelHotelDTO dto) {
        if (dto.getRatesInfo() == null) {
            return;
        }
        dto.getRatesInfo().forEach(rateInfo -> {
            if (SyncStatus.IGNORED.equals(rateInfo.getSyncStatus())) {
                return;
            }
            var cloneDto = cloneUtil.clone(dto);
            rateInfo.setOperation(Operation.SaveRatePlans);
            cloneDto.setRateInfo(rateInfo);
            activationCustomizer(dto, customizer -> customizer.customizeRatePlan(cloneDto));
            var request = translator.toRateActivationRequest(cloneDto);
            if (null == rateInfo.getSyncStatus() || SyncStatus.DRAFT.equals(rateInfo.getSyncStatus())){
                request.setPropertyCode(dto.getChannelHotelId());
                if (ItemStatus.Actived == rateInfo.getStatus()) {
                    callAndRecord(rateInfo, () -> remoteService.activateRatePlan(dto.getChannelId(), request, getOperationToken(dto)));
                } else if (ItemStatus.Deactived == rateInfo.getStatus()){
                    callAndRecord(rateInfo, () -> remoteService.deactivateRatePlan(dto.getChannelId(), request, getOperationToken(dto)));
                }
            }
        });
        if (jumpStorage(dto)) return;
        channelInfoStorageService.saveIncrementalChannelProduct(dto);
    }

    @Override
    public void triggerARIRefresh(ChannelHotelDTO dto) {
        ariRefreshService.submitARIRefresh(dto.getSupplierId(), dto.getChannelId(), List.of(dto.getChannelHotelId()), dto.getOperationToken());
    }

    @Override
    public void afterAll(ChannelHotelDTO dto) {
        activationCustomizer(dto, customizer -> customizer.doAfterActivation(dto));
    }

    protected ChannelHotelActivationCustomizeService getCustomizer(ChannelHotelDTO dto) {
        return activationCustomizers.stream()
                .filter(c -> c.channel().equals(dto.getChannelId()))
                .findFirst()
                .orElse(null);
    }

    private void activationCustomizer(ChannelHotelDTO dto, Consumer<ChannelHotelActivationCustomizeService> action){
        Optional.ofNullable(getCustomizer(dto)).ifPresent(action);
    }

    private String getOperationToken(ChannelHotelDTO dto) {
        return Optional.ofNullable(dto)
                .map(ChannelHotelDTO::getOperationToken)
                .orElseGet(() -> Optional.ofNullable(PerfLogHandler.currentHandler())
                        .map(PerfLogHandler::getToken)
                        .orElse("")
                );
    }

    protected  <T> void callAndRecord(InventoryItemStatus item, Supplier<Optional<T>> apiCall){
        try{
            var result = apiCall.get();
            if (result.isEmpty()) {
                throw new HttpIOException(200, new RuntimeException("Response is empty"));
            }

            var res = result.get();

            if (Boolean.TRUE.toString().equals(res.getString("$.responseHeader.Success"))){
                item.setSyncStatus(SyncStatus.SYNCED);
                return;
            }
            throw new HttpIOException(200, JSON.toJSONString(res));
        } catch (HttpIOException e) {
            InventoryItemErrorUtil.partnerResponseError(item, e.getMessage());
            log.error("HttpIOException: [200]", e);
        } catch (FeignException fe) {
            InventoryItemErrorUtil.partnerResponseError(item, fe.contentUTF8());
            log.error("Call Remote Server Error: [{}] {}", fe.status(),fe.contentUTF8());
        } catch (Exception e) {
            InventoryItemErrorUtil.partnerResponseError(item, e.getMessage());
            log.error("Call Remote Server Error: ",e);
        }
    }

}
