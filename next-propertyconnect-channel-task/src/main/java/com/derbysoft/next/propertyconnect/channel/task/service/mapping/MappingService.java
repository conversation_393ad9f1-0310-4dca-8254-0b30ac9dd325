package com.derbysoft.next.propertyconnect.channel.task.service.mapping;

import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelProductsDTO;

/**
 * @Created by <AUTHOR> on 2023/4/19
 */

public interface MappingService {

    String channel();

    ChannelProductsDTO getChannelProductsMapping(String channelId, String channelHotelId, Boolean snapshot);

    ChannelProductsDTO setChannelProductsMapping(String channelId, String channelHotelId, ChannelProductsDTO channelProductsDTO);

    ChannelProductsDTO deleteChannelProductsMapping(String channelId, String channelHotelId, ChannelProductsDTO channelProductsDTO);

}
