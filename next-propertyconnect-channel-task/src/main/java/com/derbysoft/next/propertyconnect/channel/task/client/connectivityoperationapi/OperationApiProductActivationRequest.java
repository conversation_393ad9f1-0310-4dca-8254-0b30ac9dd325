package com.derbysoft.next.propertyconnect.channel.task.client.connectivityoperationapi;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

/**
 * @Created by <AUTHOR> on 2023/11/6
 */

@EqualsAndHashCode(callSuper = true)
@SuperBuilder(toBuilder = true)
@Data
public class OperationApiProductActivationRequest extends OperationApiBasicRequest {
    String roomCode;
    String partnerRoomCode;
    String ratePlanCode;
    String partnerRatePlanCode;
}
