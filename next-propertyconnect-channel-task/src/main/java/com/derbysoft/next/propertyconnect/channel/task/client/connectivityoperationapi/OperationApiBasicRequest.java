package com.derbysoft.next.propertyconnect.channel.task.client.connectivityoperationapi;

import lombok.Data;
import lombok.experimental.SuperBuilder;

import java.util.Map;

@SuperBuilder(toBuilder = true)
@Data
public class OperationApiBasicRequest {
    String propertyOwner;//Required
    String propertyPartner;  //Required
    String propertyCode; //Required
    String partnerPropertyCode; // Optional
    Map<String, Object> extensions;
}
