package com.derbysoft.next.propertyconnect.channel.task.client.aimapping;

import lombok.Data;

import java.util.List;

@Data
public class OpenAIResponse{
	private String id;
	private String object;
	private int created;
	private String model;
	private Usage usage;
	private List<ChoicesItem> choices;

	@Data
	public class ChoicesItem{
		private String finishReason;
		private int index;
		private String text;
		private Object logprobs;
	}

	@Data
	public class Usage{
		private int completionTokens;
		private int promptTokens;
		private int totalTokens;
	}
}
