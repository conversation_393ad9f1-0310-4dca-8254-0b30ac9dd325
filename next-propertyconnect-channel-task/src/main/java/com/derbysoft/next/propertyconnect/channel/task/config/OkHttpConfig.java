package com.derbysoft.next.propertyconnect.channel.task.config;

import com.derbysoft.next.commons.boot.logsupport.inteceptor.OkHttpStreamLogInterceptor;
import com.derbysoft.next.commons.core.http.okhttp3.NextRefererHeaderInterceptor;
import com.derbysoft.next.commons.core.logsupport.StreamLogTemplate;
import okhttp3.OkHttpClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

/**
 * @Created by <AUTHOR> on 2023/5/22
 */

@Configuration
public class OkHttpConfig {

    @Bean
    @LoadBalanced
    public OkHttpClient.Builder okHttpClientBuilder() {
        return new OkHttpClient.Builder()
                .connectTimeout(3, TimeUnit.SECONDS)
                .readTimeout(2, TimeUnit.MINUTES);
    }

    @Bean
    public okhttp3.OkHttpClient okHttpClient(StreamLogTemplate streamLogTemplate, @Value("${spring.application.name}") String appName) {
        return new okhttp3.OkHttpClient.Builder()
                .addNetworkInterceptor(new OkHttpStreamLogInterceptor(streamLogTemplate))
                .addInterceptor(new NextRefererHeaderInterceptor(appName))
                .build();
    }

}
