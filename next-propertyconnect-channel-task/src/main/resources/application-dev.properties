#app.pc.apigateway-url=https://pcapi.derbysoftsec.com/pcapigateway/
#app.pc.apigateway-url=http://localhost:3002/pcapigateway/
app.pc.apigateway-url=https://pcendpoint.derbysoft-test.com/pcapigateway/

app.apilayer.url=http://**************:3030/
#app.apilayer.url=https://uat-api-layer-proxy.derbysoft-test.com/
app.apilayer.token=638eeb1869a757000100000c
#app.synchronizer.url=https://synchronizer2-manager.derbysoftsec.com/
app.synchronizer.url=https://synchronizer2.derbysoft-test.com/
app.s3gate.url=https://support.derbysoftsec.com/s3gate/resource/

BCOM.hotel.limit=1000

app.bookingcom.base-url=http://localhost:3000/
app.agoda.base-url=https://supply.agoda.com/
app.expedia.base-url=https://services.expediapartnercentral.com/
app.aimapping.url=http://***********:8090/
app.hotelbeds.property-to-channel=false
app.hotelbeds.base-url=http://**************:3030/hotelbeds/
app.aimapping.s3-region=us-west-2
app.aimapping.default-threshold=0.8
app.openai.url=https://api.openai.com/v1/completions/
app.ctrip.base-url=https://gateway.fat.ctripqa.com/
app.ctrip.settings.1way-prepay.group-id=1718
app.ctrip.settings.1way-prepay.username=DerbySoftmonomer0.5
app.ctrip.settings.1way-prepay.password=DerbySoftmonomer0.5 123!!
app.ctrip.settings.1way-poa.group-id=1719
app.ctrip.settings.1way-poa.username=DerbySoftmonomer0.5
app.ctrip.settings.1way-poa.password=DerbySoftmonomer0.5 123!!
app.ctrip.settings.2way-prepay.group-id=1690
app.ctrip.settings.2way-prepay.username=DerbySoftmonomer
app.ctrip.settings.2way-prepay.password=DerbySoftmonomer 123!!
app.ctrip.settings.2way-poa.group-id=1691
app.ctrip.settings.2way-poa.username=DerbySoftmonomer
app.ctrip.settings.2way-poa.password=DerbySoftmonomer 123!!
app.ctrip.oversea.send-mail=true
app.ctrip.oversea.mail-address=<EMAIL>,<EMAIL>
app.synxis.base-url=https://propertyconnect-i1.synxis.com/
app.synxis.adapter-base-url=https://synxis.derbysoft-test.com/
app.synxis.callback-url=https://d5dd-116-246-19-150.ngrok-free.app/pcchannel/api/channels/{channelId}/channelhotels/{channelHotelId}/rateplans/callback
app.fliggy.base-url=https://pcendpoint.derbysoft-test.com/pcapigateway/nextsimulator/groovy/
app.airbnb.base-url=https://uat-api-layer-proxy.derbysoft-test.com/
app.test.url=https://www.baidu.com/
app.task.hotel-setup.block-ari-refresh=EXPEDIA,AGODA
app.api-layer.custom.CTRIP.url=https://uat-api-layer-proxy.derbysoft-test.com/

app.log.enable=true
app.log.perf.app-name=pcchannel
app.log.perf.topic=perf_propertyconnect_raw

app.log.stream.app-name=pcchannel
app.log.stream.service-id=pc-channel:cn-northwest-1::::
app.log.stream.topic=streamindex_propertyconnect_raw

app.api-layer.custom.AIRBNB.url=https://uat-api-layer-proxy.derbysoft-test.com/

app.task.hotel-setup.alarm-emails=<EMAIL>

mail.log.kibana-url=https://kibanadev.dbhotelcloud.com/
mail.log.csp-url=https://cs-platform.derbysoft-test.com/

eureka.client.enabled=false

eureka.instance.hostname=1

spring.data.mongodb.auto-index-creation=true

spring.zipkin.base-url=https://dtrace-zipkin.derbysoft-test.com/
spring.zipkin.discovery-client-enabled=false

management.endpoints.web.exposure.include=health,info,refresh,conditions
#app.schedule-center.url=http://schedule-center-distributor.derbysoft-test.com
app.schedule-center.no-task-suspended-seconds=120

spring.cloud.square.okhttp.loadbalancer.enabled=false

server.servlet.context-path=/pcchannel
server.port=8100

logging.level.org.springframework.data.mongodb.core.MongoTemplate=DEBUG

springdoc.api-docs.enabled=true
springdoc.api-docs.path=/docs/api-docs

logger.root.level=DEBUG