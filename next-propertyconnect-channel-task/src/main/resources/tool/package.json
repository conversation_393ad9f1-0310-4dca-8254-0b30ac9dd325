{"name": "node-script", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"run": "node anno_test.js", "test": "node ResDump.js", "runwithbabel": "babel anno_test.js -d dist && node dist/anno_test.js"}, "type": "commonjs", "keywords": [], "author": "", "license": "ISC", "experimentalDecorators": true, "dependencies": {}, "devDependencies": {"@babel/cli": "^7.23.9", "@babel/core": "^7.24.0", "@babel/plugin-proposal-decorators": "^7.24.0", "@babel/preset-env": "^7.24.0", "webpack-cli": "^5.1.4"}}