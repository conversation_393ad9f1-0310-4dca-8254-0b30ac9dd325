我的数据源如下，其中1和3是需要远程获取的你需要使用RemoteService里的profile client来获取，4的数据源涉及到的字段由于无法使用nodejs的数据表，所以使用1中的code原文填写暂时不做映射对应的方法是 getHotelDetails 和 hotelConnections，其中propertyId相当于后端的hotelId，帮我结合DTO中的基本信息和这些补充信息
1. property.property.getProperty({ propertyId })
   最终数据源：远程接口
   请求 URL：${PCAPIGateway}/pcprofile/api/hotels/${propertyId}
   参数：propertyId
   方式：GET
   主要作用：
   提供酒店基础信息（HotelInfo Sheet）
   提供房型、价格计划的原始信息（RoomInfo、RatePlan Sheet 需要用到原始信息做映射）
   映射字段举例（见 getHotelSheetInfo、getRoomTypeSheetInfo、getRatePlanSheetInfo）：
   | Excel字段 | 数据来源字段（getPropertyRes） | Sheet |
   |-------------------|----------------------------------------|------------|
   | HotelCode | hotelId | HotelInfo |
   | HotelName | hotelNameMultiLang['zh-CN'] | HotelInfo |
   | HotelEnName | hotelNameMultiLang['en-US'] | HotelInfo |
   | CityCode | cityCode | HotelInfo |
   | Address | address/addressLine2 | HotelInfo |
   | Telephone | telephone | HotelInfo |
   | RoomType | property.roomTypes[].roomId | RoomInfo |
   | RoomNameZh | property.roomTypes[].roomName | RoomInfo |
   | RoomDescZh | property.roomTypes[].roomDescription | RoomInfo |
   | RatePlanCode | property.ratePlans[].rateId | RatePlan |
   | RatePlanNameZh | property.ratePlans[].rateName | RatePlan |
   | ... | ... | ... |
3. property.channel.generalChannel.getConnectionManagerIdSourceByHotel({ propertyId, channelId })
   最终数据源：远程接口
   请求 URL：${PCAPIGateway}/pcprofile/api/hotelConnections?hotelId=${propertyId}&channelId=${channelId}
   参数：propertyId, channelId
   方式：GET
   主要作用：
   提供渠道连接相关信息（如 groupCode、channelSettings、currency）
   这些信息会用于 HotelInfo、RoomInfo Sheet 的部分字段
   映射字段举例：
   | Excel字段 | 数据来源字段（getConnectionChannelRes） | Sheet |
   |-------------------|----------------------------------------|------------|
   | GroupID | groupCode | HotelInfo/RoomInfo |
   | LocalCurrency | currency | RoomInfo |
   | ... | ... | ... |
4. districtService.getDistrictOfCode({ countryCode, provinceCode, cityCode })
   最终数据源：MongoDB 数据库
   表/集合：District
   查找条件：{ countryCode, provinceCode, cityCode }
   方式：aggregate（含 countries、timezones 的 $lookup）
   主要作用：
   提供地区、城市、国家的名称（中英文）、时区等
   用于 HotelInfo Sheet 的地理相关字段
   映射字段举例：
   | Excel字段 | 数据来源字段（district） | Sheet |
   |-------------------|----------------------------------------|------------|
   | CityName | cityName | HotelInfo |
   | CityNameEn | enCityName | HotelInfo |
   | CountryName | country.countryName | HotelInfo |
   | CountryNameEn | country.enCountryName | HotelInfo |
   | ... | ... | ... |
