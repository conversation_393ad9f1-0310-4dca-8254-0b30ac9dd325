app.pc.apigateway-url=https://pcendpoint-next.qa.derbysoft-test.com/pcapigateway/
app.apilayer.url=http://apilayer-proxy-qa-k8s.stone:8080/
app.apilayer.token=638eeb1869a757000100000c
app.synchronizer.url=https://synchronizer2.qa.derbysoft-test.com/
BCOM.hotel.limit=30000
app.bookingcom.base-url=https://***.derbysoft-test.com/nextsimulator/mock/groovy/bookingcom/
app.agoda.base-url=https://***.derbysoft-test.com/pcapigateway/nextsimulator1/mock/
app.expedia.base-url=https://***.derbysoft-test.com/nextsimulator/mock/groovy/expedia/
app.aimapping.url=http://127.0.0.1:8090/
app.aimapping.default-threshold=0.8
app.openai.url=https://api.openai.com/v1/completions/
app.ctrip.base-url=https://***.derbysoft-test.com/
app.ctrip.settings.1way-prepay.group-id=1718
app.ctrip.settings.1way-prepay.username=DerbySoftmonomer0.5
app.ctrip.settings.1way-prepay.password=DerbySoftmonomer0.5 123!!
app.ctrip.settings.1way-poa.group-id=1719
app.ctrip.settings.1way-poa.username=DerbySoftmonomer0.5
app.ctrip.settings.1way-poa.password=DerbySoftmonomer0.5 123!!
app.ctrip.settings.2way-prepay.group-id=1690
app.ctrip.settings.2way-prepay.username=DerbySoftmonomer
app.ctrip.settings.2way-prepay.password=DerbySoftmonomer 123!!
app.ctrip.settings.2way-poa.group-id=1691
app.ctrip.settings.2way-poa.username=DerbySoftmonomer
app.ctrip.settings.2way-poa.password=DerbySoftmonomer 123!!
app.ctrip.oversea.send-mail=true
app.ctrip.oversea.mail-address=<EMAIL>,<EMAIL>,<EMAIL>
app.synxis.base-url=https://***.derbysoft-test.com/
app.synxis.adapter-base-url=https://***.derbysoft-test.com/
app.synxis.callback-url=https://pcendpoint-next.qa.derbysoft-test.com/pcapigateway/pcchannel/api/channels/{channelId}/channelhotels/{channelHotelId}/rateplans/callback
app.fliggy.base-url=https://***.derbysoft-test.com/pcapigateway/nextsimulator/groovy/
app.hotelbeds.property-to-channel=true
app.hotelbeds.base-url=${app.apilayer.url}hotelbeds/

app.task.hotel-setup.block-ari-refresh=EXPEDIA,AGODA

app.task.hotel-setup.alarm-emails=<EMAIL>,<EMAIL>,<EMAIL>

app.log.enable=true
app.log.perf.app-name=pcchannel
app.log.perf.topic=perf_propertyconnect_raw
app.log.stream.app-name=pcchannel
app.log.stream.topic=streamindex_propertyconnect_raw
app.log.stream.service-id=pc-channel:cn-northwest-1::::
mail.log.kibana-url=https://kibanadev.dbhotelcloud.com/
mail.log.csp-url=https://cs-platform.derbysoft-test.com/

eureka.instance.prefer-ip-address=true
eureka.client.serviceUrl.defaultZone=https://pceureka-next.qa.derbysoft-test.com/pceureka/eureka/
eureka.instance.lease-renewal-interval-in-seconds= 5
eureka.instance.lease-expiration-duration-in-seconds=15
eureka.client.registry-fetch-interval-seconds=5
spring.cloud.loadbalancer.cache.ttl=2s

spring.data.mongodb.auto-index-creation=false

spring.zipkin.base-url=https://dtrace-zipkin.qa.derbysoft-test.com/
spring.zipkin.discovery-client-enabled=false

management.endpoints.web.exposure.include=health,info,refresh
app.schedule-center.url = https://schedule-center.qa.derbysoft-test.com/

spring.cloud.square.okhttp.loadbalancer.enabled=false

server.servlet.context-path=/pcchannel
server.port=8080

springdoc.api-docs.enabled=true
springdoc.api-docs.path=/docs/api-docs
