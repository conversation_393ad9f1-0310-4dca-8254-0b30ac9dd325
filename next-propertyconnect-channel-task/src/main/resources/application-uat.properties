app.pc.apigateway-url=https://pcendpoint.derbysoft-test.com/pcapigateway/
app.apilayer.url=https://uat-api-layer-proxy.derbysoft-test.com/
app.apilayer.token=638eeb1869a757000100000c
app.synchronizer.url=https://synchronizer2.derbysoft-test.com/
app.s3gate.url=https://support.derbysoftsec.com/s3gate/resource/
BCOM.hotel.limit=30000
app.bookingcom.base-url=https://nextsimulator1-rt.derbysoft-test.com/nextsimulator/mock/groovy/bookingcom/
app.agoda.base-url=https://pc-rt.derbysoft-test.com/pcapigateway/nextsimulator1/mock/
app.expedia.base-url=https://nextsimulator1-rt.derbysoft-test.com/nextsimulator/mock/groovy/expedia/
app.aimapping.url=http://ai-mapping4pc-ai-mapping4pc.bps:8090/
app.aimapping.default-threshold=0.8
app.aimapping.s3-region=cn-northwest-1
app.aimapping.s3-bucket-name=derbysoft-next-mapping4pc-feedback-data-test

app.openai.url=https://api.openai.com/v1/completions/
app.ctrip.base-url=https://gateway.fat.ctripqa.com/
app.ctrip.settings.1way-prepay.group-id=1718
app.ctrip.settings.1way-prepay.username=DerbySoftmonomer0.5
app.ctrip.settings.1way-prepay.password=DerbySoftmonomer0.5 123!!
app.ctrip.settings.1way-poa.group-id=1719
app.ctrip.settings.1way-poa.username=DerbySoftmonomer0.5
app.ctrip.settings.1way-poa.password=DerbySoftmonomer0.5 123!!
app.ctrip.settings.2way-prepay.group-id=1690
app.ctrip.settings.2way-prepay.username=DerbySoftmonomer
app.ctrip.settings.2way-prepay.password=DerbySoftmonomer 123!!
app.ctrip.settings.2way-poa.group-id=1691
app.ctrip.settings.2way-poa.username=DerbySoftmonomer
app.ctrip.settings.2way-poa.password=DerbySoftmonomer 123!!
app.ctrip.oversea.send-mail=true
app.ctrip.oversea.mail-address=<EMAIL>,<EMAIL>,<EMAIL>

app.synxis.base-url=https://propertyconnect-i1.synxis.com/
app.synxis.adapter-base-url=https://synxis.derbysoft-test.com/
app.synxis.callback-url=https://pcendpoint.derbysoft-test.com/pcapigateway/pcchannel/api/channels/{channelId}/channelhotels/{channelHotelId}/rateplans/callback
app.hotelbeds.base-url=${app.apilayer.url}hotelbeds/
app.fliggy.base-url=https://pcendpoint.derbysoft-test.com/pcapigateway/nextsimulator/groovy/

app.task.hotel-setup.block-ari-refresh=EXPEDIA,AGODA
app.task.hotel-setup.alarm-emails=<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>

app.log.enable=true
app.log.perf.app-name=pcchannel
app.log.perf.topic=perf_propertyconnect_raw
app.log.stream.app-name=pcchannel
app.log.stream.topic=streamindex_propertyconnect_raw
app.log.stream.service-id=pc-channel:cn-northwest-1::::
mail.log.kibana-url=https://log-opensearch.derbysoft-test.com/
mail.log.csp-url=https://cs-platform.derbysoft-test.com/


eureka.instance.prefer-ip-address=true
eureka.client.serviceUrl.defaultZone=https://pctool-cn-k8s.derbysoft-test.com/pceureka/eureka/

eureka.client.instance-info-replication-interval-seconds=60
eureka.instance.lease-renewal-interval-in-seconds= 5
eureka.instance.lease-expiration-duration-in-seconds=15
eureka.client.registry-fetch-interval-seconds=5
eureka.client.refresh.enable=false
spring.cloud.loadbalancer.cache.ttl=30s

spring.data.mongodb.auto-index-creation=false

spring.zipkin.base-url=https://dtrace-zipkin.derbysoft-test.com/
spring.zipkin.discovery-client-enabled=false

management.endpoints.web.exposure.include=health,info,refresh
app.schedule-center.url = http://schedule-center-distributor.derbysoft-test.com
app.schedule-center.no-task-suspended-seconds=10

spring.cloud.square.okhttp.loadbalancer.enabled=false

server.servlet.context-path=/pcchannel
server.port=8080

springdoc.api-docs.enabled=true
springdoc.api-docs.path=/docs/api-docs

forwarder.enabled=true