spring.profiles.active=@env@
management.endpoint.restart.enabled=true

server.compression.enabled=true
server.compression.mime-types=text/html,text/xml,text/plain,application/json,application/xml,application/javascript
spring.servlet.multipart.enabled=true
spring.jackson.default-property-inclusion=NON_NULL
spring.jackson.serialization.WRITE_EMPTY_JSON_ARRAYS=false

logging.level.com.derbysoft.schedulecenter.task.framework.core.TaskGenerateTimer=OFF
logging.level.com.derbysoft.schedulecenter.task.framework.core.TaskStatusUpdateTimer=OFF

application.name=pcchannel
log.perf.topic=perf_propertyconnect_raw
log.stream.topic=streamindex_propertyconnect_raw
log.stream.rollDurationInMinutes=5
log.stream.rollSizeInBytes=1073741824

feign.httpclient.enabled=false
feign.okhttp.enabled = true
spring.mvc.converters.preferred-json-mapper=jackson
spring.cloud.loadbalancer.retry.enabled=false
spring.cloud.openfeign.okhttp.enabled=true

app.log.enable=true
app.log.app-name=pcchannel
app.log.perf.topic=perf_propertyconnect_raw
app.log.stream.topic=streamindex_propertyconnect_raw

application.enable-web=false
application.enable-perf-tracer=true
application.enable-stream-log=true
application.enable-lifecycle=false
application.enable-oas=false
application.enable-log=true
application.dtrace.enable-dtrace=false

