app.pc.apigateway-url=https://pcapi.derbysoftsec.com/pcapigateway/
app.apilayer.url=https://apilayer-proxy.derbysoftsec.com/
app.apilayer.token=5e201b47f80477731000008e
app.synchronizer.url=https://synchronizer2-manager.derbysoftsec.com/
app.s3gate.url=https://support.derbysoftsec.com/s3gate/resource/
BCOM.hotel.limit=30000
app.task.hotel-setup.block-ari-refresh=EXPEDIA,AGODA
app.bookingcom.base-url=https://supply-xml.booking.com/
app.agoda.base-url=https://supply.agoda.com/
app.expedia.base-url=https://services.expediapartnercentral.com/
app.aimapping.url=http://***********:8090/
app.aimapping.s3-region=ap-southeast-1
app.aimapping.s3-bucket-name=derbysoft-next-mapping4pc-feedback-data
app.aimapping.default-threshold=0.8
app.openai.url=https://api.openai.com/v1/completions/
app.ctrip.base-url=https://receive-vendor-hotel.ctrip.com/
app.ctrip.settings.1way-prepay.group-id=1718
app.ctrip.settings.1way-prepay.username=DerbySoftmonomer0.5
app.ctrip.settings.1way-prepay.password=DerbySoftmonomer0.5 123!!
app.ctrip.settings.1way-poa.group-id=1719
app.ctrip.settings.1way-poa.username=DerbySoftmonomer0.5
app.ctrip.settings.1way-poa.password=DerbySoftmonomer0.5 123!!
app.ctrip.settings.2way-prepay.group-id=1690
app.ctrip.settings.2way-prepay.username=DerbySoftmonomer
app.ctrip.settings.2way-prepay.password=DerbySoftmonomer 123!!
app.ctrip.settings.2way-poa.group-id=1691
app.ctrip.settings.2way-poa.username=DerbySoftmonomer
app.ctrip.settings.2way-poa.password=DerbySoftmonomer 123!!
app.ctrip.oversea.send-mail=true
app.ctrip.oversea.mail-address=<EMAIL>
app.synxis.base-url=https://propertyconnect-p1.synxis.com/
app.synxis.adapter-base-url=https://synxis-distributor-internal.derbysoftsec.com/
app.synxis.callback-url=https://pcapi.derbysoftsec.com/pcapigateway/pcchannel/api/channels/{channelId}/channelhotels/{channelHotelId}/rateplans/callback
app.hotelbeds.base-url=${app.apilayer.url}hotelbeds/
app.fliggy.base-url=https://apilayer-proxy.derbysoftsec.com/


app.log.enable=true
app.log.perf.app-name=pcchannel
app.log.perf.topic=perf_propertyconnect_raw
app.log.stream.app-name=pcchannel
app.log.stream.topic=streamindex_propertyconnect_raw
app.log.stream.service-id=pcchannel:ap-southeast-1::::
mail.log.kibana-url=https://log-opensearch.derbysoftsec.com/
mail.log.csp-url=https://cs-platform.derbysoftsec.com/

eureka.instance.prefer-ip-address=true
eureka.client.serviceUrl.defaultZone=http://**************:8080/pceureka/eureka/,http://*************:8080/pceureka/eureka/
eureka.instance.lease-renewal-interval-in-seconds= 5
eureka.instance.lease-expiration-duration-in-seconds=15
eureka.client.registry-fetch-interval-seconds=5
spring.cloud.loadbalancer.cache.ttl=2s

spring.data.mongodb.auto-index-creation=false

spring.zipkin.base-url=https://dtrace-collector-zipkin.derbysoftsec.com:443/
spring.zipkin.discovery-client-enabled=false

management.endpoints.web.exposure.include=health,info,refresh
app.schedule-center.url=https://sc-tasks-external.derbysoftsec.com

spring.cloud.square.okhttp.loadbalancer.enabled=false

server.servlet.context-path=/pcchannel
server.port=8080

springdoc.api-docs.enabled=false

forwarder.enabled=false