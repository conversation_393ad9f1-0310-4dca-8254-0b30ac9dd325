package com.derbysoft.next.propertyconnect.channel.task.service.impl.ctrip;

import com.derbysoft.next.commons.boot.logsupport.annotation.stream.StreamLog;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * @Created by <AUTHOR> on 2023/4/18
 */

@FeignClient(name = "ctripClient", url = "${app.ctrip.base-url}")
public interface CtripClient {

    @StreamLog(proxy = false, inheritPref = true)
    @PostMapping("/static/v2/json/mappingInfoSearch")
    Object getMappingInfo(
            @RequestHeader("Authorization") String token,
            @RequestHeader("Code") String ctripGroupId,
            @RequestBody String body
    );

    @StreamLog(proxy = false, inheritPref = true)
    @PostMapping("/static/v2/json/mappingInfoSet")
    Object setMappingInfo(
            @RequestHeader("Authorization") String token,
            @RequestHeader("Code") String ctripGroupId,
            @RequestBody CtripMappingInfoSetRequest body
    );
}