package com.derbysoft.next.propertyconnect.channel.task.service.impl.synxis;

import com.alibaba.fastjson2.JSONObject;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @Created by <AUTHOR> on 2023/5/11
 */

@FeignClient(name = "synxisAdapterClient", url = "${app.synxis.adapter-base-url}")
public interface SynxisAdapterClient {

    @PostMapping("/synxis-distributor-profile/api/hotel/query")
    String query(@RequestBody JSONObject request);
}