package com.derbysoft.next.propertyconnect.channel.task.service.impl.ctrip;

import lombok.Data;

import java.util.List;

@Data
public class CtripMappingInfoSetRequest{

	public enum SetType {
		addMapping,
		deleteHotelMapping
	}

	private String languageCode = "zh-CN";
	private SetType setType;
	private String hotelId;
	private String hotelCode;
	private List<SubRoomMappingsItem> subRoomMappings;

	@Data
	public static class SubRoomMappingsItem{
		private String subRoomId;
		private String roomTypeCode;
		private String roomTypeName;
		private String ratePlanCode;
		private String ratePlanName;
	}
}
