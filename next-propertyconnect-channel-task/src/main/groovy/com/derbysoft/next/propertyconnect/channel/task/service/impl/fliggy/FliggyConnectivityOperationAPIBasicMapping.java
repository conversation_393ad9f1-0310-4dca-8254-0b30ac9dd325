package com.derbysoft.next.propertyconnect.channel.task.service.impl.fliggy;

import org.mapstruct.Mapping;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Retention(RetentionPolicy.CLASS)
@Target({ ElementType.METHOD, ElementType.ANNOTATION_TYPE })
@Mapping(source = "propertyOwner", target = "supplierId")
@Mapping(source = "propertyPartner", target = "channelId")
@Mapping(source = "propertyCode", target = "supplierHotelId")
@Mapping(source = "partnerPropertyCode", target = "channelHotelId")
public @interface FliggyConnectivityOperationAPIBasicMapping {
}
