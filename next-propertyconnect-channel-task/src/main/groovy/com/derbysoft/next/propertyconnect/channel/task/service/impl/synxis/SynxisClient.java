package com.derbysoft.next.propertyconnect.channel.task.service.impl.synxis;

import com.derbysoft.next.commons.boot.logsupport.annotation.stream.StreamLog;
import com.derbysoft.next.commons.boot.logsupport.annotation.stream.StreamParameter;
import com.derbysoft.next.commons.core.logsupport.constant.LogConst;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * @Created by <AUTHOR> on 2023/4/28
 */

@FeignClient(name = "synxisClient", url = "${app.synxis.base-url}")
public interface SynxisClient {

    @StreamLog(inheritPref = true, wrapHeaderAsParameter = true)
    @StreamParameter(name = LogConst.Parameter.TRACE_TOKEN, value = "#traceToken")
    @PostMapping(value = "/OTA2010Av2/OTA2010A.svc", consumes = "application/soap+xml; charset=utf-8")
    String syncRatePlans(@RequestBody String ratePlans,
                         @RequestHeader(LogConst.Stream.HEADER_TRACE_TOKEN) String traceToken);
}
