package com.derbysoft.next.propertyconnect.channel.task.service.impl.expedia

import com.alibaba.fastjson2.JSONObject
import com.derbysoft.next.propertyconenct.channel.common.exception.BusinessAuthorizeException
import com.derbysoft.next.propertyconenct.channel.common.exception.BusinessException
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelProductsDTO
import com.derbysoft.next.propertyconnect.channel.task.service.AccountSettingService
import com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.RemoteChannelProductsService
import com.derbysoft.next.propertyconnect.channel.task.service.mapping.ProductRemappingService
import feign.FeignException
import groovyx.gpars.GParsPool
import okhttp3.Credentials
import org.springframework.stereotype.Service

import java.nio.charset.StandardCharsets
import java.util.concurrent.CopyOnWriteArrayList
import java.util.function.Consumer
import java.util.stream.Collectors
/**
 * @Created by jiang<PERSON><PERSON> on 2023/4/10
 */

@Service
class ExpediaProductsServiceImpl implements RemoteChannelProductsService, ProductRemappingService {

    private final AccountSettingService accountSettingService

    private final ExpediaClient expediaClient

    ExpediaProductsServiceImpl(AccountSettingService accountSettingService, ExpediaClient expediaClient) {
        this.accountSettingService = accountSettingService
        this.expediaClient = expediaClient
    }

    @Override
    ChannelProductsDTO getChannelProducts(String channelId, String channelHotelId) {
        return getResponse(channelId, channelHotelId, product -> {
            product['extensions']['roomExtension'] = null
            product['extensions']['rateExtension'] = null
        })
    }

    @Override
    ChannelProductsDTO getChannelProductsWithExtra(String supplierId, String channelId, String channelHotelId) {
        return getResponse(channelId, channelHotelId, product -> {})
    }

    ChannelProductsDTO getResponse(String channelId, String channelHotelId, Consumer<com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelProductsDTO.Product> productModifier) {
        def accountSettings = accountSettingService.getAccountSettings(channelId, channelHotelId)

        if (!accountSettings) {
            return null
        }
        def basicAuth = Credentials.basic(accountSettings?.username, accountSettings?.password)


        def resp, hotelInfo
        try{
            resp = expediaClient.getHotel(basicAuth, channelHotelId)
            hotelInfo = resp?.entity
        } catch (FeignException feignException) {
            hotelInfo = JSONObject.parse(StandardCharsets.UTF_8.decode(feignException.responseBody().orElse(null)).toString())
            def errorMsg = hotelInfo?.errors?.stream()?.map(it -> "[${it?.code}]: ${it?.message}")?.collect(Collectors.joining(", ")) ?: ""
            if (errorMsg.contains("[1000]"))  {
                throw new BusinessAuthorizeException(errorMsg)
            }
            throw new BusinessException(errorMsg)
        }

        def hotelId = hotelInfo?.resourceId as String

        if (!hotelId) {
            return null
        }

        def dto = new ChannelProductsDTO(
                channelHotelId: hotelId,
                hotelName: hotelInfo?.name,
                channelId: this.channel(),
                channelProducts: [] as CopyOnWriteArrayList,
                retrieveDate: RemoteChannelProductsService.currentDate()
        )

        GParsPool.withPool(5, {
            expediaClient.getRoomType(basicAuth, hotelId)?.entity
                    ?.eachParallel { room ->
                        def roomId = room?.resourceId as String
                        expediaClient.getRatePlan(basicAuth, hotelId, roomId)?.entity
                                ?.each { rate ->
                                    rate?.distributionRules?.each { distributionRule ->
                                        def product = new ChannelProductsDTO.Product(
                                                channelRoomId: roomId,
                                                channelRoomName: room?.name?.value,
                                                channelRateId: distributionRule?.expediaId as String,
                                                channelRateName: rate?.name,
                                                status: "Actived",
                                                availStatus: distributionRule?.manageable ? distributionRule.manageable : Boolean.FALSE,
                                                maxOccupancy: room?.maxOccupancy?.total,
                                                bedType: room?.standardBedding?.first()?.option?.first()?.type,
                                                extensions: [
                                                        "resourceId": rate?.resourceId as String,
                                                        "roomExtension": [
                                                                "distributor_hotel_id": hotelId,
                                                                "distributor_room_id": roomId,
                                                                "distributor_room_name": room?.name?.value,
                                                                "distributor_bedtype": room?.standardBedding?.first()?.option?.first()?.type,
                                                                "distributor_occupancy": room?.maxOccupancy?.total as String
                                                        ],
                                                        "rateExtension": [
                                                                "distributor_hotel_id": hotelId,
                                                                "distributor_rateplan_id": distributionRule?.expediaId as String,
                                                                "distributor_rateplan_name": rate?.name,
                                                                "distributor_meal": rate?.valueAddInclusions ? String.join(",", rate?.valueAddInclusions) : "",
                                                                "distributor_paytype": "",
                                                                "distributor_channel_name": channelId,
                                                                "distributor_collect_type": distributionRule?.distributionModel,
                                                                "distributor_extension": [:]
                                                        ]
                                                ]
                                        )
                                        productModifier?.accept(product)
                                        dto.channelProducts << product
                                    }
                                }
                    }
        })

        return dto
    }

    @Override
    String channel() {
        return "EXPEDIA"
    }

    @Override
    void remapping(ChannelProductsDTO channelProducts) {
        channelProducts?.channelProducts?.each { product ->
            product?.channelRateName = "EXPEDIA ${product?.channelRateName}"
        }
    }
}
