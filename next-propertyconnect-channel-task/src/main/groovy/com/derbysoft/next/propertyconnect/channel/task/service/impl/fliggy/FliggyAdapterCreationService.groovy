package com.derbysoft.next.propertyconnect.channel.task.service.impl.fliggy

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson2.JSONObject
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelHotelDTO
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelHotelInfo
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.InventoryItemStatus
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.SyncStatus
import com.derbysoft.next.propertyconnect.channel.task.service.AccountSettingService
import com.derbysoft.next.propertyconnect.channel.task.service.ChannelHotelSetupProcedure
import com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.adapter.ConnectivityOperationCreationService
import com.derbysoft.next.propertyconnect.channel.task.service.storageservice.ChannelHotelStorageService
import com.derbysoft.next.propertyconnect.channel.task.service.storageservice.ChannelInfoStorageService
import com.derbysoft.next.propertyconnect.channel.task.task.arirefresh.ARIRefreshService
import com.derbysoft.next.propertyconnect.channel.task.util.CloneUtil
import com.derbysoft.next.propertyconnect.channel.task.util.FillUtil
import com.derbysoft.next.propertyconnect.channel.task.util.InventoryItemErrorUtil
import com.google.common.collect.Maps
import org.slf4j.Logger
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

import java.util.stream.Collectors
/**
 * @Created by jiangxinchen on 2023/5/29
 */

@Service
class FliggyAdapterCreationService implements ConnectivityOperationCreationService, ChannelHotelSetupProcedure {
    Logger logger = org.slf4j.LoggerFactory.getLogger(FliggyAdapterCreationService.class)
    @Autowired
    FliggyAdapterClient fliggyClient
    @Autowired
    FliggyConnectivityOperationRequestTranslator translator
    @Autowired
    AccountSettingService accountSettingService
    CloneUtil cloneUtil = CloneUtil.INSTANCE
    FillUtil fillUtil = FillUtil.INSTANCE

    @Autowired
    ChannelInfoStorageService channelStorageService

    @Autowired
    ChannelHotelStorageService channelHotelStorageService

    @Autowired
    ARIRefreshService ariRefreshService

    @Override
    String channel() {
        return "FLIGGYHOTEL"
    }

    @Override
    void credentialCreation(ChannelHotelDTO dto) {
        if (dto.getAccountSettings() == null) {
            return
        }
        def localCredential = dto?.getAccountSettings()
        def remoteResponse = null
        try {
            remoteResponse = this.checkAdapterResponse(fliggyClient.credentialQuery(translator.toPropertyBasicRequest(dto), dto.getOperationToken()))
        } catch (Exception ignore) {
            logger.error("Credential query error", ignore)
        }

        def (remoteCredential) = remoteResponse?.credentials ?: []

        if (Maps.difference([
                "appkey"    : localCredential?.appKey ? localCredential?.appKey : localCredential?.appkey,
                "appsecret" : localCredential?.appSecret ? localCredential?.appSecret : localCredential?.appsecret,
                "sessionkey": localCredential?.sessionKey ? localCredential?.sessionKey : localCredential?.sessionkey,
                "vendor"    : localCredential?.vendor,
                "supplierId"  : localCredential?.supplier ? localCredential?.supplier : localCredential?.supplierId
        ], [
                "appkey"    : remoteCredential?.appKey,
                "appsecret" : remoteCredential?.appSecret,
                "sessionkey": remoteCredential?.sessionKey,
                "vendor"    : remoteCredential?.vendor,
                "supplierId"  : remoteCredential?.supplier
        ]).areEqual()) {
            logger.info("Local Credential: {}", JSON.toJSONString(localCredential))
            return
        }
        def creationRequest = JSONObject.from(translator.toPropertyBasicRequest(dto))
        creationRequest.put("credentials", [
                "appKey"    : localCredential?.appKey ? localCredential?.appKey : localCredential?.appkey,
                "appSecret" : localCredential?.appSecret ? localCredential?.appSecret : localCredential?.appsecret,
                "sessionKey": localCredential?.sessionKey ? localCredential?.sessionKey : localCredential?.sessionkey,
                "vendor"    : localCredential?.vendor,
                "supplier"  : localCredential?.supplier ? localCredential?.supplier : localCredential?.supplierId
        ])
        def localHotel = channelHotelStorageService.getChannelHotel(dto.getChannelId(), dto.getChannelHotelId())
        fillUtil.fillClone(dto, localHotel)

        try {
            if (!localHotel.getHotelInfo()) {
                localHotel.setHotelInfo(new ChannelHotelInfo())
            }
            checkAdapterResponse(fliggyClient.credentialCreation(creationRequest, dto.getOperationToken()))
            localHotel.getHotelInfo().setSyncStatus(SyncStatus.SYNCED)
        } catch (Exception e) {
            if (!dto.getHotelInfo()) {
                dto.setHotelInfo(localHotel.getHotelInfo())
            }
            InventoryItemErrorUtil.credentialCreateError(dto.getHotelInfo(), e.getMessage())
            InventoryItemErrorUtil.credentialCreateError(localHotel.getHotelInfo(), e.getMessage())
        }
        channelHotelStorageService.saveIncrementalChannelProduct(localHotel)
    }

    @Override
    ChannelHotelDTO propertyQuery(ChannelHotelDTO dto) {
        try {
            return translator.queryResponseToDto(checkAdapterResponse(fliggyClient.propertyQuery(translator.toPropertyBasicRequest(dto), dto.getOperationToken())))
        } catch (Exception e) {
            dto.getRoomsInfo()?.forEach { room ->
                InventoryItemErrorUtil.queryFromPartnerFailed(room, e.getMessage())
            }

            dto.getRatesInfo()?.forEach { rate ->
                InventoryItemErrorUtil.queryFromPartnerFailed(rate, e.getMessage())
            }
        }
        return null
    }

    @Override
    void propertyCreation(ChannelHotelDTO dto) {
        if (dto.getAccountSettings() != null && dto.getHotelInfo()?.syncStatus?.isFail()) {
            return
        }
        if (dto.getHotelInfo() == null) {
            return
        }
        try {
            checkAdapterResponse(fliggyClient.propertyCreation(translator.toPropertyCreationRequest(dto), dto.getOperationToken()))
            dto.hotelInfo.syncStatus = SyncStatus.SYNCED
        } catch (Exception e) {
            InventoryItemErrorUtil.partnerResponseError(dto.getHotelInfo(), e.getMessage())
            logger.error(e.getMessage(), e)
        }
        channelStorageService.saveIncrementalChannelProduct(dto)
    }

    @Override
    void roomTypeCreation(ChannelHotelDTO dto) {
        if (dto.getRoomsInfo() == null) {
            return
        }
        this.preCheckRooms(dto)
        dto.getRoomsInfo()?.each { room ->
            if (room.syncStatus.isFail()) {
                return
            }
            def cloneDto = cloneUtil.clone(dto)
            cloneDto.setRoomInfo(room)
            cloneDto.setRoomsInfo(null)
            try {
                checkAdapterResponse(fliggyClient.roomCreation(translator.toRoomCreationRequest(cloneDto), dto.getOperationToken()))
                room.syncStatus = SyncStatus.SYNCED

            } catch (Exception e) {
                InventoryItemErrorUtil.partnerResponseError(room, e.getMessage())
                logger.error(e.getMessage(), e)
            }
        }
        channelStorageService.saveIncrementalChannelProduct(dto)
    }

    private boolean checkHotelSyncStatus(ChannelHotelDTO dto, List<? extends InventoryItemStatus> inventoryItemStatuses) {
        def existHotelInfo = channelHotelStorageService.getChannelHotel(dto.getChannelId(), dto.getChannelHotelId())?.getHotelInfo()
        if (existHotelInfo?.syncStatus?.isSuccess()) {
            return true
        }
        inventoryItemStatuses?.each { item -> InventoryItemErrorUtil.hotelSyncFailed(item, existHotelInfo?.getErrorMessage()) }
        channelStorageService.saveIncrementalChannelProduct(dto)
        return false
    }

    @Override
    void ratePlanCreation(ChannelHotelDTO dto) {
        if (dto.getRatesInfo() == null) {
            return
        }
        this.preCheckRates(dto)
        dto.getRatesInfo()?.each { rate ->
            if (rate.syncStatus.isFail()) {
                return
            }
            def cloneDto = cloneUtil.clone(dto)
            cloneDto.setRateInfo(rate)
            cloneDto.setRatesInfo(null)
            try {
                checkAdapterResponse(fliggyClient.rateCreation(translator.toRateCreationRequest(cloneDto), dto.getOperationToken()))
                rate.syncStatus = SyncStatus.SYNCED
            } catch (Exception e) {
                InventoryItemErrorUtil.partnerResponseError(rate, e.getMessage())
                logger.error(e.getMessage(), e)
            }
        }
        channelStorageService.saveIncrementalChannelProduct(dto)
    }

    private <T> T checkAdapterResponse(T response) {
        if ("true" != response?.responseHeader?.Success) {
            throw new IllegalStateException(JSON.toJSONString(response))
        }
        return response
    }

    @Override
    void productCreation(ChannelHotelDTO dto) {
        if (dto.getProductsInfo() == null) {
            return
        }
        if (!this.checkHotelSyncStatus(dto, dto.getProductsInfo())) {
            return
        }
        def localHotel = channelStorageService.getChannelHotel(dto.getChannelId(), dto.getChannelHotelId())
        dto.getProductsInfo()?.each { product ->
            def roomStatus = localHotel?.getRoomsInfo()?.find { it.code == product.channelRoomId }?.syncStatus
            def rateStatus = localHotel?.getRatesInfo()?.find { it.code == product.channelRateId }?.syncStatus

            if (!roomStatus) {
                InventoryItemErrorUtil.roomNotFound(product)
                return
            }
            if (!rateStatus) {
                InventoryItemErrorUtil.rateNotFound(product)
                return
            }
            if (roomStatus.isFail() || rateStatus.isFail()) {
                InventoryItemErrorUtil.associatedItemStatusError(product, roomStatus.isFail() ? "Fail beacuse room sync failed" : "Fail beacuse rate sync failed")
                return
            }

            def cloneDto = cloneUtil.clone(dto)
            cloneDto.setProductInfo(product)
            cloneDto.setProductsInfo(null)
            try {
                checkAdapterResponse(fliggyClient.productCreation(translator.toProductCreationRequest(cloneDto), dto.getOperationToken()))
                product.syncStatus = SyncStatus.SYNCED
            } catch (Exception e) {
                InventoryItemErrorUtil.partnerResponseError(product, e.getMessage())
                logger.error(e.getMessage(), e)
            }
        }
        channelStorageService.saveIncrementalChannelProduct(dto)
    }

    @Override
    void triggerARIRefresh(ChannelHotelDTO dto) {
        ariRefreshService.submitARIRefresh(dto.getSupplierId(), channel(), [dto.getChannelHotelId()], dto.getOperationToken())
    }

    private void preCheckRooms(ChannelHotelDTO dto) {
        if (!this.checkHotelSyncStatus(dto, dto.getRoomsInfo())) {
            return
        }
        var query = this.propertyQuery(dto)
        this.checkDuplicated(dto.getRoomsInfo(), query?.getRoomsInfo())
    }

    private void preCheckRates(ChannelHotelDTO dto) {
        if (!this.checkHotelSyncStatus(dto, dto.getRatesInfo())) {
            return
        }
        var query = this.propertyQuery(dto)
        this.checkDuplicated(dto.getRatesInfo(), query?.getRatesInfo())
    }


    private <T extends InventoryItemStatus> void checkDuplicated(List<T> localList, List<T> remoteList) {
        var localDuplication = localList.stream()
                .filter(item -> item.getSyncStatus().isProcessingStatus())
                .collect(Collectors.groupingBy(InventoryItemStatus::getName, Collectors.mapping(InventoryItemStatus::getCode, Collectors.toList())))
                .entrySet()
                .stream()
                .filter(entry -> entry.getValue().size() > 1)
                .flatMap(entry -> entry.getValue().stream())
                .collect(Collectors.toList())

        localList.stream()
                .filter(item -> localDuplication.contains(item.getCode()))
                .forEach(item -> InventoryItemErrorUtil.requestNameDuplicateItemError(item))

        if (remoteList == null) {
            return
        }
        var localItems = localList.stream()
                .filter(item -> item.getSyncStatus().isProcessingStatus())
                .collect(Collectors.toMap(InventoryItemStatus::getName, InventoryItemStatus::getCode))

        var remoteItem = remoteList.stream()
                .collect(Collectors.groupingBy(InventoryItemStatus::getName, Collectors.mapping(InventoryItemStatus::getCode, Collectors.toList())))
        var remoteDuplication = remoteItem
                .entrySet()
                .stream()
                .filter(entry -> {
                    def localCode = localItems.get(entry.getKey())
                    if (localCode == null) {
                        return false
                    }
                    return entry.getValue().stream().anyMatch(code -> !code.contains(localCode))
                })
                .flatMap(entry -> entry.getValue()
                        .stream()
                        .filter(code -> !code.contains(localItems.get(entry.getKey())))
                        .map(code -> localItems.get(entry.getKey())))
                .collect(Collectors.toList())

        localList.stream()
                .filter(item -> remoteDuplication.contains(item.getCode()))
                .forEach(item -> InventoryItemErrorUtil.partnerDuplicateItemError(item))

    }

}

