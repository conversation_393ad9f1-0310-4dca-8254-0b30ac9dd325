package com.derbysoft.next.propertyconnect.channel.task.service.impl.fliggy;

import com.derbysoft.next.propertyconnect.channel.task.config.EnhancedObjectMapper;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelHotelInfo;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelRateInfo;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelRoomInfo;
import com.derbysoft.next.propertyconnect.channel.task.util.CloneUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * @Created by <AUTHOR> on 2023/6/16
 */

public interface FliggyCustomExtensionFilter extends CloneUtil {
    ObjectMapper OBJECT_MAPPER = new EnhancedObjectMapper();


    default Map<String,Object> doBeforeMapping(ChannelHotelInfo request) {
        var req = clone(request);
        var fixedExtensions = List.of("HotelCode", "name", "domestic", "country", "city", "address", "longitude", "latitude", "tel", "vendor", "supplier", "inventoryLevel");
        req.setExtensions(filterMap(req.getExtensions(), fixedExtensions));
        return this.map(req);
    }

    default Map<String,Object> doBeforeMapping(ChannelRoomInfo request) {
        var req = clone(request);
        var fixedExtensions = List.of("name", "bed_type", "hotelCode", "RoomTypecode");
        req.setExtensions(filterMap(req.getExtensions(), fixedExtensions));
        return this.map(req);
    }

    default Map<String,Object> doBeforeMapping(ChannelRateInfo request) {
        var req = clone(request);
        var mealPlan = Optional.ofNullable(req.getExtensions().get("mealPlan")).map(String::valueOf).orElse("");
        req.getExtensions().put("breakfast_count", generateBreakfastCount(mealPlan));
        var fixedExtensions = List.of("hotelCode", "rateplan_code", "name", "payment_type", "breakfast_count", "min_days", "max_days", "min_adv_hours", "max_adv_hours", "cancel_policy", "guarantee_type", "guarantee_start_time", "member_level", "channel", "vendor", "effective_time", "deadline_time", "rp_type", "is_student", "guarantee_mode", "base_rp_flag", "bottom_price_flag");
        req.setExtensions(filterMap(req.getExtensions(), fixedExtensions));
        return this.map(req);
    }

    static Integer generateBreakfastCount(String mealPlan) {
        if (StringUtils.isBlank(mealPlan)) return 0;
        if (List.of("BB", "HB", "FB", "AL", "BL", "AI").contains(mealPlan)) return -1;
        String mealPlanN = mealPlan.replaceAll("\\D+", "");
        if (mealPlanN.isEmpty()) return 0;
        return Integer.parseInt(mealPlanN);
    }

    static Map<String, Object> filterMap(Map<String, Object> map, List<String> allowedKeys) {
        if (map == null) return new HashMap<>();
        return map.entrySet().stream()
                .filter(entry -> allowedKeys.contains(entry.getKey()))
                .collect(HashMap::new, (m,v) -> m.put(v.getKey(), v.getValue()), HashMap::putAll);
    }

    default Map<String, Object> map(Object value) {
        if (null == value) {
            return new HashMap<>(0);
        }
        var mapType = OBJECT_MAPPER.getTypeFactory().constructMapType(Map.class, String.class, Object.class);
        var result = (Map<String, Object>) OBJECT_MAPPER.convertValue(value, mapType);
        result.remove("status");
        result.remove("settings");
        result.remove("syncStatus");
        result.remove("lastOperationToken");
        if (result.get("i18n") instanceof Map) {
            if (((Map<?, ?>) result.get("i18n")).isEmpty()) {
                result.remove("i18n");
            }
        }
        return result;
    }
}
