package com.derbysoft.next.propertyconnect.channel.task.service.impl.fliggy;

import org.mapstruct.Mapping;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * @Created by <AUTHOR> on 2023/6/2
 */



@Retention(RetentionPolicy.CLASS)
@Target({ ElementType.METHOD, ElementType.ANNOTATION_TYPE })
@Mapping(target = "propertyOwner", source = "supplierId", defaultValue = "PROPERTYCONNECT")
@Mapping(target = "propertyPartner", source = "channelId")
@Mapping(target = "propertyCode", source = "supplierHotelId", defaultExpression = "java(channelHotelDTO.getChannelHotelId())")
@Mapping(target = "partnerPropertyCode", expression = "java(channelHotelDTO.getSupplierId() + \"_\" + channelHotelDTO.getChannelHotelId())")
public @interface FliggyConnectivityOperationAPIBasicMappingReverse {
}
