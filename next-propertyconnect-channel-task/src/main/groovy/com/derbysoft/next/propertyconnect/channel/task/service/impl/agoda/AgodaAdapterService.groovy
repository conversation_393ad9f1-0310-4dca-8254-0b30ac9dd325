package com.derbysoft.next.propertyconnect.channel.task.service.impl.agoda

import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelHotelDTO
import com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.adapter.ChannelHotelActivationCustomizeService
import com.derbysoft.next.propertyconnect.channel.task.util.RemoteServiceUtil
import org.springframework.stereotype.Service

import java.time.ZoneId
import java.time.ZoneOffset
import java.time.ZonedDateTime
/**
 * @Created by jiang<PERSON><PERSON> on 2023/6/13
 */

@Service
class AgodaAdapterService implements ChannelHotelActivationCustomizeService {

    @Override
    String channel() {
        return "AGODA"
    }

    @Override
    void customizeProperty(ChannelHotelDTO channelHotelDTO) {
        def hotelItem = channelHotelDTO.hotelInfo
        def extensions = channelHotelDTO.hotelInfo.extensions
        def channelHotelId = channelHotelDTO.getChannelHotelId()
        def pcHotelId = channelHotelDTO.getSupplierHotelId()

        def channelHotelSettings = RemoteServiceUtil.getInstance().uniqueHotelConnections(channel(), channelHotelId)
                .orElseThrow(() -> new RuntimeException("${channel()}-$channelHotelId connection not found"));

        extensions.put("ariRateType", channelHotelSettings?.rateRule?.channelRateType);
        extensions.put("reservationRateType", "SellPrice".equals(channelHotelSettings?.rateRule?.channelResPriceType) ? "RefSellToAmountAfterTax" : channelHotelSettings?.rateRule?.channelResRateType);
        extensions.put("maxLos", "7");
        extensions.put("noNeedUpc", "UPC".equals(channelHotelSettings?.channelSettings?.paymentType)
                ? Boolean.FALSE.toString() : Boolean.TRUE.toString());
        extensions.put("ignorePreCheck", "false")
        extensions.put("timeZoneID", this.zoneIdToGMTZone(hotelItem?.timezone));
        extensions.put("plusChildToAdultCount", RemoteServiceUtil.getInstance().getHotelDetails(pcHotelId)
                .filter(hotelDetail -> "childAsAdult".equalsIgnoreCase(hotelDetail?.childPolicy))
                .map(hotelDetail -> "Yes")
                .orElse("No"))
        extensions.put("changeAdultCnt", "")
        extensions.put("apiKey", hotelItem?.settings?.apiKey);
    }

    private String zoneIdToGMTZone(String zoneId) {
        ZoneId shanghaiZone = ZoneId.of(zoneId);
        ZonedDateTime dateTime = ZonedDateTime.now(shanghaiZone);
        ZoneOffset offset = shanghaiZone.getRules().getOffset(dateTime.toInstant());

        return "GMT" + offset.getId().replace("Z", "+00:00");
    }

    @Override
    List<com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.RemoteChannelService.Operation> customizeProcedure() {
        return List.of(
                com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.RemoteChannelService.Operation.SaveProperty,
                com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.RemoteChannelService.Operation.TriggerARIRefresh
        )
    }

}
