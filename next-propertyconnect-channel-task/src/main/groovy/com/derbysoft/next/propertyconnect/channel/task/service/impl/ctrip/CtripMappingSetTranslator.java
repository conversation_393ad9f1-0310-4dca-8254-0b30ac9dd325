package com.derbysoft.next.propertyconnect.channel.task.service.impl.ctrip;

import com.derbysoft.next.propertyconenct.channel.common.mapstruct.BaseMapper;
import com.derbysoft.next.propertyconenct.channel.common.mapstruct.DateTimeMapper;
import com.derbysoft.next.propertyconenct.channel.common.utils.GeneratedMapper;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelProductsDTO;
import org.mapstruct.*;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
@AnnotateWith(GeneratedMapper.class)
public interface CtripMappingSetTranslator extends BaseMapper<ChannelProductsDTO, CtripMappingInfoSetRequest>, DateTimeMapper {
    @Mapping(source = "channelHotelId", target = "hotelId")
    @Mapping(source = "hotelId", target = "hotelCode")
    @Mapping(source = "channelProducts", target = "subRoomMappings")
    @Override
    CtripMappingInfoSetRequest map(ChannelProductsDTO channelProductsDTO);


    @Mapping(source = "channelRoomId", target = "subRoomId")
    @Mapping(source = "roomId", target = "roomTypeCode")
    @Mapping(source = "roomName", target = "roomTypeName")
    @Mapping(source = "rateId", target = "ratePlanCode")
    @Mapping(source = "rateName", target = "ratePlanName")
    CtripMappingInfoSetRequest.SubRoomMappingsItem productMapper(ChannelProductsDTO.Product product);

}
