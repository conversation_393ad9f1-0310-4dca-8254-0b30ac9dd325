package com.derbysoft.next.propertyconnect.channel.task.service.impl.synxis

import com.alibaba.fastjson2.JSONObject
import com.derbysoft.next.commons.core.logsupport.handler.PerfLogHandler
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelProductsDTO
import com.derbysoft.next.propertyconnect.channel.task.service.AccountSettingService
import com.derbysoft.next.propertyconnect.channel.task.service.RatePlanService
import com.derbysoft.next.propertyconnect.channel.task.service.RemoteService
import com.derbysoft.next.propertyconnect.channel.task.util.CloneUtil
import com.derbysoft.next.propertyconnect.channel.task.util.ValidateUtil
import groovy.xml.MarkupBuilder
import groovy.xml.XmlSlurper
import groovy.xml.XmlUtil
import groovyx.gpars.GParsPool
import org.apache.commons.lang3.StringUtils
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.stereotype.Service
import org.springframework.web.util.UriComponentsBuilder

import java.util.concurrent.CopyOnWriteArrayList
import java.util.stream.Collectors

/**
 * @Created by jiangxinchen on 2023/4/27
 */

@Service
@EnableConfigurationProperties(SynxisConfigProperties.class)
class SynxisServiceImpl implements RatePlanService {

    private final SynxisClient synxisClient;
    private final SynxisAdapterClient synxisAdapterClient;
    private final AccountSettingService accountSettingService
    private final RemoteService remoteService;
    private final SynxisConfigProperties configProperties;
    private final CloneUtil cloneUtil = CloneUtil.INSTANCE;
    private final Logger log = LoggerFactory.getLogger(SynxisServiceImpl.class);

    SynxisServiceImpl(SynxisClient synxisClient, AccountSettingService accountSettingService, RemoteService remoteService, SynxisConfigProperties configProperties, SynxisAdapterClient synxisAdapterClient) {
        this.synxisClient = synxisClient
        this.accountSettingService = accountSettingService
        this.remoteService = remoteService
        this.configProperties = configProperties
        this.synxisAdapterClient = synxisAdapterClient
    }

    @Override
    String channel() {
        return "SYNXISDISTRIBUTOR"
    }

    @Override
    ChannelProductsDTO syncRatePlans(String channel, String channelHotelId, ChannelProductsDTO channelProductsDTO) {
        ValidateUtil.validateNull(channelProductsDTO, "hotelId")
        channelProductsDTO.channelHotelId = channelHotelId
        channelProductsDTO.channelId = channel
        channelProductsDTO.channelProducts = []

        def adapterResponse = synxisAdapterClient.query(JSONObject.from([
                "supplierCode"     : "PROPERTYCONNECT",
                "supplierHotelCode": channelProductsDTO.hotelId,
        ]))

        if (StringUtils.isBlank(adapterResponse)) {
            throw new IllegalArgumentException("[hotelId:${channelProductsDTO.hotelId}] Hotel in adapter not found! Please setup hotel in adapter first, or check your hotelId. ")
        }

        def responseObject = JSONObject.parseObject(adapterResponse)

        if (channelHotelId && responseObject?.channelHotelCode != channelHotelId) {
            throw new IllegalArgumentException("[channelHotelId:${channelHotelId}] Wrong mapping between hotelId and channelHotelId, In adapter [${channelProductsDTO.hotelId}] mapped to channel hotel [${responseObject?.channelHotelCode}]")
        }
        channelProductsDTO.channelHotelId = responseObject?.channelHotelCode

//        def accountSettings = accountSettingService.getAccountSettings(channel, channelHotelId)

        def channelHotelMapping = remoteService.channelHotelMapping(this.channel(), channelProductsDTO.hotelId)
        if (channelHotelMapping && channelHotelMapping?.isEmpty()) {
            throw new IllegalArgumentException("[hotelId:${channelProductsDTO.hotelId}] ChannelHotelMapping in propertyconnecter not found! Please setup hotel in propertyconnect first, or check your hotelId. ")
        }

        (channelHotelMapping.first()?.productMapping as List)
                .each { product ->
                    channelProductsDTO.channelProducts.add(new ChannelProductsDTO.Product(channelRateId: product?.channelRateId, status: product?.status))
                }

        def requests = buildRequest(channelProductsDTO, responseObject)

        def result = cloneUtil.clone(channelProductsDTO)
        result.channelProducts = new CopyOnWriteArrayList<ChannelProductsDTO.Product>()
        def currentEchoToken = PerfLogHandler.currentHandler().getToken()

        GParsPool.withPool(5, {
            requests.eachParallel {channelRateId, request ->
                try{
                    def response = synxisClient.syncRatePlans(request, currentEchoToken)

                    if (null == response) {
                        throw new RuntimeException("Wrong Synxis response")
                    }
                    def root = new XmlSlurper().parseText(response)
                    def relatesTo = root.'**'.find {
                        it.name() == 'RelatesTo'
                    }.text()

                    if (currentEchoToken != relatesTo) {
                        throw new RuntimeException("Fail to sync rate plans with Synxis")
                    }

                    result.channelProducts << new ChannelProductsDTO.Product(channelRateId: channelRateId, status: "SUCCESS")
                } catch (RuntimeException e) {
                    result.channelProducts << new ChannelProductsDTO.Product(channelRateId: channelRateId, status: "FAIL")
                    log.error(e.getMessage(), e)
                }
            }
        })

        if (requests.size() == result.channelProducts.findAll({ it.status == "FAIL" }).size()) {
            throw new RuntimeException("All rateplan sync failed [${String.join(",", requests.keySet())}]")
        }

        return result
    }

    def convertRatePlanToXML(String username, String password, String echoToken, String callbackUrl, String ratePlan) {
        def xmlRequest = """<?xml version = "1.0" encoding = "UTF-8"?>
            <Envelope xmlns="http://www.w3.org/2003/05/soap-envelope">
              <soap:Header xmlns:soap="http://www.w3.org/2003/05/soap-envelope" xmlns:wsa="http://www.w3.org/2005/08/addressing" xmlns:wsse="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd" xmlns:htng="http://htng.org/PWSWG/2007/02/AsyncHeaders">
                <wsa:MessageID>${echoToken}</wsa:MessageID>
                <htng:CorrelationID>${echoToken}</htng:CorrelationID>
                <wsa:To>https://propertyconnect-i1.synxis.com/OTA2010Av2/OTA2010A.svc</wsa:To>
                <wsa:Action>http://htng.org/PWSWG/2010/12/RatePlan_SubmitRequest</wsa:Action>
                <wsa:ReplyTo>
                  <wsa:Address>${callbackUrl}</wsa:Address>
                </wsa:ReplyTo>
                <htng:ReplyTo>
                  <wsa:Address>${callbackUrl}</wsa:Address>
                </htng:ReplyTo>
                <wsse:Security soap:mustUnderstand="true">
                  <wsse:UsernameToken>
                    <wsse:Username>${username}</wsse:Username>
                    <wsse:Password>${password}</wsse:Password>
                  </wsse:UsernameToken>
                </wsse:Security>
              </soap:Header>
              <Body>
                <OTA_HotelRatePlanNotifRQ xmlns="http://www.opentravel.org/OTA/2003/05" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:schemaLocation="http://www.opentravel.org/OTA/2003/05/OTA_HotelRatePlanNotifRQ.xsd" TimeStamp="2023-04-27T07:53:32" Version="6.000" MessageContentCode="8">   
                  ${ratePlan}
                </OTA_HotelRatePlanNotifRQ>
              </Body>
            </Envelope>"""
        return XmlUtil.serialize(xmlRequest)
    }

    def buildRequest(ChannelProductsDTO channelProductsDTO, def adapterResponse) {
        def callbackUrl = buildCallbackUrl(channelProductsDTO.channelHotelId, channelProductsDTO.hotelId)
        def echoToken = PerfLogHandler.currentHandler().getToken()
//        def rateDetails = remoteService.hotelRatePlans(channelProductsDTO.hotelId)
        return getProductStatus(channelProductsDTO).entrySet().stream().collect(Collectors.toMap({ it.getKey() as String }, { entry ->
            def adapterRateDetails = adapterResponse?.ratePlanMappingList?.find { it.supplierRatePlanCode == entry.getKey() }
            def sw = new StringWriter()
            def mB = new MarkupBuilder(sw)
            mB.RatePlans(HotelCode: channelProductsDTO.channelHotelId){
                mB.RatePlan(
                        RatePlanCode: adapterRateDetails?.channelRatePlanCode
                                ? adapterRateDetails.channelRatePlanCode : entry.getKey(),
                        RatePlanType: adapterRateDetails?.channelRatePlanType
                                ? adapterRateDetails.channelRatePlanType : null,
                        RatePlanNotifType: "Overlay",
                        LastModifierID: "PMS",
                        RatePlanStatusType: entry.getValue(),
//                        CurrencyCode: rateDetails.find { entry.getKey() == it.rateId }?.currency,
                        IsCommissionable: adapterRateDetails?.channelCommissionPercent ? true : false
                ){
                    if (adapterRateDetails?.channelCommissionPercent){
                        "Commission"(Percent: adapterRateDetails.channelCommissionPercent)
                    }
                }
            }
            return convertRatePlanToXML(adapterResponse.channelHotelUserName, adapterResponse.channelHotelPassword, echoToken, callbackUrl, sw.toString())
        }))
    }

    def buildCallbackUrl(String channelHotelId, String hotelId) {
        return UriComponentsBuilder.fromHttpUrl(configProperties.callbackUrl + "?hotelId=${hotelId}")
                .buildAndExpand(channel(), channelHotelId)
                .toUriString()
    }

    def getProductStatus(ChannelProductsDTO dto) {
        def result = [:]
        dto?.channelProducts?.each { product ->
            if (dto.channelProducts.stream()
                    .filter { it?.channelRateId == product?.channelRateId }
                    .anyMatch { "Actived" == it?.status }){
                result.put(product?.channelRateId, "Active")
            }
//            //FIXME: should we use "Deactivated" or "Inactive"?
//            if (dto.channelProducts.stream()
//                    .filter { it?.channelRateId == product?.channelRateId }
//                    .allMatch { "Deactived" == it?.status }){
//                result.put(product?.channelRateId, "Deactivated")
//            }
        }
        return result
    }

}
