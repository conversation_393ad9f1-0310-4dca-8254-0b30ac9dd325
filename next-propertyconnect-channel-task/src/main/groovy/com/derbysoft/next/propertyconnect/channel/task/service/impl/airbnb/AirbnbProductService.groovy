package com.derbysoft.next.propertyconnect.channel.task.service.impl.airbnb


import com.derbysoft.next.propertyconnect.channel.task.client.connectivityoperationapi.ConnectivityOperationRequestTranslator
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelHotelDTO
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelProductsDTO
import com.derbysoft.next.propertyconnect.channel.task.service.RemoteService
import com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.adapter.impl.GeneralConnectivityOperationQueryService
import com.google.common.collect.Lists
import org.springframework.stereotype.Component
/**
 * @Created by jiang<PERSON><PERSON> on 2023/11/6
 */

@Component
class AirbnbProductService extends GeneralConnectivityOperationQueryService{
    AirbnbProductService(ConnectivityOperationRequestTranslator translator, RemoteService remoteService) {
        super(translator, remoteService)
    }

    @Override
    String channel() {
        return "AIRBNB"
    }


    @Override
    ChannelHotelDTO propertyQuery(String supplierId, String channelId, String supplierHotelId, String channelHotelId) {
        def queryResult = super.propertyQuery(supplierId, channelId, supplierHotelId, channelHotelId)

        if (null == queryResult.getRatesInfo() && null == queryResult.getProductsInfo() && null != queryResult.getRoomsInfo()) {
            queryResult.setProductsInfo(Lists.newArrayList())
            queryResult.getRoomsInfo()?.each { roomInfo ->
                queryResult.getProductsInfo().add(new ChannelProductsDTO.Product(
                        channelRoomId: roomInfo.getCode()
                ))
            }
        }

        return queryResult
    }

}
