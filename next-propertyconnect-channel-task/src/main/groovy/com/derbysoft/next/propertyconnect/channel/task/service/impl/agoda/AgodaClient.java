package com.derbysoft.next.propertyconnect.channel.task.service.impl.agoda;

import com.derbysoft.next.commons.boot.logsupport.annotation.stream.StreamLog;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "agodaClient", url = "${app.agoda.base-url}")
public interface AgodaClient {

    @StreamLog(proxy = false, inheritPref = true, wrapHeaderAsParameter = true)
    @PostMapping(value = "api/products/search", consumes = "application/xml")
    String getAgodaProduct(@RequestParam("apiKey") String apiKey,
                           @RequestBody String agodaProductsRequest);
}