package com.derbysoft.next.propertyconnect.channel.task.service.impl.expedia


import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelHotelDTO
import com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.adapter.ChannelHotelActivationCustomizeService
import com.derbysoft.next.propertyconnect.channel.task.util.ValidateUtil
import org.springframework.stereotype.Service
/**
 * @Created by jiang<PERSON><PERSON> on 2023/6/14
 */

@Service
class ExpediaAdapterService implements ChannelHotelActivationCustomizeService {

    @Override
    String channel() {
        return "EXPEDIA"
    }

    @Override
    void customizeProperty(ChannelHotelDTO channelHotelDTO) {
        def extensions = channelHotelDTO.hotelInfo.extensions
        def hotelItem = channelHotelDTO.hotelInfo

        ValidateUtil.validateNull(hotelItem, "rateType","settings.userName", "settings.password")
        def ariRateType = hotelItem?.rateType as String

        if (!["AmountBeforeTax", "AmountAfterTax"].contains(ariRateType)){
            throw new IllegalArgumentException("Invalid rate type: $ariRateType for hotel: ${hotelItem?.pcHotelId}")
        }

        extensions["rateType"] = ariRateType
        var authentication = "${hotelItem?.settings?.userName}:${hotelItem?.settings?.password}" as String
        extensions["resAuthentication"] = authentication
        extensions["ariAuthentication"] = authentication
    }

    @Override
    List<com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.RemoteChannelService.Operation> customizeProcedure() {
        return List.of(
                com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.RemoteChannelService.Operation.SaveProperty,
                com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.RemoteChannelService.Operation.TriggerARIRefresh
        )
    }
}
