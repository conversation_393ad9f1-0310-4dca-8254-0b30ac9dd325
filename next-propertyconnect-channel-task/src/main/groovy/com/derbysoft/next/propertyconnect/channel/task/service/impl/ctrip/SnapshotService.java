package com.derbysoft.next.propertyconnect.channel.task.service.impl.ctrip;

import com.derbysoft.next.propertyconenct.channel.common.mapstruct.BaseMapper;
import com.derbysoft.next.propertyconenct.channel.common.utils.GeneratedMapper;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelProductsDTO;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.SyncStatus;
import com.derbysoft.next.propertyconnect.channel.task.domain.entity.ChannelProductsMappingPO;
import lombok.RequiredArgsConstructor;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Created by <AUTHOR> on 2023/4/23
 */

@Service
@RequiredArgsConstructor
public class SnapshotService {

    private final MongoTemplate mongoTemplate;
    private final MappingDTOTranslator dtoTranslator = MappingDTOTranslator.INSTANCE;

    @Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    @AnnotateWith(GeneratedMapper.class)
    public interface MappingDTOTranslator extends BaseMapper<ChannelProductsDTO, ChannelProductsMappingPO> {
        MappingDTOTranslator INSTANCE = Mappers.getMapper(MappingDTOTranslator.class);

        @Mapping(source = "channelProductsMapping", target = "channelProducts")
        @Override
        ChannelProductsDTO reverseMap(ChannelProductsMappingPO channelProductsMappingPO);

        @Mapping(source = "channelProducts", target = "channelProductsMapping")
        @Override
        ChannelProductsMappingPO map(ChannelProductsDTO channelProductsDTO);

        @Mapping(source = "channelProducts", target = "channelProductsMapping")
        @Override
        void fillIn(ChannelProductsDTO from, @MappingTarget ChannelProductsMappingPO to);

        @Mapping(source = "channelProducts", target = "channelProductsMapping", qualifiedByName = "updateList")
        void addIn(ChannelProductsDTO from, @MappingTarget ChannelProductsMappingPO to);

        ChannelProductsMappingPO.Product mapProduct(ChannelProductsDTO.Product product);

        void fillProduct(ChannelProductsDTO.Product product, @MappingTarget ChannelProductsMappingPO.Product existingProduct);

        @Named("updateList")
        default void updateList(List<ChannelProductsDTO.Product> from, @MappingTarget List<ChannelProductsMappingPO.Product> to) {
            if (to == null) {
                to = new ArrayList<>();
            }

            for (ChannelProductsDTO.Product product : from) {
                boolean found = false;
                for (ChannelProductsMappingPO.Product existingProduct : to) {
                    if (existingProduct.getChannelRoomId().equals(product.getChannelRoomId()) &&
                            existingProduct.getChannelRateId().equals(product.getChannelRateId())) {
                        fillProduct(product, existingProduct);
                        found = true;
                        break;
                    }
                }

                if (!found) {
                    to.add(mapProduct(product));
                }
            }
        }
    }



    public ChannelProductsDTO get(String channel, String channelHotelId) {
        var query = new Query(
                Criteria.where(ChannelProductsMappingPO.Fields.channelId).is(channel)
                        .and(ChannelProductsMappingPO.Fields.channelHotelId).is(channelHotelId)
        );
        ChannelProductsMappingPO po = mongoTemplate.findOne(query, ChannelProductsMappingPO.class);

        return dtoTranslator.reverseMap(po);
    }


    public ChannelProductsDTO put(ChannelProductsDTO dto) {
        var query = new Query(
                Criteria.where(ChannelProductsMappingPO.Fields.channelId).is(dto.getChannelId())
                        .and(ChannelProductsMappingPO.Fields.channelHotelId).is(dto.getChannelHotelId())
        );

        var savingResult = mongoTemplate.save(Optional.ofNullable(mongoTemplate.findOne(query, ChannelProductsMappingPO.class))
                .map(po -> {
                    dtoTranslator.fillIn(dto, po);
                    return po;
                })
                .orElseGet(() -> dtoTranslator.map(dto)));

        return dtoTranslator.reverseMap(savingResult);
    }

    public ChannelProductsDTO incrementalPut(ChannelProductsDTO dto) {
        var query = new Query(
                Criteria.where(ChannelProductsMappingPO.Fields.channelId).is(dto.getChannelId())
                        .and(ChannelProductsMappingPO.Fields.channelHotelId).is(dto.getChannelHotelId())
        );

        var savingResult = mongoTemplate.save(Optional.ofNullable(mongoTemplate.findOne(query, ChannelProductsMappingPO.class))
                .map(po -> {
                    dtoTranslator.addIn(dto, po);
                    return po;
                })
                .orElseGet(() -> dtoTranslator.map(dto)));

        var result = dtoTranslator.reverseMap(savingResult);
        var putProduct = Optional.ofNullable(dto.getChannelProducts()).orElse(new ArrayList<>()).stream().map(ChannelProductsDTO.Product::getCode).collect(Collectors.toList());
        result.getChannelProducts().removeIf(product -> !putProduct.contains(product.getCode()));
        return result;
    }

    public ChannelProductsDTO delete(ChannelProductsDTO dto) {
        var query = new Query(
                Criteria.where(ChannelProductsMappingPO.Fields.channelId).is(dto.getChannelId())
                        .and(ChannelProductsMappingPO.Fields.channelHotelId).is(dto.getChannelHotelId())
        );
        mongoTemplate.remove(query, ChannelProductsMappingPO.class);

        return get(dto.getChannelId(), dto.getChannelHotelId());
    }


    public void updateMappingStatus(String channel, String channelHotelId, SyncStatus mappingStatus) {

    }

}
