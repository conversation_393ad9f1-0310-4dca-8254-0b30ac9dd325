package com.derbysoft.next.propertyconnect.channel.task.service.impl.hoteltonight

import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelHotelDTO
import com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.adapter.ChannelHotelActivationCustomizeService
import org.springframework.stereotype.Service

/**
 * @Created by ji<PERSON><PERSON><PERSON> on 1/15/2025
 */

@Service
class HotelTonightAdapterService implements ChannelHotelActivationCustomizeService {
    @Override
    String channel() {
        return "HOTELTONIGHT"
    }

    @Override
    void customizeProperty(ChannelHotelDTO channelHotelDTO) {
        def hotelInfo = channelHotelDTO.getHotelInfo()

        hotelInfo.setExtensions([
                "mark_up": "0"
        ])
    }

    @Override
    List<com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.RemoteChannelService.Operation> customizeProcedure() {
        return [
                com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.RemoteChannelService.Operation.SaveProperty,
                com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.RemoteChannelService.Operation.TriggerARIRefresh
        ]
    }
}
