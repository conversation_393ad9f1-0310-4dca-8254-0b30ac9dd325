package com.derbysoft.next.propertyconnect.channel.task.service.impl.bookingcom


import com.derbysoft.next.propertyconenct.channel.common.exception.BusinessException
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelProductsDTO
import com.derbysoft.next.propertyconnect.channel.task.service.AccountSettingService
import com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.RemoteChannelProductsService
import feign.FeignException
import groovy.xml.MarkupBuilder
import groovy.xml.XmlSlurper
import okhttp3.Credentials
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import org.springframework.web.context.request.RequestContextHolder
import org.springframework.web.context.request.ServletRequestAttributes

import java.util.function.Consumer

@Service
class BookingcomServiceImpl implements RemoteChannelProductsService {

    @Autowired
    private AccountSettingService accountSettingService

    @Autowired
    private BookingcomClient bookingcomClient;

    @Value('${BCOM.hotel.limit:0}')
    private Integer hotelLimit

    private final Map<Integer, String> mealPlanMap = [
            0 : "None",
            1 : "All inclusive",
            2 : "American",
            3 : "Bed & breakfast",
            4 : "Buffet breakfast",
            5 : "Caribbean breakfast",
            6 : "Continental breakfast",
            7 : "English breakfast",
            8 : "European plan",
            9 : "Family plan",
            10: "Full board",
            11: "Full breakfast",
            12: "Half board/modified American plan",
            14: "Room only",
            15: "Self catering",
            16: "Bermuda",
            17: "Dinner bed and breakfast plan",
            18: "Family American",
            19: "Breakfast",
            20: "Modified",
            21: "Lunch",
            22: "Dinner",
            23: "Breakfast & lunch",
            24: "Lunch & Dinner"
    ]

    @Override
    String channel() {
        return "BOOKINGCOM"
    }

    @Override
    Integer requestSpan() {
        return hotelLimit
    }

    @Override
    ChannelProductsDTO getChannelProducts(String channelId, String channelHotelId) {
        return this.buildChannelProducts(channelId, channelHotelId, product -> product['extensions'] = new HashMap<>())
    }

    @Override
    ChannelProductsDTO getChannelProductsWithExtra(String supplierId, String channelId, String channelHotelId) {
        return this.buildChannelProducts(channelId, channelHotelId, product -> { })
    }

    def buildChannelProducts(def channelId, def channelHotelId, Consumer<com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelProductsDTO.Product> productModifier) {
        def accountSettings = accountSettingService.getAccountSettings(channelId, channelHotelId)
        String response = null
        try{
            response = bookingcomClient.getBookingProducts(
                    this.createBcomReqAuth(accountSettings?.username, accountSettings?.password),
                    this.createRoomRateReq(channelHotelId)
            )
        }catch (FeignException e) {
            if (!getIgnoreError()) {
                handleAuthorizationError(e)
            }
        }

        if (StringUtils.isBlank(response)) {
            throw new IllegalStateException("Wrong BOOKINGCOM response status")
        }

        def dto = new ChannelProductsDTO()
        def channelProducts = []
        def product
        dto.channelId = channelId
        dto.channelHotelId = channelHotelId
        dto.channelProducts = channelProducts

        def roomRates = new XmlSlurper().parseText(response)
        roomRates?.rooms?.room?.each { roomXml ->
            dto.hotelName = channelHotelId
            def room = roomXml.attributes()
            roomXml.rates.rate.each { rateXml ->
                def mealPlan = rateXml?.meal_plan
                def rate = rateXml.attributes()
                product = new ChannelProductsDTO.Product()
                product.channelRoomId = room?.id
                product.channelRoomName = room?.room_name
                product.channelRateId = rate?.id
                product.channelRateName = rate?.rate_name
                product.availStatus = rate?.is_child_rate != "1"
                product.status = 'Actived'
                product.extensions = [
                        "roomExtension": [
                                "distributor_hotel_id" : channelHotelId,
                                "distributor_room_id"  : room?.id,
                                "distributor_room_name": room?.room_name,
                                "distributor_bedtype"  : "",
                                "distributor_occupancy": rate?.max_persons
                        ],
                        "rateExtension": [
                                "distributor_hotel_id"     : channelHotelId,
                                "distributor_rateplan_id"  : rate?.id,
                                "distributor_rateplan_name": rate?.rate_name,
                                "distributor_meal"         : mealPlanMap.getOrDefault(mealPlan.@meal_plan_code?.toInteger(), ""),
                                "distributor_paytype"      : "",
                                "distributor_channel_name" : channelId,
                                "distributor_extension"    : [:]
                        ]
                ]
                productModifier.accept(product)
                channelProducts.add(product)
            }
        }
        dto.retrieveDate = RemoteChannelProductsService.currentDate()
        return dto
    }

    def createBcomReqAuth(def userName, def password) {
        if (userName && password) {
            return Credentials.basic(userName, password)
        }
    }

    def createRoomRateReq(String channelHotelId) {
        def sw = new StringWriter()
        def mB = new MarkupBuilder(sw)
        mB.request {
            hotel_id(channelHotelId)
        }
        return sw.toString()
    }

    private void handleAuthorizationError(FeignException resp) {
        if (resp.status() == 200) {
            return
        }
        def xml = resp.contentUTF8()
        if (401 == resp.status() || 403 == resp.status()) {
            def root = new XmlSlurper().parseText(xml)
            def faultString = root?.fault?.string?.text()
            throw new BusinessException("" + faultString)
        }
        throw new BusinessException("Unexpected error: ${xml}")
    }

    def getIgnoreError() {
        def requestAttributes = RequestContextHolder.getRequestAttributes()

        if (requestAttributes instanceof ServletRequestAttributes) {
            def request = ((ServletRequestAttributes) requestAttributes).getRequest()

            if (null == request.getParameter("ignoreError")) {
                return true
            }
            return request.getParameter("ignoreError")?.toBoolean()
        }

        // 如果没有找到请求上下文，返回默认值 true
        return true
    }
}
