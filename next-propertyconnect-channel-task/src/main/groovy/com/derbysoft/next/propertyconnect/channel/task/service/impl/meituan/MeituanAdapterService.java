package com.derbysoft.next.propertyconnect.channel.task.service.impl.meituan;

import com.derbysoft.next.propertyconenct.channel.common.utils.ObjectUtil;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelHotelDTO;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelHotelInfo;
import com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.adapter.ChannelHotelActivationCustomizeService;
import org.springframework.stereotype.Component;

import java.util.HashMap;

/**
 * @Created by <AUTHOR> on 5/28/2025
 */

@Component
public class MeituanAdapterService implements ChannelHotelActivationCustomizeService {

    @Override
    public String channel() {
        return "MEITUAN";
    }


    @Override
    public void customizeCredential(ChannelHotelDTO channelHotelDTO) {
        var accountSettings = channelHotelDTO.getAccountSettings();
        ObjectUtil.getReference(channelHotelDTO.getHotelInfo(), ChannelHotelInfo::getExtensions, HashMap<String, Object>::new).putAll(accountSettings);
        channelHotelDTO.setAccountSettings(null);
    }


}
