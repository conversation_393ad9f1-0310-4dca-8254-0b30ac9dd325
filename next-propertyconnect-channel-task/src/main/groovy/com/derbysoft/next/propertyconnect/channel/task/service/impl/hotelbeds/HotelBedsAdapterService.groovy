package com.derbysoft.next.propertyconnect.channel.task.service.impl.hotelbeds

import com.derbysoft.next.commons.core.logsupport.handler.PerfLogHandler
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelHotelDTO
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.InventoryItemStatus
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.SyncStatus
import com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.adapter.ChannelHotelActivationCustomizeService
import com.derbysoft.next.propertyconnect.channel.task.service.storageservice.ChannelHotelStorageService
import feign.FeignException
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.stereotype.Service

import java.util.function.Supplier
/**
 * @Created by ji<PERSON><PERSON><PERSON> on 2023/7/27
 */

@EnableConfigurationProperties(HotelBedsConfigProperties.class)
@Service
class HotelBedsAdapterService implements ChannelHotelActivationCustomizeService {

    private final Logger log = LoggerFactory.getLogger(HotelBedsAdapterService.class)

    @Autowired
    HotelBedsConfigProperties hotelBedsConfigProperties;
    @Autowired
    HotelBedsClient hotelBedsClient;
    @Autowired
    ChannelHotelStorageService channelHotelStorageService

    @Override
    String channel() {
        return "HOTELBEDS"
    }

    @Override
    void customizeProperty(ChannelHotelDTO channelHotelDTO) {
        if (Boolean.TRUE == hotelBedsConfigProperties.getPropertyToChannel()) {
            def existHotel = channelHotelStorageService.getChannelHotel(channelHotelDTO.getChannelId(), channelHotelDTO.getChannelHotelId())
            if (!SyncStatus.SYNCED.equals(existHotel?.hotelInfo?.syncStatus)){
                callAndRecord(() -> hotelBedsClient.setupProperty(new HotelBedsPropertySetupRQ(channelHotelDTO.getSupplierId(), channelHotelDTO.getChannelHotelId())), channelHotelDTO.getHotelInfo())
            }else {
                PerfLogHandler.currentHandler().messages("ext_setup_to_channel", "Exist")
                channelHotelDTO.getHotelInfo().setSyncStatus(existHotel?.hotelInfo?.syncStatus)
            }
        }else {
            PerfLogHandler.currentHandler().messages("ext_setup_to_channel", "Ignore")
            channelHotelDTO.hotelInfo.extensions["channelProviderCode"] = "PMSCON"
        }
    }

    private callAndRecord(Supplier<?> req, InventoryItemStatus itemStatus) {
        try {
            def resp = req.get()
            PerfLogHandler.currentHandler().messages("ext_setup_to_channel", "Success")
            itemStatus.setSyncStatus(SyncStatus.SYNCED)
        }catch (FeignException  fe) {
            def errorString = fe.contentUTF8()
            itemStatus.setSyncStatus(SyncStatus.SYNC_FAILED)
            itemStatus.setErrorMessage(errorString)
            PerfLogHandler.currentHandler().messages("ext_setup_to_channel", "Fail")
            PerfLogHandler.currentHandler().messages("ext_setup_to_failed_reason", errorString)
            log.error("Failed to setup_to_channel for channel hotel [{}]: {}",
                    itemStatus.getCode(),
                    errorString
            )
        }
    }

    @Override
    boolean saveChannelHotel() {
        return true
    }
}
