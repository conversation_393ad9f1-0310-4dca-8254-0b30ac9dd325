package com.derbysoft.next.propertyconnect.channel.task.service.impl.airbnb

import com.alibaba.fastjson.JSON
import com.derbysoft.next.commons.core.logsupport.PerfLogTemplate
import com.derbysoft.next.commons.core.logsupport.constant.LogConst
import com.derbysoft.next.commons.core.logsupport.handler.PerfLogHandler
import com.derbysoft.next.propertyconenct.channel.common.utils.Constants
import com.derbysoft.next.propertyconenct.channel.common.utils.ObjectUtil
import com.derbysoft.next.propertyconnect.channel.task.client.WebhookAPI
import com.derbysoft.next.propertyconnect.channel.task.client.connectivityoperationapi.ConnectivityOperationRequestTranslator
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelHotelDTO
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelHotelInfo
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelProductsDTO
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ItemStatus
import com.derbysoft.next.propertyconnect.channel.task.service.RemoteService
import com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.adapter.ChannelHotelActivationCustomizeService
import com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.adapter.impl.GeneralChannelHotelActivationService
import com.derbysoft.next.propertyconnect.channel.task.service.storageservice.ChannelInfoStorageService
import com.derbysoft.next.propertyconnect.channel.task.task.arirefresh.ARIRefreshService
import com.derbysoft.next.propertyconnect.channel.task.util.InventoryItemErrorUtil
import feign.FeignException
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

import java.util.function.Consumer
import java.util.stream.Collectors

/**
 * @Created by jiangxinchen on 2023/11/6
 */

@Component
class AirbnbAdapterActivationService extends GeneralChannelHotelActivationService {

    @Autowired
    private WebhookAPI webhookAPI
    @Autowired
    private AirbnbProductService airbnbProductService

    AirbnbAdapterActivationService(RemoteService remoteService, ARIRefreshService ariRefreshService, ChannelInfoStorageService channelInfoStorageService, ConnectivityOperationRequestTranslator translator, List<ChannelHotelActivationCustomizeService> activationCustomizers) {
        super(remoteService, ariRefreshService, channelInfoStorageService, translator, activationCustomizers)
    }

    @Override
    String channel() {
        return "AIRBNB"
    }

    @Override
    void propertyActivation(ChannelHotelDTO dto) {
        fillAccountSettingToHotelInfo(dto)
        if (null == dto.getHotelInfo()) {
            return
        }

        def extensions = ObjectUtil.getReference(dto.getHotelInfo(), ChannelHotelInfo::getExtensions, () -> new HashMap<String, Object>())
        def ariModel = extensions?.ariModel as String
        if (ariModel?.equalsIgnoreCase('RATEPLAN')) {
            extensions['ariModel'] = 'RATE_PLAN'
        }
        if (ariModel?.equalsIgnoreCase('LOS')) {
            extensions['ariModel'] = 'LOS_RECORD'
        }
        if (StringUtils.isBlank(ariModel)) {
            extensions['ariModel'] = 'RATE_PLAN'
        }

        super.propertyActivation(dto)
    }

    @Override
    void roomTypeActivation(ChannelHotelDTO dto) {
        if (dto?.extensions?.ariModel != "STANDARD") {
            super.roomTypeActivation(dto)
            return
        }

        if (dto.getRoomsInfo() == null) {
            return
        }

        def handler = PerfLogHandler.currentHandler()
        if (null == handler) {
            PerfLogTemplate.getInstance().record("SaveProducts", dto.getOperationToken(), new Consumer<PerfLogHandler>() {
                @Override
                void accept(PerfLogHandler perfLogHandler) {
                    perfLogHandler.message(Constants.Perf.CHANNEL, dto.getChannelId())
                    perfLogHandler.message(Constants.Perf.SUPPLIER, dto.getSupplierId())
                    perfLogHandler.message(Constants.Perf.HOTEL_CHANNEL, dto.getChannelHotelId())
                    perfLogHandler.message(Constants.Perf.PRODUCT_CODE, dto.getRoomsInfo().stream().map({ it.getCode() }).collect(Collectors.joining(",")))
                    doRoomActivation(perfLogHandler, dto)
                }
            })
        } else {
            doRoomActivation(handler, dto)
        }
    }

    void doRoomActivation(PerfLogHandler logHandler, ChannelHotelDTO dto) {
        //TODO: Temporarily resolve mail ignore issue, will be deleted in future versions
        Optional.ofNullable(dto.getExtensions())
                .ifPresent(extensions -> {
                    extensions.put("ignoreSendEmail", false)
                })

        dto.setSupplierHotelId(dto.getChannelHotelId())
        channelInfoStorageService.saveIncrementalChannelProduct(dto)
        var credential = null

        try {
            credential = remoteService.queryCredential(dto.getChannelId(), translator.toPropertyBasicRequest(dto), dto.getOperationToken())
                    .map { resp -> resp?.credentials?.token as String }
                    .orElse(null)
        } catch (FeignException e) {
            logHandler.messages(LogConst.Parameter.ERR_MSG, "Query credential failed: " + e.contentUTF8())
        }

        if (!credential) {
            sendFailureMessage(logHandler, dto)
        }

        def ignoreRooms = new HashSet()
        def adapterProduct = airbnbProductService.getChannelProducts(dto.getSupplierId(), "AIRBNB", dto.getChannelHotelId())
        def adapterListing = adapterProduct?.getChannelProducts()?.collect { it.getChannelRoomId() }
        dto.setSupplierHotelId(dto.getChannelHotelId())
        dto.getRoomsInfo().eachWithIndex { roomInfo, idx ->
            if (StringUtils.isBlank(credential)){
                logHandler?.messages(LogConst.Parameter.ERR_MSG, "No active credential found, ignore")
                InventoryItemErrorUtil.partnerPrecheckResponseError(roomInfo, "No active credential found")
                logHandler?.fail()
                return
            }

            var cloneDto = cloneUtil.clone(dto)
            cloneDto.setRoomInfo(cloneUtil.clone(roomInfo))
            def cloneRoomInfo = cloneDto.getRoomInfo()
            if (cloneRoomInfo.getExtensions() == null) {
                cloneRoomInfo.setExtensions([:])
            }
            var listingId = cloneRoomInfo.getCode()
            if (!adapterListing?.contains(listingId)) {
                InventoryItemErrorUtil.itemNotFound(roomInfo)
                ignoreRooms.add(listingId)
                return
            }

            cloneRoomInfo.getExtensions().put("LISTING_ID", listingId)
            def rateId = cloneRoomInfo.getExtensions()?.get("rateId") as String
            cloneRoomInfo.getExtensions()?.remove("rateId")
            cloneDto.setProductInfo(new ChannelProductsDTO.Product(
                    channelRoomId: listingId,
                    channelRateId: StringUtils.isNotBlank(rateId) ? rateId : listingId,
                    extensions: cloneRoomInfo.getExtensions()
            ))
            var request = translator.toProductActivationRequest(cloneDto);
            if (ItemStatus.Actived == roomInfo.getStatus()) {
                callAndRecord(roomInfo, () -> remoteService.activateProduct(dto.getChannelId(), request, dto.getOperationToken()))
            }
            else {
                callAndRecord(roomInfo, () -> remoteService.deactivateProduct(dto.getChannelId(), request, dto.getOperationToken()))
            }
        }
        logHandler.message("ext_ignore_products", ignoreRooms.join(","))
        channelInfoStorageService.saveIncrementalChannelProduct(dto)
    }

    private void sendFailureMessage(PerfLogHandler logHandler, ChannelHotelDTO dto) {
        if (dto?.extensions?.failCallback) {
            var resp = webhookAPI.webhookDeleteCall(dto?.extensions?.failCallback as String)
            logHandler.messages(LogConst.Parameter.WARN_MSG, "PCContentDeleted:" + JSON.toJSONString(resp))
        }
    }

}
