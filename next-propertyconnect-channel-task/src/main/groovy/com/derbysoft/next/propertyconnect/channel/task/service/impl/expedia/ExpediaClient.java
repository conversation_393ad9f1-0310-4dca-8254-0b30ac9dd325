package com.derbysoft.next.propertyconnect.channel.task.service.impl.expedia;

import com.derbysoft.next.commons.boot.logsupport.annotation.stream.StreamLog;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.Map;

/**
 * @Created by <AUTHOR> on 2023/4/11
 */

@FeignClient(name = "expediaClient", url = "${app.expedia.base-url}")
public interface ExpediaClient {

//    @StreamLog(proxy = false, inheritPref = true)
//    @GetMapping("/products/properties")
//    ResponseEntity<Map<String, Object>> getHotelList(@RequestHeader("Authorization") String basicAuth,
//                                @RequestParam("offset") Integer offset,
//                                @RequestParam("limit") Integer limit);

    @StreamLog(proxy = false, inheritPref = true)
    @GetMapping("/products/properties/{channelHotelId}")
    Map<String, Object> getHotel(@RequestHeader("Authorization") String basicAuth,
                                 @PathVariable("channelHotelId") String channelHotelId);

    @StreamLog(proxy = false, inheritPref = true)
    @GetMapping("/properties/{channelHotelId}/roomTypes")
    Map<String, Object> getRoomType(@RequestHeader("Authorization") String basicAuth,
                                    @PathVariable("channelHotelId") String channelHotelId);

    @StreamLog(proxy = false, inheritPref = true)
    @GetMapping("/properties/{channelHotelId}/roomTypes/{roomTypeId}/ratePlans")
    Map<String, Object> getRatePlan(@RequestHeader("Authorization") String basicAuth,
                                    @PathVariable("channelHotelId") String channelHotelId,
                                    @PathVariable("roomTypeId") String roomTypeId);
}