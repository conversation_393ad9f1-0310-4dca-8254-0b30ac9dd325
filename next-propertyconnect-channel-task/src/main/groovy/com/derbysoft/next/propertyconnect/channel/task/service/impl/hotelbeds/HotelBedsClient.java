package com.derbysoft.next.propertyconnect.channel.task.service.impl.hotelbeds;

import com.derbysoft.next.commons.boot.logsupport.annotation.stream.StreamLog;
import com.derbysoft.next.propertyconnect.channel.task.client.interceptor.apilayer.ApiLayerRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "hotelbedsClient", url = "${app.hotelbeds.base-url}")
public interface HotelBedsClient {

    @StreamLog(proxy = false, inheritPref = true, wrapHeaderAsParameter = true)
    @ApiLayerRequest
    @PostMapping("/config/hotel")
    String setupProperty(@RequestBody HotelBedsPropertySetupRQ agodaProductsRequest);
}