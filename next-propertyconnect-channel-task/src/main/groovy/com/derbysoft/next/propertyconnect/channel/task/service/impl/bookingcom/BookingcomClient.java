package com.derbysoft.next.propertyconnect.channel.task.service.impl.bookingcom;

import com.derbysoft.next.commons.boot.logsupport.annotation.stream.StreamLog;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * @Created by <AUTHOR> on 2023/3/9
 */

@FeignClient(name = "bookingcomClient", url = "${app.bookingcom.base-url}")
public interface BookingcomClient {

    @StreamLog(proxy = false, inheritPref = true, wrapHeaderAsParameter = true)
    @PostMapping("/hotels/xml/roomrates")
    String getBookingProducts(@RequestHeader("Authorization") String basicAuth,
                                @RequestBody String body);
}