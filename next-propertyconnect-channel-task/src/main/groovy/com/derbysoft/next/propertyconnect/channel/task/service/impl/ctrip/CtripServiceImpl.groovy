package com.derbysoft.next.propertyconnect.channel.task.service.impl.ctrip

import com.derbysoft.next.propertyconnect.channel.task.controller.vo.mapping.MappingSnapshot
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelProductsDTO
import com.derbysoft.next.propertyconnect.channel.task.service.RemoteService
import com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.RemoteChannelProductsService
import com.derbysoft.next.propertyconnect.channel.task.service.impl.ctrip.CtripMappingInfoSetRequest.SetType
import com.derbysoft.next.propertyconnect.channel.task.service.mapping.MappingService
import com.derbysoft.next.propertyconnect.channel.task.util.CloneUtil
import org.apache.commons.collections4.CollectionUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.stereotype.Service
import reactor.core.publisher.Mono

import java.security.MessageDigest
import java.time.Duration
import java.time.LocalDateTime
import java.time.temporal.ChronoUnit
import java.util.stream.Collectors

/**
 * @Created by jiang<PERSON><PERSON> on 2023/4/18
 */

@EnableConfigurationProperties(CtripConfigProperties.class)
@Service
class CtripServiceImpl implements MappingService, RemoteChannelProductsService {

    @Autowired
    private RemoteService remoteService

    @Autowired
    private CtripClient ctripClient

    @Autowired
    private CtripConfigProperties ctripConfigProperties

    @Autowired
    private CtripMappingSetTranslator ctripMappingSetTranslator

    @Autowired
    private SnapshotService snapshotService

    private final CloneUtil cloneUtil = CloneUtil.INSTANCE

    @Override
    String channel() {
        return "CTRIP"
    }

    @Override
    ChannelProductsDTO getChannelProductsMapping(String channelId, String channelHotelId, Boolean snapshot) {
        def snapshotResult = snapshotService.get(channelId, channelHotelId)
        def result = getMappingFromCtrip(channelHotelId)

        if (null != snapshotResult && ChannelProductsDTO.MappingStatus.SUBMITTED == snapshotResult.mappingStatus) {
            if (LocalDateTime.now().isAfter(snapshotResult.operateDate.plus(ctripConfigProperties.getMappingTimeout(), ChronoUnit.MILLIS))) {
                snapshotResult.mappingStatus = ChannelProductsDTO.MappingStatus.SYNC_FAILED
            }

            if (compareWithSnapshot(snapshotResult, result)) {
                snapshotResult.mappingStatus = ChannelProductsDTO.MappingStatus.SYNCED
            }

            snapshotService.put(snapshotResult)
        }

        return snapshot ? snapshotResult : result
    }

    @Override
    ChannelProductsDTO setChannelProductsMapping(String channelId, String channelHotelId, ChannelProductsDTO channelProductsDTO) {
        def latestMapping = getMappingFromCtrip(channelHotelId)
        latestMapping?.extensions = channelProductsDTO?.extensions
        latestMapping?.hotelId = latestMapping?.hotelId ? latestMapping?.hotelId : channelProductsDTO?.hotelId

        return Mono.just(latestMapping)
                .doOnError { e -> throw e }
                .map { dto -> deleteChannelProductsMapping(channelId, channelHotelId, dto); dto }
                .delayElement(Duration.ofMillis(ctripConfigProperties.getAddDelay()))
                .map({ currentCtripMapping ->
                    def response = doSetMapping(SetType.addMapping, channelProductsDTO)

                    if (response?.code && "0" != response?.code) {
                        throw new IllegalStateException("Crip throw an error consider check your request: ${response?.code}: ${response?.message}")
                    }

                    def hotelResponseCode = response?.resultList?.hotel?.code
                    def allSuccess = true
                    if (hotelResponseCode && "0" != hotelResponseCode) {
                        allSuccess = false
//                        PerfLogHandler.currentHandler().messages("warn_msg", "HotelFail:" + "${hotelResponseCode}: ${response?.resultList?.hotel?.message}".toString())
                    }


                    def currentRooms = currentCtripMapping.channelProducts.stream().map { it.channelRoomId }.collect(Collectors.toCollection { new TreeSet<String>() })

                    currentCtripMapping.channelProducts = currentRooms.stream().flatMap { channelRoomId ->
                        ctripConfigProperties.settings.keySet().stream()
                                .map { profile ->
                                    def currentProduct = Optional.ofNullable(currentCtripMapping.channelProducts.find({ it.channelRoomId == channelRoomId && it.extensions?.profile == profile }))
                                            .map { cloneUtil.clone(it) }
                                            .orElseGet {
                                                new ChannelProductsDTO.Product(
                                                        channelRoomId: channelRoomId,
                                                        extensions: new HashMap<String, Object>()
                                                )
                                            }

                                    currentProduct.extensions.profile = profile
                                    currentProduct.mapped = false

                                    def mappingProduct = channelProductsDTO.channelProducts.find({ it.channelRoomId == channelRoomId })
                                    def mappingProductWithProfile = channelProductsDTO.channelProducts.find({ it.channelRoomId == channelRoomId && channelProductsDTO.extensions?.profile == profile })
                                    def mappingResult = response?.resultList?.roomLists?.find { it.subRoomId as String == currentProduct?.channelRoomId }

                                    //TIPS: Ctrip will remove mapped products for all groups
                                    if (mappingProduct && !mappingProductWithProfile) {
                                        return null
                                    }
                                    if (mappingResult) {
                                        if ("0" == mappingResult?.code) {
                                            currentProduct.mapped = true
                                            currentProduct.rateId = mappingProductWithProfile.rateId
                                            currentProduct.roomId = mappingProductWithProfile.roomId
                                            currentProduct.rateName = mappingProductWithProfile.rateName
                                            currentProduct.roomName = mappingProductWithProfile.roomName
                                        } else {
                                            allSuccess = false
                                        }
                                    }
                                    return currentProduct
                                }
                                .filter(Objects::nonNull)
                    }.collect(Collectors.toList())

                    currentCtripMapping.mappingStatus = allSuccess ? ChannelProductsDTO.MappingStatus.SUBMITTED : ChannelProductsDTO.MappingStatus.SUBMIT_FAILED
                    currentCtripMapping.operateDate = LocalDateTime.now()
                    snapshotService.put(currentCtripMapping)
                    return currentCtripMapping
                })
                .toFuture()
                .get()
    }

    private def doSetMapping(SetType setType, ChannelProductsDTO channelProductsDTO) {
        def profile = channelProductsDTO?.extensions?.profile
        if (!profile) {
            throw new IllegalArgumentException("Ctrip requires 'profile' in the extensions")
        }
        def setting = ctripConfigProperties.settings[profile]
        if (!setting) {
            throw new IllegalArgumentException("Profile $profile not supported")
        }

        def rateDetails = remoteService.hotelRatePlans(channelProductsDTO.hotelId)
        def roomDetails = remoteService.hotelRoomTypes(channelProductsDTO.hotelId)

        channelProductsDTO.channelProducts.each { product ->
            product.mapped = false
            product.rateName = rateDetails.find({ it.rateId == product.rateId })?.rateName
            product.roomName = roomDetails.find({ it.roomId == product.roomId })?.roomName
        }

        def request = ctripMappingSetTranslator.map(channelProductsDTO)
        request.setType = setType

        def setResult = ctripClient.setMappingInfo(encryptCtripCredentials(setting.username, setting.password), setting.groupId, request)

        if (!setResult) {
            throw new IllegalStateException("Ctrip set mapping failed")
        }
        if (!["0", "SB1012"].contains(setResult?.resultList?.hotel?.code)) {
            throw new IllegalStateException("Crip throw an error when executing [${setType.name()}] operation consider check your request: ${setResult?.resultList?.hotel?.code}: ${setResult?.resultList?.hotel?.message}")
        }

        return setResult
    }

    @Override
    ChannelProductsDTO deleteChannelProductsMapping(String channelId, String channelHotelId, ChannelProductsDTO channelProductsDTO) {
        def deleteDTO = cloneUtil.clone(channelProductsDTO)
        deleteDTO.channelProducts.removeIf { !it.mapped }
        def deleteMappingResult = this.doSetMapping(SetType.deleteHotelMapping, deleteDTO)
        if (!deleteMappingResult) {
            throw new IllegalStateException("Ctrip delete mapping failed")
        }
        def resultCode = deleteMappingResult?.resultList?.hotel?.code

        if (!"0" == resultCode) {
            throw new IllegalStateException("Crip throw an error consider check your request: ${resultCode}: ${deleteMappingResult?.resultList?.hotel?.message}")
        }

//        channelProductsDTO.channelProducts.each { product -> if (deleteDTO.channelProducts.contains(product)) product.mapped = false}
        return deleteDTO
    }

    private ChannelProductsDTO getMappingFromCtrip(channelHotelId) {
        def dtos = [:]
        ctripConfigProperties.settings.each { settingName, setting ->
            ["Mapping", "UnMapping"].each { mappingInfoType ->
                def request = """
                    {
                        "languageCode": "zh-CN",
                        "getMappingInfoType": "$mappingInfoType",
                        "hotelIds": [
                            "$channelHotelId"
                        ]
                    }
                """.trim()

                def result = ctripClient.getMappingInfo(encryptCtripCredentials(setting.username, setting.password), setting.groupId, request)


                result?.datas?.each({ hotel ->
                    dtos.computeIfAbsent(hotel.hotelId, {
                        new ChannelProductsDTO(
                                channelId: this.channel(),
                                hotelId: hotel.hotelCode,
                                channelHotelId: hotel.hotelId,
                                channelProducts: [],
                                retrieveDate: RemoteChannelProductsService.currentDate()
                        )
                    })

                    hotel?.subRoomLists?.each({ room ->
                        dtos[hotel.hotelId]?.channelProducts << new ChannelProductsDTO.Product(
                                roomId: room.roomTypeCode,
                                channelRoomId: room.roomId,
                                channelRoomName: room.roomName,
                                rateId: room.ratePlanCode,
                                channelRateName: room.ratePlanName,
                                mapped: mappingInfoType == "Mapping",
                                extensions: [
                                        "profile": settingName
                                ]
                        )
                    })

                })
            }
        }
        def result = dtos?.values()?.first() as ChannelProductsDTO
        result.channelProducts = result.channelProducts?.sort { it.channelRoomId }
        return result
    }

    private String encryptCtripCredentials(String username, String password) {
        return MessageDigest.getInstance("SHA-256")
                .digest("$username:$password".getBytes("UTF-8"))
                .collect { String.format("%02x", it) }
                .join()
    }

    @Override
    ChannelProductsDTO getChannelProducts(String channelId, String channelHotelId) {
        return this.getMappingFromCtrip(channelHotelId)
    }

    private Boolean compareWithSnapshot(MappingSnapshot source, MappingSnapshot target) {
        var difference = CollectionUtils.subtract(source.getSnapshotSignature { it?.profile }, target.getSnapshotSignature { it?.profile })
        return difference.isEmpty()
    }

}
