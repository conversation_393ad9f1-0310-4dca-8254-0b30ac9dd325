package com.derbysoft.next.propertyconnect.channel.task.service.impl.ctrip.oversea

import com.alibaba.fastjson2.JSON
import com.derbysoft.next.commons.core.logsupport.handler.PerfLogHandler
import com.derbysoft.next.propertyconenct.channel.common.utils.JSONUtil
import com.derbysoft.next.propertyconnect.channel.task.client.MultipartEmailSender
import com.derbysoft.next.propertyconnect.channel.task.client.request.SendRawEmailRequest
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelHotelDTO
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.SyncStatus
import com.derbysoft.next.propertyconnect.channel.task.service.RemoteService
import com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.adapter.ChannelHotelActivationCustomizeService
import com.derbysoft.next.propertyconnect.channel.task.service.impl.ctrip.CtripConfigProperties
import com.derbysoft.next.propertyconnect.channel.task.util.CloneUtil
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.core.io.Resource
import org.springframework.stereotype.Service
import org.springframework.util.StreamUtils
import org.springframework.web.multipart.MultipartFile

import java.nio.charset.StandardCharsets
import java.util.stream.Collectors
import java.util.stream.Stream
/**
 * @Created by jiangxinchen on 1/14/2025
 */

@EnableConfigurationProperties(CtripConfigProperties.class)
@Service
class CtripAdapterService implements ChannelHotelActivationCustomizeService {

    @Autowired
    CtripActivationMailGenerator ctripActivationMailGenerator
    @Autowired
    RemoteService remoteService
    @Autowired
    CtripConfigProperties ctripConfigProperties
    @Value("classpath:ctrip_build_report.html")
    Resource reportMailTemplate
    CloneUtil cloneUtil = CloneUtil.INSTANCE
    @Autowired
    MultipartEmailSender multipartEmailSender
    @Value('${app.pc.apigateway-url}/pcmsc/sendRawEmailWithAttachment')
    String pcmscUrl

    @Override
    String channel() {
        return "CTRIP"
    }

    @Override
    void customizeCredential(ChannelHotelDTO channelHotelDTO) {

//        if (!isCtripOversea(channelHotelDTO)) {
//            return
//        }

//        def hotelInfo = channelHotelDTO.getHotelInfo()
//        if (null == hotelInfo) {
//            return
//        }
//
//        def accountSettings = [
//                "ari_groupCode": JSONUtil.getString(hotelInfo, '$.settings.ari_groupCode'),
//                "ari_password": JSONUtil.getString(hotelInfo, '$.settings.ari_password'),
//                "ari_userName": JSONUtil.getString(hotelInfo, '$.settings.ari_userName'),
//                "password": JSONUtil.getString(hotelInfo, '$.settings.password'),
//                "groupCode": JSONUtil.getString(hotelInfo, '$.settings.groupCode'),
//                "userName": JSONUtil.getString(hotelInfo, '$.settings.userName')
//        ]
//
//        channelHotelDTO.setAccountSettings(accountSettings)

        fillSupplierHotelId(channelHotelDTO)
    }

    @Override
    void customizeProperty(ChannelHotelDTO channelHotelDTO) {
//        if (!isCtripOversea(channelHotelDTO)) {
//            return
//        }
        def hotelInfo = channelHotelDTO.getHotelInfo()
        if (hotelInfo == null) {
            return
        }
        if (hotelInfo.getSyncStatus().isFail()) {
            return
        }
        fillSupplierHotelId(channelHotelDTO)
        hotelInfo.setId(channelHotelDTO.getSupplierHotelId())
        hotelInfo.setSyncStatus(SyncStatus.DRAFT)

        if (!hotelInfo?.extensions?['groupCode']){
            channelHotelDTO.getHotelInfo().setExtensions([
                    "group": JSONUtil.getString(channelHotelDTO, '$.extensions["groupCode"]')
            ])
        }

    }

    @Override
    void customizeRoomType(ChannelHotelDTO channelHotelDTO) {
        fillSupplierHotelId(channelHotelDTO)
        channelHotelDTO.getRoomInfo()?.setSyncStatus(SyncStatus.IGNORED)
    }

    @Override
    void customizeRatePlan(ChannelHotelDTO channelHotelDTO) {
//        if (!isCtripOversea(channelHotelDTO)) {
//            return
//        }
//        def rateInfo = channelHotelDTO.getRateInfo()
//
//
//        def channelHotelMapping = RemoteServiceUtil.getInstance().uniqueChannelHotelMapping(channel(), channelHotelDTO.getChannelHotelId()).orElse(null)
//        def pcRateCode = (JSONUtil.getCollection(channelHotelMapping, '$.productMapping').find {
//            JSONUtil.getString(it, '$.channelRateId') == rateInfo.getCode()
//        })?.rateId
//
//
//        def pcRateInfo = RemoteServiceUtil.getInstance().hotelRatePlan(channelHotelDTO.getSupplierHotelId(), pcRateCode).orElse(null)
//
//        def paymentType = Optional.ofNullable(JSONUtil.getString(rateInfo, '$.extensions.paymentType'))
//                .filter { StringUtils.isNoneBlank(it)}
//                .orElseGet {JSONUtil.getString(pcRateInfo, '$.paymentType')}
//
//        def priceType = Optional.ofNullable(JSONUtil.getString(rateInfo, '$.extensions.priceType'))
//                .filter { StringUtils.isNoneBlank(it)}
//                .orElseGet {JSONUtil.getString(pcRateInfo, '$.priceType')}
//
//        def rateExtension = [
//                "Supplier": channelHotelDTO.getSupplierId(),
//                "RatePlanCode": rateInfo.getCode(),
//                "PaymentType": null == paymentType ? null : "PKG" == paymentType ? "PKG" : "PayNow" == paymentType ? "PREPAY" : "POA",
//                "PriceType": null == paymentType ? null : ["PKG", "NetPrice"].contains(priceType) ? "Cost" : "Sell"
//        ]
//
//        rateInfo.setExtensions(rateExtension)
        fillSupplierHotelId(channelHotelDTO)
        def rateInfo = channelHotelDTO.getRateInfo()
        rateInfo.setExtensions([
                "Supplier"    : channelHotelDTO.getSupplierId(),
                "RatePlanCode": rateInfo.getCode(),
                "PaymentType" : rateInfo.extensions.paymentType,
                "PriceType"   : rateInfo.extensions.priceType
        ])
    }

    @Override
    void doAfterActivation(ChannelHotelDTO channelHotelDTO) {
        if (!ctripConfigProperties?.oversea?.sendMail) {
            return
        }
        if (null == channelHotelDTO.getRoomsInfo() || channelHotelDTO.getRoomsInfo().isEmpty()){
            return
        }

        def cloneDto = cloneUtil.clone(channelHotelDTO)

        byte[] excelBytes = ctripActivationMailGenerator.generateExcel(cloneDto)

        def mailOperationId = Optional.ofNullable(cloneDto.getOperationToken()).orElseGet(() -> UUID.randomUUID().toString())
        def mailRequest = SendRawEmailRequest.builder()
                .messageId(mailOperationId)
                .mailTo(Stream.of(ctripConfigProperties?.oversea?.mailAddress?.split(",")).collect(Collectors.toSet()))
                .subject("Ctrip Overseas Datadump")
                .content(StreamUtils.copyToString(reportMailTemplate.getInputStream(), StandardCharsets.UTF_8)
                        .replace("##Hotel Name##", cloneDto?.hotelInfo?.name)
                        .replace("##Hotel ID##", cloneDto?.channelHotelId)
                        .replace("##Group ID##", cloneDto?.extensions?.groupCode as String)
                )
                .build()

        def mailJson = JSON.toJSONString(mailRequest)

        List<MultipartFile> attachments = new ArrayList<>()
        try {
            def filename = "Static Content Format - ${cloneDto.channelHotelId}.xlsx"
            def multipartFile = MultipartFileUtil.buildCommonsMultipartFile(
                excelBytes, "attachments", filename, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            )
            attachments.add(multipartFile)
        } catch (IOException e) {
            throw new RuntimeException("Attachments generate failed", e)
        }

        try{
            def resp = multipartEmailSender.sendRawEmailWithAttachment(pcmscUrl, mailRequest, attachments, mailOperationId)
            Optional.ofNullable(PerfLogHandler.currentHandler()).ifPresent {it.messages("ext_notify_mail", resp)}
        }catch (Exception ex) {
            Optional.ofNullable(PerfLogHandler.currentHandler()).ifPresent {it.messages("ext_notify_mail", "Failed: " + ex.getMessage())}
        }
    }

    @Override
    List<com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.RemoteChannelService.Operation> customizeProcedure() {
        return [
                com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.RemoteChannelService.Operation.SaveCredential,
                com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.RemoteChannelService.Operation.SaveProperty,
                com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.RemoteChannelService.Operation.SaveRoomTypes,
                com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.RemoteChannelService.Operation.SaveRatePlans,
                com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.RemoteChannelService.Operation.AfterAll,
                com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.RemoteChannelService.Operation.TriggerARIRefresh,
        ];
    }

    private fillSupplierHotelId(ChannelHotelDTO channelHotelDTO) {
        if (null != channelHotelDTO.getSupplierHotelId()){
            return
        }
        channelHotelDTO.setSupplierHotelId(channelHotelDTO.getChannelHotelId())
    }

    @Override
    boolean saveChannelHotel() {
        return true
    }

    @Override
    boolean fillCredentialToHotelExtension() {
        return false
    }

    def isCtripOversea(ChannelHotelDTO channelHotelDTO) {
        return ["Independent", "Group"].contains(JSONUtil.getString(remoteService.uniqueHotelConnections(channelHotelDTO.channelId, channelHotelDTO.channelHotelId).get(), '$.channelSettings.hotelType'))
    }
}
