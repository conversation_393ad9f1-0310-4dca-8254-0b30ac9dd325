package com.derbysoft.next.propertyconnect.channel.task.service.impl.mmt

import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelHotelDTO
import com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.adapter.ChannelHotelActivationCustomizeService
import org.springframework.stereotype.Service

/**
 * @Created by <PERSON><PERSON><PERSON><PERSON> on 1/13/2025
 */

@Service
class MMTAdapterService implements ChannelHotelActivationCustomizeService{
    @Override
    void customizeRoomType(ChannelHotelDTO channelHotelDTO) {
        channelHotelDTO.getRoomInfo()?.setOccupancy(null)
    }

    @Override
    String channel() {
        return "MMT"
    }
}
