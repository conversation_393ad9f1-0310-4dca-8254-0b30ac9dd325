package com.derbysoft.next.propertyconnect.channel.task.service.impl.fliggy;

import com.derbysoft.next.propertyconenct.channel.common.mapstruct.CommMapper;
import com.derbysoft.next.propertyconenct.channel.common.utils.GeneratedMapper;
import com.derbysoft.next.propertyconnect.channel.task.client.connectivityoperationapi.*;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelHotelDTO;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelRateInfo;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelRoomInfo;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ItemStatus;
import org.mapstruct.*;

/**
 * @Created by <AUTHOR> on 2023/6/2
 */


@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
@AnnotateWith(GeneratedMapper.class)
public interface FliggyConnectivityOperationRequestTranslator extends CommMapper, FliggyCustomExtensionFilter {

    @FliggyConnectivityOperationAPIBasicMappingReverse
    @Mapping(target = "extensions", ignore = true)
    OperationApiBasicRequest toPropertyBasicRequest(ChannelHotelDTO channelHotelDTO);

    @FliggyConnectivityOperationAPIBasicMappingReverse
    @Mapping(target = "propertyInfo", source = "hotelInfo")
    OperationApiPropertyCreationRequest toPropertyCreationRequest(ChannelHotelDTO channelHotelDTO);

    @FliggyConnectivityOperationAPIBasicMappingReverse
    OperationApiRoomCreationRequest toRoomCreationRequest(ChannelHotelDTO channelHotelDTO);

    @FliggyConnectivityOperationAPIBasicMappingReverse
    @Mapping(target = "ratePlanInfo", source = "rateInfo")
    OperationApiRateCreationRequest toRateCreationRequest(ChannelHotelDTO channelHotelDTO);

    @FliggyConnectivityOperationAPIBasicMappingReverse
    @Mapping(target = "roomCode", source = "productInfo.channelRoomId")
    @Mapping(target = "ratePlanCode", source = "productInfo.channelRateId")
    OperationApiProductCreationRequest toProductCreationRequest(ChannelHotelDTO channelHotelDTO);

    @FliggyConnectivityOperationAPIBasicMapping
    @Mapping(target = "roomsInfo", source = "rooms")
    @Mapping(target = "ratesInfo", source = "ratePlans")
//    @Mapping(target = "productsInfo", source = "products")
    ChannelHotelDTO queryResponseToDto(OperationApiQueryResponse operationApiQueryResponse);

    @Mapping(target = "code", source = "partnerRoomTypeCode")
    ChannelRoomInfo roomToDto(OperationApiQueryResponse.Room operationApiQueryResponse);

    @Mapping(target = "code", source = "partnerRatePlanCode")
    ChannelRateInfo rateToDto(OperationApiQueryResponse.RatePlan operationApiQueryResponse);


    default ItemStatus map(String status){
        return "Activate".equals(status) ? ItemStatus.Actived : ItemStatus.Deactived;
    }

}
