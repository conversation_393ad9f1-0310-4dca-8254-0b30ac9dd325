package com.derbysoft.next.propertyconnect.channel.task.service.impl.ctrip.oversea;

/**
 * @Created by <AUTHOR> on 1/15/2025
 */

import com.derbysoft.next.propertyconenct.channel.common.utils.JSONUtil;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelHotelDTO;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelHotelInfo;
import com.derbysoft.next.propertyconnect.channel.task.service.RemoteService;
import com.derbysoft.next.propertyconnect.channel.task.service.storageservice.ChannelInfoStorageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.util.*;

@Slf4j
@Service
public class CtripActivationMailGenerator {

    @Autowired
    private RemoteService remoteService;
    @Autowired
    private ChannelInfoStorageService channelInfoStorageService;

    public byte[] generateExcel(ChannelHotelDTO channelHotelDTO) {
        var existChannelHotel = channelInfoStorageService.getChannelHotel(channelHotelDTO.getChannelId(), channelHotelDTO.getChannelHotelId());
        try (XSSFWorkbook workbook = new XSSFWorkbook()) {
            Map<String, Object> connection = getHotelConnection(channelHotelDTO.getChannelId(), channelHotelDTO.getChannelHotelId());
            var hotelMappings = remoteService.uniqueChannelHotelMapping(channelHotelDTO.getChannelId(), channelHotelDTO.getChannelHotelId()).get();
            JSONUtil.set(existChannelHotel, "$.derbyHotelId", Optional.ofNullable(connection.get("hotelId")).map(Object::toString).orElse(null));
            JSONUtil.set(channelHotelDTO, "$.derbyHotelId", Optional.ofNullable(connection.get("hotelId")).map(Object::toString).orElse(null));
            JSONUtil.set(channelHotelDTO, "$.hotelInfo.name", Optional.ofNullable(existChannelHotel.getHotelInfo()).map(ChannelHotelInfo::getName).orElse(null));
            if (null == JSONUtil.getString(channelHotelDTO, "$.extension.groupCode")) {
                JSONUtil.set(channelHotelDTO, "$.extension.groupCode", JSONUtil.getString(existChannelHotel, "$.extension.groupCode"));
            }

            Map<String, Object> hotel = getHotelDetails(channelHotelDTO.getDerbyHotelId());
            // HotelInfo Sheet
            Sheet hotelSheet = workbook.createSheet("HotelInfo");
            List<Map<String, Object>> hotelData = Collections.singletonList(buildHotelInfoRow(hotel, connection, existChannelHotel));
            List<String> hotelColumns = getHotelColumns();
            fillSheetGeneric(hotelSheet, hotelData, hotelColumns, workbook, null, t -> t, t -> false);
            // RoomInfo Sheet
            Sheet roomTypeSheet = workbook.createSheet("RoomInfo");
            List<RoomRowWithStyle> roomTypeData = buildRoomTypeRowsWithStyle(hotel, connection, hotelMappings, existChannelHotel, channelHotelDTO);
            List<String> roomTypeColumns = getRoomTypeColumns();
            fillSheetGeneric(roomTypeSheet, roomTypeData, roomTypeColumns, workbook, IndexedColors.YELLOW, t -> t.row, t -> t.highlight);
            // RatePlan Sheet
            Sheet ratePlanSheet = workbook.createSheet("RatePlan");
            List<RoomRowWithStyle> ratePlanData = buildRatePlanRowsWithStyle(hotel, hotelMappings, existChannelHotel, channelHotelDTO);
            List<String> ratePlanColumns = getRatePlanColumns();
            fillSheetGeneric(ratePlanSheet, ratePlanData, ratePlanColumns, workbook, IndexedColors.YELLOW, t -> t.row, t -> t.highlight);
            try (ByteArrayOutputStream bos = new ByteArrayOutputStream()) {
                workbook.write(bos);
                return bos.toByteArray();
            }
        } catch (Exception e) {
            log.error("生成 Ctrip 激活邮件 Excel 失败", e);
            throw new RuntimeException("生成 Ctrip 激活邮件 Excel 失败", e);
        }
    }

    private Map<String, Object> getHotelDetails(String hotelId) {
        Object result = remoteService.getHotelDetails(hotelId).get();
        if (result instanceof Map) {
            return (Map<String, Object>) result;
        }
        return new HashMap<>();
    }

    private Map<String, Object> getHotelConnection(String channelId, String channelHotelId) {
        var result = remoteService.uniqueHotelConnections(channelId, channelHotelId).get();
        if (result instanceof Map) {
            return (Map<String, Object>) result;
        }
        return new HashMap<>();
    }

    private Map<String, Object> buildHotelInfoRow(Map<String, Object> hotel, Map<String, Object> connection, ChannelHotelDTO dto) {
        Map<String, Object> row = new HashMap<>();
        row.put("GroupID", JSONUtil.getString(dto, "$.hotelInfo.extensions['group']"));
        row.put("HotelCode", dto.getChannelHotelId());
        row.put("HotelName", JSONUtil.getString(hotel, "$.hotelNameMultiLang['zh-CN']"));
        row.put("HotelEnName", JSONUtil.getString(hotel, "$.hotelNameMultiLang['en-US']"));
        row.put("CityCode", JSONUtil.getString(hotel, "$.cityCode"));
        row.put("CityName", JSONUtil.getString(hotel, "$.cityCode"));
        row.put("LAT", JSONUtil.getString(hotel, "$.latitude"));
        row.put("LON", JSONUtil.getString(hotel, "$.longitude"));
        row.put("OpenYear", JSONUtil.getString(hotel, "$.extension.openDate"));
        row.put("FitmentYear", JSONUtil.getString(hotel, "$.extension.fitmentDate"));
        String hotelAddr = !isCNHotel(hotel)
                ? JSONUtil.getString(hotel, "$.address") + ((StringUtils.isNoneBlank(JSONUtil.getString(hotel, "$.addressLine2")) ? JSONUtil.getString(hotel, "$.addressLine2") : ""))
                : JSONUtil.getString(hotel, "$.address");
        row.put("Address_info", hotelAddr);
        row.put("Address_intl", hotelAddr);
        row.put("Telephone", JSONUtil.getString(hotel, "$.telephone"));
        row.put("RoomQuantity", JSONUtil.getString(hotel, "$.roomCount"));
        row.put("Brief", isCNHotel(hotel) ? JSONUtil.getString(hotel, "$.longDescription"): "");
        row.put("BriefEn", !isCNHotel(hotel) ? JSONUtil.getString(hotel, "$.longDescription"): "");
        row.put("HotelDesc", "");
        row.put("HotelDescEn", "");
        return row;
    }

    private boolean isCNHotel(Map<String, Object> hotel) {
        return "CN".equals(JSONUtil.getString(hotel, "$.property.countryCode"));
    }

    private Map<String, Object> buildRoomRow(Map<String, Object> hotel, Map<String, Object> connection, ChannelHotelDTO dto, com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelRoomInfo room, Object roomDetail) {
        Map<String, Object> row = new HashMap<>();
        row.put("GroupID", JSONUtil.getString(dto, "$.hotelInfo.extensions['group']"));
        row.put("HotelCode", dto.getChannelHotelId());
        row.put("RoomType", room.getCode());
        row.put("RoomNameZh", isCNHotel(hotel) ? JSONUtil.getString(roomDetail, "$.roomName"): "");
        row.put("RoomNameEn", !isCNHotel(hotel) ? JSONUtil.getString(roomDetail, "$.roomName"): "");
        row.put("RoomDescZh", isCNHotel(hotel) ? JSONUtil.getString(roomDetail, "$.roomDescription"): "");
        row.put("RoomDescEn", !isCNHotel(hotel) ? JSONUtil.getString(roomDetail, "$.roomDescription"): "");
        row.put("Currency", JSONUtil.getString(connection, "$.currency"));
        row.put("MaxOccupancy", JSONUtil.getString(roomDetail, "$.occupancy.maxOccupancy"));
        row.put("Person", JSONUtil.getString(roomDetail, "$.occupancy.maxAdult"));
        row.put("Children", JSONUtil.getString(roomDetail, "$.occupancy.maxChild"));
        row.put("RoomArea", "oversea hotel");
        row.put("Smoke", "oversea hotel");
        row.put("Window", "oversea hotel");
        return row;
    }

    private Map<String, Object> buildRateRow(Map<String, Object> hotel, ChannelHotelDTO dto, com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelRateInfo ratePlan, Object rateDetail) {
        Map<String, Object> row = new HashMap<>();
        row.put("Hotel Code", dto.getChannelHotelId());
        row.put("Hotel Name", isCNHotel(hotel) ? JSONUtil.getString(hotel, "$.hotelNameMultiLang['zh-CN']") : JSONUtil.getString(hotel, "$.hotelNameMultiLang['en-US']"));
        row.put("Rate Plan Code", ratePlan.getCode());
        row.put("Rate Plan Name-in Chinese", isCNHotel(hotel) ? JSONUtil.getString(rateDetail, "$.rateName") : "");
        row.put("Rate Plan Name-in English", !isCNHotel(hotel) ? JSONUtil.getString(rateDetail, "$.rateName") : "");
        row.put("Breakfast included or not?", Set.of(
                "BB", "HB", "FB", "AL", "BL", "AI",
                "BB1", "BB2", "BB3", "BB4", "BB5",
                "BB6", "BB7", "BB8", "BB9"
        ).contains(Optional.ofNullable(JSONUtil.getString(rateDetail, "$.defaultMealPlan")).orElse("")) ? "Y" : "N");
        row.put("number of Breakfast= max adults?", "N");
        row.put("Begin Sell Date", "2021-1-1");
        row.put("End Sell Date", "2099-1-1");
        row.put("Begin Stay Date", "2021-1-1");
        row.put("End Stay Date", "2099-1-1");
        String priceType = JSONUtil.getString(rateDetail, "$.priceType");
        row.put("PriceType", ("NetPrice".equals(priceType) || "Cost".equals(priceType)) ? "Cost" : "Sell");
        String paymentType = JSONUtil.getString(rateDetail, "$.paymentType");
        if ("PayLater".equals(paymentType) || "POA".equals(paymentType)) {
            row.put("Payment Method", "Pay at Hotel");
            row.put("RatePlanCategory", 16);
        } else if ("PKG".equals(paymentType) || "Package".equals(paymentType)) {
            row.put("Payment Method", "Package");
            row.put("RatePlanCategory", 502);
        } else {
            row.put("Payment Method", "Prepay Online");
            row.put("RatePlanCategory", 501);
        }
        row.put("Package or Not", Set.of("PKG", "Package").contains(Optional.ofNullable(paymentType).orElse("")) ? "Y" : "N");
        return row;
    }

    private List<RoomRowWithStyle> buildRoomTypeRowsWithStyle(Map<String, Object> hotel, Map<String, Object> connection, Object hotelMapping, ChannelHotelDTO exist, ChannelHotelDTO diff) {
        var roomTypes = remoteService.hotelRoomTypes(exist.getDerbyHotelId());
        List<RoomRowWithStyle> rows = new ArrayList<>();
        
        // 收集diff（请求）中的所有有效roomCode
        Set<String> diffRoomCodes = new HashSet<>();
        if (diff.getRoomsInfo() != null) {
            for (var room : diff.getRoomsInfo()) {
                if (room != null && StringUtils.isNotBlank(room.getCode())) {
                    diffRoomCodes.add(room.getCode());
                }
            }
        }
        
        // 收集exist中的所有有效roomCode（用于判断是否为新增）
        Set<String> existRoomCodes = new HashSet<>();
        if (exist.getRoomsInfo() != null) {
            for (var room : exist.getRoomsInfo()) {
                if (room != null && StringUtils.isNotBlank(room.getCode())) {
                    existRoomCodes.add(room.getCode());
                }
            }
        }
        
        // 添加exist中的所有room，如果在diff中就标黄，否则不标黄
        if (exist.getRoomsInfo() != null) {
            for (var room : exist.getRoomsInfo()) {
                if (room != null && StringUtils.isNotBlank(room.getCode())) {
                    var roomDetail = JSONUtil.getObject(roomTypes, "$[?(@.roomId=='" + getRoomMappingId(hotelMapping, room.getCode()) + "')][0]");
                    if (roomDetail != null) {
                        boolean shouldHighlight = diffRoomCodes.contains(room.getCode());
                        rows.add(new RoomRowWithStyle(buildRoomRow(hotel, connection, exist, room, roomDetail), shouldHighlight));
                    }
                }
            }
        }
        
        // 添加diff中的新增room（在exist中不存在的），标记为高亮
        if (diff.getRoomsInfo() != null) {
            for (var room : diff.getRoomsInfo()) {
                if (room != null && StringUtils.isNotBlank(room.getCode()) && !existRoomCodes.contains(room.getCode())) {
                    var roomDetail = JSONUtil.getObject(roomTypes, "$[?(@.roomId=='" + getRoomMappingId(hotelMapping, room.getCode()) + "')][0]");
                    if (roomDetail != null) {
                        rows.add(new RoomRowWithStyle(buildRoomRow(hotel, connection, diff, room, roomDetail), true));
                    }
                }
            }
        }
        
        return rows;
    }

    private String getRoomMappingId(Object hotelMapping, String channelRoomId) {
        return JSONUtil.getString(hotelMapping, "$.productMapping[?(@.channelRoomId=='" + channelRoomId + "')].roomId[0]");
    }

    private List<RoomRowWithStyle> buildRatePlanRowsWithStyle(Map<String, Object> hotel, Object hotelMapping, ChannelHotelDTO exist, ChannelHotelDTO diff) {
        var ratePlans = remoteService.hotelRatePlans(exist.getDerbyHotelId());
        List<RoomRowWithStyle> rows = new ArrayList<>();
        
        // 收集diff（请求）中的所有有效rateCode
        Set<String> diffRateCodes = new HashSet<>();
        if (diff.getRatesInfo() != null) {
            for (var ratePlan : diff.getRatesInfo()) {
                if (ratePlan != null && StringUtils.isNotBlank(ratePlan.getCode())) {
                    diffRateCodes.add(ratePlan.getCode());
                }
            }
        }
        
        // 收集exist中的所有有效rateCode（用于判断是否为新增）
        Set<String> existRateCodes = new HashSet<>();
        if (exist.getRatesInfo() != null) {
            for (var ratePlan : exist.getRatesInfo()) {
                if (ratePlan != null && StringUtils.isNotBlank(ratePlan.getCode())) {
                    existRateCodes.add(ratePlan.getCode());
                }
            }
        }
        
        // 添加exist中的所有rate，如果在diff中就标黄，否则不标黄
        if (exist.getRatesInfo() != null) {
            for (var ratePlan : exist.getRatesInfo()) {
                if (ratePlan != null && StringUtils.isNotBlank(ratePlan.getCode())) {
                    var rateDetail = JSONUtil.getObject(ratePlans, "$[?(@.rateId=='" + getRateMappingId(hotelMapping, ratePlan.getCode()) + "')][0]");
                    if (rateDetail != null) {
                        boolean shouldHighlight = diffRateCodes.contains(ratePlan.getCode());
                        rows.add(new RoomRowWithStyle(buildRateRow(hotel, exist, ratePlan, rateDetail), shouldHighlight));
                    }
                }
            }
        }
        
        // 添加diff中的新增rate（在exist中不存在的），标记为高亮
        if (diff.getRatesInfo() != null) {
            for (var ratePlan : diff.getRatesInfo()) {
                if (ratePlan != null && StringUtils.isNotBlank(ratePlan.getCode()) && !existRateCodes.contains(ratePlan.getCode())) {
                    var rateDetail = JSONUtil.getObject(ratePlans, "$[?(@.rateId=='" + getRateMappingId(hotelMapping, ratePlan.getCode()) + "')][0]");
                    if (rateDetail != null) {
                        rows.add(new RoomRowWithStyle(buildRateRow(hotel, diff, ratePlan, rateDetail), true));
                    }
                }
            }
        }
        
        return rows;
    }

    private String getRateMappingId(Object hotelMapping, String channelRateId) {
        return JSONUtil.getString(hotelMapping, "$.productMapping[?(@.channelRateId=='" + channelRateId + "')].rateId[0]");
    }

    private <T> void fillSheetGeneric(
            Sheet sheet,
            List<T> data,
            List<String> columns,
            Workbook workbook,
            IndexedColors highlightColor,
            java.util.function.Function<T, Map<String, Object>> rowExtractor,
            java.util.function.Predicate<T> highlightPredicate
    ) {
        Row headerRow = sheet.createRow(0);
        CellStyle headerStyle = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        font.setColor(IndexedColors.WHITE.getIndex());
        headerStyle.setFont(font);
        headerStyle.setFillForegroundColor(IndexedColors.GREY_50_PERCENT.getIndex());
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        for (int i = 0; i < columns.size(); i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(columns.get(i));
            cell.setCellStyle(headerStyle);
        }
        CellStyle highlightStyle = null;
        if (highlightColor != null) {
            highlightStyle = workbook.createCellStyle();
            highlightStyle.setFillForegroundColor(highlightColor.getIndex());
            highlightStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        }
        for (int i = 0; i < data.size(); i++) {
            T item = data.get(i);
            Map<String, Object> rowData = rowExtractor.apply(item);
            Row row = sheet.createRow(i + 1);
            for (int j = 0; j < columns.size(); j++) {
                Object value = rowData.getOrDefault(columns.get(j), "");
                Cell cell = row.createCell(j);
                cell.setCellValue(value == null ? "" : value.toString());
                if (highlightStyle != null && highlightPredicate.test(item)) {
                    cell.setCellStyle(highlightStyle);
                }
            }
        }
    }

    private List<String> getHotelColumns() {
        return Arrays.asList(
                "GroupID", "HotelCode", "ActiveStatus", "LateReserveTime", "SpecificInfo", "HotelBrand", "Star", "HotelName", "HotelEnName", "Brief", "BriefEn", "HotelDesc", "HotelDescEn", "LAT", "LON", "OpenYear", "FitmentYear", "Address_info", "Address_intl", "CityCode", "CityName", "CityNameEn", "CountryCode", "CountryName", "CountryNameEn", "Telephone", "Fax", "Email", "contactor", "RoomQuantity", "LocalName", "LocalAddress", "LocalCurrency", "SearchKey", "CheckIn", "checkOut", "HotelType", "ZIPCode", "SpokenLanguages", "ReviewNumber", "ReviewScore", "AddressVisible", "RemarkDescEn", "HotelUrl"
        );
    }

    private List<String> getRoomTypeColumns() {
        return Arrays.asList(
                "GroupID", "HotelCode", "RoomType", "RoomNameZh", "RoomNameEn", "RoomDescZh", "RoomDescEn", "ExtraBed", "Currency", "MaxOccupancy", "Person", "Children", "RoomArea", "Smoke", "Window", "FloorRange", "RoomCount", "WirelessIncluded", "WirelessFee", "WiredIncluded", "RoomBedType1Code", "RoomBedType1Num", "RoomBedType1Width", "RoomBedType1CategoryCode", "RoomBedType2Code", "RoomBedType2Num", "RoomBedType2Width", "RoomBedType2CategoryCode", "RoomBedType3Code", "RoomBedType3Num", "RoomBedType3Width", "RoomBedType3CategoryCode", "pmsRoomCode"
        );
    }

    private List<String> getRatePlanColumns() {
        return Arrays.asList(
                "Hotel Code", "Hotel Name", "Rate Plan Code", "Rate Plan Name-in Chinese", "Rate Plan Name-in English", "Package or Not", "Breakfast included or not?", "number of Breakfast= max adults?", "Begin Sell Date", "End Sell Date", "Begin Stay Date", "End Stay Date", "Payment Method", "RatePlanCategory", "PriceType"
        );
    }

    private static class RoomRowWithStyle {
        Map<String, Object> row;
        boolean highlight;
        RoomRowWithStyle(Map<String, Object> row, boolean highlight) {
            this.row = row;
            this.highlight = highlight;
        }
    }
}
