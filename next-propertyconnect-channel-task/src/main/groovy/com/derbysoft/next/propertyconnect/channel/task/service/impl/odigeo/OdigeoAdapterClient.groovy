package com.derbysoft.next.propertyconnect.channel.task.service.impl.odigeo

import com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.adapter.ChannelHotelActivationCustomizeService
import org.springframework.stereotype.Service

/**
 * @Created by <PERSON><PERSON><PERSON><PERSON> on 1/15/2025
 */

@Service
class OdigeoAdapterClient implements ChannelHotelActivationCustomizeService{
    @Override
    String channel() {
        return "ODIGEO"
    }

    @Override
    List<com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.RemoteChannelService.Operation> customizeProcedure() {
        return [
                com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.RemoteChannelService.Operation.SaveProperty,
                com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.RemoteChannelService.Operation.TriggerARIRefresh
        ];
    }
}
