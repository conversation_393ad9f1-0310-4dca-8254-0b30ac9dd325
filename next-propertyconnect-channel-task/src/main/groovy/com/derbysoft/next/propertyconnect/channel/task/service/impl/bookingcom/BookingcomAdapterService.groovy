package com.derbysoft.next.propertyconnect.channel.task.service.impl.bookingcom


import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelHotelDTO
import com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.adapter.ChannelHotelActivationCustomizeService
import org.springframework.stereotype.Service
/**
 * @Created by ji<PERSON><PERSON><PERSON> on 2023/6/14
 */

@Service
class BookingcomAdapterService implements ChannelHotelActivationCustomizeService{

    @Override
    String channel() {
        return "BOOKINGCOM"
    }

    @Override
    void customizeProperty(ChannelHotelDTO channelHotelDTO) {
        def extensions = channelHotelDTO.hotelInfo.extensions
        def hotelItem = channelHotelDTO.hotelInfo

        extensions.put("rateExpression", hotelItem?.rateType as String)
        extensions.put("userName", hotelItem?.settings?.userName as String)
        extensions.put("password", hotelItem?.settings?.password as String)

        var ariType = hotelItem?.ariType as String
        if ("Daily".equalsIgnoreCase(ariType)) {
            extensions.put("rateTaskType", "DAILY_RATE")
            extensions.put("priceModel", hotelItem?.settings?.rateModel == "OccupancyRate" ? "OBP" : "Standard")
        }
        if ("Los".equalsIgnoreCase(ariType)) {
            extensions.put("rateTaskType", "LOS_RATE")
            Optional.ofNullable(extensions?.settings?.maxLos)
                    .ifPresent(maxLos -> {
                        extensions.put("maxLos2Daily", maxLos as String)
                        extensions.put("maxLengthOfStay", maxLos as String)
                    })
        }
        extensions.put("roomLevelInventory", "true")
    }
}
