package com.derbysoft.next.propertyconnect.channel.task.service.impl.ctrip;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.Map;

/**
 * @Created by <AUTHOR> on 2023/4/18
 */

@ConfigurationProperties(prefix = "app.ctrip")
@Data
public class CtripConfigProperties {

    String baseUrl;

    Map<String, Setting> settings;

    Integer addDelay = 15000;

    Integer mappingTimeout = 1200000;

    Oversea oversea = new Oversea();

    @Data
    public static class Oversea {
        Boolean sendMail = false;
        String mailAddress = "";
    }

    @Data
    public static class Setting {
        String groupId;
        String username;
        String password;
    }

}
