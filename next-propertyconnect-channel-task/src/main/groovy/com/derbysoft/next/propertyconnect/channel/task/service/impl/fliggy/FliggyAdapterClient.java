package com.derbysoft.next.propertyconnect.channel.task.service.impl.fliggy;

import com.alibaba.fastjson2.JSONObject;
import com.derbysoft.next.commons.boot.logsupport.annotation.stream.StreamLog;
import com.derbysoft.next.commons.boot.logsupport.annotation.stream.StreamParameter;
import com.derbysoft.next.commons.core.logsupport.constant.LogConst;
import com.derbysoft.next.propertyconnect.channel.task.client.connectivityoperationapi.*;
import com.derbysoft.next.propertyconnect.channel.task.client.interceptor.apilayer.ApiLayerRequest;
import com.derbysoft.next.propertyconnect.channel.task.client.interceptor.retry.Retry;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import java.io.IOException;

/**
 * @Created by <AUTHOR> on 2023/6/8
 */

@FeignClient(name = "fliggy<PERSON>lient", url = "${app.fliggy.base-url}fliggy-next-api/fliggyhotel/")
public interface FliggyAdapterClient {

    @StreamLog(proxy = false, inheritPref = true, wrapHeaderAsParameter = true)
    @StreamParameter(name = LogConst.Parameter.TRACE_TOKEN, value = "#traceToken")
    @ApiLayerRequest
    @PostMapping("/credentials/query")
    JSONObject credentialQuery(@RequestBody OperationApiBasicRequest request,
                               @RequestHeader(LogConst.Stream.HEADER_TRACE_TOKEN) String traceToken);

    @StreamLog(proxy = false, inheritPref = true, wrapHeaderAsParameter = true)
    @StreamParameter(name = LogConst.Parameter.TRACE_TOKEN, value = "#traceToken")
    @Retry(retryOn = IOException.class)
    @ApiLayerRequest
    @PostMapping("/credentials")
    Object credentialCreation(@RequestBody JSONObject request,
                              @RequestHeader(LogConst.Stream.HEADER_TRACE_TOKEN) String traceToken);

    @StreamLog(proxy = false, inheritPref = true, wrapHeaderAsParameter = true)
    @StreamParameter(name = LogConst.Parameter.TRACE_TOKEN, value = "#traceToken")
    @ApiLayerRequest
    @PostMapping("/properties/query")
    OperationApiQueryResponse propertyQuery(@RequestBody OperationApiBasicRequest request,
                                            @RequestHeader(LogConst.Stream.HEADER_TRACE_TOKEN) String traceToken);

    @StreamLog(proxy = false, inheritPref = true, wrapHeaderAsParameter = true)
    @StreamParameter(name = LogConst.Parameter.TRACE_TOKEN, value = "#traceToken")
    @Retry(retryOn = IOException.class)
    @ApiLayerRequest
    @PostMapping("/properties/save")
    Object propertyCreation(@RequestBody OperationApiPropertyCreationRequest request,
                            @RequestHeader(LogConst.Stream.HEADER_TRACE_TOKEN) String traceToken);

    @StreamLog(proxy = false, inheritPref = true, wrapHeaderAsParameter = true)
    @StreamParameter(name = LogConst.Parameter.TRACE_TOKEN, value = "#traceToken")
    @Retry(retryOn = IOException.class)
    @ApiLayerRequest
    @PostMapping("/rooms/save")
    Object roomCreation(@RequestBody OperationApiRoomCreationRequest request,
                        @RequestHeader(LogConst.Stream.HEADER_TRACE_TOKEN) String traceToken);

    @StreamLog(proxy = false, inheritPref = true, wrapHeaderAsParameter = true)
    @StreamParameter(name = LogConst.Parameter.TRACE_TOKEN, value = "#traceToken")
    @Retry(retryOn = IOException.class)
    @ApiLayerRequest
    @PostMapping("/rateplans/save")
    Object rateCreation(@RequestBody OperationApiRateCreationRequest request,
                        @RequestHeader(LogConst.Stream.HEADER_TRACE_TOKEN) String traceToken);

    @StreamLog(proxy = false, inheritPref = true, wrapHeaderAsParameter = true)
    @StreamParameter(name = LogConst.Parameter.TRACE_TOKEN, value = "#traceToken")
    @Retry(retryOn = IOException.class)
    @ApiLayerRequest
    @PostMapping("/products/save")
    Object productCreation(@RequestBody OperationApiProductCreationRequest request,
                           @RequestHeader(LogConst.Stream.HEADER_TRACE_TOKEN) String traceToken);
}