package com.derbysoft.next.propertyconnect.channel.task.service.impl.agoda


import com.derbysoft.next.propertyconenct.channel.common.exception.BusinessException
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelProductsDTO
import com.derbysoft.next.propertyconnect.channel.task.service.AccountSettingService
import com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.RemoteChannelProductsService
import feign.FeignException
import groovy.xml.MarkupBuilder
import groovy.xml.XmlSlurper
import org.springframework.stereotype.Service
import org.springframework.web.context.request.RequestContextHolder
import org.springframework.web.context.request.ServletRequestAttributes

import java.util.function.Consumer

@Service
class AgodaServiceImpl implements RemoteChannelProductsService {

    private final AccountSettingService accountSettingService
    private final AgodaClient agodaClient

    AgodaServiceImpl(AccountSettingService accountSettingService, AgodaClient agodaClient) {
        this.accountSettingService = accountSettingService
        this.agodaClient = agodaClient
    }

    @Override
    ChannelProductsDTO getChannelProducts(String channelId, String channelHotelId) {
        def accountSettings = accountSettingService.getAccountSettings(channelId, channelHotelId)
        String response

        try {
            response = agodaClient.getAgodaProduct(accountSettings?.apiKey as String, createAgodaRequest(channelHotelId))
            return convertResponse(channelId, channelHotelId, response, product -> product['extensions'] = new HashMap<>())

        } catch (FeignException e) {
            if (!getIgnoreError()) {
                handleAuthorizationError(e)
            }
        }

        return null
    }

    @Override
    String channel() {
        return "AGODA"
    }

    def createAgodaRequest(String channelHotelId) {
        def writer = new StringWriter()
        def builder = new MarkupBuilder(writer)

        builder.request {
            criteria(language: "EN") {
                property(id: channelHotelId)
            }
        }

        writer.toString()
    }

    @Override
    ChannelProductsDTO getChannelProductsWithExtra(String supplierId, String channelId, String channelHotelId) {
        def accountSettings = accountSettingService.getAccountSettings(channelId, channelHotelId)
        String response

        try {
            response = agodaClient.getAgodaProduct(accountSettings?.apiKey as String, createAgodaRequest(channelHotelId))
            return convertResponse(channelId, channelHotelId, response, product -> {})

        } catch (FeignException e) {
            if (!getIgnoreError()) {
                handleAuthorizationError(e)
            }
        }

        return null
    }

    ChannelProductsDTO convertResponse(String channelId, String channelHotelId, String xml, Consumer<com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelProductsDTO.Product> productModifier) {
        if (!xml) {
            return null
        }
        def root = new XmlSlurper().parseText(xml)
        def property = root.property[0]
        def channelProducts = []
        property?.products?.product?.each { product ->
            def rate = property.rateplans.rateplan.find { it.@rateplan_id == product.@rateplan_id }.attributes()
            def room = property.rooms.room.find { it.@room_id == product.@room_id }.attributes()
            def channelProduct = new ChannelProductsDTO.Product(
                    channelRoomId: room?.room_id,
                    channelRoomName: room?.room_name,
                    channelRateId: rate?.rateplan_id,
                    channelRateName: rate?.rateplan_name,
                    status: 'Actived',
                    extensions: [
                            "roomExtension": [
                                    "distributor_hotel_id": channelHotelId,
                                    "distributor_room_id": room?.room_id,
                                    "distributor_room_name": room?.room_name,
                                    "distributor_bedtype": "",
                                    "distributor_occupancy": room?.total_persons
                            ],
                            "rateExtension": [
                                    "distributor_hotel_id": channelHotelId,
                                    "distributor_rateplan_id": rate?.rateplan_id,
                                    "distributor_rateplan_name": rate?.rateplan_name,
                                    "distributor_meal": rate?.valueAddInclusions ? String.join(",", rate?.valueAddInclusions) : "",
                                    "distributor_paytype": room?.offertype_name,
                                    "distributor_channel_name": channelId,
                                    "distributor_extension": [:]
                            ]
                    ]
            )
            productModifier?.accept(channelProduct)
            if (channelProduct?.channelRoomId && channelProduct?.channelRateId){
                channelProducts << channelProduct
            }
        }
        new ChannelProductsDTO(
                channelHotelId: property.@id,
                hotelName: property.@name,
                channelId: this.channel(),
                channelProducts: channelProducts,
                retrieveDate: RemoteChannelProductsService.currentDate()
        )
    }

    private void handleAuthorizationError(FeignException resp) {
        if (resp.status() < 300) {
            return
        }
        def xml = resp.contentUTF8()
        if (401 == resp.status() || 403 == resp.status()) {
            def root = new XmlSlurper().parseText(xml)
            def description = root?.errors?.error?.@description
            throw new BusinessException("" + description)
        }
        throw new BusinessException("Unexpected error: ${xml}")
    }

    def getIgnoreError() {
        def requestAttributes = RequestContextHolder.getRequestAttributes()

        if (requestAttributes instanceof ServletRequestAttributes) {
            def request = ((ServletRequestAttributes) requestAttributes).getRequest()

            if (null == request.getParameter("ignoreError")) {
                return true
            }
            return request.getParameter("ignoreError")?.toBoolean()
        }

        // 如果没有找到请求上下文，返回默认值 true
        return true
    }
}
