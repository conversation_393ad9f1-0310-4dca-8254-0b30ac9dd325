<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.derbysoft.next</groupId>
        <artifactId>next-parent</artifactId>
        <version>3.4.1.M1-SNAPSHOT</version>
    </parent>
    <modules>
        <module>next-propertyconnect-channel-api</module>
        <module>next-propertyconnect-channel-task</module>
        <module>next-propertyconnect-channel-common</module>
    </modules>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>next-propertyconnect-channel</artifactId>
    <name>PCChannel</name>
    <packaging>pom</packaging>
    <version>${revision}</version>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <revision>1.0.0</revision>
        <argLine>-Dfile.encoding=UTF-8 -Duser.language=en -Duser.region=US -Duser.timezone=UTC</argLine>
        <spring-boot.version>2.7.13</spring-boot.version>
        <fastjson.version>2.0.26</fastjson.version>
        <spring-cloud-starter-zipkin.version>2.2.8.RELEASE</spring-cloud-starter-zipkin.version>
        <spring-cloud-sgc.version>1.0.0-SNAPSHOT</spring-cloud-sgc.version>
        <log-support.version>1.0.0-SNAPSHOT</log-support.version>
        <aggregation-tool.version>1.0.1-SNAPSHOT</aggregation-tool.version>
        <extension-springdoc.version>1.0.1-SNAPSHOT</extension-springdoc.version>
        <schedulecenter-support.version>1.0.0-SNAPSHOT</schedulecenter-support.version>
        <schedule-center.version>1.2.4</schedule-center.version>
        <guava.version>31.1-jre</guava.version>
        <mapstruct.version>1.6.0</mapstruct.version>
        <lombok-mapstruct-binding.version>0.2.0</lombok-mapstruct-binding.version>
        <coverter-simplexml.version>2.9.0</coverter-simplexml.version>
        <springdoc-openapi-ui.version>1.7.0</springdoc-openapi-ui.version>
        <apache-common-collections.version>4.4</apache-common-collections.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.derbysoft.next</groupId>
                <artifactId>next-propertyconnect-channel-common</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-starter-zipkin</artifactId>
                <version>${spring-cloud-starter-zipkin.version}</version>
            </dependency>
            <dependency>
                <groupId>com.derbysoft.extension</groupId>
                <artifactId>spring-cloud-sgc-dynamic-config</artifactId>
                <version>${spring-cloud-sgc.version}</version>
            </dependency>
            <dependency>
                <groupId>com.derbysoft.extension</groupId>
                <artifactId>log-support-spring</artifactId>
                <version>${log-support.version}</version>
            </dependency>
            <dependency>
                <groupId>com.derbysoft.extension</groupId>
                <artifactId>schedulecenter-support</artifactId>
                <version>${schedulecenter-support.version}</version>
            </dependency>
            <dependency>
                <groupId>com.derbysoft.extension</groupId>
                <artifactId>springdoc-openapi</artifactId>
                <version>${extension-springdoc.version}</version>
            </dependency>
            <dependency>
                <groupId>com.derbysoft.extension</groupId>
                <artifactId>aggregation-tool</artifactId>
                <version>${aggregation-tool.version}</version>
            </dependency>
            <dependency>
                <groupId>com.derbysoft.schedulecenter</groupId>
                <artifactId>schedulecenter-taskframework</artifactId>
                <version>${schedule-center.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok-mapstruct-binding</artifactId>
                <version>${lombok-mapstruct-binding.version}</version>
            </dependency>
<!--            <dependency>-->
<!--                <groupId>org.apache.groovy</groupId>-->
<!--                <artifactId>groovy</artifactId>-->
<!--                <version>${groovy.version}</version>-->
<!--            </dependency>-->
<!--            <dependency>-->
<!--                <groupId>org.apache.groovy</groupId>-->
<!--                <artifactId>groovy-xml</artifactId>-->
<!--                <version>${groovy.version}</version>-->
<!--            </dependency>-->
            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>${apache-common-collections.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>com.alibaba.fastjson2</groupId>
            <artifactId>fastjson2</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
        </dependency>


    </dependencies>
</project>
